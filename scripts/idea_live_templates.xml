<?xml version="1.0" encoding="UTF-8"?>
<!-- IntelliJ IDEA Live Templates for OpticalRouterDriverController testConnect -->
<!-- Import this into IntelliJ IDEA: File -> Settings -> Editor -> Live Templates -> Import -->

<templateSet group="OpticalRouter">
  <template name="testconnect" value="### Test Connect Request for OpticalRouterDriverController&#10;POST $BASE_URL$/$DRIVER_PATH$/$NE_ID$/testConnect&#10;Content-Type: application/json&#10;Authorization: Bearer $AUTH_TOKEN$&#10;&#10;{&#10;  &quot;deviceType&quot;: &quot;$DEVICE_TYPE$&quot;,&#10;  &quot;ipAddress&quot;: &quot;$IP_ADDRESS$&quot;,&#10;  &quot;port&quot;: $PORT$,&#10;  &quot;timeoutSeconds&quot;: $TIMEOUT$,&#10;  &quot;authenticationData&quot;: {&#10;    &quot;type&quot;: &quot;PAP&quot;,&#10;    &quot;username&quot;: &quot;$USERNAME$&quot;,&#10;    &quot;password&quot;: &quot;$PASSWORD$&quot;&#10;  }&#10;}&#10;&#10;###" description="Generate testConnect HTTP request" toReformat="false" toShortenFQNames="true">
    <variable name="BASE_URL" expression="&quot;http://localhost:8080&quot;" defaultValue="&quot;http://localhost:8080&quot;" alwaysStopAt="true" />
    <variable name="DRIVER_PATH" expression="&quot;enc/v1/drv/juniper-ptx&quot;" defaultValue="&quot;enc/v1/drv/juniper-ptx&quot;" alwaysStopAt="true" />
    <variable name="NE_ID" expression="uuid()" defaultValue="&quot;d0baf626-c566-47c8-9ba5-390437d0913a&quot;" alwaysStopAt="true" />
    <variable name="AUTH_TOKEN" expression="&quot;{{auth_token}}&quot;" defaultValue="&quot;{{auth_token}}&quot;" alwaysStopAt="true" />
    <variable name="DEVICE_TYPE" expression="&quot;PTX10001-36MR&quot;" defaultValue="&quot;PTX10001-36MR&quot;" alwaysStopAt="true" />
    <variable name="IP_ADDRESS" expression="&quot;*************&quot;" defaultValue="&quot;*************&quot;" alwaysStopAt="true" />
    <variable name="PORT" expression="830" defaultValue="830" alwaysStopAt="true" />
    <variable name="TIMEOUT" expression="30" defaultValue="30" alwaysStopAt="true" />
    <variable name="USERNAME" expression="&quot;admin&quot;" defaultValue="&quot;admin&quot;" alwaysStopAt="true" />
    <variable name="PASSWORD" expression="&quot;password123&quot;" defaultValue="&quot;password123&quot;" alwaysStopAt="true" />
    <context>
      <option name="HTTP_REQUEST" value="true" />
      <option name="OTHER" value="true" />
    </context>
  </template>
  
  <template name="testconnectjava" value="@Test&#10;void testConnect_$TEST_NAME$() throws Exception {&#10;    // Arrange&#10;    UUID neId = UUID.fromString(&quot;$NE_ID$&quot;);&#10;    CommunicationSettings settings = new CommunicationSettings(&#10;        &quot;$DEVICE_TYPE$&quot;,&#10;        &quot;$IP_ADDRESS$&quot;,&#10;        $PORT$,&#10;        $TIMEOUT$,&#10;        new PasswordAuthenticationData(&quot;$USERNAME$&quot;, &quot;$PASSWORD$&quot;)&#10;    );&#10;    &#10;    String requestBody = &quot;&quot;&quot;&#10;        {&#10;          &quot;deviceType&quot;: &quot;$DEVICE_TYPE$&quot;,&#10;          &quot;ipAddress&quot;: &quot;$IP_ADDRESS$&quot;,&#10;          &quot;port&quot;: $PORT$,&#10;          &quot;timeoutSeconds&quot;: $TIMEOUT$,&#10;          &quot;authenticationData&quot;: {&#10;            &quot;type&quot;: &quot;PAP&quot;,&#10;            &quot;username&quot;: &quot;$USERNAME$&quot;,&#10;            &quot;password&quot;: &quot;$PASSWORD$&quot;&#10;          }&#10;        }&#10;        &quot;&quot;&quot;;&#10;    &#10;    // Act &amp; Assert&#10;    mockMvc.perform(post(&quot;/$DRIVER_PATH$/&quot; + neId + &quot;/testConnect&quot;)&#10;            .contentType(MediaType.APPLICATION_JSON)&#10;            .content(requestBody))&#10;            .andExpect(status().isOk());&#10;}&#10;$END$" description="Generate testConnect Java test method" toReformat="true" toShortenFQNames="true">
    <variable name="TEST_NAME" expression="&quot;success&quot;" defaultValue="&quot;success&quot;" alwaysStopAt="true" />
    <variable name="NE_ID" expression="uuid()" defaultValue="&quot;d0baf626-c566-47c8-9ba5-390437d0913a&quot;" alwaysStopAt="true" />
    <variable name="DEVICE_TYPE" expression="&quot;PTX10001-36MR&quot;" defaultValue="&quot;PTX10001-36MR&quot;" alwaysStopAt="true" />
    <variable name="IP_ADDRESS" expression="&quot;*************&quot;" defaultValue="&quot;*************&quot;" alwaysStopAt="true" />
    <variable name="PORT" expression="830" defaultValue="830" alwaysStopAt="true" />
    <variable name="TIMEOUT" expression="30" defaultValue="30" alwaysStopAt="true" />
    <variable name="USERNAME" expression="&quot;admin&quot;" defaultValue="&quot;admin&quot;" alwaysStopAt="true" />
    <variable name="PASSWORD" expression="&quot;password123&quot;" defaultValue="&quot;password123&quot;" alwaysStopAt="true" />
    <variable name="DRIVER_PATH" expression="&quot;enc/v1/drv/juniper-ptx&quot;" defaultValue="&quot;enc/v1/drv/juniper-ptx&quot;" alwaysStopAt="true" />
    <context>
      <option name="JAVA_CODE" value="true" />
    </context>
  </template>
  
  <template name="testconnectcurl" value="curl -X POST \&#10;  &quot;$BASE_URL$/$DRIVER_PATH$/$NE_ID$/testConnect&quot; \&#10;  -H &quot;Content-Type: application/json&quot; \&#10;  -H &quot;Authorization: Bearer $AUTH_TOKEN$&quot; \&#10;  -d '{&#10;    &quot;deviceType&quot;: &quot;$DEVICE_TYPE$&quot;,&#10;    &quot;ipAddress&quot;: &quot;$IP_ADDRESS$&quot;,&#10;    &quot;port&quot;: $PORT$,&#10;    &quot;timeoutSeconds&quot;: $TIMEOUT$,&#10;    &quot;authenticationData&quot;: {&#10;      &quot;type&quot;: &quot;PAP&quot;,&#10;      &quot;username&quot;: &quot;$USERNAME$&quot;,&#10;      &quot;password&quot;: &quot;$PASSWORD$&quot;&#10;    }&#10;  }'&#10;$END$" description="Generate testConnect cURL command" toReformat="false" toShortenFQNames="true">
    <variable name="BASE_URL" expression="&quot;http://localhost:8080&quot;" defaultValue="&quot;http://localhost:8080&quot;" alwaysStopAt="true" />
    <variable name="DRIVER_PATH" expression="&quot;enc/v1/drv/juniper-ptx&quot;" defaultValue="&quot;enc/v1/drv/juniper-ptx&quot;" alwaysStopAt="true" />
    <variable name="NE_ID" expression="uuid()" defaultValue="&quot;d0baf626-c566-47c8-9ba5-390437d0913a&quot;" alwaysStopAt="true" />
    <variable name="AUTH_TOKEN" expression="&quot;your-jwt-token-here&quot;" defaultValue="&quot;your-jwt-token-here&quot;" alwaysStopAt="true" />
    <variable name="DEVICE_TYPE" expression="&quot;PTX10001-36MR&quot;" defaultValue="&quot;PTX10001-36MR&quot;" alwaysStopAt="true" />
    <variable name="IP_ADDRESS" expression="&quot;*************&quot;" defaultValue="&quot;*************&quot;" alwaysStopAt="true" />
    <variable name="PORT" expression="830" defaultValue="830" alwaysStopAt="true" />
    <variable name="TIMEOUT" expression="30" defaultValue="30" alwaysStopAt="true" />
    <variable name="USERNAME" expression="&quot;admin&quot;" defaultValue="&quot;admin&quot;" alwaysStopAt="true" />
    <variable name="PASSWORD" expression="&quot;password123&quot;" defaultValue="&quot;password123&quot;" alwaysStopAt="true" />
    <context>
      <option name="BASH" value="true" />
      <option name="OTHER" value="true" />
    </context>
  </template>
</templateSet>
