
SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = error;
SET row_security = off;

CREATE SCHEMA IF NOT EXISTS mnc_mpd_layer3;

CREATE EXTENSION IF NOT EXISTS pg_stat_statements WITH SCHEMA public;

COMMENT ON EXTENSION pg_stat_statements IS 'track planning and execution statistics of all SQL statements executed';

CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA public;

COMMENT ON EXTENSION "uuid-ossp" IS 'generate universally unique identifiers (UUIDs)';

CREATE FUNCTION public.restore_csv_dump(dir_path text, file_separator text) RETURNS integer
    LANGUAGE plpgsql
    AS $$
DECLARE
	tab RECORD;
BEGIN
	for tab IN SELECT * FROM table_desc ORDER BY name LOOP
		PERFORM restore_csv_dump_table(dir_path, tab.name, tab.columns, file_separator);
	END LOOP;
	RETURN 1;
END;
$$;

CREATE FUNCTION public.restore_csv_dump_table(dir_path text, tbl_name text, columns_str text, file_separator text) RETURNS integer
    LANGUAGE plpgsql
    AS $$
DECLARE
	col RECORD;
BEGIN

    -- fetch all columns
	--SELECT array_to_string(array(SELECT (column_name::text) FROM information_schema.columns WHERE table_name = tbl_name order by column_name),', ')
	--		INTO columns_str;

    -- deletes content of the table
	EXECUTE 'DELETE FROM ' || tbl_name;

	-- copy data from CSV to the table
	-- PERFORM copy_from( tbl_name, columns_str, dir_path || '/' || tbl_name ) version  for unix;
	PERFORM copy_from( tbl_name, columns_str, dir_path || file_separator || tbl_name );

    -- update fields decoded as HEX in CSV file
	FOR col IN SELECT * FROM information_schema.columns WHERE table_name = tbl_name and data_type = 'bytea' LOOP
		EXECUTE format('UPDATE %I SET %I = decode(encode(%I,''escape''),''hex'')', tbl_name, col.column_name, col.column_name);
	END LOOP;
	RETURN 1;
END;
$$;

SET default_tablespace = '';

SET default_table_access_method = heap;

CREATE TABLE mnc_mpd_layer3.pdl3_ipvpn_service (
    ackd_at bigint,
    ackd_by character varying(255),
    acknowledged boolean DEFAULT false,
    customer_group_uuid uuid,
    customer_uuid uuid,
    id integer NOT NULL,
    lifecycle_state integer,
    oper_state integer DEFAULT 5,
    parent_id integer,
    performancetemplateid integer,
    reason_code integer DEFAULT 0,
    secondary_state character varying(255),
    service_mode integer,
    service_name character varying(255),
    timestamp_degraded bigint,
    timestamp_faulted bigint,
    uuid uuid,
    version integer
);

CREATE TABLE mnc_mpd_layer3.pdl3_ipvpn_service_entity_list (
    entity_id integer,
    index integer,
    ipvpn_service_id integer
);

CREATE TABLE public.activemq_acks (
    client_id character varying(250) NOT NULL,
    container character varying(250) NOT NULL,
    last_acked_id bigint,
    priority bigint DEFAULT 5 NOT NULL,
    selector character varying(250),
    sub_dest character varying(250),
    sub_name character varying(250) NOT NULL,
    xid bytea
);

CREATE TABLE public.activemq_lock (
    "time" bigint,
    broker_name character varying(250),
    id bigint NOT NULL
);

CREATE TABLE public.activemq_msgs (
    container character varying(250),
    expiration bigint,
    id bigint NOT NULL,
    msg bytea,
    msgid_prod character varying(250),
    msgid_seq bigint,
    priority bigint,
    xid bytea
);

CREATE TABLE public.bk_bookmark (
    contained_node_id integer,
    creation_time bigint DEFAULT 0 NOT NULL,
    id integer NOT NULL,
    jdoclass character varying(255),
    jdoversion integer,
    name0 character varying(1000) DEFAULT NULL::character varying,
    ntntype integer,
    parent_id integer,
    user_id integer DEFAULT 0 NOT NULL
);

CREATE TABLE public.bk_bookmark_group (
    id integer NOT NULL,
    jdoclass character varying(255),
    jdoversion integer,
    name0 character varying(1000) DEFAULT NULL::character varying,
    parent_id integer,
    user_id integer DEFAULT 0 NOT NULL
);

CREATE TABLE public.clockmtieresultdbimpl (
    id integer NOT NULL
);

CREATE TABLE public.cm_cua (
    id integer NOT NULL,
    password bytea,
    role text,
    scheduled_time bigint,
    username text
);

CREATE TABLE public.cm_cua_ne_ids (
    id integer NOT NULL,
    ne_ids integer NOT NULL,
    ne_order integer NOT NULL
);

CREATE TABLE public.cm_cua_subnet_ids (
    id integer NOT NULL,
    subnet_ids integer NOT NULL,
    subnet_order integer NOT NULL
);

CREATE TABLE public.cm_graph_node_location (
    client_id integer,
    client_ip character varying(255),
    graph_id integer,
    graph_type character varying(255),
    id integer NOT NULL,
    jdoversion integer,
    node_id integer,
    node_uid character varying(255),
    parent_uid character varying(255),
    x integer,
    y integer,
    z integer
);

CREATE TABLE public.cm_pca (
    id bigint NOT NULL,
    password bytea,
    username bytea,
    version integer
);

CREATE TABLE public.cm_pca_ne_ids (
    id integer NOT NULL,
    ne_ids integer NOT NULL,
    ne_order integer NOT NULL
);

CREATE TABLE public.cm_pca_subnet_ids (
    id integer NOT NULL,
    subnet_ids integer NOT NULL,
    subnet_order integer NOT NULL
);

CREATE TABLE public.cn_amp_config (
    ampconfigindex integer,
    enabled integer,
    id integer NOT NULL,
    portoid character varying(255),
    protocol integer,
    remsysdefgateway character varying(255),
    remsysipaddr character varying(255),
    remsysipmask character varying(255),
    remsysname character varying(255),
    remsyssnmpv1ifname character varying(255),
    remsyssrcipaddrifname character varying(255),
    remsyssrcipaddrtype integer,
    remtunnelbuffersize bigint,
    remtunnelcir bigint,
    remtunnelcos integer,
    remtunneleir bigint,
    remtunnelencaptype integer,
    remtunnelindex integer,
    remtunnelipaddr character varying(255),
    remtunnelipmask character varying(255),
    remtunnelmtu integer,
    remtunnelname character varying(255),
    remtunnelrip2pktsenabled integer,
    remtunnelsvlanid integer,
    remtunnelsvlanidenabled integer,
    remtunneltype integer,
    remtunnelvlanid integer,
    role integer,
    status integer
);

CREATE TABLE public.cn_apsgroup (
    apstype integer,
    clientport_id integer,
    direction integer,
    holdoff integer,
    id integer NOT NULL,
    isworkingeast boolean,
    protectionmech integer,
    protectionmodule_id integer NOT NULL,
    protectionport_aid character varying(255),
    protectionvch_id integer,
    revertivemode integer,
    workingmodule_id integer NOT NULL,
    workingport_aid character varying(255),
    workingvch_id integer
);

CREATE TABLE public.cn_bfd_mhop_session_status_f4 (
    id integer NOT NULL
);

CREATE TABLE public.cn_bfd_multi_hop_session_f4 (
    dest_address character varying(255),
    entity_name character varying(255),
    id integer NOT NULL,
    local_multiplier integer,
    min_rx_interval bigint,
    min_tx_interval bigint,
    source_address character varying(255),
    vrf_name character varying(255)
);

CREATE TABLE public.cn_boundary_clock (
    admin_state integer,
    bc_index integer,
    clock_class bigint,
    clock_identity character varying(255),
    domain_number integer,
    id integer NOT NULL,
    mediation_control integer,
    ne_index integer,
    oper_state integer,
    ptp_clock_timing_source integer,
    secondary_state integer,
    timing_source integer,
    timing_source_index character varying(255) NOT NULL
);

CREATE TABLE public.cn_card_cluster_f8 (
    id integer NOT NULL,
    name character varying(255),
    type character varying(255),
    user_label character varying(255)
);

CREATE TABLE public.cn_card_cluster_f8_card_list (
    card_list character varying(255),
    cn_card_cluster_f8_id integer
);

CREATE TABLE public.cn_card_translation_ec (
    description character varying(255),
    id integer NOT NULL,
    name character varying(255),
    translation character varying(255)
);

CREATE TABLE public.cn_cfm_eth (
    id integer NOT NULL,
    maindex integer,
    mdindex integer,
    mepindex integer,
    tgtmac character varying(255),
    tgtmepid integer
);

CREATE TABLE public.cn_cfm_template_template (
    _interface integer,
    administrative integer,
    ccmgeneration integer,
    ccminterval integer,
    direction integer,
    id integer NOT NULL,
    level integer,
    maformattype character varying(255),
    maindex integer,
    maname character varying(255),
    mdformattype character varying(255),
    mdid integer,
    mdname character varying(255),
    meplist text,
    primaryvid integer,
    profilename character varying(255),
    vlanpriority integer
);

CREATE TABLE public.cn_cgrxsc_f4 (
    createtime character varying(500) DEFAULT NULL::character varying,
    cursaid integer,
    id integer NOT NULL,
    name character varying(255),
    sci character varying(500) DEFAULT NULL::character varying,
    startedtime character varying(500) DEFAULT NULL::character varying,
    state character varying(500) DEFAULT NULL::character varying,
    stoppedtime character varying(500) DEFAULT NULL::character varying
);

CREATE TABLE public.cn_cgtxsc_f4 (
    createtime character varying(500) DEFAULT NULL::character varying,
    cursaid integer,
    id integer NOT NULL,
    name character varying(255),
    sci character varying(500) DEFAULT NULL::character varying,
    startedtime character varying(500) DEFAULT NULL::character varying,
    state character varying(500) DEFAULT NULL::character varying,
    stoppedtime character varying(500) DEFAULT NULL::character varying
);

CREATE TABLE public.cn_clock_probe (
    constteclrthreshold bigint,
    consttethreshold bigint,
    consttewindow bigint,
    ffoobserwindow integer,
    ffotarget integer,
    id integer NOT NULL,
    last_tie integer,
    lastfforesult integer,
    lasttealertcleartime bigint,
    lasttealerttime bigint,
    lasttietime bigint,
    margin_failed boolean,
    margincrosstime bigint,
    maskcrosstime bigint,
    maske_failed boolean,
    maxteclrthreshold integer,
    maxtethreshold integer,
    measurementtype integer,
    mtie_margin bigint,
    mtie_mask integer,
    mtie_restart integer DEFAULT 0,
    mtierestarttime bigint,
    runningfailedcount integer,
    source_failure boolean,
    source_oid character varying(255),
    srcdisqualify integer,
    srcdisqualifyonctethreshold integer,
    srcdisqualifyonmtiemask integer,
    tealertclearthreshold integer,
    tealertthreshold integer,
    tie_mes_rate integer,
    tie_mes_type integer,
    timeoflastfforesult bigint,
    user_mtie_mask integer
);

CREATE TABLE public.cn_clock_probe_history (
    constteclrthreshold bigint,
    consttemeasurementtime bigint,
    consttethreshold bigint,
    consttetotalarmtime bigint,
    consttewindow bigint,
    id integer NOT NULL,
    maxteclrthreshold integer,
    maxtemeasurementtime bigint,
    maxtethreshold integer,
    maxtetotalarmtime bigint,
    measurement_type integer,
    source_failure boolean,
    source_oid character varying(255),
    source_type integer,
    srcdisqualify integer,
    srcdisqualifyonctethreshold integer,
    srcdisqualifyonmtiemask integer,
    tie_meas_rate integer,
    user_mtie_mask integer
);

CREATE TABLE public.cn_cnx_connection_f7 (
    id integer NOT NULL,
    neposition integer,
    restorationstate integer,
    role integer,
    tunnelid character varying(255),
    tunnelinstance bigint,
    tunnelno bigint DEFAULT '-1'::integer NOT NULL
);

CREATE TABLE public.cn_config_ctrl (
    default_ne_identity_type integer DEFAULT 0,
    discover_topology_enabled boolean,
    globalperformancetemplateid integer,
    globalpollingmanagerid integer,
    jdoclass character varying(255),
    jdoid bigint NOT NULL,
    jdoversion integer,
    lldp_discover_topology_enabled boolean DEFAULT true,
    map_icon_setting_row1 integer DEFAULT 3 NOT NULL,
    map_icon_setting_row2 integer DEFAULT 4 NOT NULL,
    map_icon_setting_row3 integer DEFAULT 4 NOT NULL,
    service_map_icon_setting_row1 integer DEFAULT 0 NOT NULL,
    service_map_icon_setting_row2 integer DEFAULT 1 NOT NULL,
    service_map_icon_setting_row3 integer DEFAULT 4 NOT NULL,
    time_zone_enabled boolean DEFAULT false,
    time_zone_id character varying(255) DEFAULT ''::character varying,
    tree_icon_setting integer DEFAULT 3 NOT NULL,
    useadvaspecificserialnumbers boolean DEFAULT false
);

CREATE TABLE public.cn_conn_comp_restn_route (
    comp_restn_route_order integer,
    conn_id integer NOT NULL,
    line_id integer NOT NULL
);

CREATE TABLE public.cn_conn_crossconnects (
    crossconn_id integer NOT NULL,
    crossconn_order integer,
    subchconn_id integer NOT NULL
);

CREATE TABLE public.cn_conn_info (
    conn_id integer,
    entity_aid character varying(255) DEFAULT NULL::character varying,
    entity_entityindex character varying(255),
    entity_id integer,
    eth_intermediate boolean DEFAULT false,
    id bigint NOT NULL,
    is_alarmed boolean DEFAULT true,
    is_base_entity boolean DEFAULT false,
    is_downstream boolean DEFAULT true,
    is_endpoint boolean,
    is_interconnection_entity boolean DEFAULT false,
    is_oper_state_source boolean,
    is_working boolean,
    jdoclass character varying(255),
    jdoversion integer,
    line_id integer,
    locked_line_endpoints integer DEFAULT 0,
    mo_aid character varying(255),
    mo_type character varying(255),
    mod_type integer,
    module_aid character varying(255) DEFAULT NULL::character varying,
    module_entityindex character varying(255),
    module_id integer,
    ne_id integer,
    ordered integer,
    parent_conn_info_id bigint,
    subnet_id integer,
    termination_aid character varying(255)
);

CREATE TABLE public.cn_conn_nominal_prot_route (
    conn_id integer NOT NULL,
    line_id integer NOT NULL,
    nominal_route_order integer
);

CREATE TABLE public.cn_conn_nominal_route (
    conn_id integer NOT NULL,
    line_id integer NOT NULL,
    nominal_route_order integer
);

CREATE TABLE public.cn_conn_ochconns (
    optchconn_id integer NOT NULL,
    subchconn_id integer NOT NULL
);

CREATE TABLE public.cn_conn_ochvirtchannels (
    optchconn_id integer,
    optchconn_virtch_id integer,
    virtch_id integer
);

CREATE TABLE public.cn_conn_peer_vc (
    conn_id integer,
    vc_index integer
);

CREATE TABLE public.cn_conn_prot_route (
    conn_id integer NOT NULL,
    line_id integer NOT NULL,
    route_order integer
);

CREATE TABLE public.cn_conn_prot_trailconn (
    conn_id integer NOT NULL,
    connection_id integer NOT NULL,
    connection_order integer
);

CREATE TABLE public.cn_conn_restn_route (
    conn_id integer NOT NULL,
    line_id integer NOT NULL,
    restoration_route_order integer
);

CREATE TABLE public.cn_conn_route (
    conn_id integer NOT NULL,
    line_id integer NOT NULL,
    route_order integer
);

CREATE TABLE public.cn_conn_start_vc (
    conn_id integer,
    vc_index integer
);

CREATE TABLE public.cn_conn_state_counter (
    degradedcnt integer,
    failedcnt integer,
    id integer NOT NULL,
    jdoversion integer,
    niscnt integer,
    okcnt integer,
    unknowncnt integer
);

CREATE TABLE public.cn_conn_to_line_end_points (
    aend_neid integer,
    aend_port_index character varying(255),
    aend_port_label character varying(255) DEFAULT NULL::character varying,
    conn_id integer,
    id integer NOT NULL,
    line_id integer,
    zend_neid integer,
    zend_port_index character varying(255),
    zend_port_label character varying(255) DEFAULT NULL::character varying
);

CREATE TABLE public.cn_conn_trailconn (
    conn_id integer NOT NULL,
    connection_id integer NOT NULL,
    connection_order integer
);

CREATE TABLE public.cn_connect_guard_config (
    configfileloadapproved integer,
    cryptopasswordcontrol integer DEFAULT 1,
    id integer NOT NULL,
    portmirroringapproved integer DEFAULT 2,
    restoredatabaseapproved integer,
    restorefactoryapproved integer,
    simpleltpapproved integer DEFAULT 2,
    snmppropsid integer,
    softwareinstallapproved integer,
    softwareversionapproved character varying(255)
);

CREATE TABLE public.cn_connection (
    a_end_aid character varying(255),
    a_end_container_aid character varying(255),
    adminstate integer,
    alternatename character varying(1000) DEFAULT NULL::character varying,
    class_type integer,
    confpeerdirection boolean,
    confstartdirection boolean,
    containingochconnection_id integer,
    cptunnel_id integer,
    customer_id integer,
    entityresource integer,
    erp_state integer DEFAULT '-1'::integer,
    eth_a_end_neid integer,
    eth_z_end_neid integer,
    explorationstatus integer,
    facilitytype integer,
    flowbandwidth integer DEFAULT 0,
    id integer NOT NULL,
    is_explored boolean,
    is_managed boolean,
    ishandoverservice boolean DEFAULT false,
    ishidden boolean DEFAULT false,
    ismultilayernfc boolean DEFAULT false,
    istunnelabandoned boolean DEFAULT false,
    jdoclass character varying(255),
    jdoversion integer,
    keep_label boolean DEFAULT false,
    label character varying(1010) DEFAULT NULL::character varying,
    loose_path boolean DEFAULT false,
    nhcnxid character varying(1000) DEFAULT ''::character varying,
    ni_service_id character varying(255),
    nr_avail_restn_paths bigint,
    nr_guaranteed_paths bigint DEFAULT 0,
    number integer,
    oper_state integer,
    peer_ech_aid character varying(255),
    peer_ptp_aid character varying(255),
    peer_restoration_channel character varying(255),
    peerapsgroup_aid character varying(255) DEFAULT NULL::character varying,
    peerapsgroup_entityindex character varying(255),
    peerapsgroup_id integer,
    peerbundleindex integer,
    peerflowbandwidth integer DEFAULT 0,
    peermodule_aid character varying(255) DEFAULT NULL::character varying,
    peermodule_entityindex character varying(255),
    peermodule_id integer,
    peerne_id integer,
    peerpm_aid character varying(255) DEFAULT NULL::character varying,
    peerpm_entityindex character varying(255),
    peerpm_id integer,
    peerport_aid character varying(255) DEFAULT NULL::character varying,
    peerport_entityindex character varying(255),
    peerport_id integer,
    peerportptp_aid character varying(255),
    performancetemplateid integer,
    prot_peer_ptp_aid character varying(255),
    prot_start_ptp_aid character varying(255),
    protectionstate integer,
    protectiontype integer,
    protpeercm_aid character varying(255) DEFAULT NULL::character varying,
    protpeercm_entityindex character varying(255),
    protpeercm_id integer,
    protpeerport_id integer,
    protpeerportptp_aid character varying(255),
    protstartcm_aid character varying(255) DEFAULT NULL::character varying,
    protstartcm_entityindex character varying(255),
    protstartcm_id integer,
    protstartport_id integer,
    protstartportptp_aid character varying(255),
    provisioned_restoration_path character varying(255),
    remarks character varying(1000) DEFAULT NULL::character varying,
    restoration_mode integer DEFAULT 0,
    restore_type integer,
    reversion_mode integer DEFAULT 0,
    service integer,
    service_status_timestamp bigint DEFAULT '-1'::integer,
    servicegroup_id integer,
    serviceintent_id integer,
    servicetype integer,
    setupmode integer,
    spur_link boolean DEFAULT false,
    start_ech_aid character varying(255),
    start_ptp_aid character varying(255),
    start_restoration_channel character varying(255),
    startapsgroup_aid character varying(255) DEFAULT NULL::character varying,
    startapsgroup_entityindex character varying(255),
    startapsgroup_id integer,
    startbundleindex integer,
    startmodule_aid character varying(255) DEFAULT NULL::character varying,
    startmodule_entityindex character varying(255),
    startmodule_id integer,
    startne_id integer,
    startpm_aid character varying(255) DEFAULT NULL::character varying,
    startpm_entityindex character varying(255),
    startpm_id integer,
    startport_aid character varying(255) DEFAULT NULL::character varying,
    startport_entityindex character varying(255),
    startport_id integer,
    startportptp_aid character varying(255),
    subring boolean,
    terminationlevel integer,
    wdm_a_end_aid character varying(255),
    wdm_a_end_assigned_type integer,
    wdm_prot_a_end_aid character varying(255),
    wdm_prot_a_end_assigned_type integer,
    wdm_prot_z_end_aid character varying(255),
    wdm_prot_z_end_assigned_type integer,
    wdm_z_end_aid character varying(255),
    wdm_z_end_assigned_type integer,
    z_end_aid character varying(255),
    z_end_container_aid character varying(255)
);

CREATE TABLE public.cn_croma_conn_map (
    aendcroma integer,
    conn_type character varying(255),
    id integer NOT NULL,
    zendcroma integer
);

CREATE TABLE public.cn_croma_degrees_ec (
    cn_croma_service_path_id integer,
    degrees_list character varying(255)
);

CREATE TABLE public.cn_croma_entity_ec (
    associated_port character varying(500),
    croma_type character varying(255),
    display_name character varying(255),
    grid_spacing integer,
    id integer NOT NULL,
    is_auto_created boolean,
    max_bandwidth integer,
    max_grid integer,
    min_bandwidth integer,
    min_grid integer,
    number integer,
    resource_type integer,
    shared_spectrum_org character varying(500)
);

CREATE TABLE public.cn_croma_port_endpoints_ec (
    cn_croma_entity_ec_id integer,
    port_endpoint_list character varying(255)
);

CREATE TABLE public.cn_croma_service_path (
    croma_entity_id integer,
    id integer NOT NULL,
    org character varying(255),
    port_id character varying(255),
    service_path_index integer DEFAULT 0 NOT NULL,
    slc character varying(255)
);

CREATE TABLE public.cn_croma_slc (
    a_end_active_endpoint character varying(255),
    a_end_admin_state integer,
    a_end_eqerr character varying(20),
    a_end_eqstate character varying(20),
    a_end_eqtime bigint,
    a_end_operation integer,
    a_end_path_node_number integer,
    a_end_slc_inventory character varying(1500),
    alnmigtm bigint,
    id integer NOT NULL,
    identifier integer,
    userlabel character varying(255),
    z_end_active_endpoint character varying(255),
    z_end_admin_state integer,
    z_end_eqerr character varying(20),
    z_end_eqstate character varying(20),
    z_end_eqtime bigint,
    z_end_operation integer,
    z_end_path_node_number integer,
    z_end_slc_inventory character varying(1500)
);

CREATE TABLE public.cn_croma_slc_bandwidth (
    bandwidth integer,
    cn_croma_slc_id integer
);

CREATE TABLE public.cn_croma_slc_frequency (
    center_frequency integer,
    cn_croma_slc_id integer
);

CREATE TABLE public.cn_croma_uris_ec (
    cn_croma_entity_ec_id integer,
    endpoint_uri character varying(255)
);

CREATE TABLE public.cn_cross_connect (
    adminstate integer DEFAULT '-1'::integer NOT NULL,
    connalias character varying(255),
    conndirection integer,
    crs_ne_id integer NOT NULL,
    crscontaidlist character varying(255),
    crscontaidlisttwo character varying(255),
    crsfromaidtwo character varying(255),
    crsmcaidlist character varying(255),
    crstoaidtwo character varying(255),
    crstype integer,
    entityfrom_index character varying(500) NOT NULL,
    entityto_index character varying(500) DEFAULT NULL::character varying,
    id integer NOT NULL,
    path_node integer,
    type integer
);

CREATE TABLE public.cn_cross_connect_ec (
    aend_list bytea,
    direction character varying(100),
    entity_name character varying(255),
    id integer NOT NULL,
    is_fixed boolean,
    layer character varying(100),
    name character varying(255),
    snc_type character varying(100),
    zend_list bytea
);

CREATE TABLE public.cn_cross_connect_ec_tplist (
    cross_connect_id integer,
    index integer,
    tp_list character varying(255)
);

CREATE TABLE public.cn_cross_connects_ref (
    alias character varying(255),
    channel_from character varying(255),
    channel_to character varying(255),
    direction integer,
    entityfrom character varying(255),
    entityto character varying(255),
    facility_type character varying(255),
    id integer NOT NULL,
    nodeinpathforward integer,
    nodeinpathreverse integer,
    path_from character varying(255),
    path_to character varying(255),
    replace_status character varying(255),
    roadm_id integer,
    service_id integer
);

CREATE TABLE public.cn_ec_ctp (
    afltrshp character varying(255),
    afp_type character varying(255),
    assumedql integer,
    autoneg boolean,
    baudrate character varying(255),
    bits_per_symbol double precision,
    cbr_client_signal character varying(255),
    cbr_rate bigint,
    cd_compensation_range character varying(255),
    cfgspeed character varying(10),
    communication_channel character varying(255),
    constel character varying(255),
    delaycompensation integer,
    dupmode character varying(10),
    egress_shaped_speed integer,
    egress_shaping boolean,
    encryption_enabled boolean DEFAULT false,
    eth_type integer,
    expectedql integer,
    facility_type integer,
    fec_type character varying(255),
    filter_rolloff double precision,
    filter_shape character varying(255),
    frameformat integer,
    freqdet integer,
    frequency bigint,
    id integer NOT NULL,
    ingsvc character varying(255),
    layer_rate bytea,
    linecode integer,
    linetype integer,
    macaddr character varying(255),
    maintenance_signal character varying(10),
    mdix character varying(10),
    modulation character varying(255),
    mtu integer,
    odtu_typ character varying(255),
    odu_mux_type character varying(10),
    opt_bw integer,
    orginal_oper_state character varying(255),
    payload_type integer DEFAULT 0 NOT NULL,
    peermacaddress character varying(255),
    polarization_track_state character varying(255),
    portdirection integer,
    pvid character varying(255),
    qlenabled integer,
    rate integer,
    receivedql integer,
    rxpauseenabled boolean,
    sabitdes integer,
    secondstates integer,
    sk_id integer,
    so_id integer,
    sop character varying(255),
    sourceoftod character varying(255),
    squelchcontrol integer,
    squelchql integer,
    termination_mode character varying(255),
    transmitql integer,
    trib_slot character varying(512),
    tributary_port integer,
    trlayout character varying(10),
    txpauseenabled boolean
);

CREATE TABLE public.cn_ec_fltp (
    direction character varying(255),
    id integer NOT NULL,
    layer bytea,
    termination_mode character varying(255),
    termination_points bytea
);

CREATE TABLE public.cn_ec_optl (
    id integer NOT NULL,
    laneid integer,
    tunedfrequency integer,
    tunedwavelength integer
);

CREATE TABLE public.cn_ec_otsi_cpmgt (
    attenuation integer,
    id integer NOT NULL,
    path_loss integer,
    setpoint integer,
    setpoint_deviation integer
);

CREATE TABLE public.cn_ec_ptp (
    associated_plug_holder character varying(255),
    err_forward_mode integer,
    id integer NOT NULL,
    layer_rate bytea,
    opt_bandwidth integer,
    opt_laserstate boolean,
    opt_secondstates integer,
    opt_set integer,
    opt_tunedfrequency integer,
    opt_wavelength integer,
    optm_laserstate boolean,
    optm_secondstates integer,
    orginal_oper_state character varying(255),
    pluggable boolean,
    ratessupported bytea,
    type character varying(50)
);

CREATE TABLE public.cn_ec_ptp_otsi (
    constel character varying(255),
    ec_ctp_id integer,
    ec_ctp_otsi_id integer,
    entity_index character varying(500) DEFAULT NULL::character varying,
    filter_shape character varying(255),
    freqdet integer,
    frequency bigint,
    id integer NOT NULL,
    optical_bandwidth integer,
    otsi_id integer,
    slotwidth bigint
);

CREATE TABLE public.cn_ech_f7 (
    bandwidth integer,
    bitrate bigint,
    channel integer,
    chromdisper integer,
    connectionstate integer,
    datatype integer,
    facility integer,
    fectype integer,
    fend bytea,
    frameformat integer,
    host_name character varying(120),
    id integer NOT NULL,
    linecoding integer,
    maxbiterror integer,
    maxchromdisper integer,
    maxoptpower integer,
    maxpmd integer,
    minchromdisper integer,
    minoptpower integer,
    minosnr integer,
    opticalpower integer,
    osnrtransmit bigint,
    pmdtransmit integer,
    sourceprofile bytea
);

CREATE TABLE public.cn_egm_module (
    current_type character varying(255),
    id integer NOT NULL,
    slot_inventory_index integer
);

CREATE TABLE public.cn_elan_termination_point (
    conn_id integer,
    eth_aid character varying(255),
    eth_andpoint_container_aid character varying(255),
    eth_neid integer,
    tp_sequence integer
);

CREATE TABLE public.cn_elp_group (
    adminstate integer,
    elpgroupindex integer,
    freezeaction integer,
    holdofftime integer,
    id integer NOT NULL,
    label character varying(255),
    neindex integer,
    operationalstate integer,
    protectiontype integer,
    protectmep character varying(255),
    protectport character varying(255),
    protectportid integer,
    revertive integer,
    secondarystate integer,
    statisticsaction integer,
    status integer,
    switchaction integer,
    waittorestore integer,
    workingmep character varying(255),
    workingport character varying(255),
    workingportid integer
);

CREATE TABLE public.cn_elp_protflow (
    elpid integer,
    elpindex integer,
    id integer NOT NULL,
    neindex integer,
    protectedflow character varying(255)
);

CREATE TABLE public.cn_elp_unit (
    elpgroupindex integer,
    elpid integer,
    elpunitindex integer,
    id integer NOT NULL,
    neindex integer,
    port character varying(255),
    state integer,
    type integer
);

CREATE TABLE public.cn_encryption_status (
    encryptionaction integer,
    encryptionflowid integer,
    encryptionlastexecution bigint,
    encryptionresult character varying(1000),
    encryptionstatus integer,
    encryptionstatusid bigint,
    groupelementid integer,
    id integer NOT NULL
);

CREATE TABLE public.cn_entity (
    adminstate integer,
    aidstring character varying(255),
    arcstate integer,
    assignedshelftype character varying(255),
    assignedtype integer,
    assignmentstate integer,
    assignstate integer,
    bundle character varying(3000),
    cardname character varying(255),
    channel_prov integer,
    chassisid integer,
    cir bigint,
    class_instance_no_rel_entity integer,
    clei character varying(255),
    cmentityindex integer,
    connector integer,
    containedin character varying(500),
    coo character varying(255),
    deviceid integer,
    entity_a_aidstring character varying(255),
    entity_b_aidstring character varying(255),
    entityclass integer,
    entityclassofrelatedentity integer,
    entityindex character varying(500) NOT NULL,
    entityindexofrelatedentity character varying(500),
    equippedtype integer,
    facility_type integer,
    farendlifid integer,
    farendnodeipmonitor character varying(255),
    fiber integer,
    firmwarerev character varying(255),
    frequency bigint,
    from_rx_att_pad bigint,
    from_tx_att_pad bigint,
    fspentityclass integer,
    fulldescription character varying(255),
    hardwarerev character varying(255),
    id integer NOT NULL,
    identifier character varying(255),
    installdate bigint,
    installstate integer,
    inventorytype character varying(255) DEFAULT ''::character varying,
    ipaddr character varying(255),
    iptype integer,
    lane_channel integer,
    lifid integer,
    logicalinterfaceaid integer,
    logicalinterfaceid bigint,
    manufacturedate bigint,
    manufacturer character varying(255),
    modelname character varying(255),
    ne_id integer NOT NULL,
    neindex integer,
    nendtelinkidunn bigint,
    odu_tributary_time_slot_nums character varying(255),
    odutribportno character varying(255),
    operstate integer,
    original_serial_num character varying(255),
    oscillatortype character varying(255),
    outputcurrent integer,
    outputpower integer,
    outputvoltage integer,
    parent_id integer,
    parentrelpos integer,
    partnumber character varying(255),
    protection_cable_type integer DEFAULT 0,
    psuindex integer,
    ptpalias character varying(255),
    rack_number bigint,
    secondstates integer,
    serialnum character varying(255),
    shelfindex integer,
    sig_deg_period_odu bigint,
    sig_deg_thres_odu integer,
    single_fiber_location integer,
    size0 integer,
    slotindex integer,
    softwarerev character varying(255),
    temperature integer,
    termlevel integer,
    tim_mode_odu integer,
    to_rx_att_pad bigint,
    to_tx_att_pad bigint,
    transmit_frequency bigint,
    type integer,
    typestring character varying(255),
    typestring_keyword character varying(255),
    usi character varying(255),
    vcg_type integer,
    vchowneraid integer,
    vchowneraidstring character varying(255) DEFAULT ''::character varying,
    vendid character varying(255),
    virtual_port_channel_band integer DEFAULT 0,
    virtual_port_opu_payload_type integer,
    virtualportterminationmode integer DEFAULT 0,
    wch_optical_line_conn_state integer DEFAULT 0
);

CREATE TABLE public.cn_entity_cp (
    cpentityindex integer NOT NULL,
    id integer NOT NULL,
    indexaid character varying(255),
    ne_id integer NOT NULL
);

CREATE TABLE public.cn_entity_power_level (
    id integer NOT NULL,
    latest_opr_channel double precision,
    latest_opr_total double precision,
    latest_opt_channel double precision,
    latest_opt_total double precision,
    monitor_opr boolean,
    monitor_opt boolean,
    opr_high double precision,
    opr_low double precision,
    opt_high double precision,
    opt_low double precision
);

CREATE TABLE public.cn_eo_mpls_pw (
    adminstate integer,
    controlwordrx integer,
    controlwordtx integer,
    destip character varying(255),
    destmac character varying(255),
    discovertype integer,
    dscpmapcontrol integer,
    egressinterface character varying(500) DEFAULT NULL::character varying,
    encapsulationentity bytea,
    encapsulationtype integer,
    eomplspwindex integer,
    id integer NOT NULL,
    innerexpmappingcontrol integer,
    mode integer,
    neindex integer,
    operationalstate integer,
    outerexpmappingcontrol integer,
    outerstagcontrol integer,
    outerstagvlanid integer,
    outerstagvlanpri integer,
    outertagprimappingcontrol integer,
    outervlanethertype integer,
    rowstatus integer,
    rxtunnelexp bigint,
    rxtunnellabel bigint,
    rxtunnelttl bigint,
    rxvcexp bigint,
    rxvclabel bigint,
    rxvclabelcontrol integer,
    rxvcttl bigint,
    secondarystate integer,
    servicedelimiting integer,
    storagetype integer,
    txtunnelexp bigint,
    txtunnellabel bigint,
    txtunnelttl bigint,
    txvcexp bigint,
    txvclabel bigint,
    txvclabelcontrol integer,
    txvcttl bigint
);

CREATE TABLE public.cn_erp_f3 (
    admin_state integer,
    compatibile_version integer,
    erp_index integer,
    guard_time integer,
    hold_off_time integer,
    id integer NOT NULL,
    interconnection_erp integer,
    interconnection_erp_eid character varying(255),
    max_fp_num integer,
    neindex integer,
    node_state integer,
    oper_state integer,
    protect_mgmt_tunnel integer,
    raps_md_level integer,
    raps_node_id character varying(255),
    raps_ring_id integer,
    raps_virtual_channel_mep character varying(255),
    raps_vlan_ether_type bigint,
    raps_vlan_id integer,
    raps_vlan_prio integer,
    revertive integer,
    ring_port0 character varying(255),
    ring_port0_mep character varying(255),
    ring_port0_role integer,
    ring_port1 character varying(255),
    ring_port1_mep character varying(255),
    ring_port1_role integer,
    ringport0erpunitid integer,
    ringport0id integer,
    ringport1erpunitid integer,
    ringport1id integer,
    secondary_state integer,
    subring_without_virt_chan integer,
    user_label character varying(255),
    wait_to_restore_time integer
);

CREATE TABLE public.cn_erp_protflow_f3 (
    erp_index integer,
    erpid integer,
    flowref_index character varying(255),
    id integer NOT NULL,
    neindex integer
);

CREATE TABLE public.cn_erp_unit_f3 (
    erpindex integer,
    id integer NOT NULL,
    neindex integer,
    portstatus integer,
    ring_port_mep character varying(255),
    ring_port_role integer,
    unitindex integer
);

CREATE TABLE public.cn_esa_probe (
    destipaddress character varying(255),
    destmepmacaddr character varying(255),
    destmeptype integer,
    esaprobeindex character varying(255),
    id integer NOT NULL,
    multicos boolean DEFAULT false,
    portindex integer,
    protocol integer,
    sourceipaddress character varying(255),
    sourcemep character varying(255),
    sourcesubnetmask character varying(255),
    vlantagenabled integer,
    vlantagid integer,
    vlantagprio integer
);

CREATE TABLE public.cn_esa_probe_f3 (
    assoc_sched_group character varying(500) DEFAULT NULL::character varying,
    destipaddress character varying(255),
    destmepmacaddr character varying(255),
    destmeptype integer,
    direction integer,
    disthistorybins integer,
    disthistorytinterval integer,
    esaindex integer,
    esaname character varying(255) NOT NULL,
    historybins integer,
    historytinterval integer,
    id integer NOT NULL,
    inner2_vlan integer,
    inner2_vlan_enabled boolean,
    inner2_vlan_priority integer,
    inner_vlan integer,
    inner_vlan_enabled boolean,
    inner_vlan_priority integer,
    lagesa boolean DEFAULT false,
    multicos boolean DEFAULT false,
    ne_id integer NOT NULL,
    neindex integer,
    protocol integer,
    shelfindex integer,
    sla_monitor_type integer,
    slotindex integer,
    sourceipaddress character varying(255),
    sourcemep character varying(255),
    sourceport character varying(255),
    sourcesubnetmask character varying(255),
    vlantagenabled integer,
    vlantagid integer,
    vlantagprio integer
);

CREATE TABLE public.cn_esa_probe_mep_cos (
    condeltats bigint,
    cosconfiginterval integer,
    cosindex integer,
    costype integer,
    flrdeltat bigint,
    frlthreshold bigint,
    id integer NOT NULL,
    lmcosconfiginterval integer,
    lmpktsize integer,
    mepid bigint,
    mepindex integer,
    mepmacaddr character varying(255),
    meptype integer,
    multicos boolean DEFAULT false,
    neindex integer,
    pktsize integer,
    portindex integer,
    probeindex integer,
    probename character varying(255),
    shelfindex integer,
    slotindex integer
);

CREATE TABLE public.cn_esa_probe_reflector (
    id integer NOT NULL,
    ipaddress character varying(255),
    neindex integer,
    port_index character varying(500) DEFAULT NULL::character varying,
    probeindex integer,
    reflector_name character varying(255),
    shelfindex integer,
    slotindex integer
);

CREATE TABLE public.cn_esa_probe_schedule_group (
    "interval" bigint,
    action integer,
    actionprobelist character varying(255),
    descr character varying(255),
    duration bigint,
    esaprobeschedulegroupindex integer,
    id integer NOT NULL,
    neindex integer,
    probelist character varying(255),
    shelfindex integer,
    slotindex integer,
    starttime bigint,
    status integer,
    type integer
);

CREATE TABLE public.cn_eth_bw_info (
    cir_a_to_z bigint,
    cir_z_to_a bigint,
    eir_a_to_z bigint,
    eir_z_to_a bigint,
    id integer NOT NULL,
    jdoversion integer,
    total_bandwidth_a_to_z bigint,
    total_bandwidth_z_to_a bigint
);

CREATE TABLE public.cn_eth_flow_policer_f3 (
    adminstate integer,
    algorithm integer,
    associatedprofileindex integer,
    associatedshaperoid character varying(255),
    cbs integer,
    cir bigint,
    cirhi bigint,
    cirmaxhi bigint,
    cirmaxlo bigint,
    colormarkingflag integer,
    colormode integer,
    couplingflag integer,
    ebs integer,
    eir bigint,
    eirhi bigint,
    eirmaxhi bigint,
    eirmaxlo bigint,
    envelopeobject character varying(255),
    flow_index integer NOT NULL,
    flowpointid integer,
    id integer NOT NULL,
    ne_id integer NOT NULL,
    neindex integer NOT NULL,
    operstate integer,
    policerindex integer NOT NULL,
    policingenabled integer,
    polprof character varying(255),
    portindex integer NOT NULL,
    rank integer,
    secondarystate integer,
    shelfindex integer NOT NULL,
    slotindex integer NOT NULL,
    typeindex integer NOT NULL
);

CREATE TABLE public.cn_eth_ring_associated_object (
    assoc_obj_aid character varying(255),
    assoc_obj_entityindex character varying(255),
    assoc_obj_type character varying(255),
    id integer NOT NULL,
    ne_id integer,
    parent_id integer,
    ring_id integer
);

CREATE TABLE public.cn_eth_ring_group (
    id integer NOT NULL,
    jdoversion integer,
    name character varying(255),
    parent integer
);

CREATE TABLE public.cn_eth_ring_path (
    aid character varying(255),
    elementtype integer,
    entitymlid integer,
    entitymoid integer,
    eth_ring_id bigint,
    id integer NOT NULL,
    mo_ne_id integer,
    seqno integer
);

CREATE TABLE public.cn_eth_service (
    assigned integer,
    id integer NOT NULL,
    ifindex integer,
    portfsp150ccdbimpl_id integer,
    serviceindex integer,
    sfpdateofmanufacture character varying(255),
    sfppartnumber character varying(255),
    sfpserialnumber character varying(255),
    sfpvendorname character varying(255)
);

CREATE TABLE public.cn_evc_hn (
    adminstate integer,
    description bytea,
    evcid integer,
    fdfr_id integer,
    id integer NOT NULL,
    name bytea,
    numactunis integer,
    numtotalunis integer,
    objectstate integer,
    operstate integer,
    type integer
);

CREATE TABLE public.cn_evpn_f4 (
    arp_snooping_enabled boolean,
    evi integer,
    fld_ukn_unicast_suprr boolean,
    id integer NOT NULL,
    local_bum_label integer,
    local_route_distinguisher character varying(255),
    local_unicast_label integer,
    name character varying(255)
);

CREATE TABLE public.cn_evpn_flows_f4 (
    evpn_id integer,
    flows character varying(255),
    index integer
);

CREATE TABLE public.cn_evpn_local_route_target_f4 (
    evpn_id integer,
    index integer,
    local_route_target character varying(255)
);

CREATE TABLE public.cn_f3prc (
    accuracyadjustment integer,
    acknowledgeaction integer,
    adminstate integer,
    altitudecompensation integer,
    clockmode integer,
    currentql integer,
    id integer NOT NULL,
    neid integer,
    neindex integer,
    operationalstate integer,
    prcclocktype integer,
    prcindex integer,
    restartaction integer,
    secondarystate integer,
    shelfindex integer,
    slotindex integer,
    ssmmode integer
);

CREATE TABLE public.cn_f3sync (
    adminstate integer,
    cascadedeecs integer,
    cascadedeeec integer,
    clockidentity character varying(255),
    clockmode integer,
    domain integer DEFAULT 0,
    id integer NOT NULL,
    neid integer,
    neindex integer,
    networkclocktype integer,
    operstate integer,
    pplbw integer,
    secondarystate integer,
    selectedreference character varying(255),
    selectionmode integer,
    shelfindex integer,
    slotindex integer,
    syncindex integer,
    syncql integer,
    transmittedsynceclockidentity character varying(255),
    useeqlsforrefselection integer,
    wtrtime integer
);

CREATE TABLE public.cn_f3sync_ref (
    effectiveql integer,
    id integer NOT NULL,
    neid integer NOT NULL,
    neindex integer NOT NULL,
    operationtype integer,
    priority integer,
    receivedql integer,
    ref_id integer,
    refindex integer NOT NULL,
    shelfindex integer NOT NULL,
    slotindex integer NOT NULL,
    state integer,
    status integer,
    syncindex integer NOT NULL,
    syncreference character varying(255)
);

CREATE TABLE public.cn_f3systemclock (
    activetimesourceeid character varying(255),
    id integer NOT NULL,
    neid integer,
    neindex integer,
    systemclockindex integer,
    systemclockstatus integer
);

CREATE TABLE public.cn_f3timeclock (
    adminstate integer,
    atoi_current_offset integer,
    atoi_display_name character varying(255),
    atoi_jump_seconds integer,
    atoi_time_of_next_jump bigint,
    clockclass bigint,
    clockmode integer,
    crossref_phase_validation integer,
    crossref_validation_status integer,
    current_mode integer,
    currentql integer,
    e_prtc_mode_enabled integer,
    eprtc_ho_control integer,
    expectedql integer,
    frequencyclockmode integer,
    frequencyreference character varying(255),
    ho_recovery_threshold bigint,
    ho_suspend_time bigint,
    holdover_ready_progress integer,
    id integer NOT NULL,
    leap59 integer,
    leap61 integer,
    lock_progress integer,
    manual_ho_length integer,
    max_slew_rate character varying(255),
    neindex integer,
    operatedtimeref character varying(255),
    operational_mode integer,
    operationtype integer,
    operstate integer,
    phase_adjust integer,
    prtc_type integer,
    ref_list_mode integer,
    secondarystate integer,
    selectedreference character varying(255),
    selectionmode integer,
    shelfindex integer,
    slotindex integer,
    smooth_ho_recovery integer,
    steering_offset character varying(255),
    steering_state integer,
    syncrefcandidate integer,
    time_holdover_timeout integer,
    time_in_holdover integer,
    time_left_in_eprtc_holdover integer,
    timeholdoverperformance integer,
    timeindex integer,
    timescale integer,
    todsource integer,
    traceabilitystatus integer,
    utcoffset bigint,
    weighting_mode integer,
    wtrtime integer
);

CREATE TABLE public.cn_f3timeclock_ref (
    actualweight bigint,
    averphaseoffset integer,
    eprtcfiltertype integer,
    freqstability integer,
    id integer NOT NULL,
    neid integer NOT NULL,
    neindex integer NOT NULL,
    operationtype integer,
    priority integer,
    ref_id integer,
    refindex integer NOT NULL,
    shelfindex integer NOT NULL,
    slotindex integer NOT NULL,
    state integer,
    status integer,
    timeindex integer NOT NULL,
    timereference character varying(255),
    todsource character varying(255),
    todsourcestatus integer,
    weightpercent bigint
);

CREATE TABLE public.cn_f8_gtp (
    id integer NOT NULL,
    termination_points bytea
);

CREATE TABLE public.cn_f8_spsl (
    ec_ctp_id integer,
    entityindex character varying(500) DEFAULT NULL::character varying,
    id integer NOT NULL,
    slot_frq integer,
    slot_width integer
);

CREATE TABLE public.cn_fallback_ne_pwd (
    encr_pswd bytea NOT NULL,
    encr_pswd_2 bytea,
    id integer NOT NULL,
    jdoversion integer,
    ne_id integer NOT NULL,
    pswd_status character varying(255) NOT NULL,
    revealed_status_timestamp timestamp without time zone,
    username character varying(255) NOT NULL
);

CREATE TABLE public.cn_fdfr (
    adminstate integer,
    aend_jdoid integer,
    aendcrs_jdoid integer,
    evc_id integer,
    fdfrname character varying(255) NOT NULL,
    id integer NOT NULL,
    userlabel character varying(255),
    zend_jdoid integer,
    zendcrs_jdoid integer
);

CREATE TABLE public.cn_fdfr_end (
    flowdbimpl integer,
    ftpdbimpl integer,
    id integer NOT NULL,
    portdbimpl integer,
    portname character varying(255),
    shelfindex integer,
    slotindex integer
);

CREATE TABLE public.cn_fdfr_to_crs (
    crs_jdoid integer NOT NULL,
    fdfr_jdoid integer NOT NULL,
    fdfr_order integer
);

CREATE TABLE public.cn_fiber_connection_ec (
    a_end_entity_index_string character varying(255),
    id integer NOT NULL,
    layer character varying(255),
    link_name character varying(255),
    link_type character varying(255),
    tl_direction character varying(255),
    z_end_entity_index_string character varying(255)
);

CREATE TABLE public.cn_flow_cm (
    a2n_shaping_type character varying(255),
    a2ncir bigint,
    a2ncirhi bigint,
    a2neir bigint,
    a2neirhi bigint,
    accportindex integer,
    adminstate integer,
    autobandwidthconfigenabled integer,
    autocirpercentage integer,
    blockingenabled integer,
    cfmkey character varying(255),
    circuitname text,
    connectguardflowlist character varying(255),
    connectguardflowobject character varying(255),
    cos integer,
    ctagcontrol integer,
    ctagmatchrxprio integer,
    ctagvlanid integer,
    ctagvlanprio integer,
    esframeslossthres integer,
    flow_port1 character varying(255) DEFAULT '0.0'::character varying,
    flowindex integer,
    flown2acosprioritytagtype integer,
    flowname text,
    flowpolicingcontrol integer,
    guaranteeda2nbandwidthhi bigint,
    guaranteeda2nbandwidthlo bigint,
    hcosmgmtenabled integer,
    id integer NOT NULL,
    independenta2nshaperbw integer,
    loop_avoid_object character varying(500) DEFAULT '0'::character varying,
    maximuma2nbandwidthhi bigint,
    maximuma2nbandwidthlo bigint,
    multicos integer,
    n2acir bigint,
    n2acirhi bigint,
    n2aeir bigint,
    n2aeirhi bigint,
    n2aratelimitingenabled integer,
    n2ashapingtype character varying(255),
    neindex integer,
    network_interface character varying(255) DEFAULT '0.0'::character varying,
    operationalstate integer,
    policingenabled integer DEFAULT 0,
    portfsp150cmaccdbimpl integer,
    prio_map_profile character varying(255),
    prio_map_profile_n2a character varying(255),
    secondarystate integer,
    secureblock integer,
    securestate character varying(255),
    securestatelist character varying(255),
    sesframeslossthreshratio integer,
    shelfindex integer,
    slotindex integer,
    stagcontrol integer,
    stagmatchrxprio integer,
    stagvlanid integer,
    stagvlanprio integer,
    traffictype integer,
    type integer,
    untaggedmemberenabled integer,
    vlanmemberlist text
);

CREATE TABLE public.cn_flow_f3 (
    adminstate integer,
    flowindex integer NOT NULL,
    flowpointa integer,
    flowpointb integer,
    flowpointlist character varying(1500),
    flowtype integer,
    fpaindex character varying(255),
    fpbindex character varying(255),
    id integer NOT NULL,
    neid integer NOT NULL,
    neindex integer NOT NULL,
    operstate integer,
    secondarystate integer
);

CREATE TABLE public.cn_flow_hn (
    adminstate integer,
    deviceid integer,
    evcid integer,
    flowname bytea,
    id integer NOT NULL,
    objectstate integer,
    operstate integer,
    porthn4000dbimpl integer,
    type integer,
    uniid integer,
    vlanlist bytea,
    vlanpreservation integer
);

CREATE TABLE public.cn_flowpoint_cpd (
    associatedcpdprofile integer,
    cpdprofileindex integer,
    flowpointindex integer,
    id integer NOT NULL,
    neindex integer,
    portindex integer,
    shelfindex integer,
    slotindex integer
);

CREATE TABLE public.cn_flowpoint_cpd_profile (
    id integer NOT NULL,
    profileindex integer,
    profilename character varying(255)
);

CREATE TABLE public.cn_flowpoint_f3 (
    accportindex integer,
    adminstate integer,
    associatedaclprofile character varying(255),
    associatedelp character varying(255),
    auto_bw_config_enabled integer,
    auto_cir_percentage integer,
    bcast_rate_limit_enabled integer,
    bcast_rate_limit_speed_hi bigint,
    bcast_rate_limit_speed_lo bigint,
    circuitname character varying(255),
    combined_rate_limit_enabled integer,
    combined_rate_limit_speed_hi bigint,
    combined_rate_limit_speed_lo bigint,
    connect_guard_flow_object character varying(255),
    cos integer,
    ctag_control integer,
    ctag_vlan_id integer,
    ctag_vlan_priority integer,
    defaultmemberenabled integer,
    eg_inner_tag_enabled integer,
    eg_outer_tag_prio_map_enabled integer,
    egress_shaping_type integer,
    elpid integer,
    flow_point_ref_priomap_profile character varying(500) DEFAULT NULL::character varying,
    flowid integer,
    flowindex character varying(255),
    flowpointcpdid integer,
    flowpointindex integer,
    fpoamowner integer,
    fpowner integer,
    frame_fwd_enabled integer,
    guaranteed_flow_bandwith_hi bigint,
    guaranteed_flow_bandwith_lo bigint,
    hierarchical_cos integer,
    id integer NOT NULL,
    loop_avoidance character varying(255),
    max_flow_bandwith_hi bigint,
    max_flow_bandwith_lo bigint,
    mcast_rate_limit_enabled integer,
    mcast_rate_limit_speed_hi bigint,
    mcast_rate_limit_speed_lo bigint,
    mpflow integer,
    multicos integer,
    neindex integer,
    operationalstate integer,
    secondarystate integer,
    secure_blocking_control integer,
    secure_state integer,
    ses_frameloss_ratio integer,
    shelfindex integer,
    shg character varying(255) DEFAULT '0.0'::character varying,
    slotindex integer,
    stag_control integer,
    stag_vlan_id integer,
    stag_vlan_priority integer,
    untaggedframeenabled integer,
    vlan_member_action integer,
    vlan_member_action_vlan character varying(255),
    vlanmemberlist character varying(512)
);

CREATE TABLE public.cn_flowpoint_f4 (
    crypto_inner1_ether_type character varying(255),
    crypto_inner1_vlan_tag character varying(255),
    crypto_key_exchange_enable boolean,
    crypto_outer_ether_type character varying(255),
    crypto_outer_vlan_tag character varying(255),
    crypto_secure_block_enabled boolean DEFAULT false,
    crypto_secure_block_status character varying(255),
    crypto_secure_entity character varying(500) DEFAULT NULL::character varying,
    ctag character varying(255),
    ctagctl character varying(255),
    defme boolean,
    esi_enabled boolean,
    ethernet_segment_identifier character varying(255),
    guaranteed_bw bigint,
    hcosenabled boolean,
    id integer NOT NULL,
    ingmbrs character varying(255),
    inguntag boolean,
    max_bw bigint,
    mcosenabled boolean,
    polprof character varying(255),
    stag character varying(255),
    stagctl character varying(255)
);

CREATE TABLE public.cn_flowpoint_f4_rngprofs (
    flowpoint_f4_id integer,
    index integer,
    rngprofs character varying(255)
);

CREATE TABLE public.cn_ftp (
    aport_id integer,
    bport_id integer,
    fdfr_counter integer,
    ftpname character varying(255),
    id integer NOT NULL,
    pg_id integer
);

CREATE TABLE public.cn_ftp_cp (
    activenetwork integer,
    id integer NOT NULL,
    protectionstatus integer,
    protectiontype integer
);

CREATE TABLE public.cn_generic_mo_f3 (
    adminstate integer,
    id integer NOT NULL,
    index integer,
    neindex integer,
    operationalstate integer,
    rxmplslabel bigint,
    rxpwlabel bigint,
    secondarystates integer,
    shelfindex integer,
    slotindex integer,
    tdmentity character varying(255),
    txmplslabel bigint,
    txpwlabel bigint,
    vlan_priority integer,
    vlantag integer
);

CREATE TABLE public.cn_gps_receiver (
    admin_status integer,
    adv_interference_detection integer,
    agc integer,
    agc_l2 integer,
    agc_l5 integer,
    alarms_while_suspend integer,
    alias character varying(255),
    antenna_cable_length bigint,
    antenna_dc_power_disabled integer,
    antenna_dc_voltage integer,
    antenna_status integer,
    block_if_interferenced integer,
    block_if_jammed integer,
    block_if_spoofed integer,
    cno_mask bigint,
    coordinate_altitude bigint,
    coordinate_latitude character varying(255),
    coordinate_longitude character varying(255),
    delay_option integer,
    delay_value integer,
    elevation_mask bigint,
    failure_suspend_time bigint,
    forced_antenna_connected integer,
    gnss_bands_bitmap integer,
    gnss_dynamic_model integer,
    gnss_system integer,
    gnss_systems_bitmap integer,
    gnss_type integer,
    hdop bigint,
    horizontal_accuracy bigint,
    id integer NOT NULL,
    installation_type integer,
    location_mode integer,
    oper_status integer,
    pdop bigint,
    pdop_mask bigint,
    port_info character varying(255),
    pps_gen_condition integer,
    pps_generated_flag integer,
    receiver_index character varying(500) DEFAULT NULL::character varying,
    receiver_port_action integer,
    receiver_type integer,
    sat_min1_threshold bigint,
    sat_min2_threshold bigint,
    satellites_usable integer,
    self_survey_control integer,
    self_survey_period bigint,
    self_survey_position_accuracy bigint,
    self_survey_progress bigint,
    service_available_percentage character varying(255),
    service_available_time bigint,
    service_unavailable_time bigint,
    spoofing_location_difference bigint,
    spoofing_location_threshold bigint,
    spoofing_pps_difference bigint,
    spoofing_pps_threshold bigint,
    tdop bigint,
    time_advance_mode integer,
    time_advance_value integer,
    tracked_satellite_number bigint,
    vdop bigint,
    vertical_accuracy bigint
);

CREATE TABLE public.cn_gre_ip_interface (
    adminstate integer,
    greipinterfaceindex integer,
    id integer NOT NULL,
    neindex integer
);

CREATE TABLE public.cn_gre_tunnel (
    adminstate integer,
    gretunnelindex integer,
    id integer NOT NULL,
    neindex integer
);

CREATE TABLE public.cn_hdsl2shdsl (
    id integer NOT NULL,
    ifindex integer,
    index integer,
    portindex integer,
    slotindex integer
);

CREATE TABLE public.cn_intermediate_neids (
    conn_id integer,
    intermediatenetworkelements integer,
    ne_sequence integer
);

CREATE TABLE public.cn_intra_connection_ref (
    a_end_aid character varying(255),
    a_end_details character varying(255),
    id integer NOT NULL,
    replace_status character varying(255),
    roadm_id integer NOT NULL,
    type integer,
    z_end_aid character varying(255),
    z_end_details character varying(255)
);

CREATE TABLE public.cn_ip_interface_f4 (
    active_ip_v4_addrs bytea,
    active_ip_v6_addrs bytea,
    alias character varying(255),
    fnm character varying(255),
    id integer NOT NULL,
    if_type character varying(255),
    ip_v4_addrs bytea,
    ip_v6_addrs bytea,
    mtu integer,
    name character varying(255)
);

CREATE TABLE public.cn_key_exchange_profile (
    authpassword character varying(255),
    authpasswordtype integer DEFAULT 1,
    diffiehellmankeypairlen integer,
    id integer NOT NULL,
    index integer,
    mode integer,
    name character varying(255),
    userid character varying(255)
);

CREATE TABLE public.cn_key_exchange_profile_f4 (
    id integer NOT NULL,
    keylen character varying(255),
    mode character varying(255),
    name character varying(255)
);

CREATE TABLE public.cn_key_exchange_template (
    authpassword bytea,
    diffiehellmankeypairlen integer,
    id integer NOT NULL,
    mode integer,
    profilename character varying(255)
);

CREATE TABLE public.cn_l2_remote_slave (
    clock_class_ec integer,
    id integer NOT NULL,
    ismaster integer,
    mac_address character varying(255),
    neindex integer,
    parent_slot_name character varying(255),
    pcindex integer,
    port_clock integer,
    port_identity character varying(255),
    ppindex integer,
    rsindex integer,
    storage_type integer,
    type integer
);

CREATE TABLE public.cn_l3_flow_point (
    adminstate integer,
    id integer NOT NULL,
    l3flowpointindex integer,
    l3flowpointportindex integer,
    l3flowpointporttypeindex integer,
    neindex integer,
    operationalstate integer,
    secondarystate integer,
    shelfindex integer,
    slotindex integer
);

CREATE TABLE public.cn_l3_flowpoint_f4 (
    ctag character varying(255),
    ctagctl character varying(255),
    egress_shaping_type character varying(255),
    entity character varying(255),
    guaranteed_bw bigint,
    hcosenabled boolean,
    id integer NOT NULL,
    ingress_untag_control boolean,
    max_bw bigint,
    mcosenabled boolean,
    stag character varying(255),
    stagctl character varying(255),
    userlabel character varying(255)
);

CREATE TABLE public.cn_l3_flowpoint_ip_f4 (
    active_ip_v4_addrs bytea,
    active_ip_v6_addrs bytea,
    entity character varying(255),
    fpktfwd boolean,
    id integer NOT NULL,
    ip_v4_addrs bytea,
    ip_v6_addrs bytea,
    ipintf_asscsnc character varying(255),
    ipintf_type character varying(255),
    lastchg character varying(255),
    ldp_enabled boolean,
    mpls_enabled boolean,
    name character varying(255),
    userlabel character varying(255)
);

CREATE TABLE public.cn_l3_port_clock (
    admin_state integer,
    announce_msg_rate integer,
    announce_receipt_timeout integer,
    auto_asymmetry_comp_status integer,
    auto_ptp_disqualification integer,
    bmca_decision_code integer,
    clock_accuracy integer,
    default_gateway_control integer,
    delay_asymmetry integer,
    delay_asymmetry_comp integer,
    delay_req_dest_ip_addr_type integer,
    delay_req_msg_rate integer,
    delay_resp_timeout integer,
    event_msg_on_passive_port integer,
    expected_clock_class integer,
    flow_point_eid character varying(255),
    gateway character varying(255),
    gm_identity character varying(255),
    id integer NOT NULL,
    if_name character varying(255),
    ip_priority integer,
    ip_priority_map_mode integer,
    ip_protocol integer,
    ip_v4_address character varying(255),
    ip_v4_subnet_mask character varying(255),
    ip_v6_address character varying(255),
    ip_v6_gateway character varying(255),
    ipv6_prefix_length integer,
    local_priority bigint,
    master_ip_v4_address character varying(255),
    master_ip_v6_address character varying(255),
    ne_index integer,
    oper_state integer,
    parent_id integer,
    peer_clock_class bigint,
    port_clock_index integer,
    port_identity character varying(255),
    port_state integer,
    ptp_clock_id integer,
    ptp_clock_index integer,
    scaled_log_variance integer,
    secondary_state integer,
    steps_removed integer,
    sync_msg_rate integer,
    sync_receipt_timeout integer,
    synce_enabled integer,
    transmit_duration integer,
    unicast_message_neg_enabled integer,
    unicast_restart_timer integer,
    unicast_timeout integer,
    virtual_port_ctrl integer
);

CREATE TABLE public.cn_l3_qos_policer (
    adminstate integer,
    id integer NOT NULL,
    l3flowpointindex integer,
    l3flowpointportindex integer,
    l3flowpointporttypeindex integer,
    l3qospolicerindex integer,
    neindex integer,
    operationalstate integer,
    secondarystate integer,
    shelfindex integer,
    slotindex integer
);

CREATE TABLE public.cn_l3_qos_policer_f4 (
    adminstate integer,
    cos integer,
    envelope_id character varying(255),
    envelope_rank_id integer,
    id integer NOT NULL,
    operationalstate integer,
    polprof character varying(255),
    secondarystate integer
);

CREATE TABLE public.cn_l3_qos_shaper (
    adminstate integer,
    id integer NOT NULL,
    l3flowpointindex integer,
    l3flowpointportindex integer,
    l3flowpointporttypeindex integer,
    l3qosshaperindex integer,
    neindex integer,
    operationalstate integer,
    secondarystate integer,
    shelfindex integer,
    slotindex integer
);

CREATE TABLE public.cn_l3_queue_f4 (
    adminstate integer,
    avg_frame_size integer,
    bandwidth_profile character varying(255),
    buffersize_profile character varying(255),
    cos integer,
    id integer NOT NULL,
    operationalstate integer,
    secondarystate integer,
    type character varying(255)
);

CREATE TABLE public.cn_l3_traffic_ip_interface (
    adminstate integer,
    id integer NOT NULL,
    l3flowpointindex integer,
    l3flowpointportindex integer,
    l3trafficipifindex integer,
    neindex integer,
    operationalstate integer,
    secondarystate integer,
    shelfindex integer,
    slotindex integer
);

CREATE TABLE public.cn_lag_ec (
    ccm boolean,
    id integer NOT NULL,
    lacp boolean,
    lag_name character varying(255),
    mode character varying(255),
    user_desc character varying(255)
);

CREATE TABLE public.cn_lag_f3 (
    actor_admin_key integer,
    actor_oper_key integer,
    actor_system_id character varying(255),
    actor_system_priority integer,
    ccmdefect integer,
    collector_max_delay integer,
    framedistalgorithm integer,
    id integer NOT NULL,
    ifindex integer,
    ignore_collector_max_delay integer,
    lacpcontrol integer,
    lag_index integer,
    logicalport_id integer,
    mode integer,
    name character varying(255),
    neindex integer,
    partner_oper_key integer,
    partner_system_id character varying(255),
    partner_system_priority integer,
    protocols integer
);

CREATE TABLE public.cn_lag_member_ec (
    id integer NOT NULL,
    lag_id character varying(255),
    lagmember_port character varying(500) DEFAULT NULL::character varying,
    port_state character varying(255)
);

CREATE TABLE public.cn_lagport_f3 (
    actor_admin_key integer,
    actor_admin_state integer,
    actor_oper_key integer,
    actor_oper_state integer,
    actor_port integer,
    actor_port_priority integer,
    actor_system_id character varying(255),
    actor_system_priority integer,
    id integer NOT NULL,
    lag_member_oid character varying(255) DEFAULT '0.0'::character varying,
    lagid integer,
    lagindex integer,
    lagmember_id integer,
    lagportentityindex character varying(500) DEFAULT NULL::character varying,
    lagportindex integer,
    lagportstatus integer,
    neindex integer,
    oid_lacp_force_out_of_sync integer,
    port_aggregate_or_individual integer
);

CREATE TABLE public.cn_lagservicemap_f3 (
    currentmemberlink integer,
    id integer NOT NULL,
    lagid integer,
    lagindex integer,
    lagservicemapentityindex character varying(500) DEFAULT NULL::character varying,
    lagservicemapindex integer,
    linkassignmode integer,
    memberlinklist character varying(255),
    neindex integer,
    serviceobj character varying(255) DEFAULT '0.0'::character varying,
    storagetype integer
);

CREATE TABLE public.cn_lambda_info (
    conn_id integer,
    id bigint NOT NULL,
    jdoversion integer,
    lambda_name character varying(255),
    line_id integer
);

CREATE TABLE public.cn_lif_cp (
    activationstate integer,
    destnodeip character varying(255),
    destnodeip_monitor character varying(255),
    farendlifid integer,
    farendlifidstring character varying(255),
    fendnumb character varying(255),
    fendunnumb bigint,
    id integer NOT NULL,
    layer integer,
    lid bigint,
    lifcolorlink bigint,
    nendnumb character varying(255),
    nendunnumb bigint,
    startnodeip character varying(255),
    startnodeipmonitor character varying(255),
    transport character varying(255),
    transportstring character varying(255),
    tunnelid integer,
    type integer
);

CREATE TABLE public.cn_line (
    a_end_label character varying(255) DEFAULT NULL::character varying,
    a_endpoint_type character varying(255),
    a_entityindex character varying(255),
    aendalmport_id integer,
    aendlabel character varying(255) DEFAULT NULL::character varying,
    aendoscport character varying(500),
    aendrsm_id integer,
    aendrsmport integer,
    attenuationrx double precision,
    attenuationtx double precision,
    cloudrendered boolean,
    configstate integer,
    creationdate bigint DEFAULT 0 NOT NULL,
    eastne_id integer,
    ethbandwidthdata integer,
    fiberbreak boolean,
    fibertype character varying(255) DEFAULT NULL::character varying,
    flexgrid_total_capacity integer DEFAULT 0,
    id integer NOT NULL,
    is_flexgrid boolean DEFAULT false,
    islinea boolean,
    jdoclass character varying(255),
    jdoversion integer,
    label character varying(1000) DEFAULT NULL::character varying,
    length double precision,
    mismatchautodiscoveryraised boolean,
    protline_id integer,
    sharedrisklinkgroup integer,
    spanbudget double precision,
    spanlossenabled boolean,
    temetric integer,
    total_capacity integer DEFAULT 0,
    westne_id integer,
    z_end_label character varying(255) DEFAULT NULL::character varying,
    z_endpoint_type character varying(255),
    z_entityindex character varying(255),
    zendalmport_id integer,
    zendlabel character varying(255) DEFAULT NULL::character varying,
    zendoscport character varying(500),
    zendrsm_id integer,
    zendrsmport integer
);

CREATE TABLE public.cn_line_bw_restrict (
    bw_restrict_id integer NOT NULL,
    line_id integer NOT NULL
);

CREATE TABLE public.cn_line_conns (
    conn_id integer NOT NULL,
    line_id integer NOT NULL
);

CREATE TABLE public.cn_line_stby_conns (
    conn_id integer NOT NULL,
    line_id integer NOT NULL
);

CREATE TABLE public.cn_lldp_local_port (
    adminstatus integer,
    destaddressindex integer,
    id integer NOT NULL,
    ifref character varying(255) NOT NULL,
    ifreftype integer,
    lldpsystemdatadb_id integer NOT NULL,
    notificationenable integer,
    portid character varying(255) NOT NULL,
    portidsubtype integer
);

CREATE TABLE public.cn_lldp_port_config_ext (
    admin_status integer,
    dest_address_index integer,
    id integer NOT NULL,
    ifindex integer,
    notification_enable boolean,
    tlvs_tx_enable integer
);

CREATE TABLE public.cn_lldp_rem_table_entry (
    chassisid character varying(255) NOT NULL,
    chassisidsubtype integer,
    id integer NOT NULL,
    lldpsystemdatadb_id integer,
    localdestmacaddress integer,
    localifref character varying(255),
    localifreftype integer,
    localport_id integer,
    portid character varying(255) NOT NULL,
    portidsubtype integer,
    remindex integer,
    timemark bigint
);

CREATE TABLE public.cn_lldp_system_data (
    ageouts bigint,
    chassisid character varying(255) NOT NULL,
    chassisidsubtype integer,
    deletes bigint,
    drops bigint,
    id integer NOT NULL,
    inserts bigint,
    jdoversion integer,
    lastchangetime bigint,
    ne_id integer NOT NULL
);

CREATE TABLE public.cn_logicalobject_cc (
    assigned integer,
    containedin bigint,
    id integer NOT NULL,
    objectindex character varying(255),
    objecttype integer
);

CREATE TABLE public.cn_logicalport_f3 (
    adminstate integer,
    id integer NOT NULL,
    ifdesc character varying(255) NOT NULL,
    ifindex integer,
    ne_id integer NOT NULL,
    neindex integer,
    ocnstmindex integer,
    operstate integer,
    parentifindex integer,
    portindex integer,
    secondarystates integer,
    shelfindex integer,
    slotindex integer,
    vcgportindex integer,
    vcpathindex integer
);

CREATE TABLE public.cn_ma (
    ccminterval integer,
    componentid integer,
    id integer NOT NULL,
    madeletedmismatch boolean,
    maindex integer NOT NULL,
    mdfsp150cc825dbimpl integer,
    mdindex integer NOT NULL,
    meplist text,
    name bytea,
    name_format integer,
    ne_id integer NOT NULL,
    numberofvids integer,
    primaryvid integer,
    vlantable character varying(20000)
);

CREATE TABLE public.cn_ma_comp (
    compid_uuid character varying(255),
    componentid integer,
    flowpoint_uuid character varying(255),
    id integer NOT NULL,
    macompnumber bigint,
    maindex integer NOT NULL,
    mdindex integer NOT NULL,
    mipcreationcontrol integer,
    ne_id integer NOT NULL,
    numberofvids bigint,
    primaryvid integer
);

CREATE TABLE public.cn_ma_net (
    ccminterval integer,
    cfmmanetremotemepautodiscovery integer,
    id integer NOT NULL,
    maindex integer NOT NULL,
    mdindex integer NOT NULL,
    meplist text,
    name bytea,
    name_format integer,
    ne_id integer NOT NULL
);

CREATE TABLE public.cn_managed_object (
    creationdate bigint DEFAULT 0 NOT NULL,
    entityalias text,
    entityindex character varying(500) DEFAULT NULL::character varying,
    fulldescription character varying(255),
    id integer NOT NULL,
    jdoclass character varying(255),
    jdoversion integer,
    mtosiname text,
    ne_id integer,
    pmid integer,
    shortdescription character varying(255),
    uuid character varying(500)
);

CREATE TABLE public.cn_management_tunnel (
    associatedportoid character varying(255),
    buffersize bigint,
    cir bigint,
    cos integer,
    cvlanenabled integer,
    dhcpenabled integer,
    eir bigint,
    encapsulationtype integer,
    id integer NOT NULL,
    ipaddress character varying(255),
    ipmode integer,
    lineid integer,
    managementtunnelindex integer,
    mtu integer,
    name character varying(255),
    physicaladdress character varying(255),
    rip2pktsenabled integer,
    subnetmask character varying(255),
    svlanenabled integer,
    svlanid integer,
    type integer,
    vlanid integer
);

CREATE TABLE public.cn_master_clock (
    active_grants_announce_service integer,
    active_grants_delay_req_serv integer,
    active_grants_sync_service integer,
    active_time_ref_oid character varying(255),
    adminstate integer,
    clock_accuracy character varying(255),
    clock_class character varying(255),
    clock_state integer,
    id integer NOT NULL,
    master_index integer,
    master_ip character varying(255),
    master_ip_gateway character varying(255),
    master_ip_mask character varying(255),
    ne_index integer,
    operstate integer,
    physical_entity_index integer,
    port_status integer,
    ptp_clock_id character varying(255),
    ptp_domain_num bigint,
    ptp_prior_1 bigint,
    ptp_prior_2 bigint,
    ptp_profile integer DEFAULT 1 NOT NULL,
    redundancy_group integer,
    secstate integer,
    selectedtimeref integer,
    service_available_percentage character varying(255),
    service_available_time bigint,
    service_unavailable_time bigint,
    slot_string character varying(255),
    smpte_frame_rate_denominator bigint,
    smpte_frame_rate_numerator bigint,
    smpte_time_address_flags integer,
    time_clock_oid character varying(255),
    time_scale integer,
    time_source integer,
    timeclock integer,
    two_step_flag integer,
    used_input integer,
    utc_offset integer,
    utc_offset_valid integer,
    vlan_enabled boolean DEFAULT false,
    vlan_id integer,
    vlan_priority integer
);

CREATE TABLE public.cn_master_clock_interface (
    admin_state integer,
    announce_extension_tlv integer,
    bc_index integer,
    boundary_clock integer,
    clock_class bigint,
    clock_class_profile integer,
    delay_mechanism integer,
    domain integer,
    force_prtc_clock_quality integer,
    id integer NOT NULL,
    ip_priority integer,
    ip_priority_map_mode integer,
    ip_protocol integer,
    master_clock integer,
    master_clock_type integer,
    master_ip character varying(255),
    master_ipv6 character varying(255),
    master_ipv6_prefix_length integer,
    master_subnet character varying(255),
    master_virtual_port integer,
    max_announce_msg_rate integer,
    max_delay_resp_msg_rate integer,
    max_lease_duration integer,
    max_slaves integer,
    max_static_slaves integer,
    max_sync_msg_rate integer,
    mci_index integer,
    name character varying(255),
    ne_index integer,
    oper_state integer,
    parent_id integer,
    port_identity character varying(255),
    ptp_transport integer,
    ptp_transport_mode integer,
    remote_slave_aging_timeout integer,
    secondary_state integer,
    service_flow character varying(255),
    slaves_warning_threshold integer
);

CREATE TABLE public.cn_master_clock_vlan_ip (
    id integer NOT NULL,
    jdoversion integer,
    master_id integer,
    master_ip character varying(255) NOT NULL,
    master_vlan_gateway character varying(255) NOT NULL,
    master_vlan_id integer NOT NULL,
    master_vlan_priority integer NOT NULL,
    master_vlan_subnet_mask character varying(255) NOT NULL
);

CREATE TABLE public.cn_master_profile_file (
    "timestamp" bigint,
    comment character varying(1000),
    filename character varying(255),
    id integer NOT NULL,
    name character varying(255),
    ne_sw_version character varying(255),
    upload_timestamp bigint,
    version integer
);

CREATE TABLE public.cn_master_virtual_port (
    admin_state integer,
    bc_index integer,
    flow_point character varying(255),
    id integer NOT NULL,
    master_clock_interface integer,
    mci_index integer,
    mvp_index integer,
    ne_index integer,
    oper_state integer,
    ptpflowpoint integer,
    secondary_state integer
);

CREATE TABLE public.cn_md (
    cfm_mhf_creation integer DEFAULT 0 NOT NULL,
    id integer NOT NULL,
    level integer,
    mddeletedmismatch boolean,
    mdindex integer NOT NULL,
    name bytea,
    name_format integer,
    ne_id integer NOT NULL
);

CREATE TABLE public.cn_membership_range_profile (
    id integer NOT NULL,
    name character varying(255),
    vidhigh integer,
    vidlow integer
);

CREATE TABLE public.cn_mep (
    active integer,
    adminstate integer,
    aisclientmdlevel integer,
    aisgenenabled integer,
    aisgentriggertypes integer,
    aisinterval integer,
    aispriority integer,
    arcstate integer DEFAULT 1,
    associatedobject character varying(255),
    ccienabled integer,
    ccmltmpriority bigint,
    cfmmepdefects integer,
    cfmmeptagethertype bigint,
    direction integer,
    dot1agcfmmeplowprdef integer,
    dual_ended_count_all_prio integer,
    errorccmlastfailure text,
    highestprdefect integer,
    id integer NOT NULL,
    identifier integer NOT NULL,
    ifindex integer,
    lbm_data_tlv bytea,
    lbm_dest_is_mep_id integer,
    lbm_dest_mac_address character varying(255),
    lbm_dest_mep_id bigint,
    lbm_messages integer,
    lbm_result_ok integer,
    lbm_seq_number bigint,
    lbm_status integer,
    lbm_vlan_drop_enable integer,
    lbm_vlan_priority integer,
    lbr_bad_msdu bigint,
    lbr_in bigint,
    lbr_in_prev bigint,
    lbr_inout_of_order bigint,
    lbr_out bigint,
    lbr_out_prev bigint,
    llftriggertypes integer,
    lm_count_in_profile_only integer,
    lm_rx_count_all_priorities integer,
    lm_tx_count_all_priorities integer,
    lowprdef integer,
    macaddress bytea,
    mafsp150cc825dbimpl integer,
    maindex integer NOT NULL,
    mdfsp150cc825dbimpl integer,
    mdindex integer NOT NULL,
    mepdefects integer,
    mepdeletedmismatch boolean,
    ne_id integer NOT NULL,
    next_lbm_trans_id bigint,
    operstate integer,
    primaryvid bigint,
    secondarystates integer,
    xconccmlastfailure text
);

CREATE TABLE public.cn_mo_attr (
    id integer NOT NULL,
    jdoversion integer,
    managedobjectdb integer,
    shelfloc character varying(120)
);

CREATE TABLE public.cn_module (
    apsactiveindex integer,
    apsenabled boolean,
    apsworkingindex integer,
    channel bigint,
    cleicode character varying(255),
    cmdbimplextension_jdoid bigint,
    containerindex integer,
    finegrainedpminterval integer,
    frequency bigint,
    id integer NOT NULL,
    identifier2 character varying(255),
    issubmodule boolean,
    llfmode integer,
    llfmodeaction integer,
    lockout integer,
    mode integer DEFAULT 0,
    moduletypeid character varying(255),
    neindex integer,
    officialname character varying(255),
    osctype character varying(255),
    outputprotection integer,
    parent_name character varying(255),
    plugtype integer,
    portmode integer,
    rack integer,
    restartaction integer,
    security integer,
    sfpindex integer,
    sfplaserwavelength integer,
    sfpporttype integer,
    sfptype character varying(255),
    shelfindex integer,
    slotindex integer,
    snmpdyinggaspenabled integer,
    temperature integer,
    timingsource integer,
    voltage integer,
    wdmchannel integer
);

CREATE TABLE public.cn_module_cm_ext (
    cardmode integer,
    jdoclass character varying(255),
    jdoid bigint NOT NULL,
    jdoversion integer,
    module_id integer
);

CREATE TABLE public.cn_module_ec (
    assigned_typestring_keyword character varying(255),
    assignedcardtype character varying(255),
    channel integer,
    connector character varying(255),
    equipped_typestring_keyword character varying(255),
    equippedcardtype character varying(255),
    fiber character varying(255),
    id integer NOT NULL,
    mode character varying(255),
    partner_card bytea,
    reach character varying(255)
);

CREATE TABLE public.cn_module_f7 (
    assignmenttypechanged boolean,
    band integer,
    bidi_channel integer,
    cf_model character varying(255) DEFAULT ''::character varying,
    cf_sn character varying(255) DEFAULT ''::character varying,
    channel integer,
    channel_range integer DEFAULT 0,
    channels integer DEFAULT 0,
    channelspace integer,
    clevel integer DEFAULT 0,
    cmdbimplextension_jdoid bigint,
    cplugs integer,
    cports bigint,
    crates integer,
    currentassignmenttype integer,
    dcfibertype integer,
    deploy integer,
    dispersioncompensation integer,
    edfagain integer,
    edfapoweroutput integer DEFAULT 0,
    edfatilt integer,
    gainrange integer,
    id integer NOT NULL,
    lanaid integer,
    lanaidstring character varying(255) DEFAULT ''::character varying,
    laserdelaytimer integer,
    mapping integer DEFAULT 0,
    mode integer,
    module_network_fec integer DEFAULT 0,
    modulegroup integer,
    modulepartner1 character varying(255),
    modulepartner2 character varying(255),
    modulepartner3 character varying(255),
    modulepartner4 character varying(255),
    muxmethod integer DEFAULT 0,
    nplugs integer,
    nports bigint,
    nrates integer,
    osdbextension_jdoid bigint,
    peeraid character varying(255),
    plug_type_provision integer,
    psuoutputpower integer,
    rateprovision integer,
    reach integer,
    roadmno integer,
    subband integer,
    supplytype integer,
    thirdpartyusage integer,
    topology integer DEFAULT 0
);

CREATE TABLE public.cn_module_os_ext (
    jdoclass character varying(255),
    jdoid bigint NOT NULL,
    jdoversion integer,
    module_id integer,
    rawactiveport integer,
    rawlocked integer,
    rawpreferredport integer,
    revertivemode integer,
    statechangenetimestamp bigint
);

CREATE TABLE public.cn_msppg_f3 (
    action integer,
    allocatednumber integer,
    allocationmaximum integer,
    btodegtrigger integer,
    connectionlessport boolean,
    direction integer,
    fragmentserverlayer integer,
    groupstatus integer,
    id integer NOT NULL,
    interfacetype integer,
    macaddress character varying(255),
    maxnumfdfrs integer,
    neindex integer,
    numconfiguredfdfrs integer,
    protectiongroupindex integer,
    protport character varying(255),
    revertive integer,
    rowstatus integer,
    storagetype integer,
    switchmode integer,
    userlabel character varying(255),
    waittorestore integer,
    workport character varying(255)
);

CREATE TABLE public.cn_msppgport_f3 (
    groupindex integer,
    id integer NOT NULL,
    msppg_id integer,
    neindex integer,
    port_id integer,
    portindex character varying(255),
    unitindex integer,
    unitstate integer,
    unittype integer
);

CREATE TABLE public.cn_ne_backup (
    encrypted boolean,
    id integer NOT NULL,
    jdoversion integer,
    last_backup bigint,
    last_error character varying(1000),
    ne_id integer,
    ne_iden text,
    next_auto_backup bigint,
    next_manual_backup bigint,
    result integer,
    status integer,
    use_global boolean
);

CREATE TABLE public.cn_ne_backup_file (
    filename character varying(255),
    id integer NOT NULL,
    jdoversion integer,
    last_modified bigint,
    ne_id integer
);

CREATE TABLE public.cn_ne_backup_properties (
    "interval" integer,
    encryption_pwd bytea,
    id integer NOT NULL,
    jdoversion integer,
    max_retry_count integer
);

CREATE TABLE public.cn_ne_branches (
    line_id integer NOT NULL,
    ne_id integer NOT NULL
);

CREATE TABLE public.cn_ne_capabilities (
    ftplogin character varying(255),
    ftppassword bytea,
    id bigint NOT NULL,
    jdoversion integer,
    sw_transfer_protocol character varying(10)
);

CREATE TABLE public.cn_ne_config_file (
    globalsettings boolean,
    id bigint NOT NULL,
    neid integer
);

CREATE TABLE public.cn_ne_events (
    dbchangereceived boolean,
    jdoclass character varying(255),
    jdoversion integer,
    ne_id integer NOT NULL,
    nealarmeventsreceived integer,
    neeventsreceived integer,
    nereboottimestamp bigint
);

CREATE TABLE public.cn_ne_management_status (
    bulktrapenabled boolean,
    id integer NOT NULL,
    jdoversion integer,
    snmpsectrapsinkstatus integer,
    snmpsecwriteaccessstatus integer,
    snmptrapsinkstatus integer,
    snmpwriteaccessstatus integer,
    trapsinkregistration boolean,
    trapsinkregistrationdate bigint,
    trapsinkregistrationerrormsg character varying(255)
);

CREATE TABLE public.cn_nemi_sw_file (
    comment bytea,
    filename character varying(255),
    jdoclass character varying(255),
    jdoid bigint NOT NULL,
    jdoversion integer,
    servername character varying(255),
    servertype character varying(255)
);

CREATE TABLE public.cn_network_element (
    altipaddress character varying(255),
    cliprops_id integer,
    coldstarttime bigint,
    controlplanestate integer DEFAULT 0 NOT NULL,
    coordinate_latitude character varying(255),
    coordinate_longitude character varying(255),
    cp_auto_lif_prov boolean DEFAULT false,
    cp_node_name_syntax integer,
    cp_otn_enabled boolean DEFAULT false,
    cp_rest_interface_enabled boolean DEFAULT false,
    cp_wdm_enabled boolean DEFAULT false,
    createdbymtosi boolean DEFAULT false,
    creationdate bigint DEFAULT 0 NOT NULL,
    cryptopassword bytea,
    customicon character varying(255),
    defaultgateway character varying(255),
    dhcpenabled integer DEFAULT 2,
    dhcpipaddress character varying(255),
    dhcpmask character varying(255),
    dirty boolean DEFAULT false,
    discoverystate integer,
    encryptionmode character varying(255),
    event_proc_suppressed boolean DEFAULT false,
    finaldiscoverystate integer,
    fipsopermode integer DEFAULT 2,
    hidden boolean DEFAULT false,
    hostname character varying(255),
    httpprops_id integer,
    id integer NOT NULL,
    identity_type integer DEFAULT 0 NOT NULL,
    identkey integer DEFAULT 0 NOT NULL,
    interfacemask character varying(255),
    ipaddress character varying(255),
    jdoclass character varying(255),
    jdoversion integer,
    local_id integer,
    lpbcksysadminstate integer,
    lpbksysmacaddress character varying(255),
    mac character varying(100) DEFAULT ''::character varying,
    managed_by_ni_controller boolean DEFAULT false,
    managementstatusid integer,
    mibvariant integer,
    mindeptypestring character varying(255),
    minpasswordlength integer,
    modelname character varying(255),
    name0 character varying(1000) DEFAULT NULL::character varying,
    name_id character varying(1000) DEFAULT NULL::character varying,
    ne_capabilities_id bigint,
    neindex integer,
    netconfprops_id integer,
    networkintelligencestate integer DEFAULT 0 NOT NULL,
    niadvertisementmode integer DEFAULT 0 NOT NULL,
    niagentconnectionstatus character varying(30),
    nifirstsynctime timestamp without time zone,
    niid character varying(20),
    nilastsynctime timestamp without time zone,
    original_serial_num character varying(100) DEFAULT ''::character varying,
    performancemanagerid integer,
    performancetemplateid integer,
    pollingmanagerid integer,
    preferred_facility_type integer DEFAULT 0,
    profile_name character varying(255),
    proxyarpenabled integer DEFAULT 2,
    security_mode integer DEFAULT 1 NOT NULL,
    serial character varying(100) DEFAULT ''::character varying,
    slotindex integer,
    snmpprops_id integer,
    specifictypestring character varying(255),
    subnet_id integer,
    swupgradene_id bigint,
    sync boolean,
    sysautoprov integer,
    syscontact text,
    sysdescr character varying(255),
    sysip character varying(255),
    syslocation text,
    sysswver character varying(255),
    systemid character varying(255),
    timezoneoffset integer,
    type0 character varying(255),
    typestring character varying(255),
    usealtipaddress boolean,
    userdescr character varying(1000) DEFAULT ''::character varying,
    usertxt character varying(256) DEFAULT ''::character varying,
    uuid character varying(255),
    webinterface boolean
);

CREATE TABLE public.cn_network_element_swupgrade (
    affectedentity integer,
    common_upgrade_state character varying(255),
    continued boolean,
    current_swversion character varying(50),
    f7sw_download_mode integer DEFAULT 0,
    fw_activate_effect integer,
    id bigint NOT NULL,
    is_running_or_scheduled boolean,
    jdoversion integer,
    last_message character varying(1000),
    message character varying(1000),
    previous_swversion character varying(50),
    previous_upgrade_type character varying(255),
    priority integer,
    responding boolean,
    retries_count bigint,
    sw_file_name character varying(250),
    sw_server_type character varying(255),
    time_activate_start bigint,
    time_last_update bigint,
    time_upgrade_end bigint,
    time_upgrade_start bigint,
    upgrade_state character varying(255),
    upgrade_step character varying(255),
    upgrade_type character varying(255)
);

CREATE TABLE public.cn_ni_controller (
    connectionstatus character varying(30),
    controllerversion character varying(30),
    current boolean DEFAULT false,
    id integer NOT NULL,
    ipaddress character varying(50),
    jdoversion integer,
    lastresponsetime bigint,
    nictrlcommons_id integer,
    portnumber integer,
    protocoltype character varying(10)
);

CREATE TABLE public.cn_ni_controller_commons (
    id integer NOT NULL,
    jdoversion integer,
    lastreceivedevent character varying(30),
    password bytea,
    username bytea
);

CREATE TABLE public.cn_ni_tunnel (
    bitstuffing integer,
    facilitytype integer,
    farendterm integer,
    fectype integer,
    fromaid character varying(255),
    fromneid integer,
    fromneip character varying(255),
    id character varying(255) NOT NULL,
    jdoclass character varying(255),
    protectedpathrestored boolean,
    protectiontype integer,
    restorationmode integer DEFAULT 0,
    restorationtype integer DEFAULT 0,
    reversiontype integer DEFAULT 0,
    servicelayer integer,
    setpointdelta integer DEFAULT 0,
    term integer,
    toaid character varying(255),
    toneid integer,
    toneip character varying(255),
    tunnelname character varying(1000),
    workingpathrestored boolean
);

CREATE TABLE public.cn_notification_ec (
    alarm_id integer,
    id integer NOT NULL,
    message character varying(1000),
    name character varying(255),
    notification_ec_type character varying(255),
    raise_clear_name character varying(255),
    raise_clear_number integer,
    severity_no_service character varying(255),
    severity_protecting character varying(255),
    severity_working character varying(255),
    short_name character varying(255),
    trap_enterprise character varying(255),
    trap_name character varying(255)
);

CREATE TABLE public.cn_notification_ne_type_list (
    ne_type_list character varying(255),
    notification_ec_id integer
);

CREATE TABLE public.cn_ntp_clock (
    admin_state integer,
    clock_index integer,
    clock_precision bigint,
    id integer NOT NULL,
    leap_indicator bigint,
    ne_index integer,
    ntp_mode integer,
    ntp_type integer,
    oper_state integer,
    raw_data_transfer_interval integer,
    ref_clock_id character varying(255),
    server_state integer,
    stratum_level bigint,
    time_clock character varying(255),
    time_scale character varying(255)
);

CREATE TABLE public.cn_ntp_clock_collection_info (
    failed_file_name character varying(255),
    last_file_timestamp timestamp without time zone,
    ne_id integer NOT NULL,
    ntp_clock_id integer NOT NULL,
    retry_count integer
);

CREATE TABLE public.cn_ntp_clock_interface (
    admin_state integer,
    association_mode integer,
    broaadcast_ntp_version integer,
    broadcast_interval integer,
    broadcast_ip_v4_address character varying(255),
    broadcast_ip_v6_address character varying(255),
    broadcast_max_hops integer,
    broadcast_state integer,
    broadcast_sym_key_id integer,
    ci_index integer,
    clock_index integer,
    daytime_service_control integer,
    default_gateway_control integer,
    dscp bigint,
    hardened_responder_control integer,
    id integer NOT NULL,
    if_name character varying(255),
    ip_protocol integer,
    ip_v4_address character varying(255),
    ip_v4_gateway character varying(255),
    ip_v4_subnet_mask character varying(255),
    ip_v6_address character varying(255),
    ip_v6_address_prefix_length integer,
    ip_v6_gateway character varying(255),
    ne_index integer,
    ntp_clock integer,
    oper_state integer,
    parent_id integer,
    time_scale integer,
    time_service_control integer,
    udp_checksum integer
);

CREATE TABLE public.cn_ntp_dynamic_client (
    client_first_request_time timestamp without time zone NOT NULL,
    client_ip character varying(255) NOT NULL,
    client_last_request_time timestamp without time zone,
    client_mode integer,
    client_poll_interval integer,
    client_requests_count bigint,
    client_version integer,
    id integer NOT NULL,
    ignore boolean,
    jdoversion integer,
    ne_id integer,
    ntp_clock character varying(255),
    ntp_clock_interface character varying(255),
    ntp_clock_interface_ip character varying(255) NOT NULL,
    subnet_id integer
);

CREATE TABLE public.cn_ntp_flowpoint (
    admin_state integer,
    buffersize bigint,
    cirhigh bigint,
    cirlow bigint,
    cos integer,
    eirhigh bigint,
    eirlow bigint,
    id integer NOT NULL,
    inner1untaggedenabled integer,
    inner1vlanethertype integer,
    inner1vlanmemberlist character varying(255),
    inner2untaggedenabled integer,
    inner2vlanethertype integer,
    inner2vlanmemberlist character varying(255),
    ne_index integer,
    ntp_flow_index integer,
    oper_state integer,
    outeruntaggedenabled integer,
    outervlanethertype integer,
    outervlanmemberlist character varying(255),
    port_index integer,
    rowstatus integer,
    shelf_index integer,
    slot_index integer,
    storagetype integer,
    type smallint
);

CREATE TABLE public.cn_ntp_remote_client (
    ci_index integer,
    clock_index integer,
    cn_ntp_clock_interface integer,
    id integer NOT NULL,
    ipv4_address character varying(255),
    ne_index integer,
    ntp_mode integer,
    ntp_rc_index integer,
    ntp_version integer,
    parent_id integer
);

CREATE TABLE public.cn_ntp_remote_server (
    admin_state integer,
    clock_index integer,
    id integer NOT NULL,
    max_poll_interval integer,
    min_poll_interval integer,
    ne_index integer,
    ntp_clock integer,
    ntp_rs_index integer,
    oper_state integer,
    parent_id integer,
    preferred integer,
    reach bigint,
    row_status integer,
    secondary_state integer,
    selection_status integer,
    server_address character varying(255),
    storage_type integer
);

CREATE TABLE public.cn_ntp_server_activity (
    ne_id integer NOT NULL,
    ntp_clock character varying(255) NOT NULL,
    num_requests bigint,
    time_stamp timestamp without time zone NOT NULL
);

CREATE TABLE public.cn_ntp_tracked_client (
    ci_index integer,
    clock_index integer,
    cn_ntp_clock_interface integer,
    id integer NOT NULL,
    ipv4_address character varying(255),
    ipv6_address character varying(255),
    ne_index integer,
    ntp_tc_index integer,
    parent_id integer
);

CREATE TABLE public.cn_ntpfiletransfer_properties (
    collection_cycle integer,
    connection_timeout integer,
    file_filter character varying(255),
    id integer NOT NULL,
    jdoversion integer,
    last_connection_time timestamp without time zone,
    ntp_secure_mode integer,
    number_of_retries integer,
    read_timeout integer
);

CREATE TABLE public.cn_olm_session_monitor (
    admin_state integer,
    controller_index integer,
    id integer NOT NULL,
    neindex integer,
    session_monitor_index integer,
    shelfindex integer,
    slotindex integer
);

CREATE TABLE public.cn_opt_router_plug (
    id integer NOT NULL,
    nrl character varying(255),
    unified_admin_state character varying(255),
    unified_operational_state character varying(255),
    vendor_part_number character varying(255)
);

CREATE TABLE public.cn_opt_router_port (
    id integer NOT NULL,
    layer_qualifiers bytea,
    nrl character varying(255),
    target_output_power numeric(20,2),
    unified_admin_state character varying(255),
    unified_operational_state character varying(255)
);

CREATE TABLE public.cn_optrouterport_frequencyslot (
    center_frequency numeric(20,6),
    port_id integer,
    slot_width numeric(20,2)
);

CREATE TABLE public.cn_optrouterport_wavelength (
    port_id integer,
    wavelength numeric(20,5)
);

CREATE TABLE public.cn_path (
    id integer NOT NULL,
    name character varying(255),
    role integer,
    tunnelindex character varying(255)
);

CREATE TABLE public.cn_path_elements (
    channeldown integer,
    channelup integer,
    follow integer,
    id integer NOT NULL,
    ip character varying(255),
    ipmonitor character varying(255),
    jdoversion integer,
    lid bigint,
    lifip character varying(255),
    line bigint,
    linestring character varying(255),
    path_id integer,
    pathid character varying(255),
    sequencenumber bigint,
    tet integer,
    tid character varying(255),
    tidmonitor character varying(255),
    tie integer
);

CREATE TABLE public.cn_pg_f3 (
    action integer,
    allocatednumber integer,
    allocationmaximum integer,
    connectionlessport boolean,
    direction integer,
    fragmentserverlayer integer,
    groupstatus integer,
    id integer NOT NULL,
    interfacetype integer,
    macaddress character varying(255),
    maxnumfdfrs integer,
    neindex integer,
    numconfiguredfdfrs integer,
    porttprolestate integer,
    protectiongroupindex integer,
    protport character varying(255),
    revertive boolean,
    shelfindex integer,
    slotindex integer,
    switchmode integer,
    userlabel character varying(255),
    waittorestore integer,
    workport character varying(255)
);

CREATE TABLE public.cn_pgport_f3 (
    groupindex integer,
    id integer NOT NULL,
    neindex integer,
    pg_id integer,
    port_id integer,
    shelfindex integer,
    slotindex integer,
    unitindex integer,
    unitstate integer,
    unittype integer
);

CREATE TABLE public.cn_phyots_oms_ctp_f8 (
    actgain integer,
    acttilt integer,
    actvoa integer,
    autogain integer,
    autostatus boolean,
    autotilt integer,
    autovoa integer,
    entityindex character varying(500) DEFAULT NULL::character varying,
    gain integer,
    gaintype character varying(255),
    id integer NOT NULL,
    nwfbrtyp character varying(255),
    tilt integer,
    voa integer
);

CREATE TABLE public.cn_planner_pm (
    attribute character varying(255),
    entity_id integer,
    historical_index integer,
    id integer NOT NULL,
    module_id integer,
    name character varying(255),
    ne_id integer,
    time_stamp character varying(255),
    uri character varying(255),
    value character varying(255)
);

CREATE TABLE public.cn_plug_ec_rates (
    plug_ec_id integer,
    rates character varying(255)
);

CREATE TABLE public.cn_policer_envelope_f3 (
    couplingflag integer,
    id integer NOT NULL,
    neindex integer,
    policerenvelopeindex integer
);

CREATE TABLE public.cn_policer_profile (
    cbs integer,
    cirhi bigint,
    cirlo bigint,
    colormode integer,
    couplingflag integer,
    ebs integer,
    eirhi bigint,
    eirlo bigint,
    id integer NOT NULL,
    index integer,
    name character varying(255)
);

CREATE TABLE public.cn_port (
    alsconfig integer,
    assigned boolean,
    autoconfig integer,
    connected_internal_channel character varying(255),
    containing_module_id integer,
    containing_plug_id integer,
    descr character varying(255),
    extid character varying(255),
    fecconfig integer,
    fingerprintmode integer,
    highspeed bigint,
    id integer NOT NULL,
    ifindex integer,
    lastchange bigint,
    linkbudget integer,
    linkbudgetwarning integer,
    linkupdowntrapenable integer,
    mtu integer,
    physaddress character varying(255),
    portno integer,
    porttype integer,
    sfpdateofmanufacture character varying(255),
    sfppartnumber character varying(255),
    sfpserialnumber character varying(255),
    sfpvendorname character varying(255),
    speed bigint,
    tonestatus integer,
    ttpdbimplextension_jdoid bigint,
    type0 integer,
    wanindex integer
);

CREATE TABLE public.cn_port_150cp (
    adminstatus integer,
    advertisedtechnologyability integer,
    assignstate integer,
    autoneg integer,
    connectortype character varying(255),
    continuitytest integer,
    currentdatarate integer,
    dasaswapping integer,
    datarate integer,
    equipstate integer,
    fullduplex integer,
    id integer NOT NULL,
    label character varying(255),
    linklossfwd integer,
    linklossfwdactive integer,
    localtechnologyability integer,
    loopbackstatus integer,
    loopbacktimer integer,
    numconfiguredfdfrs integer,
    oamactive integer,
    oamenable integer,
    oaminfoenable integer,
    receivedtechnologyability integer,
    remportindex integer,
    remsignallingdetected integer,
    ringcontroller integer,
    status integer,
    suppautoneg integer,
    supprates integer,
    supptxmodes integer,
    userstring character varying(255)
);

CREATE TABLE public.cn_port_bits_group_member (
    activation_state integer,
    dtype character varying(31),
    group_id integer NOT NULL,
    group_index integer,
    id integer NOT NULL,
    jdoversion integer,
    line_type integer,
    ne_id integer,
    ne_index integer,
    port_alias character varying(255),
    port_index integer,
    shelf_index integer,
    slot_index integer
);

CREATE TABLE public.cn_port_clock (
    admin_state integer,
    announce_msg_rate integer,
    announce_receipt_timeout integer,
    auto_asymmetry_comp_status integer,
    auto_ptp_disqualification integer,
    bandwidth_ec bigint,
    bmca_decision_code integer,
    clock_accuracy integer,
    clock_class bigint,
    cos_ec integer,
    delay_asymmetry integer,
    delay_asymmetry_comp integer,
    delay_mechanism integer,
    delay_req_msg_rate integer,
    delay_res_msg_rate integer,
    delay_resp_timeout integer,
    dest_mac_address integer,
    encapsulation_ec integer,
    ether_type_ec integer,
    event_msg_on_passive_port integer,
    expected_clock_class integer,
    flow_point_eid character varying(255),
    force_bmca_on_master integer,
    force_prtc_clock_quality integer,
    frequency_traceable integer,
    gm_identity character varying(255),
    id integer NOT NULL,
    if_name character varying(255),
    is_probing_slave integer,
    local_priority bigint,
    master_clock_type integer,
    master_only integer,
    max_expected_slaves bigint,
    ne_index integer,
    not_slave integer,
    oper_state integer,
    parent_id integer,
    parent_wr_calibrated integer,
    parent_wr_config integer,
    parent_wr_mode_on integer,
    peer_port_identity character varying(255),
    peer_port_mac_address character varying(255),
    port_clock_index integer,
    port_identity character varying(255),
    port_state integer,
    profile_ec integer,
    ptp_clock_id integer,
    ptp_clock_index integer,
    role_ec integer,
    scaled_log_variance integer,
    secondary_state integer,
    steps_removed integer,
    sync_msg_rate integer,
    sync_resync_timeout integer,
    synce_enabled integer,
    time_traceable integer,
    transport_mode_ec integer,
    virtual_port_ctrl integer,
    vlan_stack_position_ec character varying(255),
    vlan_tag_ec character varying(255),
    vlan_type_ec integer,
    wr_calibrated integer,
    wr_config integer,
    wr_mode integer,
    wr_mode_on integer,
    wr_port_state integer
);

CREATE TABLE public.cn_port_cm (
    a2n_push_port_vid_enabled boolean,
    activeports integer,
    afptype integer,
    ais integer,
    assumedclockclass bigint,
    assumedql integer,
    autodiagenabled integer,
    bonding_port integer,
    ccgroupactiveports integer,
    ccgroupbpv integer,
    ccgroupdutycycle integer,
    ccgroupg703 integer,
    ccgroupphaseadjust integer,
    clkifenabledports integer,
    clockaccuracy bigint,
    clockclass bigint,
    configspeed integer,
    controlfield integer,
    crc4 integer,
    deienable integer,
    delaycompensation integer,
    egresspoppriorityvidenabled integer,
    egresspoppvidenabled integer,
    egressvlantrunkingenabled integer,
    elmi_enabled integer,
    embedded_gps_receiver integer,
    embedded_stl_receiver integer,
    expectedql integer,
    extensiondetection integer,
    extensiontype integer,
    frameformat integer,
    frequency bigint,
    happsfinetuning integer,
    id integer NOT NULL,
    idlecode integer,
    impedance integer,
    ingresscospriority integer,
    ingresspushpvidenabled integer,
    initiatediag integer,
    innervlanloopback1 character varying(255),
    innervlanloopback2 character varying(255),
    innervlanloopback3 character varying(255),
    innervlanloopbackmask integer,
    inputrate integer,
    inputsignaltype integer,
    irigdigitalsignalvoltage integer,
    irigoutputtype integer,
    irigoutputunitportindex integer,
    jumboframesenabled integer,
    laseristunable integer,
    leap59 integer,
    leap61 integer,
    leapseconddate bigint,
    linebuildout integer,
    linecode integer,
    linetype integer,
    linklossfwdactive integer,
    linklossfwddelay integer,
    linklossfwdenabled integer,
    linklossfwdlocallinkid integer,
    linklossfwdpartnerenabled integer,
    linklossfwdremotelinkids integer,
    linklossfwdremotelinkidsstring character varying(255),
    linklossfwdrxrldlinkidsstring character varying(255),
    linklossfwdsignaltype integer,
    linklossfwdtriggertypes integer,
    linklossfwdtxactiontype integer,
    llfwdrxrldlinksids integer,
    loopbackconfig integer,
    loopbackstatus integer,
    loopbackswapsada integer,
    loopbacktime integer,
    mdixstatus integer,
    mdixtype integer,
    mediatype integer,
    mode integer,
    n2a_pop_port_vid_enabled boolean,
    n2a_vlan_trunking_enabled boolean,
    negotiatedspeed integer,
    neindex integer,
    numconfiguredfdfrs integer,
    oamadmindisposition integer,
    oamadminstate integer,
    oamdiscoverystate integer,
    oamenabled integer,
    oamid integer,
    oamlocallinkeventsenabled integer,
    oamlocalmaxpdusize integer,
    oamlocalmode integer,
    oamlocaloamloopbackssupported integer,
    oamlocalunidirsupportenabled integer,
    oamlocalvarrtrvsenabled integer,
    oamremotelinkeventsenabled integer,
    oamremotemacaddress character varying(255),
    oamremotemaxpdusize integer,
    oamremotemode integer,
    oamremoteoamloopbackssupported integer,
    oamremoteunidirsupportenabled integer,
    oamremotevarrtrvsenabled integer,
    outervlanloopback1 character varying(255),
    outervlanloopback2 character varying(255),
    outervlanloopback3 character varying(255),
    outervlanloopbackmask integer,
    outputdelay bigint,
    outputformat integer,
    peer_mac_address character varying(255),
    phyinterfacetype integer,
    plugtype integer,
    port1alias character varying(255),
    port2alias character varying(255),
    port3alias character varying(255),
    port4alias character varying(255),
    port5alias character varying(255),
    port6alias character varying(255),
    port7alias character varying(255),
    port8alias character varying(255),
    portdirection integer,
    portimpedance integer,
    portindex integer,
    portname character varying(255),
    portpolarity integer,
    porttype integer,
    portvlanid integer,
    portwidth bigint,
    ppsinternaldelay integer,
    ppswidth integer,
    prio_map_profile character varying(255),
    priomapmode integer,
    priorityvlanid integer,
    qinqethertype bigint,
    qlenabled integer,
    qlmodenabled integer,
    receivedql integer,
    redundancy_group integer,
    relayoperation integer,
    rxpausedisposition integer,
    rxpauseenabled integer,
    rxssmql integer,
    sabitdes integer,
    sabitsadminstate integer,
    scisignaldirection integer,
    selected_media_type integer,
    sfpconnectorvalue integer,
    sfpdateofmanufacture character varying(255),
    sfplaserwavelengthpicometer bigint,
    sfplinklength integer,
    sfpmediatype integer,
    sfppartnumber character varying(255),
    sfpreach integer,
    sfprxpowerlevel character varying(255),
    sfpserialnumber character varying(255),
    sfptxpowerlevel character varying(255),
    sfpvendorname character varying(255),
    shapedspeedhi bigint,
    shapedspeedlo bigint,
    shapingenabled integer,
    shelfindex integer,
    signalfrequency integer,
    signalmode integer,
    signalpolarity integer,
    signalshape integer,
    signaltype integer,
    slotindex integer,
    squelchcontrol integer,
    squelchql integer,
    svctype integer,
    synceenabled integer,
    syncemode integer,
    synceserviceavailabletime bigint,
    synceserviceavailabletimeper character varying(255),
    synceserviceunavailabletime bigint,
    timecode integer,
    timescale integer,
    timesource integer,
    todsource character varying(255),
    todsourcetype integer,
    trafficmodel integer,
    transmitql integer,
    txpauseenabled integer,
    txssm integer,
    typeindex integer,
    utcoffset bigint,
    wdmchannel integer,
    working_path character varying(255),
    xgephytype integer
);

CREATE TABLE public.cn_port_cpd_profile (
    id integer NOT NULL,
    profileindex integer,
    profilename character varying(255)
);

CREATE TABLE public.cn_port_edfa_ext (
    edfatype integer,
    jdoclass character varying(255),
    jdoid bigint NOT NULL,
    jdoversion integer,
    maxoperpower integer,
    opticalbandtype integer,
    opticalinputpower integer,
    opticaloutputpower integer,
    port_id integer,
    pumplaserconfig integer,
    pumplasercurrent integer,
    pumplaserpower integer,
    pumplasertxstate integer,
    pumplasertxtemp integer,
    submoduletemp integer,
    teccurrent integer
);

CREATE TABLE public.cn_port_endpoint (
    bandwidth_index integer,
    center_freqency_index integer,
    ctp_uri character varying(255),
    fix_spectrum boolean,
    id integer NOT NULL,
    port_name character varying(255),
    port_uri character varying(255),
    ptp_uri character varying(255)
);

CREATE TABLE public.cn_port_ext (
    autonegotiation integer,
    behave integer,
    bidi_channel integer,
    bitrate integer,
    channel integer,
    channelbandwidth integer,
    defaultpcp bigint,
    disparitycorrection integer,
    duplex integer,
    errorforwarding integer,
    ethertagtype character varying(255),
    frequency bigint,
    jdoclass character varying(255),
    jdoid bigint NOT NULL,
    jdoversion integer,
    laseroffdelayfunction integer,
    maxframesize bigint,
    monitoredbitrate bigint,
    monitoredduplex integer,
    ne_id integer,
    opupayloadtype integer DEFAULT 0,
    physical_port_opu_payload_type integer,
    portdbimpl_id integer,
    portmode integer,
    portrole integer,
    pvid bigint,
    reach integer,
    rx_frequency bigint,
    sigdegperiododu bigint,
    sigdegperiodotu bigint,
    sigdegthresodu integer,
    sigdegthresotu integer,
    sigdegthressdhms bigint,
    sigdegthressonetline integer,
    signaldegradeperiod bigint,
    stuff integer,
    tcmalevel integer,
    tcmblevel integer,
    tcmclevel integer,
    termlevel integer,
    timmodeodu integer,
    timmodeodutcma integer,
    timmodeodutcmb integer,
    timmodeodutcmc integer,
    timmodeotu integer,
    timmodesonetsection integer,
    traceexpectedodu bytea,
    traceexpectedodutcma bytea,
    traceexpectedodutcmb bytea,
    traceexpectedodutcmc bytea,
    traceexpectedotu bytea,
    traceexpectedsonetsection bytea,
    traceformsonetsection integer,
    tracercvdapiodu bytea,
    tracercvdapiotu bytea,
    tracercvdapitcma bytea,
    tracercvdapitcmb bytea,
    tracercvdapitcmc bytea,
    tracercvopspodu bytea,
    tracercvopspotu bytea,
    tracercvopsptcma bytea,
    tracercvopsptcmb bytea,
    tracercvopsptcmc bytea,
    tracercvsapiodu bytea,
    tracercvsapiotu bytea,
    tracercvsapitcma bytea,
    tracercvsapitcmb bytea,
    tracercvsapitcmc bytea,
    tracetransmitdapiodu bytea,
    tracetransmitdapiodutcma bytea,
    tracetransmitdapiodutcmb bytea,
    tracetransmitdapiodutcmc bytea,
    tracetransmitdapiotu bytea,
    tracetransmitopspodu bytea,
    tracetransmitopspodutcma bytea,
    tracetransmitopspodutcmb bytea,
    tracetransmitopspodutcmc bytea,
    tracetransmitopspotu bytea,
    tracetransmitsapiodu bytea,
    tracetransmitsapiodutcma bytea,
    tracetransmitsapiodutcmb bytea,
    tracetransmitsapiodutcmc bytea,
    tracetransmitsapiotu bytea,
    tracetransmitsonetsection bytea,
    tx_frequency bigint,
    utag integer,
    vethaid integer,
    vethaidstring character varying(255) DEFAULT ''::character varying,
    voamode integer,
    wavelength bigint
);

CREATE TABLE public.cn_port_ext_cli (
    farendcommunication integer DEFAULT 0,
    jdoid bigint NOT NULL,
    laserdelaytimer integer DEFAULT 0,
    laserofftimer bigint DEFAULT 0,
    laserontimer bigint DEFAULT 0,
    ratelimit integer DEFAULT 0,
    sigdegpcslthreshold bigint DEFAULT 0,
    sigdegperiodintegration bigint DEFAULT 0
);

CREATE TABLE public.cn_port_ext_net (
    cdc_range integer DEFAULT 0,
    codegain integer DEFAULT 0,
    delay_in_frames bigint,
    delay_measurement_layer integer DEFAULT 1,
    delay_measurement_status integer,
    delay_measurement_time bigint,
    delay_time bigint,
    encryptionchannel integer DEFAULT 0,
    fraction character varying(20),
    jdoid bigint NOT NULL,
    lanechannelsetting integer,
    mode integer,
    timingsource integer,
    wdmrxchannel integer
);

CREATE TABLE public.cn_port_f7 (
    band integer,
    cdcmode integer,
    channellane1 integer,
    channellane2 integer,
    connectiontype integer,
    currentassignmenttype integer,
    facilitytype integer,
    id integer NOT NULL,
    optset integer,
    optsetlane1 integer,
    optsetlane2 integer,
    physicalportconstellation integer,
    physicalportfreqoffset integer,
    physicalportodutriblayout integer,
    physicalportterminationmode integer DEFAULT 0,
    port_client_ext_jdoid bigint,
    port_net_ext_jdoid bigint,
    portedfadbimplextension_jdoid bigint,
    protectionrole integer,
    rate bigint,
    trafficdirection integer,
    ttpdbimplextension_jdoid bigint
);

CREATE TABLE public.cn_port_hn (
    actualduplexmode integer,
    actualmdimode integer,
    actualpmeportlist bytea,
    actualremotemodel character varying(255),
    actualremotetype integer,
    afdidelay bigint,
    afdienabled integer,
    aggregaterate bigint,
    attenuation integer,
    autonegotiation integer,
    bestethrate bigint,
    cpehostname bytea,
    cpetemplateid integer,
    deviceid integer,
    dot3ahaggregation integer,
    duplexmode integer,
    efmoamadministration integer,
    efmoamfunctionssupported integer,
    efmoammaxpdusize integer,
    efmoammode integer,
    efmoamservicestate integer,
    hnportindex integer,
    id integer NOT NULL,
    lagno integer,
    linestatus integer,
    llfport integer,
    loadbalance integer,
    lpbckadminstate integer,
    lpbkmode integer,
    lpbkrmtenable integer,
    lpbkstatus integer,
    maxfragsize bigint,
    mdimode integer,
    mediatype integer,
    minratealarm bigint,
    oamsuspend integer,
    objectstate integer,
    peerattenuation integer,
    peersnrmgn integer,
    pmeportlist bytea,
    pmevalidation integer,
    portlist character varying(255),
    portname character varying(255),
    porttype integer,
    remote2bpme integer,
    remotemacaddress character varying(255),
    remotemacaddressact character varying(255),
    remotetype integer,
    retrainreason integer,
    rxframes integer,
    shelfindex integer,
    slotindex integer,
    snrmgn integer,
    spanalarmprofileid integer,
    spanprofileid integer,
    spanuptime bigint,
    speedrate integer,
    txframes integer,
    unidbimpl integer,
    worstethrate bigint
);

CREATE TABLE public.cn_port_juniper (
    fecmode integer,
    id integer NOT NULL,
    laserenable integer,
    txopticalpower integer,
    wavelength bigint,
    workingmode integer
);

CREATE TABLE public.cn_port_tt_ext (
    channelno character varying(255),
    jdoclass character varying(255),
    jdoid bigint NOT NULL,
    jdoversion integer,
    lasertxstate integer,
    loopstate integer,
    port_id integer,
    rxclockfreq integer,
    rxclocktype integer,
    wavelength character varying(255)
);

CREATE TABLE public.cn_port_vlan_lpbk (
    id integer NOT NULL,
    innervlanenabled integer,
    innervlanlpbktag character varying(255),
    loopbackconfig integer,
    loopbackstatus integer,
    loopbacktime integer,
    neindex integer,
    outervlanenabled integer,
    outervlanlpbktag character varying(255),
    portindex integer,
    portvlanlpbkindex integer,
    shelfindex integer,
    slotindex integer
);

CREATE TABLE public.cn_power_level_status (
    "timestamp" bigint,
    id integer NOT NULL,
    is_update_complete boolean
);

CREATE TABLE public.cn_prio_map (
    cos integer,
    id integer NOT NULL,
    inner_tag_priority integer,
    inpriority integer,
    neindex integer,
    outer_tag_ctrl integer,
    outer_tag_priority integer,
    portindex integer,
    priomapindex integer,
    shelfindex integer,
    slotindex integer,
    x_tag_ctrl integer,
    x_tag_priority integer
);

CREATE TABLE public.cn_profile (
    id integer NOT NULL,
    mapmode integer,
    profile_index integer
);

CREATE TABLE public.cn_profile_ne (
    comment character varying(1000),
    id integer NOT NULL,
    last_op_error character varying(1000),
    last_op_started_at bigint,
    last_op_status character varying(20),
    last_op_try_count integer,
    last_operation character varying(20),
    mp_id integer,
    name character varying(255),
    ne_id integer,
    profile_ver integer,
    state integer,
    sw_version character varying(255),
    tag integer
);

CREATE TABLE public.cn_profile_prio_map (
    cos integer,
    id integer NOT NULL,
    inner_tag_priority integer,
    inpriority integer,
    outer_tag_priority integer,
    priomapindex integer,
    profileindex integer,
    x_tag_ctrl integer,
    x_tag_priority integer
);

CREATE TABLE public.cn_profile_transfer_properties (
    id integer NOT NULL,
    jdoversion integer,
    max_retry_count integer,
    min_interval integer
);

CREATE TABLE public.cn_prot_group_f8 (
    cccp_identifier character varying(255),
    direction character varying(20),
    epte_opp_1 character varying(255),
    epte_opp_2 character varying(255),
    epte_opp_3 character varying(255),
    epte_opp_4 character varying(255),
    freeze_enabled boolean,
    freeze_state character varying(255),
    holdoff integer,
    id integer NOT NULL,
    is_cccp boolean,
    protect_entity character varying(500) NOT NULL,
    protection_unit character varying(500),
    prt_snc character varying(500),
    revertive boolean,
    switch_on_peer_comm_fail boolean,
    user_label character varying(255),
    wait_to_restore integer,
    working_entity character varying(500) NOT NULL
);

CREATE TABLE public.cn_prot_unit_f8 (
    id integer NOT NULL,
    state character varying(20),
    transport_entity character varying(500) NOT NULL,
    type character varying(20)
);

CREATE TABLE public.cn_protected_flowpoint_list_ec (
    index integer,
    pf_list character varying(255),
    protection_flowpoint_id integer
);

CREATE TABLE public.cn_ptp_clock (
    active_grants_announce_service integer,
    active_grants_delay_req_serv integer,
    active_grants_sync_service integer,
    active_slave_port integer,
    active_slave_port_index character varying(255),
    admin_state integer,
    assumed_ql integer,
    bc_class integer,
    clock_accuracy bigint,
    clock_class bigint,
    clock_identity character varying(255),
    clock_recovery_state integer,
    clock_state character varying(255),
    domain_number integer,
    expected_ql integer,
    freq_traceability_status integer,
    grandmaster_id integer,
    id integer NOT NULL,
    local_priority bigint,
    max_steps_removed_ec integer,
    ne_index integer,
    network_time_inaccuracy bigint,
    oper_mode integer,
    oper_state integer,
    phase_recovery_state integer,
    physical_entity_index integer,
    priority_1 bigint,
    priority_2 bigint,
    profile integer,
    profile_application_mode integer,
    ptp_clock_index integer,
    ql_mode_enabled integer,
    received_ql integer,
    recovery_mode integer,
    redundancy_group integer,
    scaled_log_variance integer,
    secondary_state integer,
    service_available_percentage character varying(255),
    service_available_time bigint,
    service_unavailable_time bigint,
    slot_string character varying(255),
    storage_type integer,
    sync_eid integer,
    sync_eid_index character varying(255),
    sync_enabled integer,
    sync_ref_candidate_ec integer,
    synchronization_not_assured integer,
    time_inaccuracy bigint,
    time_source integer,
    time_source_index character varying(255),
    time_traceability_status integer,
    type integer
);

CREATE TABLE public.cn_ptp_clock_probe (
    constteclrthreshold bigint,
    consttethreshold bigint,
    consttewindow bigint,
    delaycompensation integer,
    delayms integer,
    delaysm integer,
    direction integer NOT NULL,
    ffoobserwindow integer,
    ffotarget integer,
    flow_oid character varying(255) NOT NULL,
    id integer NOT NULL,
    instteclrthreshold bigint,
    insttethreshold bigint,
    ip_protocol integer,
    last_tie integer,
    lastfforesult integer,
    lasttealertcleartime bigint,
    lasttealerttime bigint,
    lasttietime bigint,
    margin_failed boolean NOT NULL,
    margincrosstime bigint,
    maskcrosstime bigint,
    maske_failed boolean NOT NULL,
    master_ip character varying(255),
    master_ipv6 character varying(255),
    master_port_identity character varying(255),
    maxteclrthreshold integer,
    maxtethreshold integer,
    measurementtype integer,
    mtie_margin bigint NOT NULL,
    mtie_mask integer NOT NULL,
    mtie_restart integer DEFAULT 0,
    mtierestarttime bigint,
    no_event_msg_failure boolean NOT NULL,
    no_timestamp_failure boolean NOT NULL,
    runningfailedcount integer,
    slave_ip character varying(255),
    slave_ipv6 character varying(255),
    slave_port_identity character varying(255),
    tasymmetry integer,
    tealertclearthreshold integer,
    tealertthreshold integer,
    timeoflastfforesult bigint,
    user_mtie_mask integer
);

CREATE TABLE public.cn_ptp_clock_probe_history (
    constteclrthreshold bigint,
    consttemeasurementtime bigint,
    consttethreshold bigint,
    consttetotalarmtime bigint,
    consttewindow bigint,
    direction integer,
    flow_oid character varying(255),
    id integer NOT NULL,
    instteclrthreshold bigint,
    insttemeasurementtime bigint,
    insttethreshold bigint,
    insttetotalarmtime bigint,
    ip_protocol integer,
    master_ip character varying(255),
    master_ipv6 character varying(255),
    master_port_identity character varying(255),
    maxteclrthreshold integer,
    maxtemeasurementtime bigint,
    maxtethreshold integer,
    maxtetotalarmtime bigint,
    measurement_type integer,
    no_event_msg_failure boolean,
    no_timestamp_failure boolean,
    slave_ip character varying(255),
    slave_ipv6 character varying(255),
    slave_port_identity character varying(255),
    user_mtie_mask integer
);

CREATE TABLE public.cn_ptp_flowpoint (
    admin_state integer,
    blockingenabled integer,
    buffersize bigint,
    cirhigh bigint,
    cirlow bigint,
    clock_id character varying(255),
    connect_guard_flow_object character varying(255),
    cos integer,
    eirhigh bigint,
    eirlow bigint,
    id integer NOT NULL,
    inner1untaggedenabled integer,
    inner1vlanethertype integer,
    inner1vlanmemberlist character varying(255),
    inner2untaggedenabled integer,
    inner2vlanethertype integer,
    inner2vlanmemberlist character varying(255),
    ne_index integer,
    oper_state integer,
    outeruntaggedenabled integer,
    outervlanethertype integer,
    outervlanmemberlist character varying(255),
    port_index integer,
    ptp_flow_index integer,
    secondary_state integer,
    securestate integer,
    service_id character varying(255),
    shelf_index integer,
    slot_index integer,
    type smallint
);

CREATE TABLE public.cn_ptp_mci_protection_group (
    activeclockid integer,
    activemciid integer,
    activemcioid character varying(255),
    adminstate integer,
    grouppriority integer,
    id integer NOT NULL,
    lastswitchoverreason integer,
    lastswitchovertime bigint,
    neindex integer,
    protectiongroupindex integer,
    standbyclockid integer,
    standbymciid integer,
    standbymcioid character varying(255)
);

CREATE TABLE public.cn_ptp_network_probe (
    adminstate integer,
    flow_oid character varying(255) NOT NULL,
    fwd_network_us bigint,
    id integer NOT NULL,
    ip_protocol integer,
    master_ip character varying(255),
    master_ipv6 character varying(255),
    no_event_msg_failure boolean NOT NULL,
    no_timestamp_failure boolean NOT NULL,
    operstate integer,
    pdv_assured_high bigint NOT NULL,
    pdv_assured_low bigint NOT NULL,
    pdv_satisfied_high bigint NOT NULL,
    pdv_satisfied_low bigint NOT NULL,
    res_pdv_fwd_hi_range bigint NOT NULL,
    res_pdv_fwd_low_range bigint NOT NULL,
    res_pdv_fwd_med_range bigint NOT NULL,
    res_pdv_rev_hi_range bigint NOT NULL,
    res_pdv_rev_low_range bigint NOT NULL,
    res_pdv_rev_med_range bigint NOT NULL,
    resultsavailable integer,
    rev_network_us bigint,
    secondarystate integer,
    slave_ip character varying(255),
    slave_ipv6 character varying(255)
);

CREATE TABLE public.cn_ptp_system_slaves (
    curr_num_static_slaves integer,
    id integer NOT NULL,
    max_static_slaves_allowed integer,
    max_uni_dyn_slaves_allowed integer,
    ne_index integer,
    ptp_system_slave_index integer,
    shelf_index integer,
    slaves_warn_threshold integer,
    slot_index integer,
    system_slaves_config_enabled integer
);

CREATE TABLE public.cn_pwr_consumption (
    id integer NOT NULL,
    jdoversion integer,
    ne_id integer,
    ne_type integer,
    pwr_1day_mean integer,
    pwr_1day_timestamp bigint
);

CREATE TABLE public.cn_pwr_consumption_historical (
    ne_id integer NOT NULL,
    power_consumption integer,
    power_consumption_timestamp timestamp without time zone NOT NULL
);

CREATE TABLE public.cn_queue_bwprof_f4 (
    cir bigint,
    cirhi bigint,
    eir bigint,
    eirhi bigint,
    id integer NOT NULL,
    name character varying(255)
);

CREATE TABLE public.cn_queue_profile (
    buffersize integer,
    id integer NOT NULL,
    index integer,
    name character varying(255),
    redenabled integer
);

CREATE TABLE public.cn_redundancy_group (
    active_card character varying(255),
    active_card_entity integer,
    active_card_state integer,
    active_managed_object integer,
    activeslot character varying(255),
    id integer NOT NULL,
    last_switch_over_reason integer,
    last_switch_over_time bigint,
    ne_index integer,
    redundancy_group_index integer,
    standby_card character varying(255),
    standby_card_entity integer,
    standby_card_state integer,
    standbyslot character varying(255),
    state integer,
    sync_enabled integer,
    sync_status integer,
    type integer,
    user_label character varying(255)
);

CREATE TABLE public.cn_remote_auth_server (
    accounting_port integer,
    certificate_identity character varying(255),
    id integer NOT NULL,
    index integer,
    ip_version integer,
    ipv4_address character varying(255),
    ipv6_address character varying(255),
    num_retries integer,
    port integer,
    remote_auth_server_enabled integer,
    remote_auth_server_order integer,
    timeout integer
);

CREATE TABLE public.cn_remote_mep (
    id integer NOT NULL,
    macaddress character varying(255),
    maindex integer,
    mdindex integer,
    mepindex integer,
    rmepindex integer
);

CREATE TABLE public.cn_remote_slave (
    admin_state integer,
    announce_msg_rate integer,
    bcindex integer,
    delay_rsp_msg_rate integer,
    id integer NOT NULL,
    ipaddress character varying(255),
    mac_address character varying(255),
    mci integer,
    mciindex integer,
    neg_announce_lease_dur integer,
    neg_delay_rsp_lease_dur integer,
    neg_sync_lease_dur integer,
    neindex integer,
    oper_state integer,
    parent_slot_name character varying(255),
    port_identity character varying(255),
    rsindex integer,
    secondary_state integer,
    slave_clock_identity character varying(255),
    slave_ipv6 character varying(255),
    sync_msg_rate integer,
    time_created bigint,
    type integer,
    umn_control integer
);

CREATE TABLE public.cn_remote_slave_capacity (
    dtype character varying(31),
    free_slaves integer,
    id integer NOT NULL,
    is_alarmed boolean,
    jdoversion integer,
    master_function_id integer,
    ne_id integer,
    suppress_tca boolean,
    total_slaves integer,
    type integer,
    used_slaves integer,
    utilization_threshold integer
);

CREATE TABLE public.cn_remote_slave_connectivity (
    admin_state integer,
    alias character varying(255),
    announce_msg_rate integer,
    connection_state integer,
    delay_rsp_msg_rate integer,
    dtype character varying(31),
    entity_id character varying(255),
    id integer NOT NULL,
    ip_address character varying(255),
    jdoversion integer,
    last_outage_time bigint,
    mac_address character varying(255),
    master_function_id integer,
    master_ip character varying(255),
    master_slot_name character varying(255),
    ne_id integer,
    neg_announce_lease_dur integer,
    neg_delay_rsp_lease_dur integer,
    neg_sync_lease_dur integer,
    oper_state integer,
    parent_entity_id character varying(255),
    parent_id integer,
    parent_port_identity character varying(255),
    port_identity character varying(255),
    rs_id integer,
    secondary_state integer,
    slave_clock_identity character varying(255),
    suppress_alarms_events boolean DEFAULT false,
    sync_msg_rate integer,
    time_created bigint,
    type integer
);

CREATE TABLE public.cn_roadm_configuration_data (
    id integer NOT NULL,
    mode character varying(255),
    neid integer,
    shelf integer,
    slot integer
);

CREATE TABLE public.cn_route_proto_f4 (
    entity_name character varying(255),
    id integer NOT NULL,
    type character varying(255),
    vrf_name character varying(255)
);

CREATE TABLE public.cn_sat_stream_f3 (
    cirlo bigint,
    colormode integer,
    ctrlindex integer,
    destmepid integer,
    destmeptype integer,
    eirlo bigint,
    id integer NOT NULL,
    neindex integer,
    sacprofileid character varying(500) DEFAULT NULL::character varying,
    srcmepid character varying(500) DEFAULT NULL::character varying,
    streamindex integer,
    streamname character varying(255),
    testport character varying(500) DEFAULT NULL::character varying
);

CREATE TABLE public.cn_satop_tdm_entity (
    satopdbimpl_id integer,
    tdmentities character varying(255)
);

CREATE TABLE public.cn_secure_entity_f4 (
    associatedmep character varying(500) DEFAULT NULL::character varying,
    ciphersuite character varying(255),
    id integer NOT NULL,
    keyexchangechannel character varying(500) DEFAULT NULL::character varying,
    keyexchangeinterval bigint,
    keyexchangeprofile character varying(500) DEFAULT NULL::character varying,
    keyinjectflowpoint character varying(500) DEFAULT NULL::character varying,
    name character varying(255),
    remoteendlagpeerenabled boolean,
    remotemacaddr character varying(255),
    remotemacaddrenabled boolean,
    replayprotectionenabled boolean,
    replayprotectionwindow bigint,
    tagsclear integer
);

CREATE TABLE public.cn_secure_flow (
    action integer,
    adminstate integer,
    associatedmep character varying(500) DEFAULT NULL::character varying,
    ciphersuite integer,
    egressinterface character varying(500) DEFAULT NULL::character varying,
    flow_id integer,
    flowindex integer,
    id integer NOT NULL,
    keyexchangeframeinner1vlanid integer,
    keyexchangeframeinner1vlanon integer,
    keyexchangeframeinner1vlanprio integer,
    keyexchangeframeinner2vlanid integer,
    keyexchangeframeinner2vlanon integer,
    keyexchangeframeinner2vlanprio integer,
    keyexchangeframeoutervlanid integer,
    keyexchangeframeoutervlanon integer,
    keyexchangeframeoutervlanprio integer,
    keyexchangeframetagcontrol integer,
    keyexchangeinterval integer,
    keyexchangeprofile character varying(500) DEFAULT NULL::character varying,
    keyinjectflowpoint character varying(500) DEFAULT NULL::character varying,
    keyxchgfailscounts bigint,
    neindex integer,
    operationalstate integer,
    remotemacaddr character varying(255),
    remotemacaddrenabled integer,
    replayprotectionenabled integer,
    replayprotectionwindow bigint,
    secondarystate integer,
    tagsclear integer
);

CREATE TABLE public.cn_secure_flow_rx_sc (
    flowindex integer,
    id integer NOT NULL,
    neindex integer,
    rxscindex integer
);

CREATE TABLE public.cn_secure_flow_template (
    adminstate integer,
    cfmprofile character varying(255),
    ciphersuite integer,
    egressinterfacetype integer,
    id integer NOT NULL,
    keyexchangeframeinner1vlanid integer,
    keyexchangeframeinner1vlanon boolean,
    keyexchangeframeinner1vlanprio integer,
    keyexchangeframeinner2vlanid integer,
    keyexchangeframeinner2vlanon boolean,
    keyexchangeframeinner2vlanprio integer,
    keyexchangeframeoutervlanenabled boolean,
    keyexchangeframeoutervlanid integer,
    keyexchangeframeoutervlanpriority integer,
    keyexchangeframetagcontrol integer,
    keyexchangeinterval integer,
    keyexchangeprofile character varying(255),
    profilename character varying(255),
    remotemacaddr character varying(255),
    remotemacaddrenabled integer,
    replayprotectionenabled boolean,
    replayprotectionwindow bigint,
    tagsclear integer
);

CREATE TABLE public.cn_secure_flow_tx_sc (
    flowindex integer,
    id integer NOT NULL,
    neindex integer,
    txscindex integer
);

CREATE TABLE public.cn_secy_f4 (
    id integer NOT NULL,
    name character varying(255),
    secureentity character varying(500) DEFAULT NULL::character varying,
    secyid integer
);

CREATE TABLE public.cn_server_module (
    hypervisor_version character varying(255),
    id integer NOT NULL,
    memory_available integer,
    memory_total integer,
    storage_available integer,
    storage_total integer,
    storage_type integer,
    uptime integer,
    virtual_cpu_available integer,
    virtual_cpu_total integer,
    vm_number integer
);

CREATE TABLE public.cn_service_mrv (
    cports character varying(255),
    cvlans character varying(1536),
    flowid bigint,
    id integer NOT NULL,
    includesuntagged integer,
    lasterror character varying(255),
    maindex bigint,
    mdlevel integer,
    mepid bigint,
    mepportindex integer,
    serviceid character varying(255),
    servicename character varying(255),
    sports character varying(255),
    svlanid integer
);

CREATE TABLE public.cn_shaper_cm (
    adminstate integer,
    associatedqueueprofileindex integer,
    bsprof character varying(255),
    buffersize bigint,
    bwprof character varying(255),
    cbs bigint,
    cir bigint,
    cirhi bigint,
    cos integer,
    direction integer,
    ebs bigint,
    eir bigint,
    eirhi bigint,
    ethid integer,
    flowfsp150cmdbimpl integer,
    flowindex integer DEFAULT 0 NOT NULL,
    flowpointid integer,
    id integer NOT NULL,
    ne_id integer NOT NULL,
    neindex integer NOT NULL,
    operationalstate integer,
    portfsp150cmaccdbimpl integer,
    portindex integer NOT NULL,
    secondarystate integer,
    shaperindex integer NOT NULL,
    shelfindex integer NOT NULL,
    slotindex integer NOT NULL,
    typeindex integer NOT NULL
);

CREATE TABLE public.cn_shelf (
    activenetwork integer,
    additionalstatusinfo character varying(255),
    assignedtypestring character varying(255),
    bpdufilter integer,
    cleicode character varying(255),
    id integer NOT NULL,
    ifindex integer,
    ismaster integer,
    lasttestingport integer,
    linklossfwd integer,
    macaddresslearning integer,
    mode integer,
    neindex integer,
    oidcontents integer DEFAULT 1 NOT NULL,
    oscillatortype character varying(255),
    pauseenable integer,
    physicalindex integer,
    protstate integer,
    prottype integer,
    shelf_type integer,
    shelfindex integer,
    tmsupp integer,
    userstring1 character varying(255),
    userstring2 character varying(255),
    userstring3 character varying(255)
);

CREATE TABLE public.cn_shg_f3 (
    id integer NOT NULL,
    neindex integer,
    shgindex integer
);

CREATE TABLE public.cn_shg_members_f3 (
    id integer NOT NULL,
    member_id integer,
    memberref_index character varying(255),
    neindex integer,
    shg integer,
    shgindex integer
);

CREATE TABLE public.cn_sj_probe (
    actualstarttime bigint,
    expected_ql integer NOT NULL,
    id integer NOT NULL,
    ne_index integer,
    probe_index integer,
    rawdatacollectionenabled integer,
    reference_failure boolean NOT NULL,
    reference_oid character varying(255),
    scheduler_id integer,
    scheduler_oid character varying(255),
    state integer NOT NULL
);

CREATE TABLE public.cn_sj_probe_history (
    actual_duration bigint,
    actualstarttime bigint,
    expected_ql integer,
    history_index integer,
    id integer NOT NULL,
    margin_failed boolean,
    margincrosstime bigint,
    maskcrosstime bigint,
    maske_failed boolean,
    mtie_margin bigint,
    mtie_mask integer,
    ne_index integer,
    probe_index integer,
    rawdatacollectionenabled integer,
    reference_failure boolean,
    reference_oid character varying(255),
    running_failed_count integer
);

CREATE TABLE public.cn_sj_probe_mtie_result (
    history_index integer,
    id integer NOT NULL,
    mtie_value integer NOT NULL,
    mtie_value_index integer,
    ne_index integer,
    owner_probe_history integer,
    owner_probe_history_id integer,
    probe_index integer
);

CREATE TABLE public.cn_sj_scheduler (
    duration bigint,
    endtime bigint,
    entitylist character varying(255) NOT NULL,
    id integer NOT NULL,
    index integer,
    neindex integer,
    shelfindex integer,
    slotindex integer,
    starttime bigint
);

CREATE TABLE public.cn_slave_vp (
    admin_state integer,
    id integer NOT NULL,
    ne_index integer,
    ocs_port_index integer,
    oper_state integer,
    port_identity character varying(255),
    port_state integer,
    ptp_flow_point character varying(255),
    secondary_state integer,
    slave_index integer,
    sooc_index integer
);

CREATE TABLE public.cn_slot (
    assignmentstate integer,
    containerindex integer,
    equipment integer,
    id integer NOT NULL,
    neindex integer,
    physical_address character varying(255),
    shelfindex integer,
    slotindex integer,
    type integer
);

CREATE TABLE public.cn_soam_xg3xx (
    cfgenabled integer,
    cfggenprofile character varying(255) DEFAULT ''::character varying,
    cfgindex bigint NOT NULL,
    cfgprofile character varying(255) DEFAULT ''::character varying,
    flowid bigint NOT NULL,
    id integer NOT NULL,
    serviceid character varying(255) NOT NULL,
    serviceindex integer,
    soamtype integer
);

CREATE TABLE public.cn_sooc (
    admin_state integer,
    algorithm_ptp_aware integer,
    announce_msg_clk_cls integer,
    auto_ptp_disqualification integer,
    class_class bigint,
    clockrecoverystate integer,
    default_gateway_control integer,
    e2e_asymm_comp_value integer,
    e2e_auto_asymm_comp_status integer,
    e2e_delay_asymm_comp integer,
    expected_clock_class integer,
    gateway character varying(255),
    id integer NOT NULL,
    ip_prior_map_mode integer,
    ip_priority integer,
    ip_v6_gateway character varying(255),
    lockout_control integer,
    master_annc_msg_rate integer,
    master_annc_msg_rec_to integer,
    master_clock_type integer,
    master_delay_mechanism integer,
    master_ds_msg_rate integer,
    master_ds_msg_rec_to integer,
    master_ip character varying(255),
    master_ipv6 character varying(255),
    master_lease_duration integer,
    master_message_mode integer,
    master_prior integer,
    master_protocol integer,
    master_sync_msg_rate integer,
    master_sync_msg_rec_to integer,
    name character varying(255),
    ne_index integer,
    oper_state integer,
    phaserecoverystate integer,
    protection_state integer,
    secondary_state integer,
    service_flow_id character varying(255),
    slave integer,
    slave_id integer,
    slave_index integer,
    slave_ip character varying(255),
    slave_ipv6 character varying(255),
    slave_ipv6_prefix_length integer,
    slave_subnet_mask character varying(255),
    sooc_index integer,
    unicast_msg_neg_enabled integer,
    unicast_res_timer integer,
    unicast_timeout integer,
    virtual_port integer,
    wtr integer
);

CREATE TABLE public.cn_spectrum_inventory (
    croma_conn_id integer,
    croma_entity_id integer,
    degree_entity_id integer,
    id integer NOT NULL,
    slc_identifier integer,
    slice_count integer,
    slice_index integer
);

CREATE TABLE public.cn_static_route (
    advertise integer,
    dest character varying(255),
    id integer NOT NULL,
    mask character varying(255),
    metric integer,
    nexthop character varying(255),
    routeinterface character varying(255)
);

CREATE TABLE public.cn_stl_receiver (
    action integer,
    admin_status integer,
    alarms_while_suspend integer,
    alias character varying(255),
    antenna_dc_power_disabled integer,
    antenna_dc_voltage integer,
    antenna_delay_type integer,
    antenna_delay_value integer,
    antenna_status integer,
    antenna_type integer,
    coordinate_atitude character varying(255),
    coordinate_longitude character varying(255),
    coordinate_string_altitude character varying(255),
    east_north_pos_uncertainty integer,
    east_pos_uncertainty integer,
    east_up_pos_uncertainty integer,
    failure_suspend_time bigint,
    forced_antenna_connected integer,
    id integer NOT NULL,
    north_pos_uncertainty integer,
    north_up_pos_uncertainty integer,
    num_tracking_satellites bigint,
    oper_status integer,
    pfom bigint,
    pps_source integer,
    pps_status integer,
    proc_bursts_per_min_thr bigint,
    receiver_index character varying(500) DEFAULT NULL::character varying,
    self_survey_control integer,
    self_survey_in_progress integer,
    self_survey_period bigint,
    self_survey_pos_valid integer,
    self_survey_progress_status bigint,
    serial_number character varying(255),
    subscription_feature_conf integer,
    subscription_feature_enabled integer,
    subscription_key character varying(255),
    tfom bigint,
    up_pos_uncertainty integer
);

CREATE TABLE public.cn_subnet (
    geographylocationtable bytea,
    id integer NOT NULL,
    jdoclass character varying(255),
    jdoversion integer,
    name0 character varying(1000) DEFAULT NULL::character varying,
    parent_id integer,
    performancetemplateid integer,
    pollingmanagerid integer,
    topologylocationtable bytea,
    uuid character varying(255)
);

CREATE TABLE public.cn_subnetwork_ec (
    id integer NOT NULL,
    layer integer,
    name character varying(255),
    usrlbl character varying(255)
);

CREATE TABLE public.cn_supported_netype_ver (
    current_supported_version character varying(255),
    id bigint NOT NULL,
    ne_type integer,
    supported_native_version character varying(255)
);

CREATE TABLE public.cn_switch_traffic_conn_data (
    bundle_id character varying(255),
    conn_id integer NOT NULL,
    line_id integer NOT NULL,
    ni_legacy_service_id integer,
    ni_service_id character varying(255),
    switch_traffic_status integer
);

CREATE TABLE public.cn_swupgrade_properties (
    id integer NOT NULL,
    jdoversion integer,
    max_retry_count integer,
    min_interval integer
);

CREATE TABLE public.cn_sync_event_data (
    events_processed bigint DEFAULT 0,
    jdoversion integer,
    ne_id integer NOT NULL
);

CREATE TABLE public.cn_synce_protection_group (
    activecardid integer,
    activecardoid character varying(255),
    adminstate integer,
    grouppriority integer,
    id integer NOT NULL,
    lastswitchoverreason integer,
    lastswitchovertime bigint,
    neindex integer,
    protectiongroupindex integer,
    standbycardid integer,
    standbycardoid character varying(255)
);

CREATE TABLE public.cn_system_feature (
    enabled integer,
    featureindex integer,
    id integer NOT NULL,
    name character varying(255)
);

CREATE TABLE public.cn_system_status_tp5000 (
    jdoversion integer,
    ne_id integer NOT NULL,
    selected_tod_source integer
);

CREATE TABLE public.cn_tc (
    adminstate integer,
    clock_id character varying(255),
    delay_mechanism integer,
    id integer NOT NULL,
    ne_index integer,
    operstate integer,
    secondarystate integer,
    service_flow character varying(255),
    sync character varying(255),
    tc_index integer
);

CREATE TABLE public.cn_tc_vp (
    admin_state integer,
    id integer NOT NULL,
    ne_index integer,
    oper_state integer,
    port_identity character varying(255),
    ptp_flow_point character varying(255),
    ptp_port_state integer,
    secondary_state integer,
    tc_index integer,
    tc_vp_index integer
);

CREATE TABLE public.cn_telecom_slave (
    admin_state integer,
    clockaccuracy bigint,
    clockassumedql integer,
    clockclass bigint,
    clockexpectedql integer,
    clockid character varying(255),
    clockqlmodeenabled integer,
    clockreceivedql integer,
    clockrecoverymode integer,
    clockrecoverystate integer,
    clocksynceenabled integer,
    current_cr_score bigint,
    current_pr_score bigint,
    domain_number integer,
    freqrecoverytarget integer,
    freqtraceabilitystatus integer,
    id integer NOT NULL,
    ne_index integer,
    oper_state integer,
    phaserecoverystate integer,
    ptpwtrtime integer,
    secondary_state integer,
    selectedclock integer,
    selectedclockstr character varying(255) NOT NULL,
    slave_index integer,
    syncid character varying(255),
    targetphaserecoveryaccuracy bigint,
    timeholdoveraccuracy integer,
    timelastcrscore date,
    timelastprscore date,
    timesource integer,
    timetraceabilitystatus integer
);

CREATE TABLE public.cn_timeclock_protection_group (
    activecardid integer,
    activecardoid character varying(255),
    adminstate integer,
    grouppriority integer,
    id integer NOT NULL,
    lastswitchoverreason integer,
    lastswitchovertime bigint,
    neindex integer,
    protectiongroupindex integer,
    standbycardid integer,
    standbycardoid character varying(255)
);

CREATE TABLE public.cn_topologies (
    alias character varying(1010) DEFAULT NULL::character varying,
    id bigint NOT NULL,
    is_subring boolean,
    jdoclass character varying(255),
    name character varying(1010) DEFAULT NULL::character varying,
    parent_id integer,
    raps_ring_id integer,
    raps_vlan_prio integer,
    raps_vlan_tag integer,
    ring_state integer
);

CREATE TABLE public.cn_traffic_acl_profile (
    id integer NOT NULL,
    profile_index integer,
    profile_name character varying(255)
);

CREATE TABLE public.cn_traffic_port_cpd (
    associatedcpdprofile integer,
    cpdprofileindex integer,
    id integer NOT NULL,
    neindex integer,
    portindex integer,
    shelfindex integer,
    slotindex integer
);

CREATE TABLE public.cn_tunnel (
    adminstate integer,
    datalayer integer DEFAULT 0,
    diversitypath character varying(255),
    erolifecycle character varying(255),
    eroscope character varying(255),
    excludedpaths character varying(255),
    facilitytype integer,
    fectype integer,
    fromaid integer,
    fromaidstring character varying(255),
    id integer NOT NULL,
    monent integer DEFAULT 0,
    operstate integer,
    pcemode integer DEFAULT 0,
    pin integer DEFAULT 0,
    preferpcs integer DEFAULT 0,
    provisionedpaths character varying(255),
    recentfailureinformation character varying(255),
    recoverytype integer,
    restntype integer DEFAULT 0,
    restorationmode integer DEFAULT 0,
    restorationpaths character varying(100) DEFAULT ''::character varying,
    restorationpathscomputed character varying(100) DEFAULT ''::character varying,
    restorationpathsnoavail bigint DEFAULT 0,
    restorationpathsnumber integer DEFAULT 0,
    reversiontype integer DEFAULT 0,
    setpointdelta integer DEFAULT 0,
    sublayer integer DEFAULT 0,
    terminationlevel integer,
    toaid integer,
    toaidstring character varying(255),
    tonodeip character varying(255),
    tonodeipmonitor character varying(255) DEFAULT NULL::character varying,
    totid character varying(255),
    tunnelid character varying(255),
    tunneltype integer,
    xrolifecycle character varying(255),
    xroscope character varying(255)
);

CREATE TABLE public.cn_twamp_session_sender (
    admin_state integer,
    fnm character varying(255),
    id integer NOT NULL,
    ip_interface character varying(255),
    neindex integer,
    oper_state integer,
    reflector_ip character varying(255),
    secondary_state integer,
    session_id character varying(255),
    source_ip character varying(255),
    twamp_index integer
);

CREATE TABLE public.cn_uni4xx_hn (
    description1 bytea,
    description2 bytea,
    egrcir bigint,
    egrcirqueue bigint,
    id integer NOT NULL,
    ingcbs bigint,
    ingcir bigint,
    txuntagged integer,
    uni4xxegcir bigint
);

CREATE TABLE public.cn_uni_hn (
    cosmaptype integer,
    defaultcos integer,
    defaultvlanid integer,
    description bytea,
    deviceid integer,
    id integer NOT NULL,
    name bytea,
    numevcs bigint,
    objectstate integer,
    portid integer,
    porttype integer,
    rxunknown integer,
    rxuntagged integer,
    stpadminstate integer,
    tagethertype integer
);

CREATE TABLE public.cn_unmanaged_cross_connect (
    a_entity integer,
    b_entity integer,
    channel character varying(20),
    end_timeslots character varying(255),
    frequency integer DEFAULT 0,
    id integer NOT NULL,
    snd_channel character varying(20),
    snd_frequency integer DEFAULT 0,
    start_timeslots character varying(255),
    unmanagedcrs_type character varying(20)
);

CREATE TABLE public.cn_unmanaged_object (
    handover_port_id integer,
    id integer NOT NULL,
    odu_layer character varying(10),
    port_type character varying(20),
    service_name character varying(255),
    servicetype character varying(500) DEFAULT '0'::character varying,
    tp_parent_id integer,
    tributary_slots character varying(255),
    uno_business_id character varying(255)
);

CREATE TABLE public.cn_unsupported_network_element (
    became_supported boolean,
    detected_at bigint,
    id integer NOT NULL,
    ne_id integer,
    ne_version character varying(255)
);

CREATE TABLE public.cn_user_mtie_mask (
    display_points character varying(1000),
    id integer NOT NULL,
    index integer,
    measurement_points character varying(255),
    name character varying(255)
);

CREATE TABLE public.cn_user_mtie_mask_history (
    display_points character varying(1000),
    id integer NOT NULL,
    index integer,
    measurement_points character varying(255),
    name character varying(255)
);

CREATE TABLE public.cn_user_notification (
    id integer NOT NULL,
    jdoversion integer,
    message text,
    sender_id integer,
    tstamp_exp bigint,
    tstamp_sent bigint
);

CREATE TABLE public.cn_vc (
    bundle_id integer,
    id integer NOT NULL,
    port_index integer,
    vcindex integer
);

CREATE TABLE public.cn_virt_otn_ports (
    cpmux character varying(50),
    cppayload character varying(50),
    cpportid integer,
    cptribport integer,
    cptribslots character varying(255),
    cptype character varying(50),
    id integer NOT NULL,
    layerprot character varying(50),
    tpmux character varying(50),
    tppayload character varying(50),
    tpportid integer,
    tptribport integer,
    tptribslots character varying(255),
    tptype character varying(50),
    type character varying(50)
);

CREATE TABLE public.cn_vrf_f4 (
    direction character varying(255),
    entity_name character varying(255),
    id integer NOT NULL,
    l3_enable boolean,
    layer character varying(255),
    name character varying(255),
    snc_type character varying(255)
);

CREATE TABLE public.cn_vrf_tplist_f4 (
    index integer,
    tp_list character varying(255),
    vrf_id integer
);

CREATE TABLE public.cn_vtep (
    adminstate integer,
    id integer NOT NULL,
    neindex integer,
    operationalstate integer,
    vtepindex integer
);

CREATE TABLE public.cn_vtep_ip_interface (
    adminstate integer,
    associatedport character varying(255),
    id integer NOT NULL,
    neindex integer,
    operationalstate integer,
    protectportid integer,
    vtepid integer,
    vtepindex integer,
    vtepipifindex integer
);

CREATE TABLE public.cn_vx_lan_segment (
    adminstate integer,
    alias character varying(255),
    id integer NOT NULL,
    neindex integer,
    operstate integer,
    vtepid integer,
    vtepindex integer,
    vxlansegmentindex integer
);

CREATE SEQUENCE public.configfilesequence
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

CREATE TABLE public.db_reports (
    content bytea,
    id integer NOT NULL,
    name character varying(255),
    report_id integer
);

CREATE TABLE public.ec_bulk_transfer (
    direction integer,
    file_name character varying(255),
    id bigint NOT NULL,
    is_assigned boolean,
    jdoversion integer,
    last_message character varying(1000),
    neid integer,
    time_bulk_transfer_scheduled bigint,
    time_bulk_transfer_start bigint,
    transfer_file_type character varying(255),
    transfer_state character varying(255)
);

CREATE TABLE public.ec_configuration_file (
    apply_mode integer,
    file_name character varying(255),
    id bigint NOT NULL,
    jdoversion integer,
    ne_id integer,
    p bytea,
    user_name character varying(255)
);

CREATE TABLE public.ec_ne_types (
    file_id integer,
    ne_types character varying(255)
);

CREATE TABLE public.ec_service_templates (
    id integer NOT NULL,
    jdoversion integer,
    ne_id integer NOT NULL,
    service_id integer NOT NULL,
    template_name character varying(255)
);

CREATE TABLE public.ec_template_files (
    config_category integer NOT NULL,
    file_id integer NOT NULL,
    file_name character varying(255) NOT NULL,
    ftp_server_type integer NOT NULL,
    is_file boolean NOT NULL,
    jdoversion integer,
    last_modified bigint,
    summary bytea
);

CREATE TABLE public.efd_hist_segment (
    alm_ip_address character varying(255),
    hist_segment_length integer,
    hist_segment_loss integer,
    hist_segment_loss_per_km integer,
    hist_segment_timestamp bigint,
    hist_segment_uncertain boolean,
    id integer NOT NULL,
    port character varying(255),
    segment_number integer
);

CREATE SEQUENCE public.efd_hist_segment_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

ALTER SEQUENCE public.efd_hist_segment_id_seq OWNED BY public.efd_hist_segment.id;

CREATE TABLE public.efd_last_change_time (
    entity_name character varying(255),
    id integer NOT NULL,
    last_change bigint
);

CREATE SEQUENCE public.efd_last_change_time_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

ALTER SEQUENCE public.efd_last_change_time_id_seq OWNED BY public.efd_last_change_time.id;

CREATE TABLE public.efd_loss_guidance (
    cleaved boolean,
    id integer NOT NULL,
    loss_per_conn double precision,
    loss_per_km double precision,
    loss_per_splice double precision,
    num_connectors integer,
    num_splices integer,
    splitter1 character varying(255),
    splitter2 character varying(255),
    target_insertion_loss integer,
    type character varying(255),
    upper_threshold double precision
);

CREATE SEQUENCE public.efd_loss_guidance_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

ALTER SEQUENCE public.efd_loss_guidance_id_seq OWNED BY public.efd_loss_guidance.id;

CREATE TABLE public.efd_segment (
    alm_ip_address character varying(255),
    id integer NOT NULL,
    port character varying(255),
    segment_fp_length integer,
    segment_fp_loss integer,
    segment_fp_loss_per_km integer,
    segment_number integer,
    segment_remark character varying(255),
    segment_row_status integer,
    segment_start_correction integer,
    segment_start_event_id integer,
    segment_stop_correction integer,
    segment_stop_event_id integer
);

CREATE SEQUENCE public.efd_segment_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

ALTER SEQUENCE public.efd_segment_id_seq OWNED BY public.efd_segment.id;

CREATE TABLE public.efd_trace_attribute (
    averaging character varying(255),
    coupler_loss double precision,
    dpa_mode integer,
    duration integer,
    external_offset integer,
    id integer NOT NULL,
    link_length integer,
    max_laser_power double precision,
    measurement_length integer,
    pulse_width integer,
    refractive_index double precision,
    transient_fault_detection character varying(255)
);

CREATE SEQUENCE public.efd_trace_attribute_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

ALTER SEQUENCE public.efd_trace_attribute_id_seq OWNED BY public.efd_trace_attribute.id;

CREATE TABLE public.efd_trace_template (
    creation_date character varying(255),
    id integer NOT NULL,
    loss_guidance_id bigint,
    mode character varying(255),
    name character varying(255),
    trace_attribute_id bigint NOT NULL,
    trace_template_type character varying(255)
);

CREATE SEQUENCE public.efd_trace_template_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

ALTER SEQUENCE public.efd_trace_template_id_seq OWNED BY public.efd_trace_template.id;

CREATE TABLE public.egm_bandwidth_profile (
    ebsadmin integer,
    eiradmin integer,
    id integer NOT NULL
);

CREATE TABLE public.egm_classification_rule (
    dscprange character varying(255),
    id integer NOT NULL,
    pbitrange character varying(255),
    ruleno integer,
    servicename character varying(255),
    serviceno integer,
    serviceportidx integer,
    serviceportno integer,
    subpbitrange character varying(255),
    subvlanrange character varying(255),
    vlanrange character varying(255)
);

CREATE TABLE public.egm_cos_bw_profile (
    bwprofilename character varying(255),
    cosno integer,
    id integer NOT NULL,
    index integer,
    name character varying(255)
);

CREATE TABLE public.egm_service_port (
    cosprofilename character varying(255),
    egressaction integer,
    egressvlanid integer,
    id integer NOT NULL,
    ifindex integer,
    profilename character varying(255),
    qosprofilename character varying(255),
    serviceportindex integer
);

CREATE TABLE public.els_sso_account (
    account_type character varying(255),
    enabled boolean,
    id integer NOT NULL,
    password bytea,
    username character varying(255)
);

CREATE TABLE public.ev_counter (
    clearedhighestseveritytype integer,
    cr_total integer,
    cr_unackn integer,
    highestseveritytype integer,
    jdoclass character varying(255),
    jdoversion integer,
    mj_total integer,
    mj_unackn integer,
    mn_total integer,
    mn_unackn integer,
    objid integer NOT NULL,
    objtype integer NOT NULL
);

CREATE TABLE public.ev_event (
    acknowledge boolean,
    ackntimestamp bigint,
    acknuser character varying(20),
    alarmclass integer,
    alarmtype integer,
    arcsupport boolean,
    auxdata integer,
    category integer,
    clearingalarmid bigint,
    comment character varying(255),
    correlation integer,
    customername character varying(255),
    debuglog character varying(64),
    detectiontype integer,
    direction integer,
    dtype character varying(31),
    enterprise character varying(100) DEFAULT ''::character varying,
    entityalias text,
    entitydescription character varying(120),
    entityfulldescription character varying(255),
    first_raised_alarm bigint,
    id bigint NOT NULL,
    impairment boolean,
    isevent boolean,
    jdoversion integer,
    lineid integer,
    location integer,
    moduleindex character varying(500),
    moduletype integer,
    moduletypename character varying(120),
    neclearedtimestamp bigint,
    nelogindex integer,
    netimestamp bigint,
    newstringvalue character varying(255),
    newvalue bigint,
    nmsclearedtimestamp bigint,
    nmstimestamp bigint,
    objectindex character varying(500),
    objectindexes character varying(500),
    parameter text,
    parameterid integer,
    path integer,
    phylocation character varying(120),
    portindex character varying(500),
    pventityid character varying(100),
    pvsourceid character varying(100),
    pvsourcetype character varying(100),
    rootcause bigint,
    securityevent boolean,
    servicename character varying(255),
    severity integer,
    shortname character varying(50),
    sourcename character varying(255),
    sourcene character varying(20),
    sourceneid integer,
    sourcenetype integer,
    subnetid integer,
    syncrouteid integer,
    textincomplete boolean,
    trapid integer,
    typ integer
)
WITH (autovacuum_vacuum_scale_factor='0.005');

CREATE TABLE public.ev_event_assoc (
    id bigint,
    objectid integer,
    objecttype integer,
    objectuuid character varying(255)
)
WITH (autovacuum_vacuum_scale_factor='0.005');

CREATE TABLE public.ev_event_conns (
    id bigint,
    val integer
)
WITH (autovacuum_vacuum_scale_factor='0.005');

CREATE TABLE public.ev_event_hist_assoc (
    id bigint,
    objectid integer,
    objecttype integer,
    objectuuid character varying(255)
)
WITH (autovacuum_vacuum_scale_factor='0.005');

CREATE TABLE public.ev_event_history (
    acknowledge boolean,
    ackntimestamp bigint,
    acknuser character varying(20),
    alarmclass integer,
    arcsupport boolean,
    auxdata integer,
    category integer,
    comment character varying(255),
    correlatedalarm bigint,
    correlation integer,
    customername character varying(255),
    detectiontype integer,
    direction integer,
    dtype character varying(31),
    enterprise character varying(100) DEFAULT ''::character varying,
    entityalias text,
    entitydescription character varying(120),
    entityfulldescription character varying(255),
    id bigint NOT NULL,
    impairment boolean,
    jdoversion integer,
    lineid integer,
    location integer,
    moduletypename character varying(120),
    nelogindex integer,
    netimestamp bigint,
    nmstimestamp bigint,
    objectindex character varying(500),
    parameter text,
    path integer,
    phylocation character varying(120),
    pventityid character varying(100),
    pvsourceid character varying(100),
    pvsourcetype character varying(100),
    rootcause bigint,
    securityevent boolean,
    servicename character varying(255),
    severity integer,
    shortname character varying(50),
    sourcename character varying(255),
    sourcene character varying(20),
    sourceneid integer,
    sourcenetype integer,
    subnetid integer,
    trapid integer,
    typ integer
)
WITH (autovacuum_vacuum_scale_factor='0.005');

CREATE TABLE public.ev_event_multivar (
    event_id bigint,
    event_multivar_order integer,
    isserviceaffecting boolean,
    jdoid bigint NOT NULL,
    jdoversion integer,
    newvalue bigint,
    parameter character varying(1000),
    paramid integer,
    stringnewvalue character varying(255)
)
WITH (autovacuum_vacuum_scale_factor='0.005');

CREATE TABLE public.ev_event_sounds (
    active boolean,
    event_description character varying(255),
    file_contents bytea,
    file_modified_date bigint,
    file_name character varying(255),
    id bigint NOT NULL,
    jdoversion integer,
    owner integer,
    severity integer,
    sound_category character varying(255)
);

CREATE TABLE public.ev_event_sync_ncd (
    event_id bigint,
    ncd_id integer,
    temp_ncd_id integer
)
WITH (autovacuum_vacuum_scale_factor='0.005');

CREATE TABLE public.ev_event_sync_node (
    event_id bigint,
    sync_node_id integer,
    temp_sync_node_id integer
)
WITH (autovacuum_vacuum_scale_factor='0.005');

CREATE TABLE public.ev_filter (
    "timestamp" bigint,
    enabled boolean,
    filtermask integer,
    id integer NOT NULL,
    jdoclass character varying(255),
    mindelay bigint,
    name character varying(255),
    type integer,
    user_id integer,
    version integer
);

CREATE TABLE public.ev_filter_node (
    filter_id integer,
    node_id integer
);

CREATE TABLE public.ev_latency_monitor (
    aid character varying(255),
    associated_probe_id integer,
    description character varying(255),
    direction integer,
    id bigint NOT NULL,
    jdoclass character varying(255),
    jdoversion integer,
    monitored_status integer,
    name character varying(255),
    ne_id integer,
    tca_timestamp timestamp without time zone
);

CREATE TABLE public.ev_latency_monitor_cos (
    cos_index integer,
    description character varying(255),
    id bigint NOT NULL,
    jdoversion integer,
    latency_id bigint
);

CREATE TABLE public.ev_nm_settings (
    id bigint NOT NULL,
    jdoclass character varying(255),
    timeout bigint,
    version integer
);

CREATE TABLE public.ev_property (
    category character varying(255),
    id integer NOT NULL,
    instance integer,
    jdoversion integer,
    key character varying(50),
    value text
);

CREATE TABLE public.ev_severity (
    jdoid bigint,
    severity integer,
    severity_order integer
);

CREATE TABLE public.ev_subscription (
    id integer NOT NULL,
    jdoversion integer,
    topicname character varying(255)
);

CREATE TABLE public.ev_subscription_filter (
    id integer,
    type character varying(255)
);

CREATE TABLE public.ev_transport (
    agentid character varying(255),
    agenttype character varying(255),
    filter integer,
    id bigint NOT NULL,
    jdoclass character varying(255),
    parameters character varying(255),
    subject character varying(255),
    version integer
);

CREATE TABLE public.ev_trap_severity (
    alarm_class integer,
    channelmodule boolean,
    enterpriseid character varying(255),
    jdoclass character varying(255),
    jdoid bigint NOT NULL,
    jdoversion integer,
    netype integer,
    nochannelmodule boolean,
    trapid integer
);

CREATE TABLE public.ev_trash (
    jdoid bigint,
    trash boolean,
    trash_order integer
);

CREATE TABLE public.fam_event (
    atten character varying(255),
    comment character varying(1000),
    custom_event boolean,
    customid character varying(255),
    display_name character varying(255),
    event_name character varying(255),
    event_type character varying(255),
    loss character varying(255),
    monitoring_point character varying(255),
    owner_id integer,
    pos character varying(255),
    refl character varying(255),
    status character varying(255),
    typeontsplitter character varying(255)
);

CREATE TABLE public.fam_link_properties (
    id integer,
    key character varying(255),
    value character varying(255)
);

CREATE TABLE public.fam_point (
    fa_record integer,
    fp_record integer,
    id integer NOT NULL,
    ip_address character varying(255),
    link_name character varying(1000),
    ne_name character varying(255),
    neid integer,
    port_aid character varying(255),
    port_name character varying(255),
    portid integer
);

CREATE TABLE public.fam_record (
    c_time bigint,
    cleicode character varying(50),
    contents bytea,
    email_sent boolean,
    famportconfigurationtype integer,
    faultposition double precision,
    filename character varying(255),
    firmwarerevision character varying(50),
    fpgarevision character varying(50),
    hardwarerevision character varying(50),
    id integer NOT NULL,
    inventorytype character varying(50),
    jdoversion integer,
    linklength double precision,
    linkloss double precision,
    partnumber character varying(50),
    releasenumber character varying(50),
    retrieval_status character varying(255),
    serialnumber character varying(50),
    shelfname character varying(50),
    shelfunitname character varying(50),
    softwarerevision character varying(50),
    type integer,
    usi character varying(50),
    vendorid character varying(50),
    wavelength character varying(50)
);

CREATE TABLE public.fam_trace (
    id integer NOT NULL,
    jdoversion integer,
    name character varying(255),
    owner_id integer,
    type integer
);

CREATE TABLE public.fam_trace_data (
    ord integer,
    owner_id integer,
    x double precision,
    y double precision
);

CREATE TABLE public.fam_trace_settings (
    key character varying(255),
    owner_id integer,
    value character varying(255)
);

CREATE TABLE public.fl_alm (
    alarm_state character varying(255),
    building_id integer,
    connectivity_state character varying(255),
    event_id integer,
    id integer NOT NULL,
    ip_address character varying(255),
    name character varying(255),
    ne_type character varying(255)
);

CREATE TABLE public.fl_port (
    admin_state character varying(255),
    aid character varying(255),
    alarm_state character varying(255),
    ext_id character varying(255),
    fa_record integer,
    id integer NOT NULL,
    ip character varying(255),
    loss double precision,
    monitoring_port boolean,
    name character varying(255),
    ne_id integer,
    number integer,
    number_str character varying(255),
    oper_state character varying(255),
    port_extender boolean,
    tone_status boolean
);

CREATE TABLE public.geo_server_settings (
    enc_fbr_geo_version character varying(255),
    host character varying(255),
    id integer NOT NULL,
    login character varying(255),
    password bytea,
    port integer,
    protocol character varying(255),
    timeout integer
);

CREATE TABLE public.gnss_firewall_rules (
    action_type smallint NOT NULL,
    id integer NOT NULL,
    jdoclass character varying(255),
    jdoversion integer,
    time_to_action integer,
    trigger_event_id integer NOT NULL,
    trigger_event_type smallint NOT NULL
);

CREATE TABLE public.gnss_ne_block_list (
    gnss_firewall_disable boolean DEFAULT true,
    jdoversion integer,
    ml_alarms_disable boolean DEFAULT true,
    ne_id integer NOT NULL,
    rca_disable boolean DEFAULT true
);

CREATE TABLE public.gnss_rca_settings (
    dtype character varying(31),
    gnss_rca_enabled boolean DEFAULT false,
    id integer NOT NULL,
    rca_event_burst_idle_time integer,
    rca_idle_time integer,
    rca_max_idle_time integer
);

CREATE TABLE public.gnss_settings (
    dtype character varying(31),
    id integer NOT NULL,
    jdoversion integer,
    override_device_thresholds boolean,
    pps_gen_condition integer,
    sat_min1_threshold integer,
    sat_min2_threshold integer
);

CREATE TABLE public.hm_alarm_samples_history (
    critical integer,
    jdoclass character varying(255),
    major integer,
    minor integer,
    time_stamp bigint DEFAULT 0 NOT NULL
);

CREATE TABLE public.hm_health_monitoring_sample (
    free_hdd bigint,
    free_memory bigint,
    free_swap bigint,
    host_id character varying(255),
    id integer NOT NULL,
    jdoclass character varying(255),
    jdoversion integer,
    system_cpu_load double precision,
    time_stamp bigint DEFAULT 0 NOT NULL,
    total_hdd bigint,
    total_memory bigint,
    total_swap bigint
);

CREATE TABLE public.hm_ne_samples_history (
    connected integer,
    disconnected integer,
    jdoclass character varying(255),
    time_stamp bigint DEFAULT 0 NOT NULL
);

CREATE TABLE public.hm_pwr_consump_samples_history (
    f7_count integer,
    f7_power_consumption double precision,
    jdoclass character varying(255),
    time_stamp bigint DEFAULT 0 NOT NULL
);

CREATE TABLE public.hm_service_samples_history (
    degraded integer,
    faulted integer,
    jdoclass character varying(255),
    ok integer,
    time_stamp bigint DEFAULT 0 NOT NULL
);

CREATE TABLE public.images (
    id integer NOT NULL,
    image bytea,
    name character varying(255),
    root_id integer
);

CREATE TABLE public.jdo_sequence (
    id character varying(50) NOT NULL,
    sequence_value numeric(38,0)
);

CREATE TABLE public.ma_tone (
    "time" character varying(255),
    aid character varying(255),
    id integer NOT NULL,
    ip character varying(255)
);

CREATE TABLE public.md_global_field_contents (
    content character varying(10000),
    id integer DEFAULT 0 NOT NULL,
    jdoversion integer,
    listindex integer,
    ntntype integer
);

CREATE TABLE public.md_userdefined_text_fields (
    content character varying(10000),
    description character varying(255),
    globaltextfield_id integer DEFAULT 0 NOT NULL,
    id integer DEFAULT 0 NOT NULL,
    jdoversion integer,
    text_fieldholder_id integer
);

CREATE TABLE public.ml_adaptation (
    id integer NOT NULL
);

CREATE TABLE public.ml_adaptation_client (
    adapt_id integer,
    id integer NOT NULL,
    topo_element_id integer
);

CREATE TABLE public.ml_adaptation_server (
    adapt_id integer,
    id integer NOT NULL,
    topo_element_id integer
);

CREATE TABLE public.ml_bw_profile (
    aggregate boolean DEFAULT false,
    cbs bigint,
    cir bigint,
    colormode integer,
    couplingflag boolean,
    ebs bigint,
    eir bigint,
    id integer NOT NULL,
    name character varying(255),
    parent integer
);

CREATE TABLE public.ml_bw_resources (
    cir bigint,
    id integer NOT NULL,
    jdoversion integer
);

CREATE TABLE public.ml_connection (
    a_end_id integer,
    ackd_at bigint,
    ackd_by character varying(255),
    acknowledged boolean DEFAULT false,
    adminstate integer,
    conntype integer,
    directed boolean,
    id integer NOT NULL,
    layer integer,
    life_cycle integer,
    oper_state integer,
    physical_conn boolean DEFAULT false,
    reason_code integer,
    resource_type integer,
    secondary_state character varying(255),
    timestamp_degraded bigint,
    timestamp_faulted bigint,
    tot_resource_id integer,
    z_end_id integer
);

CREATE TABLE public.ml_connection_point (
    admin_state integer,
    as_mo_ref character varying(255) DEFAULT NULL::character varying,
    id integer NOT NULL,
    location character varying(255),
    oper_state integer,
    oper_state_ne_time bigint,
    oper_state_nms_time bigint,
    parent_cp_id integer,
    parent_id integer,
    parent_module_id integer,
    parent_node_id integer,
    term_layer_string character varying(1000) DEFAULT NULL::character varying,
    type integer
);

CREATE TABLE public.ml_connection_reservation (
    conn_id integer,
    id bigint NOT NULL,
    jdoversion integer,
    reserved_resource_id integer,
    trail_id integer
);

CREATE TABLE public.ml_connection_resource (
    channels text,
    fiber_type integer,
    id integer NOT NULL,
    jdoclass character varying(255),
    jdoversion integer,
    layer integer,
    resource_id integer,
    tp_pair_id integer,
    ts_type integer
);

CREATE TABLE public.ml_cp_mon_layers (
    mon_layers character varying(255),
    topo_id integer
);

CREATE TABLE public.ml_cp_path_seq (
    cp_id integer NOT NULL,
    cp_order integer,
    path_id integer NOT NULL
);

CREATE TABLE public.ml_cp_term_layers (
    term_layers character varying(255),
    topo_id integer
);

CREATE TABLE public.ml_end_point (
    cp_id integer,
    id integer NOT NULL,
    if_type integer,
    nrl character varying(120),
    prot_types character varying(120),
    resources character varying(500),
    term_type character varying(10),
    type integer
);

CREATE TABLE public.ml_layer_extension (
    associated_connection_point integer,
    egress_bw_accounting boolean DEFAULT false,
    egress_bw_profile_id integer,
    id integer NOT NULL,
    ingress_bw_accounting boolean DEFAULT false,
    ingress_bw_profile_id integer,
    jdoclass character varying(255),
    layertype integer
);

CREATE TABLE public.ml_log_link (
    a_end_cep character varying(255),
    a_end_nep character varying(255),
    availability character varying(20),
    compact_nrl character varying(255),
    computed_cost integer DEFAULT 0,
    computed_srlg character varying(1000),
    id integer NOT NULL,
    jdoversion integer,
    last_update bigint,
    layer_protocol_qualifier character varying(30),
    operational_state character varying(10),
    standard_nrl character varying(255),
    unique_srlg_of_fiber_ll integer,
    user_cost integer DEFAULT 0,
    user_label character varying(255),
    user_srlg character varying(1000),
    uuid character varying(255),
    z_end_cep character varying(255),
    z_end_nep character varying(255)
);

CREATE TABLE public.ml_log_link_dev_endpoint (
    aid character varying(255),
    id bigint NOT NULL,
    jdoversion integer,
    log_link_id integer,
    node_id integer NOT NULL
);

CREATE TABLE public.ml_log_link_fiber_srlg (
    id integer NOT NULL
);

CREATE SEQUENCE public.ml_log_link_fiber_srlg_seq
    START WITH 268435456
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

CREATE TABLE public.ml_log_link_oms_prt_path (
    dev_endpoint_id bigint,
    dev_endpoint_id_order integer,
    id integer
);

CREATE TABLE public.ml_log_link_oms_wkg_path (
    dev_endpoint_id bigint,
    dev_endpoint_id_order integer,
    id integer
);

CREATE TABLE public.ml_log_link_sup_link_resource (
    id bigint NOT NULL,
    jdoversion integer,
    layer_protocol_qualifier character varying(30),
    log_link_id integer
);

CREATE TABLE public.ml_module (
    id integer NOT NULL,
    location character varying(255),
    moduletype integer,
    node_id integer
);

CREATE TABLE public.ml_monitoring_section (
    ackd_at bigint,
    ackd_by character varying(255),
    acknowledged boolean DEFAULT false,
    admin_state integer,
    directed boolean,
    id integer NOT NULL,
    layer integer,
    life_cycle integer,
    ml_parent_entity_id integer,
    oper_state integer,
    path integer,
    reason_code integer,
    reverse_path integer,
    secondary_state character varying(255),
    timestamp_degraded bigint,
    timestamp_faulted bigint,
    type integer
);

CREATE TABLE public.ml_monitoring_section_entities (
    entity_id integer NOT NULL,
    monitoring_section_id integer NOT NULL
);

CREATE TABLE public.ml_mtp_conn_point (
    conn_id integer,
    connpoint_id integer,
    id integer NOT NULL,
    jdoversion integer,
    tag integer
);

CREATE TABLE public.ml_ni_link (
    a_end_id integer,
    id integer NOT NULL,
    label character varying(1000) DEFAULT NULL::character varying,
    linktype integer,
    z_end_id integer
);

CREATE TABLE public.ml_node (
    id integer NOT NULL,
    netype integer
);

CREATE TABLE public.ml_path (
    a_end_id integer,
    id integer NOT NULL,
    jdoversion integer,
    linkless boolean,
    z_end_id integer
)
WITH (autovacuum_vacuum_scale_factor='0.005');

CREATE TABLE public.ml_path_seq (
    connection_id integer NOT NULL,
    connection_order integer,
    path_id integer NOT NULL
);

CREATE TABLE public.ml_res_tp_pair (
    a_end_tp integer,
    id integer NOT NULL,
    z_end_tp integer
);

CREATE TABLE public.ml_res_ts_pair (
    a_end_ts integer,
    id integer NOT NULL,
    res_ref integer,
    z_end_ts integer
);

CREATE TABLE public.ml_segment_adaptation (
    id integer NOT NULL,
    layer integer,
    segment_type integer
);

CREATE TABLE public.ml_segment_client (
    id integer NOT NULL,
    seg_id integer,
    topo_element_id integer
);

CREATE TABLE public.ml_segment_entities (
    entity_id integer NOT NULL,
    seg_id integer NOT NULL
);

CREATE TABLE public.ml_segment_path (
    id integer NOT NULL,
    seg_id integer,
    seg_p_id integer,
    seg_path_type integer
);

CREATE TABLE public.ml_ser_view_lines (
    line_id integer NOT NULL,
    view_id integer NOT NULL
);

CREATE TABLE public.ml_ser_view_nes (
    ne_id integer NOT NULL,
    view_id integer NOT NULL
);

CREATE TABLE public.ml_ser_view_subnets (
    subnet_id integer NOT NULL,
    view_id integer NOT NULL
);

CREATE TABLE public.ml_service (
    a_end_cep character varying(120),
    a_end_id integer,
    ackd_at bigint,
    ackd_by character varying(255),
    acknowledged boolean DEFAULT false,
    admin_state integer,
    bk_w_id integer,
    contract_id integer,
    customer_id integer,
    directed boolean,
    fw_w_id integer,
    id integer NOT NULL,
    is_managed boolean,
    layer integer,
    life_cycle integer,
    ni_link_id integer,
    oper_state integer,
    prot boolean,
    reason_code integer,
    remarks character varying(1000) DEFAULT NULL::character varying,
    reserved_resource_id integer,
    resource_type integer,
    secondary_state character varying(255),
    servicegroup_id integer,
    timestamp_degraded bigint,
    timestamp_faulted bigint,
    topo_update_time bigint,
    uuid character varying(255),
    z_end_cep character varying(120),
    z_end_id integer
);

CREATE TABLE public.ml_service_endpoint (
    connpoint_id integer,
    id integer NOT NULL,
    jdoversion integer,
    service_id integer,
    tag integer
);

CREATE TABLE public.ml_service_entities (
    entity_id integer NOT NULL,
    service_id integer NOT NULL
);

CREATE TABLE public.ml_te_property (
    key integer,
    te_id integer,
    value text
);

CREATE TABLE public.ml_topo_mo_ref (
    descr character varying(500) NOT NULL,
    entity_class character varying(255),
    entityindex character varying(500) DEFAULT NULL::character varying,
    id integer NOT NULL,
    jdoversion integer,
    te_id integer
);

CREATE TABLE public.ml_topology_element (
    description character varying(1000) DEFAULT NULL::character varying,
    entityresource integer,
    id integer NOT NULL,
    jdoclass character varying(255),
    jdoversion integer,
    label character varying(1000) DEFAULT NULL::character varying,
    lifecycle_pri_state integer,
    lifecycle_primary_state integer,
    lifecycle_sec_states integer
);

CREATE TABLE public.ml_topology_layer_custom_names (
    a_end_ne_id integer,
    a_end_te character varying(1000),
    id integer NOT NULL,
    jdoversion integer,
    layer integer,
    mismatch_timestamp bigint,
    name character varying(1000),
    type integer,
    z_end_ne_id integer,
    z_end_te character varying(1000)
);

CREATE TABLE public.ml_ui_service (
    configurationstate integer,
    cpobject character varying(255),
    entityresource integer,
    eodlayerprotocol character varying(120),
    eodpayload character varying(120),
    ethernetservice boolean,
    explorationstate integer,
    id integer NOT NULL,
    jdoversion integer,
    layerprotocol character varying(255),
    layerprotocol_id integer,
    lifecycle character varying(255),
    lifecyclestate integer,
    mlconnectivityspan integer,
    pathstate character varying(100),
    peer_cep_compact_nrl character varying(120),
    peerne character varying(255),
    peerne_id integer,
    peertp character varying(255),
    pmtemplate character varying(255),
    protectionrole integer,
    releasestate boolean,
    restoration boolean,
    servicelayer integer,
    servicemode integer,
    servicename character varying(1000) DEFAULT NULL::character varying,
    servicetype integer,
    start_cep_compact_nrl character varying(120),
    startne character varying(255),
    startne_id integer,
    starttp character varying(255),
    tetype integer,
    wavelength character varying(255)
);

CREATE TABLE public.ml_vlan_values (
    id integer NOT NULL,
    jdoversion integer,
    resource_id integer,
    vlan_end_value integer,
    vlan_priority integer,
    vlan_start_value integer
);

CREATE TABLE public.mt_tunnelctp_dt (
    headipaddress character varying(255),
    headmtosiname character varying(255),
    headname character varying(255),
    headtunnelname character varying(255),
    jdoversion integer,
    remoteipaddress character varying(255),
    remotename character varying(255),
    remotetype character varying(255),
    tunnel_id bigint NOT NULL
);

CREATE TABLE public.mtosi_subscriptions (
    endpoint character varying(255),
    id bigint NOT NULL,
    jdoversion integer,
    md_id character varying(255),
    mtosi_version integer,
    topic character varying(255)
);

CREATE TABLE public.mtosi_user_policy (
    dateset timestamp without time zone,
    history bytea,
    id bigint NOT NULL,
    jdoversion integer,
    keep integer,
    name character varying(255),
    pass bytea
);

CREATE TABLE public.nc_assigned_snmp_profile_info (
    connectivity integer,
    id integer NOT NULL,
    neid integer,
    neidentifier character varying(1000),
    neip character varying(255),
    nename character varying(1000),
    neswversion character varying(255),
    netype integer,
    snmpprofile_id bigint,
    snmpprofile_name character varying(255),
    subnetid integer,
    subnetname character varying(1000)
);

CREATE TABLE public.nc_cliprops (
    clipassword bytea,
    cliprotocol integer,
    cliuser bytea,
    connecttimeout integer,
    id integer NOT NULL,
    intervalbtwretries integer,
    inuse boolean,
    jdoclass character varying(255),
    jdoversion integer,
    numberofretries integer,
    portnumber integer,
    readtimeout integer
);

CREATE TABLE public.nc_ftp_preferences (
    ftp_conn_type integer,
    ftp_type integer NOT NULL,
    home_dir character varying(255),
    id bigint DEFAULT 0 NOT NULL,
    jdoversion integer,
    prefer_key_auth boolean,
    server_dir character varying(255),
    server_login character varying(255),
    server_node character varying(255),
    server_password bytea,
    server_protocol integer
);

CREATE TABLE public.nc_httpprops (
    automsgsubscr boolean,
    ecsessionkeepalivems integer,
    id integer NOT NULL,
    jdoversion integer,
    lastsubscrdate bigint NOT NULL,
    netlscerthandling character varying(255),
    password bytea,
    portno integer,
    protocoltype character varying(255),
    timeout integer,
    useglobal boolean,
    username bytea
);

CREATE TABLE public.nc_netconf_props (
    id integer NOT NULL,
    jdoversion integer,
    lastsubscrdate bigint NOT NULL,
    netconfver character varying(255),
    password bytea,
    portno integer,
    timeout integer,
    useglobal boolean,
    username bytea
);

CREATE TABLE public.nc_preferences (
    brmodeconfigfile integer,
    brsecuritymode integer,
    ecparalleluploads integer,
    ecsecuritymode integer,
    famemail character varying(255),
    famsecuritymode integer,
    jdoclass character varying(255),
    jdoid bigint DEFAULT 0 NOT NULL,
    jdoversion integer,
    nemaxbackupfiles integer,
    nemaxconfigbackupfiles integer,
    nipafsecuritymode integer,
    ntpsecuritymode integer,
    pafsecuritymode integer,
    pollint integer,
    profilesecuritymode integer,
    proxyport integer,
    shortenfingerprint boolean DEFAULT false,
    swparalleldownloads integer,
    swsecuritymode integer,
    trapport integer
);

CREATE TABLE public.nc_profile_snmp (
    authpassword bytea,
    authtype integer,
    autotrapagingenabled boolean,
    comment character varying(255),
    getcommunity bytea NOT NULL,
    id bigint NOT NULL,
    inuse boolean,
    jdoversion integer,
    maxprocessingfrequency integer,
    name character varying(64),
    privpassword bytea,
    privtype integer,
    retrycount integer NOT NULL,
    seclevel integer,
    setcommunity bytea NOT NULL,
    snmpversion integer NOT NULL,
    timeout integer NOT NULL,
    trapsfromunknownenabled boolean,
    user_id bytea
);

CREATE TABLE public.nc_snmpprops (
    authpassword bytea,
    authtype integer,
    autotrapagingenabled boolean,
    customengineid character varying(255) DEFAULT ''::character varying,
    getcommunity bytea,
    id integer NOT NULL,
    inuse boolean,
    jdoclass character varying(255),
    jdoversion integer,
    maxprocessingfrequency integer,
    privpassword bytea,
    privtype integer,
    retrycount integer,
    seclevel integer,
    setcommunity bytea,
    snmpengineboots integer DEFAULT 0 NOT NULL,
    timeout integer,
    trapsfromunknownenabled boolean,
    usecustomengineid boolean DEFAULT false,
    user_id bytea,
    version integer
);

CREATE TABLE public.ni_res_settings (
    id integer NOT NULL,
    ocs_id integer,
    restoration_mode integer,
    restoration_type integer,
    reversion_mode integer
);

CREATE TABLE public.nms_clock_probe_history (
    constteclrthreshold bigint,
    consttemeasurementtime bigint,
    consttethreshold bigint,
    consttetotalarmtime bigint,
    consttewindow bigint,
    id integer NOT NULL,
    maxteclrthreshold integer,
    maxtemeasurementtime bigint,
    maxtethreshold integer,
    maxtetotalarmtime bigint,
    measurement_type integer,
    source_failure boolean,
    source_oid character varying(255),
    source_type integer,
    srcdisqualify integer,
    srcdisqualifyonctethreshold integer,
    srcdisqualifyonmtiemask integer,
    tie_meas_rate integer,
    user_mtie_mask integer
);

CREATE TABLE public.nms_ptp_clock_probe_history (
    constteclrthreshold bigint,
    consttemeasurementtime bigint,
    consttethreshold bigint,
    consttetotalarmtime bigint,
    consttewindow bigint,
    direction integer,
    flow_oid character varying(255),
    id integer NOT NULL,
    instteclrthreshold bigint,
    insttemeasurementtime bigint,
    insttethreshold bigint,
    insttetotalarmtime bigint,
    master_ip character varying(255),
    master_port_identity character varying(255),
    maxteclrthreshold integer,
    maxtemeasurementtime bigint,
    maxtethreshold integer,
    maxtetotalarmtime bigint,
    measurement_type integer,
    no_event_msg_failure boolean,
    no_timestamp_failure boolean,
    slave_ip character varying(255),
    slave_port_identity character varying(255),
    user_mtie_mask integer
);

CREATE TABLE public.nms_sj_probe_history (
    actual_duration bigint,
    actualstarttime bigint,
    dtype character varying(31),
    expected_ql integer,
    history_index integer,
    id integer NOT NULL,
    jdoversion integer,
    margin_failed boolean,
    margincrosstime bigint,
    maskcrosstime bigint,
    maske_failed boolean,
    mtie_margin bigint,
    mtie_mask integer,
    ne_id integer,
    ne_index integer,
    original_owner_probe_id integer,
    probe_index integer,
    rawdatacollectionenabled integer,
    reference_failure boolean,
    reference_oid character varying(255),
    running_failed_count integer
);

CREATE TABLE public.nms_sj_probe_mtie_result (
    dtype character varying(31),
    history_index integer,
    id integer NOT NULL,
    jdoversion integer,
    mtie_value integer NOT NULL,
    mtie_value_index integer,
    ne_index integer,
    original_owner_probe_id integer,
    probe_index integer
);

CREATE TABLE public.nmsclockmtieresultdbimpl (
    id integer NOT NULL
);

CREATE TABLE public.nmsptpclockmtieresultdbimpl (
    id integer NOT NULL
);

CREATE TABLE public.p_properties (
    jdoversion integer,
    keyword character varying(100) NOT NULL,
    str_value character varying(512)
);

CREATE TABLE public.pd_bw_profile (
    aggregate boolean DEFAULT false,
    buffersize bigint,
    cbs bigint,
    cir bigint,
    colormode integer,
    couplingflag boolean,
    ebs bigint,
    eir bigint,
    id integer NOT NULL,
    name character varying(255),
    parent integer,
    policingenabled boolean
);

CREATE TABLE public.pd_bw_resources (
    cir bigint,
    id integer NOT NULL,
    jdoversion integer
);

CREATE TABLE public.pd_cfm_entry (
    admin_state integer,
    ccm_generation_enabled boolean,
    direction integer,
    id integer NOT NULL,
    jdoversion integer,
    layer_extension_id integer,
    ma_name character varying(255),
    md_level integer,
    md_name character varying(255),
    mep_id integer,
    object_ref character varying(255)
);

CREATE TABLE public.pd_cfmentry_pvids (
    cfm_entry_id integer,
    pvid bigint
);

CREATE TABLE public.pd_cfmentry_rmeps (
    cfm_entry_id integer,
    rmeps integer
);

CREATE TABLE public.pd_connection (
    a_end_id integer,
    ackd_at bigint,
    ackd_by character varying(255),
    acknowledged boolean DEFAULT false,
    adminstate integer,
    conntype integer,
    directed boolean,
    id integer NOT NULL,
    layer integer,
    life_cycle integer,
    oper_state integer,
    physical_conn boolean DEFAULT false,
    reason_code integer,
    resource_type integer,
    secondary_state character varying(255),
    timestamp_degraded bigint,
    timestamp_faulted bigint,
    tot_resource_id integer,
    z_end_id integer
);

CREATE TABLE public.pd_connection_point (
    admin_state integer,
    as_mo_ref character varying(255) DEFAULT NULL::character varying,
    id integer NOT NULL,
    location character varying(255),
    oper_state integer,
    oper_state_ne_time bigint,
    oper_state_nms_time bigint,
    parent_cp_id integer,
    parent_id integer,
    parent_module_id integer,
    parent_node_id integer,
    term_layer_string character varying(1000) DEFAULT NULL::character varying,
    type integer
);

CREATE TABLE public.pd_connection_resource (
    child_vlan_resource integer,
    fiber_type integer,
    id integer NOT NULL,
    jdoclass character varying(255),
    jdoversion integer,
    layer integer,
    resource_id integer,
    vlans text
);

CREATE TABLE public.pd_cp_path_seq (
    cp_id integer NOT NULL,
    cp_order integer,
    path_id integer NOT NULL
);

CREATE TABLE public.pd_cp_term_layers (
    term_layers character varying(255),
    topo_id integer
);

CREATE TABLE public.pd_eth_service_intent (
    admin_state integer,
    customer_id integer,
    id integer NOT NULL,
    layerprotocol integer,
    lifecycle_state integer,
    name text,
    parent_id integer
);

CREATE TABLE public.pd_eth_service_intent_param (
    id integer NOT NULL,
    jdoversion integer,
    key integer,
    paramgroup integer,
    ref_id integer,
    value text
);

CREATE TABLE public.pd_eth_service_intent_tp (
    aid character varying(255),
    id integer NOT NULL,
    jdoversion integer,
    ne_id integer,
    service_id integer,
    tptype integer
);

CREATE TABLE public.pd_evpn_service_entity_list (
    entity_id integer,
    evpn_service_id integer,
    index integer
);

CREATE TABLE public.pd_layer_ext_pd_conn_point (
    connection_point_id integer NOT NULL,
    ml_lag_id integer NOT NULL
);

CREATE TABLE public.pd_layer_extension (
    associated_connection_point integer,
    ctag_control integer,
    ctag_vlan_id integer,
    ctag_vlan_priority integer,
    default_cos integer,
    default_member_enabled boolean DEFAULT false,
    egress_bw_accounting boolean DEFAULT false,
    egress_bw_profile_id integer,
    id integer NOT NULL,
    ingress_bw_accounting boolean DEFAULT false,
    ingress_bw_profile_id integer,
    interface_name character varying(255),
    jdoclass character varying(255),
    layertype integer,
    mtu integer,
    port_service_type integer,
    stag_control integer,
    stag_vlan_id integer,
    stag_vlan_priority integer,
    untagged_frames_enabled boolean DEFAULT false,
    vlan_member_list text
);

CREATE TABLE public.pd_module (
    id integer NOT NULL,
    location character varying(255),
    moduletype integer,
    node_id integer
);

CREATE TABLE public.pd_mtp_conn_point (
    conn_id integer,
    connpoint_id integer,
    id integer NOT NULL,
    jdoversion integer,
    tag integer
);

CREATE TABLE public.pd_node (
    id integer NOT NULL,
    netype integer
);

CREATE TABLE public.pd_path (
    a_end_id integer,
    id integer NOT NULL,
    jdoversion integer,
    linkless boolean,
    z_end_id integer
);

CREATE TABLE public.pd_path_seq (
    connection_id integer NOT NULL,
    connection_order integer,
    path_id integer NOT NULL
);

CREATE TABLE public.pd_pm_profile (
    id integer NOT NULL,
    owner integer,
    profile_name character varying(255)
);

CREATE TABLE public.pd_pm_profile_counter (
    accumulate boolean,
    aggregation_type integer,
    id bigint NOT NULL,
    layer integer,
    max_threshold character varying(255),
    max_value double precision,
    min_threshold character varying(255),
    min_value double precision,
    pm_group character varying(255),
    pm_id integer,
    pm_name character varying(255),
    profile integer,
    profile_id integer,
    service_layer character varying(255),
    threshold_name character varying(255),
    time_type integer,
    unit character varying(255)
);

CREATE TABLE public.pd_ser_view_nes (
    associated_nodes integer,
    view_id integer
);

CREATE TABLE public.pd_ser_view_subnets (
    associated_subnets integer,
    view_id integer
);

CREATE TABLE public.pd_service (
    a_end_id integer,
    ackd_at bigint,
    ackd_by character varying(255),
    acknowledged boolean DEFAULT false,
    admin_state integer,
    bk_w_id integer,
    contract_id integer,
    customer_id integer,
    directed boolean,
    fw_w_id integer,
    id integer NOT NULL,
    is_managed boolean,
    layer integer,
    life_cycle integer,
    oper_state integer,
    performancetemplateid integer,
    prot boolean,
    reason_code integer,
    remarks character varying(1000) DEFAULT NULL::character varying,
    reserved_resource_id integer,
    resource_type integer,
    secondary_state character varying(255),
    servicegroup_id integer,
    timestamp_degraded bigint,
    timestamp_faulted bigint,
    topo_update_time bigint,
    z_end_id integer
);

CREATE TABLE public.pd_service_endpoint (
    connpoint_id integer,
    id integer NOT NULL,
    jdoversion integer,
    service_id integer,
    tag integer
);

CREATE TABLE public.pd_service_entities (
    entity_id integer NOT NULL,
    service_id integer NOT NULL
);

CREATE TABLE public.pd_te_property (
    key integer,
    te_id integer,
    value text
);

CREATE TABLE public.pd_topo_mo_ref (
    descr character varying(500) NOT NULL,
    entity_class character varying(255),
    entityindex character varying(500) DEFAULT NULL::character varying,
    id integer NOT NULL,
    jdoversion integer,
    te_id integer
);

CREATE TABLE public.pd_topology_element (
    description character varying(1000) DEFAULT NULL::character varying,
    entityresource integer,
    id integer NOT NULL,
    jdoclass character varying(255),
    jdoversion integer,
    label character varying(1000) DEFAULT NULL::character varying,
    lifecycle_pri_state integer,
    lifecycle_sec_states integer
);

CREATE TABLE public.pd_ui_service (
    configurationstate integer,
    entityresource integer,
    ethernetservice boolean,
    explorationstate integer,
    id integer NOT NULL,
    jdoversion integer,
    layerprotocol character varying(255),
    layerprotocol_id integer,
    lifecycle character varying(255),
    lifecyclestate integer,
    mlconnectivityspan integer,
    pathstate character varying(100),
    peerne character varying(255),
    peerne_id integer,
    peertp character varying(255),
    pmtemplate character varying(255),
    protectionrole integer,
    releasestate boolean,
    restoration boolean,
    servicelayer integer,
    servicemode integer,
    servicename character varying(1000) DEFAULT NULL::character varying,
    servicetype integer,
    startne character varying(255),
    startne_id integer,
    starttp character varying(255),
    tetype integer,
    wavelength character varying(255)
);

CREATE TABLE public.pl_polling_onetime_properties (
    jdoversion integer,
    pointoftime bigint,
    pollingmanagerid integer NOT NULL,
    pollingtype bigint NOT NULL
);

CREATE TABLE public.pl_polling_result (
    jdoversion integer,
    lasterrormessage character varying(1000),
    lastresult integer,
    lasttime bigint,
    pollingmanagerid integer NOT NULL,
    pollingtype bigint NOT NULL
);

CREATE TABLE public.pl_polling_type_properties (
    compression double precision,
    durationtime bigint,
    intervalcompression integer,
    intervall bigint,
    jdoversion integer,
    pointoftime bigint,
    pollingmanagerid integer NOT NULL,
    pollingtype bigint NOT NULL,
    status integer
);

CREATE TABLE public.pm_bin_f8 (
    "timestamp" bigint,
    id_ne integer,
    jdoid bigint NOT NULL,
    timetype integer
);

CREATE TABLE public.pm_csv_bookmark (
    id_bookmark bigint NOT NULL,
    jdoversion integer,
    last_record_id bigint,
    last_record_ts bigint,
    time_type integer NOT NULL
);

CREATE TABLE public.pm_manager_cfg (
    connection_id integer,
    do_polling15 boolean,
    do_polling24 boolean,
    entity_class character varying(255),
    entitytype integer,
    id_ne integer,
    index_list character varying(255),
    jdoid integer NOT NULL,
    jdoversion integer,
    manager_type integer,
    map15_id integer,
    map24_id integer,
    netype integer,
    recent15ts bigint,
    recent24ts bigint,
    serie15_0 integer,
    serie24_0 integer,
    side integer DEFAULT 0,
    summarytype integer,
    template_id integer,
    templatelayer integer
)
WITH (autovacuum_vacuum_scale_factor='0.005');

CREATE TABLE public.pm_mapping (
    column1 integer,
    column10 integer,
    column11 integer,
    column12 integer,
    column13 integer,
    column14 integer,
    column15 integer,
    column16 integer,
    column17 integer,
    column18 integer,
    column19 integer,
    column2 integer,
    column20 integer,
    column21 integer,
    column22 integer,
    column23 integer,
    column24 integer,
    column25 integer,
    column26 integer,
    column27 integer,
    column28 integer,
    column29 integer,
    column3 integer,
    column30 integer,
    column31 integer,
    column32 integer,
    column33 integer,
    column34 integer,
    column35 integer,
    column36 integer,
    column37 integer,
    column38 integer,
    column39 integer,
    column4 integer,
    column40 integer,
    column41 integer,
    column42 integer,
    column43 integer,
    column44 integer,
    column45 integer,
    column46 integer,
    column47 integer,
    column48 integer,
    column49 integer,
    column5 integer,
    column50 integer,
    column51 integer,
    column52 integer,
    column53 integer,
    column54 integer,
    column55 integer,
    column56 integer,
    column57 integer,
    column58 integer,
    column59 integer,
    column6 integer,
    column60 integer,
    column61 integer,
    column62 integer,
    column63 integer,
    column64 integer,
    column7 integer,
    column8 integer,
    column9 integer,
    dirty boolean,
    entitytype integer,
    id_man bigint,
    id_map integer NOT NULL,
    jdoversion integer,
    netype integer,
    template_id integer,
    timetype integer
);

CREATE TABLE public.pm_profile (
    id integer NOT NULL,
    owner integer,
    profile_name character varying(255)
);

CREATE TABLE public.pm_profile_counter (
    accumulate boolean,
    aggregation_type integer,
    id bigint NOT NULL,
    layer integer,
    max_threshold character varying(255),
    max_value double precision,
    min_threshold character varying(255),
    min_value double precision,
    pm_group character varying(255),
    pm_id integer,
    pm_name character varying(255),
    profile integer,
    profile_id integer,
    service_layer character varying(255),
    threshold_name character varying(255),
    time_type integer,
    unit character varying(255)
);

CREATE TABLE public.pm_record (
    "timestamp" bigint,
    creation_time bigint DEFAULT 0 NOT NULL,
    data1 bigint DEFAULT 0,
    data10 bigint DEFAULT 0,
    data11 bigint DEFAULT 0,
    data12 bigint DEFAULT 0,
    data13 bigint DEFAULT 0,
    data14 bigint DEFAULT 0,
    data15 bigint DEFAULT 0,
    data16 bigint DEFAULT 0,
    data17 bigint DEFAULT 0,
    data18 bigint DEFAULT 0,
    data19 bigint DEFAULT 0,
    data2 bigint DEFAULT 0,
    data20 bigint DEFAULT 0,
    data21 bigint DEFAULT 0,
    data22 bigint DEFAULT 0,
    data23 bigint DEFAULT 0,
    data24 bigint DEFAULT 0,
    data25 bigint DEFAULT 0,
    data26 bigint DEFAULT 0,
    data27 bigint DEFAULT 0,
    data28 bigint DEFAULT 0,
    data29 bigint DEFAULT 0,
    data3 bigint DEFAULT 0,
    data30 bigint DEFAULT 0,
    data31 bigint DEFAULT 0,
    data32 bigint DEFAULT 0,
    data33 bigint,
    data34 bigint,
    data35 bigint,
    data36 bigint,
    data37 bigint,
    data38 bigint,
    data39 bigint,
    data4 bigint DEFAULT 0,
    data40 bigint,
    data41 bigint,
    data42 bigint,
    data43 bigint,
    data44 bigint,
    data45 bigint,
    data46 bigint,
    data47 bigint,
    data48 bigint,
    data49 bigint,
    data5 bigint DEFAULT 0,
    data50 bigint,
    data51 bigint,
    data52 bigint,
    data53 bigint,
    data54 bigint,
    data55 bigint,
    data56 bigint,
    data57 bigint,
    data58 bigint,
    data59 bigint,
    data6 bigint DEFAULT 0,
    data60 bigint,
    data61 bigint,
    data62 bigint,
    data63 bigint,
    data64 bigint,
    data7 bigint DEFAULT 0,
    data8 bigint DEFAULT 0,
    data9 bigint DEFAULT 0,
    id_entity integer,
    id_mapping integer,
    id_ne integer,
    jdoid bigint NOT NULL,
    jdoversion integer,
    timetype integer,
    valid32 bigint
)
WITH (autovacuum_vacuum_scale_factor='0.005');

CREATE TABLE public.pm_serie (
    id_serie integer NOT NULL,
    jdoversion integer,
    ser15_1 integer,
    ser24_1 integer
);

CREATE TABLE public.pm_template (
    id integer NOT NULL,
    jdoclass character varying(255),
    jdoversion integer
);

CREATE TABLE public.pm_template_data_map (
    id integer,
    key0 integer,
    selected boolean
);

CREATE TABLE public.pm_template_identifier (
    collectionfilter integer,
    id integer NOT NULL,
    isdefault boolean DEFAULT false,
    jdoclass character varying(255),
    jdoversion integer,
    name0 bytea,
    uuid uuid
);

CREATE TABLE public.ptpclockmtieresultdbimpl (
    id integer NOT NULL
);

CREATE TABLE public.re_ha_properites (
    ha_enabled boolean,
    ha_paused boolean,
    id bigint NOT NULL,
    jdoclass character varying(255),
    jdoversion integer,
    primary_fnm_application_path character varying(255),
    primary_ip_address character varying(255),
    primary_port integer,
    primary_trapsink_ip_address character varying(255),
    primary_trapsink_ip_address6 character varying(255),
    remote_account_login character varying(255),
    remote_account_password bytea,
    secondary_fnm_application_path character varying(255),
    secondary_ip_address character varying(255),
    secondary_port integer,
    secondary_trapsink_ip_address character varying(255),
    secondary_trapsink_ip_address6 character varying(255)
);

CREATE TABLE public.reflector_fam_record (
    access_point_id integer,
    c_time bigint,
    cleaved boolean,
    cleicode character varying(50),
    comment character varying(255),
    contents bytea,
    creation_user character varying(255),
    dpa_mode integer,
    email_sent boolean,
    famportconfigurationtype integer,
    faultposition double precision,
    filename character varying(255),
    firmwarerevision character varying(50),
    fp_feeder_loss double precision,
    fp_time bigint,
    fpgarevision character varying(50),
    hardwarerevision character varying(50),
    id integer NOT NULL,
    inspect_loss1 double precision,
    inspect_loss1_distance double precision,
    inventorytype character varying(50),
    jdoversion integer,
    latitude character varying(255),
    longitude character varying(255),
    loss_budget_conn double precision,
    loss_budget_splice double precision,
    loss_guidance_type character varying(255),
    loss_per_km double precision,
    mode character varying(255),
    ne_id integer,
    num_connectors integer,
    num_splices integer,
    partnumber character varying(50),
    port_id integer,
    releasenumber character varying(50),
    retrieval_status character varying(255),
    serialnumber character varying(50),
    shelfname character varying(50),
    shelfunitname character varying(50),
    softwarerevision character varying(50),
    splitter1 character varying(255),
    splitter2 character varying(255),
    target_insertion_loss integer,
    trace_display_name character varying(255),
    trace_name character varying(255),
    trace_type character varying(255),
    type integer,
    upper_threshold double precision,
    usi character varying(50),
    vendorid character varying(50),
    wavelength character varying(50)
);

CREATE TABLE public.rp_report (
    description character varying(255),
    id integer NOT NULL,
    jasper_filename character varying(255),
    jdoversion integer,
    name character varying(255),
    ne_id integer,
    node_type integer,
    pending boolean,
    producer_id integer DEFAULT 0,
    report_time bigint,
    restricted boolean DEFAULT true,
    settings character varying(255),
    size bigint,
    type integer
);

CREATE TABLE public.rp_report_custom_fields (
    fault_columns bytea,
    fault_columns_selected_all boolean DEFAULT false,
    id bigint NOT NULL,
    inventory_columns bytea,
    inventory_columns_selected_all boolean DEFAULT false,
    inventory_recurring_action boolean DEFAULT false,
    service_fault_columns bytea,
    service_fault_columns_sel_all boolean DEFAULT false,
    service_inv_columns_sel_all boolean DEFAULT false,
    service_inv_recurring_action boolean DEFAULT false,
    service_inventory_columns bytea
);

CREATE TABLE public.se_action (
    acronym character varying(255),
    classid character varying(255),
    description character varying(10000),
    id integer NOT NULL,
    isreadonly boolean,
    isrolerelevant boolean,
    jdoversion integer,
    message character varying(255),
    name0 character varying(255),
    objectid integer,
    severity integer
);

CREATE TABLE public.se_action_children (
    children_id integer NOT NULL,
    children_order integer,
    id integer NOT NULL
);

CREATE TABLE public.se_group (
    customerviewid integer,
    description character varying(10000),
    id integer NOT NULL,
    jdoversion integer,
    name0 character varying(255),
    networkviewid integer,
    roleid integer
);

CREATE TABLE public.se_group_user (
    id integer NOT NULL,
    users_id integer NOT NULL
);

CREATE TABLE public.se_keystore (
    encrypted_bytes bytea,
    encrypted_pw bytea,
    id integer NOT NULL,
    name character varying(64)
);

CREATE TABLE public.se_mountedtreenodes (
    element integer,
    id integer,
    temp_element integer
);

CREATE TABLE public.se_properties (
    authenticationtype integer,
    fmnbikeystorepass bytea,
    id bigint NOT NULL,
    jdoversion integer,
    oldforbidenpasswordsnr integer,
    passwordminlength integer,
    passwordminlowercasesamount integer,
    passwordminnumbersamount integer,
    passwordminspecialcharsamount integer,
    passwordminuppercasesamount integer,
    primarysharedsecret bytea,
    secondarysharedsecret bytea,
    tertiarysharedsecret bytea,
    timetobeautolock bigint DEFAULT 0,
    timetobeautologout bigint DEFAULT 0,
    timetobedeactivated integer,
    timetochangepassword integer,
    timetochangepasswordforadmin integer,
    usernamecaching boolean DEFAULT true,
    usernameminlength integer
);

CREATE TABLE public.se_restrictedtreenodes (
    element integer,
    id integer,
    temp_element integer
);

CREATE TABLE public.se_role (
    classid character varying(255),
    dependencies character varying(255),
    description character varying(10000),
    id integer NOT NULL,
    isreadonly boolean,
    istwomanrule boolean,
    jdoversion integer,
    name0 character varying(255),
    objectid integer,
    state integer
);

CREATE TABLE public.se_role_children (
    children_id integer NOT NULL,
    children_order integer,
    id integer NOT NULL
);

CREATE TABLE public.se_sab_event (
    count integer NOT NULL,
    creation_time bigint NOT NULL,
    id integer NOT NULL,
    operation_type character varying(255) NOT NULL,
    resource_type character varying(255) NOT NULL,
    user_group character varying(255) NOT NULL,
    user_id integer NOT NULL
);

CREATE TABLE public.se_user (
    accountactivationtime bigint,
    authenticationtype integer,
    change_passwd_on_next_log_req boolean,
    description character varying(10000),
    email1 character varying(255),
    email2 character varying(255),
    email3 character varying(255),
    fullname character varying(1000),
    id integer NOT NULL,
    isaccountdisabled boolean,
    ispasswordneverexpired boolean,
    jdoversion integer,
    last_message_id integer,
    lastlogontime bigint,
    lastpasswordchange bigint,
    lastunsuccessfullogons integer,
    logontime bigint,
    name0 character varying(1000) DEFAULT NULL::character varying,
    oldpasswords bytea,
    password bytea,
    privatekey bytea,
    publickey bytea,
    salt bytea,
    unsuccessfullogons integer,
    uuid character varying(255),
    verificationalgorithm integer
);

CREATE TABLE public.se_view (
    id integer NOT NULL,
    jdoversion integer
);

CREATE TABLE public.se_viewpoints (
    id integer NOT NULL,
    thread_id bigint NOT NULL
);

CREATE TABLE public.se_visibletreenodes (
    element integer,
    id integer,
    temp_element integer
);

CREATE TABLE public.sm_bw_restrict (
    frequency_hi double precision,
    frequency_lo double precision,
    id integer NOT NULL,
    name character varying(255)
);

CREATE TABLE public.sm_contacts (
    address1 character varying(255),
    address2 character varying(255),
    city character varying(255),
    contactname character varying(255),
    country character varying(255),
    emailid character varying(255),
    id integer NOT NULL,
    jdoclass character varying(255),
    jdoversion integer,
    phone1 character varying(255),
    phone2 character varying(255),
    state character varying(255),
    type character varying(255),
    zipcode character varying(255)
);

CREATE TABLE public.sm_customer_contact_link (
    contact_id integer NOT NULL,
    customer_id integer NOT NULL
);

CREATE TABLE public.sm_customer_service_group (
    customer integer,
    descr character varying(255),
    group_name character varying(1010) DEFAULT NULL::character varying,
    group_type integer,
    id integer NOT NULL,
    iscustomergroup boolean,
    jdoclass character varying(255),
    jdoversion integer,
    parent_group integer,
    parentid integer,
    related_intent integer DEFAULT 0,
    topologylocationtable bytea,
    uuid character varying(255)
);

CREATE TABLE public.sm_customers (
    custid character varying(255),
    customer_group integer,
    customername character varying(1000),
    id integer NOT NULL,
    jdoclass character varying(255),
    jdoversion integer,
    parent_id integer,
    topologylocationtable bytea,
    uuid character varying(255)
);

CREATE TABLE public.sm_diversity (
    diversity_type integer,
    id integer NOT NULL,
    trail1_endpoint_a character varying(255),
    trail1_endpoint_z character varying(255),
    trail1_layer integer,
    trail1_ne_a integer,
    trail1_ne_z integer,
    trail2_endpoint_a character varying(255),
    trail2_endpoint_z character varying(255),
    trail2_layer integer,
    trail2_ne_a integer,
    trail2_ne_z integer
);

CREATE TABLE public.sm_operational_status (
    ackd_at bigint,
    ackd_by character varying(255),
    acknowledged boolean DEFAULT false,
    id integer NOT NULL,
    jdoversion integer,
    operational_status integer,
    reason_code integer,
    secondary_code character varying(255),
    timestamp_degraded bigint,
    timestamp_faulted bigint
);

CREATE TABLE public.sm_properties (
    id integer NOT NULL,
    jdoversion integer,
    key character varying(255),
    value character varying(255)
);

CREATE TABLE public.sm_service (
    admin_state integer,
    admin_state_eod character varying(255),
    classic_only boolean DEFAULT false,
    id integer NOT NULL,
    layer_protocol_eod character varying(255),
    layerprotocol integer,
    lifecycle_state integer,
    servicetype integer,
    shadow_copy boolean DEFAULT false
);

CREATE TABLE public.sm_service_param (
    id integer NOT NULL,
    jdoversion integer,
    key integer,
    paramgroup integer,
    ref_id integer,
    value text
);

CREATE TABLE public.sm_service_refs (
    service_id integer NOT NULL,
    serviceref_id integer NOT NULL
);

CREATE TABLE public.sm_service_tp (
    aid character varying(255),
    id integer NOT NULL,
    jdoversion integer,
    ne_id integer,
    service_id integer,
    tptype integer
);

CREATE TABLE public.sm_tags (
    id integer NOT NULL,
    tag_resource character varying(255),
    tag_resource_type integer,
    user_id character varying(255)
);

CREATE TABLE public.snt_sensors (
    id integer NOT NULL,
    jdoclass character varying(255),
    jdoversion integer,
    snt_sensor_group smallint NOT NULL,
    snt_sensor_group_status smallint NOT NULL,
    snt_sensor_path character varying(255)
);

CREATE TABLE public.snt_setup (
    dtype character varying(31),
    id integer NOT NULL,
    jdoversion integer,
    snt_collection_interval smallint NOT NULL,
    snt_collection_status smallint NOT NULL,
    snt_ntp_group_status smallint NOT NULL,
    snt_ptp_group_status smallint NOT NULL,
    snt_systemclock_group_status smallint NOT NULL
);

CREATE TABLE public.span_loss_record (
    endne character varying(255),
    endnecomment character varying(255),
    endnerxlossesisuser boolean,
    endnerxmeasuringpoint character varying(20),
    endnerxmeasuringpointtype character varying(20),
    endnerxpassthroughelements character varying(255),
    endnerxpassthroughestlosses double precision,
    endnerxpassthroughusrlosses double precision,
    endnerxtimestamp bigint,
    endnerxvalue double precision,
    endnetxlossesisuser boolean,
    endnetxmeasuringpoint character varying(20),
    endnetxmeasuringpointtype character varying(20),
    endnetxpassthroughelements character varying(255),
    endnetxpassthroughestlosses double precision,
    endnetxpassthroughusrlosses double precision,
    endnetxtimestamp bigint,
    endnetxvalue double precision,
    endsubnetwork character varying(255),
    id integer NOT NULL,
    isreference boolean,
    jdoversion integer,
    lastupdatetimestamp bigint,
    linkid integer,
    linkname character varying(255),
    spanlossendtostartne character varying(255),
    spanlossstarttoendne character varying(255),
    startne character varying(255),
    startnecomment character varying(255),
    startnerxlossesisuser boolean,
    startnerxmeasuringpoint character varying(20),
    startnerxmeasuringpointtype character varying(20),
    startnerxpassthroughelements character varying(255),
    startnerxpassthroughestlosses double precision,
    startnerxpassthroughusrlosses double precision,
    startnerxtimestamp bigint,
    startnerxvalue double precision,
    startnetxlossesisuser boolean,
    startnetxmeasuringpoint character varying(20),
    startnetxmeasuringpointtype character varying(20),
    startnetxpassthroughelements character varying(255),
    startnetxpassthroughestlosses double precision,
    startnetxpassthroughusrlosses double precision,
    startnetxtimestamp bigint,
    startnetxvalue double precision,
    startsubnetwork character varying(255),
    threshold double precision,
    thresholdenabled boolean
);

CREATE TABLE public.sr_build (
    build_date bigint,
    build_number integer,
    id bigint NOT NULL,
    is_patch boolean,
    jdoclass character varying(255),
    major integer,
    minor integer,
    patch integer,
    patch_description character varying(3000),
    version integer
);

CREATE TABLE public.sr_db_version_info (
    builddate bigint,
    jdoclass character varying(255),
    jdoid bigint NOT NULL,
    jdoversion integer,
    majorversion integer,
    minorversion integer,
    patchversion integer,
    version double precision
);

CREATE TABLE public.sr_email_pops (
    email_address character varying(255),
    id bigint NOT NULL,
    login character varying(255),
    password bytea,
    port integer,
    smtp_server character varying(255),
    test_email_address character varying(255),
    version integer
);

CREATE TABLE public.sr_multi_server (
    comment character varying(1000),
    dtype character varying(31),
    id bigint NOT NULL,
    myself boolean,
    ordering integer,
    servername character varying(255),
    version integer
);

CREATE TABLE public.sso_user (
    fn character varying(255),
    id integer NOT NULL,
    ln character varying(255),
    ne_id integer,
    ne_user_id character varying(255),
    pw character varying(255),
    un character varying(255)
);

CREATE TABLE public.sync_dup_identity_port (
    dup_identity_type integer DEFAULT 1 NOT NULL,
    other_dup_node integer,
    port_identity character varying(255),
    syncnodedbimpl_id integer
);

CREATE TABLE public.sync_dup_masters (
    master_id integer,
    ref_count integer,
    syncnodedbimpl_id integer
);

CREATE TABLE public.sync_ncd (
    id integer NOT NULL,
    jdoclass character varying(255),
    jdoversion integer,
    ncd_name character varying(255) NOT NULL,
    subnet_id integer DEFAULT '-1'::integer NOT NULL
);

CREATE TABLE public.sync_node (
    active_freq_master integer,
    active_master integer,
    active_master_port character varying(255),
    active_timing_source character varying(255),
    active_timing_type smallint,
    alias character varying(255),
    bc integer,
    bc_class integer,
    clock_accuracy_health smallint DEFAULT 0 NOT NULL,
    clock_analysis_health smallint DEFAULT 0 NOT NULL,
    domain_number integer,
    dup_ip_nodes_ref_count integer DEFAULT 0 NOT NULL,
    dup_pi_nodes_ref_count integer DEFAULT 0 NOT NULL,
    function_id integer,
    function_index integer NOT NULL,
    function_type smallint NOT NULL,
    health integer DEFAULT 0 NOT NULL,
    id integer NOT NULL,
    jdoclass character varying(255),
    jdoversion integer,
    master_ip character varying(255),
    mc integer,
    ncd_id integer NOT NULL,
    ne_id integer,
    node_name character varying(1000) DEFAULT NULL::character varying,
    oc_slave integer,
    ocs_id integer,
    port_identity character varying(255),
    ptp_clock integer,
    ptp_net_analysis_health smallint DEFAULT 0 NOT NULL,
    ptp_profile integer DEFAULT 0 NOT NULL,
    selected_reference character varying(255),
    selected_sooc_id integer,
    selected_timeref_id integer,
    status integer DEFAULT 0 NOT NULL,
    subnet_id integer,
    sync_entity integer,
    sync_id character varying(255),
    sync_service_alias character varying(255),
    tc integer,
    timeclock_id integer,
    type smallint NOT NULL
);

CREATE TABLE public.sync_node_masters (
    sync_master integer NOT NULL,
    sync_node integer NOT NULL
);

CREATE TABLE public.sync_perf_data (
    clock_class integer,
    effective_ql integer,
    id integer NOT NULL,
    jdoversion integer,
    ne_id integer,
    ne_index integer,
    sync_ql integer,
    utc_offset integer
);

CREATE TABLE public.sync_properties (
    dtype character varying(31),
    gnss_firewall_enabled boolean DEFAULT false,
    id integer NOT NULL,
    jdoversion integer,
    rs_con_suppress_alarms boolean DEFAULT false
);

CREATE TABLE public.sync_route (
    health smallint DEFAULT 0 NOT NULL,
    id integer NOT NULL,
    jdoversion integer,
    route_name character varying(255) NOT NULL,
    route_type smallint NOT NULL,
    status smallint NOT NULL,
    sync_service_alias character varying(255)
);

CREATE TABLE public.sync_route_member_nodes (
    node_id integer NOT NULL,
    pos integer,
    route_id integer NOT NULL
);

CREATE TABLE public.sync_subnet_migration (
    id integer NOT NULL,
    init_config integer NOT NULL,
    jdoversion integer,
    separator character varying(255) NOT NULL,
    status integer NOT NULL,
    topic character varying(255) NOT NULL
);

CREATE TABLE public.sync_tc_location (
    dtype character varying(255),
    id integer NOT NULL,
    jdoversion integer,
    master integer NOT NULL,
    parent_tc integer,
    slave integer NOT NULL,
    tc_id integer
);

CREATE TABLE public.sync_test (
    category integer NOT NULL,
    csmprobeselection integer,
    customer character varying(255),
    debug_mode boolean DEFAULT false,
    dtype character varying(255),
    health smallint DEFAULT 0 NOT NULL,
    id integer NOT NULL,
    importance integer,
    jdoversion integer,
    mtie_restart integer DEFAULT 0,
    mtie_restart_time character varying(255) DEFAULT 'N/A'::character varying,
    ncd integer NOT NULL,
    ne integer,
    nms_probe_history integer,
    nms_probe_history_id integer,
    online_max_abs_pkt_2way_te integer,
    online_mtie integer,
    online_p2p_pkt_2way_te integer,
    online_pkt_selected_2way_te integer,
    owner integer,
    owner_name character varying(255),
    probe integer,
    probe_history integer,
    probe_history_id integer,
    probe_id integer,
    probing_slave boolean DEFAULT false,
    ptp_transport integer,
    rawdatacollectionenabled integer,
    remark character varying(255),
    state integer DEFAULT 0 NOT NULL,
    test_name character varying(255) NOT NULL,
    test_type integer NOT NULL,
    tested_device_ip character varying(255),
    tested_node_id integer
);

CREATE TABLE public.sync_test_nms_creation_params (
    category integer NOT NULL,
    csmprobeselection integer,
    customer character varying(255),
    debug_mode boolean DEFAULT false,
    dtype character varying(31),
    id integer NOT NULL,
    importance integer,
    jdoversion integer,
    ncd integer NOT NULL,
    ne_id integer NOT NULL,
    owner integer,
    owner_name character varying(255),
    probing_slave boolean DEFAULT false,
    remark character varying(255),
    test_name character varying(255) NOT NULL,
    test_type integer NOT NULL,
    tested_device_ip character varying(255)
);

CREATE TABLE public.ui_template_props (
    prop_key text,
    prop_value text,
    templatedatadbimpl_id integer
);

CREATE TABLE public.ui_templates (
    id integer NOT NULL,
    jdoversion integer,
    pagename character varying(120),
    tempname character varying(64)
);

CREATE TABLE public.ui_usr (
    id integer NOT NULL,
    jdoversion integer,
    name character varying(64)
);

CREATE TABLE public.ui_usr_sec (
    id integer NOT NULL,
    jdoversion integer,
    section character varying(64),
    usrid integer
);

CREATE TABLE public.ui_usr_sec_props (
    prop_key text,
    prop_value text,
    sectiondatadbimpl_id integer
);

ALTER TABLE ONLY public.efd_hist_segment ALTER COLUMN id SET DEFAULT nextval('public.efd_hist_segment_id_seq'::regclass);

ALTER TABLE ONLY public.efd_last_change_time ALTER COLUMN id SET DEFAULT nextval('public.efd_last_change_time_id_seq'::regclass);

ALTER TABLE ONLY public.efd_loss_guidance ALTER COLUMN id SET DEFAULT nextval('public.efd_loss_guidance_id_seq'::regclass);

ALTER TABLE ONLY public.efd_segment ALTER COLUMN id SET DEFAULT nextval('public.efd_segment_id_seq'::regclass);

ALTER TABLE ONLY public.efd_trace_attribute ALTER COLUMN id SET DEFAULT nextval('public.efd_trace_attribute_id_seq'::regclass);

ALTER TABLE ONLY public.efd_trace_template ALTER COLUMN id SET DEFAULT nextval('public.efd_trace_template_id_seq'::regclass);

COPY public.jdo_sequence (id, sequence_value) FROM stdin;
DEFAULT	0
EVENTS	0
HC_GEN	0
PM_GEN	0
\.

SELECT pg_catalog.setval('public.configfilesequence', 1, false);

SELECT pg_catalog.setval('public.efd_hist_segment_id_seq', 1, false);

SELECT pg_catalog.setval('public.efd_last_change_time_id_seq', 1, false);

SELECT pg_catalog.setval('public.efd_loss_guidance_id_seq', 1, false);

SELECT pg_catalog.setval('public.efd_segment_id_seq', 1, false);

SELECT pg_catalog.setval('public.efd_trace_attribute_id_seq', 1, false);

SELECT pg_catalog.setval('public.efd_trace_template_id_seq', 1, false);

SELECT pg_catalog.setval('public.ml_log_link_fiber_srlg_seq', 268435456, false);

ALTER TABLE ONLY mnc_mpd_layer3.pdl3_ipvpn_service
    ADD CONSTRAINT pdl3_ipvpn_service_name_key UNIQUE (service_name);

ALTER TABLE ONLY mnc_mpd_layer3.pdl3_ipvpn_service
    ADD CONSTRAINT pdl3_ipvpn_service_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.activemq_acks
    ADD CONSTRAINT activemq_acks_pkey PRIMARY KEY (container, client_id, sub_name, priority);

ALTER TABLE ONLY public.activemq_lock
    ADD CONSTRAINT activemq_lock_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.activemq_msgs
    ADD CONSTRAINT activemq_msgs_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.bk_bookmark_group
    ADD CONSTRAINT bk_bookmark_group_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.bk_bookmark
    ADD CONSTRAINT bk_bookmark_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.clockmtieresultdbimpl
    ADD CONSTRAINT clockmtieresultdbimpl_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cm_cua
    ADD CONSTRAINT cm_cua_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cm_graph_node_location
    ADD CONSTRAINT cm_graph_node_location_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cm_pca
    ADD CONSTRAINT cm_pca_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_amp_config
    ADD CONSTRAINT cn_amp_config_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_apsgroup
    ADD CONSTRAINT cn_apsgroup_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_bfd_mhop_session_status_f4
    ADD CONSTRAINT cn_bfd_mhop_session_status_f4_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_bfd_multi_hop_session_f4
    ADD CONSTRAINT cn_bfd_multi_hop_session_f4_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_boundary_clock
    ADD CONSTRAINT cn_boundary_clock_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_card_cluster_f8
    ADD CONSTRAINT cn_card_cluster_f8_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_card_translation_ec
    ADD CONSTRAINT cn_card_translation_ec_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_cfm_eth
    ADD CONSTRAINT cn_cfm_eth_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_cfm_template_template
    ADD CONSTRAINT cn_cfm_template_template_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_cfm_template_template
    ADD CONSTRAINT cn_cfm_template_template_unq_profile_name UNIQUE (profilename);

ALTER TABLE ONLY public.cn_cgrxsc_f4
    ADD CONSTRAINT cn_cgrxsc_f4_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_cgtxsc_f4
    ADD CONSTRAINT cn_cgtxsc_f4_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_clock_probe_history
    ADD CONSTRAINT cn_clock_probe_history_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_clock_probe
    ADD CONSTRAINT cn_clock_probe_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_cnx_connection_f7
    ADD CONSTRAINT cn_cnx_connection_f7_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_config_ctrl
    ADD CONSTRAINT cn_config_ctrl_pkey PRIMARY KEY (jdoid);

ALTER TABLE ONLY public.cn_conn_comp_restn_route
    ADD CONSTRAINT cn_conn_comp_restn_route_pkey PRIMARY KEY (conn_id, line_id);

ALTER TABLE ONLY public.cn_conn_crossconnects
    ADD CONSTRAINT cn_conn_crossconnects_pkey PRIMARY KEY (subchconn_id, crossconn_id);

ALTER TABLE ONLY public.cn_conn_info
    ADD CONSTRAINT cn_conn_info_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_conn_nominal_prot_route
    ADD CONSTRAINT cn_conn_nominal_prot_route_pkey PRIMARY KEY (conn_id, line_id);

ALTER TABLE ONLY public.cn_conn_nominal_route
    ADD CONSTRAINT cn_conn_nominal_route_pkey PRIMARY KEY (conn_id, line_id);

ALTER TABLE ONLY public.cn_conn_ochconns
    ADD CONSTRAINT cn_conn_ochconns_pkey PRIMARY KEY (subchconn_id, optchconn_id);

ALTER TABLE ONLY public.cn_conn_prot_route
    ADD CONSTRAINT cn_conn_prot_route_pkey PRIMARY KEY (conn_id, line_id);

ALTER TABLE ONLY public.cn_conn_prot_trailconn
    ADD CONSTRAINT cn_conn_prot_trailconn_pkey PRIMARY KEY (conn_id, connection_id);

ALTER TABLE ONLY public.cn_conn_restn_route
    ADD CONSTRAINT cn_conn_restn_route_pkey PRIMARY KEY (conn_id, line_id);

ALTER TABLE ONLY public.cn_conn_route
    ADD CONSTRAINT cn_conn_route_pkey PRIMARY KEY (conn_id, line_id);

ALTER TABLE ONLY public.cn_conn_state_counter
    ADD CONSTRAINT cn_conn_state_counter_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_conn_to_line_end_points
    ADD CONSTRAINT cn_conn_to_line_end_points_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_conn_trailconn
    ADD CONSTRAINT cn_conn_trailconn_pkey PRIMARY KEY (conn_id, connection_id);

ALTER TABLE ONLY public.cn_connect_guard_config
    ADD CONSTRAINT cn_connect_guard_config_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_connection
    ADD CONSTRAINT cn_connection_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_croma_conn_map
    ADD CONSTRAINT cn_croma_conn_map_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_croma_entity_ec
    ADD CONSTRAINT cn_croma_entity_ec_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_croma_service_path
    ADD CONSTRAINT cn_croma_service_path_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_croma_slc
    ADD CONSTRAINT cn_croma_slc_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_cross_connect_ec
    ADD CONSTRAINT cn_cross_connect_ec_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_cross_connect
    ADD CONSTRAINT cn_cross_connect_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_cross_connects_ref
    ADD CONSTRAINT cn_cross_connects_ref_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_ec_ctp
    ADD CONSTRAINT cn_ec_ctp_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_ec_fltp
    ADD CONSTRAINT cn_ec_fltp_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_ec_optl
    ADD CONSTRAINT cn_ec_optl_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_ec_otsi_cpmgt
    ADD CONSTRAINT cn_ec_otsi_cpmgt_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_ec_ptp_otsi
    ADD CONSTRAINT cn_ec_ptp_otsi_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_ec_ptp
    ADD CONSTRAINT cn_ec_ptp_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_ech_f7
    ADD CONSTRAINT cn_ech_f7_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_egm_module
    ADD CONSTRAINT cn_egm_module_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_elp_group
    ADD CONSTRAINT cn_elp_group_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_elp_protflow
    ADD CONSTRAINT cn_elp_protflow_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_elp_unit
    ADD CONSTRAINT cn_elp_unit_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_encryption_status
    ADD CONSTRAINT cn_encryption_status_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_entity_cp
    ADD CONSTRAINT cn_entity_cp_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_entity
    ADD CONSTRAINT cn_entity_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_entity_power_level
    ADD CONSTRAINT cn_entity_power_level_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_eo_mpls_pw
    ADD CONSTRAINT cn_eo_mpls_pw_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_erp_f3
    ADD CONSTRAINT cn_erp_f3_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_erp_protflow_f3
    ADD CONSTRAINT cn_erp_protflow_f3_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_erp_unit_f3
    ADD CONSTRAINT cn_erp_unit_f3_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_esa_probe_f3
    ADD CONSTRAINT cn_esa_probe_f3_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_esa_probe_mep_cos
    ADD CONSTRAINT cn_esa_probe_mep_cos_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_esa_probe
    ADD CONSTRAINT cn_esa_probe_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_esa_probe_reflector
    ADD CONSTRAINT cn_esa_probe_reflector_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_esa_probe_schedule_group
    ADD CONSTRAINT cn_esa_probe_schedule_group_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_eth_bw_info
    ADD CONSTRAINT cn_eth_bw_info_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_eth_flow_policer_f3
    ADD CONSTRAINT cn_eth_flow_policer_f3_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_eth_ring_associated_object
    ADD CONSTRAINT cn_eth_ring_associated_object_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_eth_ring_group
    ADD CONSTRAINT cn_eth_ring_group_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_eth_ring_path
    ADD CONSTRAINT cn_eth_ring_path_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_eth_service
    ADD CONSTRAINT cn_eth_service_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_evc_hn
    ADD CONSTRAINT cn_evc_hn_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_evpn_f4
    ADD CONSTRAINT cn_evpn_f4_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_f3prc
    ADD CONSTRAINT cn_f3prc_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_f3sync
    ADD CONSTRAINT cn_f3sync_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_f3sync_ref
    ADD CONSTRAINT cn_f3sync_ref_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_f3systemclock
    ADD CONSTRAINT cn_f3systemclock_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_f3timeclock
    ADD CONSTRAINT cn_f3timeclock_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_f3timeclock_ref
    ADD CONSTRAINT cn_f3timeclock_ref_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_f8_gtp
    ADD CONSTRAINT cn_f8_gtp_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_f8_spsl
    ADD CONSTRAINT cn_f8_spsl_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_fallback_ne_pwd
    ADD CONSTRAINT cn_fallback_ne_pwd_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_fdfr_end
    ADD CONSTRAINT cn_fdfr_end_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_fdfr
    ADD CONSTRAINT cn_fdfr_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_fdfr_to_crs
    ADD CONSTRAINT cn_fdfr_to_crs_pkey PRIMARY KEY (fdfr_jdoid, crs_jdoid);

ALTER TABLE ONLY public.cn_fiber_connection_ec
    ADD CONSTRAINT cn_fiber_connection_ec_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_flow_cm
    ADD CONSTRAINT cn_flow_cm_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_flow_f3
    ADD CONSTRAINT cn_flow_f3_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_flow_hn
    ADD CONSTRAINT cn_flow_hn_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_flowpoint_cpd
    ADD CONSTRAINT cn_flowpoint_cpd_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_flowpoint_cpd_profile
    ADD CONSTRAINT cn_flowpoint_cpd_profile_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_flowpoint_f3
    ADD CONSTRAINT cn_flowpoint_f3_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_flowpoint_f4
    ADD CONSTRAINT cn_flowpoint_f4_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_ftp_cp
    ADD CONSTRAINT cn_ftp_cp_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_ftp
    ADD CONSTRAINT cn_ftp_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_generic_mo_f3
    ADD CONSTRAINT cn_generic_mo_f3_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_gps_receiver
    ADD CONSTRAINT cn_gps_receiver_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_gre_ip_interface
    ADD CONSTRAINT cn_gre_ip_interface_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_gre_tunnel
    ADD CONSTRAINT cn_gre_tunnel_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_hdsl2shdsl
    ADD CONSTRAINT cn_hdsl2shdsl_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_intra_connection_ref
    ADD CONSTRAINT cn_intra_connection_ref_pkey PRIMARY KEY (id, roadm_id);

ALTER TABLE ONLY public.cn_ip_interface_f4
    ADD CONSTRAINT cn_ip_interface_f4_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_key_exchange_profile_f4
    ADD CONSTRAINT cn_key_exchange_profile_f4_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_key_exchange_profile
    ADD CONSTRAINT cn_key_exchange_profile_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_key_exchange_template
    ADD CONSTRAINT cn_key_exchange_template_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_l2_remote_slave
    ADD CONSTRAINT cn_l2_remote_slave_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_l3_flow_point
    ADD CONSTRAINT cn_l3_flow_point_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_l3_flowpoint_f4
    ADD CONSTRAINT cn_l3_flowpoint_f4_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_l3_flowpoint_ip_f4
    ADD CONSTRAINT cn_l3_flowpoint_ip_f4_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_l3_port_clock
    ADD CONSTRAINT cn_l3_port_clock_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_l3_qos_policer_f4
    ADD CONSTRAINT cn_l3_qos_policer_f4_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_l3_qos_policer
    ADD CONSTRAINT cn_l3_qos_policer_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_l3_qos_shaper
    ADD CONSTRAINT cn_l3_qos_shaper_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_l3_queue_f4
    ADD CONSTRAINT cn_l3_queue_f4_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_l3_traffic_ip_interface
    ADD CONSTRAINT cn_l3_traffic_ip_interface_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_lag_ec
    ADD CONSTRAINT cn_lag_ec_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_lag_f3
    ADD CONSTRAINT cn_lag_f3_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_lag_member_ec
    ADD CONSTRAINT cn_lag_member_ec_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_lagport_f3
    ADD CONSTRAINT cn_lagport_f3_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_lagservicemap_f3
    ADD CONSTRAINT cn_lagservicemap_f3_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_lambda_info
    ADD CONSTRAINT cn_lambda_info_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_lif_cp
    ADD CONSTRAINT cn_lif_cp_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_line_bw_restrict
    ADD CONSTRAINT cn_line_bw_restrict_pkey PRIMARY KEY (line_id, bw_restrict_id);

ALTER TABLE ONLY public.cn_line_conns
    ADD CONSTRAINT cn_line_conns_pkey PRIMARY KEY (line_id, conn_id);

ALTER TABLE ONLY public.cn_line
    ADD CONSTRAINT cn_line_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_line_stby_conns
    ADD CONSTRAINT cn_line_stby_conns_pkey PRIMARY KEY (line_id, conn_id);

ALTER TABLE ONLY public.cn_lldp_local_port
    ADD CONSTRAINT cn_lldp_local_port_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_lldp_local_port
    ADD CONSTRAINT cn_lldp_local_port_unique UNIQUE (ifref, lldpsystemdatadb_id);

ALTER TABLE ONLY public.cn_lldp_port_config_ext
    ADD CONSTRAINT cn_lldp_port_config_ext_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_lldp_rem_table_entry
    ADD CONSTRAINT cn_lldp_rem_table_entry_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_lldp_rem_table_entry
    ADD CONSTRAINT cn_lldp_rem_table_entry_unique_1 UNIQUE (timemark, localifref, localdestmacaddress, remindex, lldpsystemdatadb_id);

ALTER TABLE ONLY public.cn_lldp_rem_table_entry
    ADD CONSTRAINT cn_lldp_rem_table_entry_unique_2 UNIQUE (localport_id, chassisid, portid, lldpsystemdatadb_id);

ALTER TABLE ONLY public.cn_lldp_rem_table_entry
    ADD CONSTRAINT cn_lldp_rem_table_entry_unique_3 UNIQUE (localport_id, lldpsystemdatadb_id, localdestmacaddress);

ALTER TABLE ONLY public.cn_lldp_system_data
    ADD CONSTRAINT cn_lldp_system_data_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_lldp_system_data
    ADD CONSTRAINT cn_lldp_system_data_unique UNIQUE (ne_id);

ALTER TABLE ONLY public.cn_lldp_system_data
    ADD CONSTRAINT cn_lldp_system_data_unique_2 UNIQUE (chassisid, chassisidsubtype);

ALTER TABLE ONLY public.cn_logicalobject_cc
    ADD CONSTRAINT cn_logicalobject_cc_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_logicalport_f3
    ADD CONSTRAINT cn_logicalport_f3_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_ma_comp
    ADD CONSTRAINT cn_ma_comp_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_ma_net
    ADD CONSTRAINT cn_ma_net_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_ma
    ADD CONSTRAINT cn_ma_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_managed_object
    ADD CONSTRAINT cn_managed_object_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_management_tunnel
    ADD CONSTRAINT cn_management_tunnel_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_master_clock_interface
    ADD CONSTRAINT cn_master_clock_interface_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_master_clock
    ADD CONSTRAINT cn_master_clock_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_master_clock_vlan_ip
    ADD CONSTRAINT cn_master_clock_vlan_ip_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_master_profile_file
    ADD CONSTRAINT cn_master_profile_file_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_master_virtual_port
    ADD CONSTRAINT cn_master_virtual_port_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_md
    ADD CONSTRAINT cn_md_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_membership_range_profile
    ADD CONSTRAINT cn_membership_range_profile_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_mep
    ADD CONSTRAINT cn_mep_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_mo_attr
    ADD CONSTRAINT cn_mo_attr_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_module_cm_ext
    ADD CONSTRAINT cn_module_cm_ext_pkey PRIMARY KEY (jdoid);

ALTER TABLE ONLY public.cn_module_ec
    ADD CONSTRAINT cn_module_ec_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_module_f7
    ADD CONSTRAINT cn_module_f7_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_module_os_ext
    ADD CONSTRAINT cn_module_os_ext_pkey PRIMARY KEY (jdoid);

ALTER TABLE ONLY public.cn_module
    ADD CONSTRAINT cn_module_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_msppg_f3
    ADD CONSTRAINT cn_msppg_f3_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_msppgport_f3
    ADD CONSTRAINT cn_msppgport_f3_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_ne_backup_file
    ADD CONSTRAINT cn_ne_backup_file_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_ne_backup
    ADD CONSTRAINT cn_ne_backup_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_ne_backup_properties
    ADD CONSTRAINT cn_ne_backup_properties_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_ne_branches
    ADD CONSTRAINT cn_ne_branches_pkey PRIMARY KEY (ne_id, line_id);

ALTER TABLE ONLY public.cn_ne_capabilities
    ADD CONSTRAINT cn_ne_capabilities_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_ne_config_file
    ADD CONSTRAINT cn_ne_config_file_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_ne_events
    ADD CONSTRAINT cn_ne_events_pkey PRIMARY KEY (ne_id);

ALTER TABLE ONLY public.cn_ne_management_status
    ADD CONSTRAINT cn_ne_management_status_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_nemi_sw_file
    ADD CONSTRAINT cn_nemi_sw_file_pkey PRIMARY KEY (jdoid);

ALTER TABLE ONLY public.cn_network_element
    ADD CONSTRAINT cn_network_element_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_network_element_swupgrade
    ADD CONSTRAINT cn_network_element_swupgrade_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_ni_controller_commons
    ADD CONSTRAINT cn_ni_controller_commons_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_ni_controller
    ADD CONSTRAINT cn_ni_controller_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_ni_tunnel
    ADD CONSTRAINT cn_ni_tunnel_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_notification_ec
    ADD CONSTRAINT cn_notification_ec_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_ntp_clock_collection_info
    ADD CONSTRAINT cn_ntp_clock_collection_info_pkey PRIMARY KEY (ne_id, ntp_clock_id);

ALTER TABLE ONLY public.cn_ntp_clock_interface
    ADD CONSTRAINT cn_ntp_clock_interface_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_ntp_clock
    ADD CONSTRAINT cn_ntp_clock_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_ntp_dynamic_client
    ADD CONSTRAINT cn_ntp_dynamic_client_pkey PRIMARY KEY (client_first_request_time, ntp_clock_interface_ip, client_ip);

ALTER TABLE ONLY public.cn_ntp_flowpoint
    ADD CONSTRAINT cn_ntp_flowpoint_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_ntp_remote_client
    ADD CONSTRAINT cn_ntp_remote_client_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_ntp_remote_server
    ADD CONSTRAINT cn_ntp_remote_server_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_ntp_server_activity
    ADD CONSTRAINT cn_ntp_server_activity_pkey PRIMARY KEY (ne_id, ntp_clock, time_stamp);

ALTER TABLE ONLY public.cn_ntp_tracked_client
    ADD CONSTRAINT cn_ntp_tracked_client_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_ntpfiletransfer_properties
    ADD CONSTRAINT cn_ntpfiletransfer_properties_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_olm_session_monitor
    ADD CONSTRAINT cn_olm_session_monitor_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_opt_router_plug
    ADD CONSTRAINT cn_opt_router_plug_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_opt_router_port
    ADD CONSTRAINT cn_opt_router_port_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_path_elements
    ADD CONSTRAINT cn_path_elements_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_path
    ADD CONSTRAINT cn_path_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_pg_f3
    ADD CONSTRAINT cn_pg_f3_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_pgport_f3
    ADD CONSTRAINT cn_pgport_f3_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_phyots_oms_ctp_f8
    ADD CONSTRAINT cn_phyots_oms_ctp_f8_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_planner_pm
    ADD CONSTRAINT cn_planner_pm_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_policer_envelope_f3
    ADD CONSTRAINT cn_policer_envelope_f3_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_policer_profile
    ADD CONSTRAINT cn_policer_profile_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_port_150cp
    ADD CONSTRAINT cn_port_150cp_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_port_bits_group_member
    ADD CONSTRAINT cn_port_bits_group_member_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_port_clock
    ADD CONSTRAINT cn_port_clock_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_port_cm
    ADD CONSTRAINT cn_port_cm_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_port_cpd_profile
    ADD CONSTRAINT cn_port_cpd_profile_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_port_edfa_ext
    ADD CONSTRAINT cn_port_edfa_ext_pkey PRIMARY KEY (jdoid);

ALTER TABLE ONLY public.cn_port_endpoint
    ADD CONSTRAINT cn_port_endpoint_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_port_ext_cli
    ADD CONSTRAINT cn_port_ext_cli_pkey PRIMARY KEY (jdoid);

ALTER TABLE ONLY public.cn_port_ext_net
    ADD CONSTRAINT cn_port_ext_net_pkey PRIMARY KEY (jdoid);

ALTER TABLE ONLY public.cn_port_ext
    ADD CONSTRAINT cn_port_ext_pkey PRIMARY KEY (jdoid);

ALTER TABLE ONLY public.cn_port_f7
    ADD CONSTRAINT cn_port_f7_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_port_hn
    ADD CONSTRAINT cn_port_hn_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_port_juniper
    ADD CONSTRAINT cn_port_juniper_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_port
    ADD CONSTRAINT cn_port_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_port_tt_ext
    ADD CONSTRAINT cn_port_tt_ext_pkey PRIMARY KEY (jdoid);

ALTER TABLE ONLY public.cn_port_vlan_lpbk
    ADD CONSTRAINT cn_port_vlan_lpbk_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_power_level_status
    ADD CONSTRAINT cn_power_level_status_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_prio_map
    ADD CONSTRAINT cn_prio_map_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_profile_ne
    ADD CONSTRAINT cn_profile_ne_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_profile
    ADD CONSTRAINT cn_profile_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_profile_prio_map
    ADD CONSTRAINT cn_profile_prio_map_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_profile_transfer_properties
    ADD CONSTRAINT cn_profile_transfer_properties_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_prot_group_f8
    ADD CONSTRAINT cn_prot_group_f8_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_prot_unit_f8
    ADD CONSTRAINT cn_prot_unit_f8_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_ptp_clock
    ADD CONSTRAINT cn_ptp_clock_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_ptp_clock_probe_history
    ADD CONSTRAINT cn_ptp_clock_probe_history_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_ptp_clock_probe
    ADD CONSTRAINT cn_ptp_clock_probe_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_ptp_flowpoint
    ADD CONSTRAINT cn_ptp_flowpoint_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_ptp_mci_protection_group
    ADD CONSTRAINT cn_ptp_mci_protection_group_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_ptp_network_probe
    ADD CONSTRAINT cn_ptp_network_probe_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_ptp_system_slaves
    ADD CONSTRAINT cn_ptp_system_slaves_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_pwr_consumption_historical
    ADD CONSTRAINT cn_pwr_consumption_historical_pkey PRIMARY KEY (ne_id, power_consumption_timestamp);

ALTER TABLE ONLY public.cn_pwr_consumption
    ADD CONSTRAINT cn_pwr_consumption_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_queue_bwprof_f4
    ADD CONSTRAINT cn_queue_bwprof_f4_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_queue_profile
    ADD CONSTRAINT cn_queue_profile_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_redundancy_group
    ADD CONSTRAINT cn_redundancy_group_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_remote_auth_server
    ADD CONSTRAINT cn_remote_auth_server_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_remote_mep
    ADD CONSTRAINT cn_remote_mep_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_remote_slave_capacity
    ADD CONSTRAINT cn_remote_slave_capacity_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_remote_slave_connectivity
    ADD CONSTRAINT cn_remote_slave_connectivity_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_remote_slave
    ADD CONSTRAINT cn_remote_slave_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_roadm_configuration_data
    ADD CONSTRAINT cn_roadm_configuration_data_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_route_proto_f4
    ADD CONSTRAINT cn_route_proto_f4_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_sat_stream_f3
    ADD CONSTRAINT cn_sat_stream_f3_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_secure_entity_f4
    ADD CONSTRAINT cn_secure_entity_f4_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_secure_flow
    ADD CONSTRAINT cn_secure_flow_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_secure_flow_rx_sc
    ADD CONSTRAINT cn_secure_flow_rx_sc_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_secure_flow_template
    ADD CONSTRAINT cn_secure_flow_template_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_secure_flow_tx_sc
    ADD CONSTRAINT cn_secure_flow_tx_sc_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_secy_f4
    ADD CONSTRAINT cn_secy_f4_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_server_module
    ADD CONSTRAINT cn_server_module_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_service_mrv
    ADD CONSTRAINT cn_service_mrv_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_shaper_cm
    ADD CONSTRAINT cn_shaper_cm_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_shelf
    ADD CONSTRAINT cn_shelf_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_shg_f3
    ADD CONSTRAINT cn_shg_f3_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_shg_members_f3
    ADD CONSTRAINT cn_shg_members_f3_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_sj_probe_history
    ADD CONSTRAINT cn_sj_probe_history_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_sj_probe_mtie_result
    ADD CONSTRAINT cn_sj_probe_mtie_result_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_sj_probe
    ADD CONSTRAINT cn_sj_probe_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_sj_scheduler
    ADD CONSTRAINT cn_sj_scheduler_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_slave_vp
    ADD CONSTRAINT cn_slave_vp_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_slot
    ADD CONSTRAINT cn_slot_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_soam_xg3xx
    ADD CONSTRAINT cn_soam_xg3xx_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_sooc
    ADD CONSTRAINT cn_sooc_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_spectrum_inventory
    ADD CONSTRAINT cn_spectrum_inventory_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_static_route
    ADD CONSTRAINT cn_static_route_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_stl_receiver
    ADD CONSTRAINT cn_stl_receiver_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_subnet
    ADD CONSTRAINT cn_subnet_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_subnetwork_ec
    ADD CONSTRAINT cn_subnetwork_ec_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_supported_netype_ver
    ADD CONSTRAINT cn_supported_netype_ver_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_switch_traffic_conn_data
    ADD CONSTRAINT cn_switch_traffic_conn_data_pkey PRIMARY KEY (conn_id, line_id);

ALTER TABLE ONLY public.cn_swupgrade_properties
    ADD CONSTRAINT cn_swupgrade_properties_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_sync_event_data
    ADD CONSTRAINT cn_sync_event_data_pkey PRIMARY KEY (ne_id);

ALTER TABLE ONLY public.cn_synce_protection_group
    ADD CONSTRAINT cn_synce_protection_group_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_system_feature
    ADD CONSTRAINT cn_system_feature_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_system_status_tp5000
    ADD CONSTRAINT cn_system_status_tp5000_pkey PRIMARY KEY (ne_id);

ALTER TABLE ONLY public.cn_tc
    ADD CONSTRAINT cn_tc_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_tc_vp
    ADD CONSTRAINT cn_tc_vp_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_telecom_slave
    ADD CONSTRAINT cn_telecom_slave_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_timeclock_protection_group
    ADD CONSTRAINT cn_timeclock_protection_group_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_topologies
    ADD CONSTRAINT cn_topologies_name_unique UNIQUE (name);

ALTER TABLE ONLY public.cn_topologies
    ADD CONSTRAINT cn_topologies_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_traffic_acl_profile
    ADD CONSTRAINT cn_traffic_acl_profile_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_traffic_port_cpd
    ADD CONSTRAINT cn_traffic_port_cpd_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_tunnel
    ADD CONSTRAINT cn_tunnel_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_twamp_session_sender
    ADD CONSTRAINT cn_twamp_session_sender_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_uni4xx_hn
    ADD CONSTRAINT cn_uni4xx_hn_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_uni_hn
    ADD CONSTRAINT cn_uni_hn_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_unmanaged_cross_connect
    ADD CONSTRAINT cn_unmanaged_cross_connect_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_unmanaged_object
    ADD CONSTRAINT cn_unmanaged_object_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_network_element
    ADD CONSTRAINT cn_unq_name UNIQUE (name0);

ALTER TABLE ONLY public.cn_unsupported_network_element
    ADD CONSTRAINT cn_unsupported_network_element_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_user_mtie_mask_history
    ADD CONSTRAINT cn_user_mtie_mask_history_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_user_mtie_mask
    ADD CONSTRAINT cn_user_mtie_mask_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_user_notification
    ADD CONSTRAINT cn_user_notification_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_vc
    ADD CONSTRAINT cn_vc_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_virt_otn_ports
    ADD CONSTRAINT cn_virt_otn_ports_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_vrf_f4
    ADD CONSTRAINT cn_vrf_f4_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_vtep_ip_interface
    ADD CONSTRAINT cn_vtep_ip_interface_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_vtep
    ADD CONSTRAINT cn_vtep_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_vx_lan_segment
    ADD CONSTRAINT cn_vx_lan_segment_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.pm_bin_f8
    ADD CONSTRAINT constraint_ts_ne_tt UNIQUE ("timestamp", id_ne, timetype);

ALTER TABLE ONLY public.pm_record
    ADD CONSTRAINT constraint_ts_tt_ne_ent_int UNIQUE ("timestamp", id_entity, id_ne, timetype);

ALTER TABLE ONLY public.db_reports
    ADD CONSTRAINT db_reports_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.ec_bulk_transfer
    ADD CONSTRAINT ec_bulk_transfer_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.ec_configuration_file
    ADD CONSTRAINT ec_configuration_file_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.ec_service_templates
    ADD CONSTRAINT ec_service_templates_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.ec_template_files
    ADD CONSTRAINT ec_template_files_pkey PRIMARY KEY (file_id);

ALTER TABLE ONLY public.efd_hist_segment
    ADD CONSTRAINT efd_hist_segment_ip_port_nr_timestamp UNIQUE (alm_ip_address, port, segment_number, hist_segment_timestamp);

ALTER TABLE ONLY public.efd_hist_segment
    ADD CONSTRAINT efd_hist_segment_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.efd_last_change_time
    ADD CONSTRAINT efd_last_change_time_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.efd_last_change_time
    ADD CONSTRAINT efd_last_time_change_entity_key UNIQUE (entity_name);

ALTER TABLE ONLY public.efd_loss_guidance
    ADD CONSTRAINT efd_loss_guidance_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.efd_segment
    ADD CONSTRAINT efd_segment_ip_port_segment_key UNIQUE (alm_ip_address, port, segment_number);

ALTER TABLE ONLY public.efd_segment
    ADD CONSTRAINT efd_segment_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.efd_trace_attribute
    ADD CONSTRAINT efd_trace_attribute_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.efd_trace_template
    ADD CONSTRAINT efd_trace_template_name_key UNIQUE (name);

ALTER TABLE ONLY public.efd_trace_template
    ADD CONSTRAINT efd_trace_template_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.egm_bandwidth_profile
    ADD CONSTRAINT egm_bandwidth_profile_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.egm_classification_rule
    ADD CONSTRAINT egm_classification_rule_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.egm_cos_bw_profile
    ADD CONSTRAINT egm_cos_bw_profile_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.egm_service_port
    ADD CONSTRAINT egm_service_port_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.els_sso_account
    ADD CONSTRAINT els_sso_account_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.ev_counter
    ADD CONSTRAINT ev_counter_pkey PRIMARY KEY (objid, objtype);

ALTER TABLE ONLY public.ev_event_history
    ADD CONSTRAINT ev_event_history_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.ev_event_multivar
    ADD CONSTRAINT ev_event_multivar_pkey PRIMARY KEY (jdoid);

ALTER TABLE ONLY public.ev_event
    ADD CONSTRAINT ev_event_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.ev_event_sounds
    ADD CONSTRAINT ev_event_sounds_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.ev_filter
    ADD CONSTRAINT ev_filter_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.ev_latency_monitor_cos
    ADD CONSTRAINT ev_latency_monitor_cos_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.ev_latency_monitor
    ADD CONSTRAINT ev_latency_monitor_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.ev_nm_settings
    ADD CONSTRAINT ev_nm_settings_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.ev_property
    ADD CONSTRAINT ev_property_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.ev_subscription
    ADD CONSTRAINT ev_subscription_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.ev_transport
    ADD CONSTRAINT ev_transport_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.ev_trap_severity
    ADD CONSTRAINT ev_trap_severity_pkey PRIMARY KEY (jdoid);

ALTER TABLE ONLY public.fam_point
    ADD CONSTRAINT fam_point_ne_port_unique UNIQUE (neid, portid);

ALTER TABLE ONLY public.fam_point
    ADD CONSTRAINT fam_point_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.fam_record
    ADD CONSTRAINT fam_record_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.fam_trace
    ADD CONSTRAINT fam_trace_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.fl_alm
    ADD CONSTRAINT fl_alm_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.fl_port
    ADD CONSTRAINT fl_port_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.geo_server_settings
    ADD CONSTRAINT geo_server_settings_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.gnss_firewall_rules
    ADD CONSTRAINT gnss_firewall_rules_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.gnss_ne_block_list
    ADD CONSTRAINT gnss_ne_block_list_pkey PRIMARY KEY (ne_id);

ALTER TABLE ONLY public.gnss_rca_settings
    ADD CONSTRAINT gnss_rca_settings_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.gnss_settings
    ADD CONSTRAINT gnss_settings_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.hm_alarm_samples_history
    ADD CONSTRAINT hm_alarm_samples_history_pkey PRIMARY KEY (time_stamp);

ALTER TABLE ONLY public.hm_health_monitoring_sample
    ADD CONSTRAINT hm_health_monitoring_sample_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.hm_ne_samples_history
    ADD CONSTRAINT hm_ne_samples_history_pkey PRIMARY KEY (time_stamp);

ALTER TABLE ONLY public.hm_pwr_consump_samples_history
    ADD CONSTRAINT hm_pwr_consump_samples_history_pkey PRIMARY KEY (time_stamp);

ALTER TABLE ONLY public.hm_service_samples_history
    ADD CONSTRAINT hm_service_samples_history_pkey PRIMARY KEY (time_stamp);

ALTER TABLE ONLY public.images
    ADD CONSTRAINT images_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.jdo_sequence
    ADD CONSTRAINT jdo_sequence_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.ma_tone
    ADD CONSTRAINT ma_tone_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.md_global_field_contents
    ADD CONSTRAINT md_global_field_contents_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.md_userdefined_text_fields
    ADD CONSTRAINT md_userdefined_text_fields_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.ml_adaptation_client
    ADD CONSTRAINT ml_adaptation_client_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.ml_adaptation
    ADD CONSTRAINT ml_adaptation_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.ml_adaptation_server
    ADD CONSTRAINT ml_adaptation_server_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.ml_bw_profile
    ADD CONSTRAINT ml_bw_profile_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.ml_bw_profile
    ADD CONSTRAINT ml_bw_profile_unq UNIQUE (name, parent);

ALTER TABLE ONLY public.ml_bw_resources
    ADD CONSTRAINT ml_bw_resources_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.ml_connection
    ADD CONSTRAINT ml_connection_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.ml_connection_point
    ADD CONSTRAINT ml_connection_point_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.ml_connection_reservation
    ADD CONSTRAINT ml_connection_reservation_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.ml_connection_resource
    ADD CONSTRAINT ml_connection_resource_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.ml_cp_path_seq
    ADD CONSTRAINT ml_cp_path_seq_pkey PRIMARY KEY (path_id, cp_id);

ALTER TABLE ONLY public.ml_end_point
    ADD CONSTRAINT ml_end_point_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.ml_layer_extension
    ADD CONSTRAINT ml_layer_extension_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.ml_log_link_dev_endpoint
    ADD CONSTRAINT ml_log_link_dev_endpoint_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.ml_log_link_fiber_srlg
    ADD CONSTRAINT ml_log_link_fiber_srlg_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.ml_log_link
    ADD CONSTRAINT ml_log_link_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.ml_log_link_sup_link_resource
    ADD CONSTRAINT ml_log_link_sup_link_resource_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.ml_module
    ADD CONSTRAINT ml_module_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.ml_monitoring_section_entities
    ADD CONSTRAINT ml_monitoring_section_entities_pkey PRIMARY KEY (monitoring_section_id, entity_id);

ALTER TABLE ONLY public.ml_monitoring_section
    ADD CONSTRAINT ml_monitoring_section_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.ml_mtp_conn_point
    ADD CONSTRAINT ml_mtp_conn_point_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.ml_ni_link
    ADD CONSTRAINT ml_ni_link_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.ml_node
    ADD CONSTRAINT ml_node_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.ml_path
    ADD CONSTRAINT ml_path_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.ml_path_seq
    ADD CONSTRAINT ml_path_seq_pkey PRIMARY KEY (path_id, connection_id);

ALTER TABLE ONLY public.ml_res_tp_pair
    ADD CONSTRAINT ml_res_tp_pair_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.ml_res_ts_pair
    ADD CONSTRAINT ml_res_ts_pair_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.ml_segment_adaptation
    ADD CONSTRAINT ml_segment_adaptation_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.ml_segment_client
    ADD CONSTRAINT ml_segment_client_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.ml_segment_entities
    ADD CONSTRAINT ml_segment_entities_pkey PRIMARY KEY (seg_id, entity_id);

ALTER TABLE ONLY public.ml_segment_path
    ADD CONSTRAINT ml_segment_path_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.ml_ser_view_lines
    ADD CONSTRAINT ml_ser_view_lines_pkey PRIMARY KEY (view_id, line_id);

ALTER TABLE ONLY public.ml_ser_view_nes
    ADD CONSTRAINT ml_ser_view_nes_pkey PRIMARY KEY (view_id, ne_id);

ALTER TABLE ONLY public.ml_ser_view_subnets
    ADD CONSTRAINT ml_ser_view_subnets_pkey PRIMARY KEY (view_id, subnet_id);

ALTER TABLE ONLY public.ml_service_endpoint
    ADD CONSTRAINT ml_service_endpoint_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.ml_service_entities
    ADD CONSTRAINT ml_service_entities_pkey PRIMARY KEY (service_id, entity_id);

ALTER TABLE ONLY public.ml_service
    ADD CONSTRAINT ml_service_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.ml_topo_mo_ref
    ADD CONSTRAINT ml_topo_mo_ref_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.ml_topology_element
    ADD CONSTRAINT ml_topology_element_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.ml_topology_layer_custom_names
    ADD CONSTRAINT ml_topology_layer_custom_names_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.ml_ui_service
    ADD CONSTRAINT ml_ui_service_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.ml_vlan_values
    ADD CONSTRAINT ml_vlan_values_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.mt_tunnelctp_dt
    ADD CONSTRAINT mt_tunnelctp_dt_pkey PRIMARY KEY (tunnel_id);

ALTER TABLE ONLY public.mtosi_subscriptions
    ADD CONSTRAINT mtosi_subscriptions_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.mtosi_user_policy
    ADD CONSTRAINT mtosi_user_policy_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.nc_assigned_snmp_profile_info
    ADD CONSTRAINT nc_assigned_snmp_profile_info_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.nc_cliprops
    ADD CONSTRAINT nc_cliprops_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.nc_ftp_preferences
    ADD CONSTRAINT nc_ftp_preferences_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.nc_httpprops
    ADD CONSTRAINT nc_httpprops_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.nc_netconf_props
    ADD CONSTRAINT nc_netconf_props_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.nc_preferences
    ADD CONSTRAINT nc_preferences_pkey PRIMARY KEY (jdoid);

ALTER TABLE ONLY public.nc_profile_snmp
    ADD CONSTRAINT nc_profile_snmp_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.nc_snmpprops
    ADD CONSTRAINT nc_snmpprops_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.ni_res_settings
    ADD CONSTRAINT ni_res_settings_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.nms_clock_probe_history
    ADD CONSTRAINT nms_clock_probe_history_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.nms_ptp_clock_probe_history
    ADD CONSTRAINT nms_ptp_clock_probe_history_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.nms_sj_probe_history
    ADD CONSTRAINT nms_sj_probe_history_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.nms_sj_probe_mtie_result
    ADD CONSTRAINT nms_sj_probe_mtie_result_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.nmsclockmtieresultdbimpl
    ADD CONSTRAINT nmsclockmtieresultdbimpl_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.nmsptpclockmtieresultdbimpl
    ADD CONSTRAINT nmsptpclockmtieresultdbimpl_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.p_properties
    ADD CONSTRAINT p_properties_pkey PRIMARY KEY (keyword);

ALTER TABLE ONLY public.pd_bw_profile
    ADD CONSTRAINT pd_bw_profile_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.pd_bw_profile
    ADD CONSTRAINT pd_bw_profile_unq UNIQUE (name, parent);

ALTER TABLE ONLY public.pd_bw_resources
    ADD CONSTRAINT pd_bw_resources_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.pd_cfm_entry
    ADD CONSTRAINT pd_cfm_entry_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.pd_connection
    ADD CONSTRAINT pd_connection_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.pd_connection_point
    ADD CONSTRAINT pd_connection_point_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.pd_connection_resource
    ADD CONSTRAINT pd_connection_resource_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.pd_cp_path_seq
    ADD CONSTRAINT pd_cp_path_seq_pkey PRIMARY KEY (path_id, cp_id);

ALTER TABLE ONLY public.pd_eth_service_intent_param
    ADD CONSTRAINT pd_eth_service_intent_param_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.pd_eth_service_intent
    ADD CONSTRAINT pd_eth_service_intent_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.pd_eth_service_intent_tp
    ADD CONSTRAINT pd_eth_service_intent_tp_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.pd_layer_ext_pd_conn_point
    ADD CONSTRAINT pd_layer_ext_pd_conn_point_pkey PRIMARY KEY (ml_lag_id, connection_point_id);

ALTER TABLE ONLY public.pd_layer_extension
    ADD CONSTRAINT pd_layer_extension_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.pd_module
    ADD CONSTRAINT pd_module_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.pd_mtp_conn_point
    ADD CONSTRAINT pd_mtp_conn_point_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.pd_node
    ADD CONSTRAINT pd_node_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.pd_path
    ADD CONSTRAINT pd_path_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.pd_path_seq
    ADD CONSTRAINT pd_path_seq_pkey PRIMARY KEY (path_id, connection_id);

ALTER TABLE ONLY public.pd_pm_profile_counter
    ADD CONSTRAINT pd_pm_profile_counter_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.pd_pm_profile_counter
    ADD CONSTRAINT pd_pm_profile_counter_unq_pm_id UNIQUE (pm_id, profile_id);

ALTER TABLE ONLY public.pd_pm_profile
    ADD CONSTRAINT pd_pm_profile_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.pd_pm_profile
    ADD CONSTRAINT pd_pm_profile_template_unq_profile_name UNIQUE (profile_name);

ALTER TABLE ONLY public.pd_service_endpoint
    ADD CONSTRAINT pd_service_endpoint_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.pd_service_entities
    ADD CONSTRAINT pd_service_entities_pkey PRIMARY KEY (service_id, entity_id);

ALTER TABLE ONLY public.pd_service
    ADD CONSTRAINT pd_service_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.pd_topo_mo_ref
    ADD CONSTRAINT pd_topo_mo_ref_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.pd_topology_element
    ADD CONSTRAINT pd_topology_element_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.pd_ui_service
    ADD CONSTRAINT pd_ui_service_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.pl_polling_onetime_properties
    ADD CONSTRAINT pl_polling_onetime_properties_pkey PRIMARY KEY (pollingmanagerid, pollingtype);

ALTER TABLE ONLY public.pl_polling_result
    ADD CONSTRAINT pl_polling_result_pkey PRIMARY KEY (pollingmanagerid, pollingtype);

ALTER TABLE ONLY public.pl_polling_type_properties
    ADD CONSTRAINT pl_polling_type_properties_pkey PRIMARY KEY (pollingmanagerid, pollingtype);

ALTER TABLE ONLY public.pm_bin_f8
    ADD CONSTRAINT pm_bin_f8_pkey PRIMARY KEY (jdoid);

ALTER TABLE ONLY public.pm_csv_bookmark
    ADD CONSTRAINT pm_csv_bookmark_pkey PRIMARY KEY (id_bookmark);

ALTER TABLE ONLY public.pm_csv_bookmark
    ADD CONSTRAINT pm_csv_bookmark_time_type_key UNIQUE (time_type);

ALTER TABLE ONLY public.pm_manager_cfg
    ADD CONSTRAINT pm_manager_cfg_pkey PRIMARY KEY (jdoid);

ALTER TABLE ONLY public.pm_mapping
    ADD CONSTRAINT pm_mapping_pkey PRIMARY KEY (id_map);

ALTER TABLE ONLY public.pm_profile_counter
    ADD CONSTRAINT pm_profile_counter_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.pm_profile_counter
    ADD CONSTRAINT pm_profile_counter_unq_pm_id UNIQUE (pm_id, profile_id);

ALTER TABLE ONLY public.pm_profile
    ADD CONSTRAINT pm_profile_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.pm_profile
    ADD CONSTRAINT pm_profile_template_unq_profile_name UNIQUE (profile_name);

ALTER TABLE ONLY public.pm_record
    ADD CONSTRAINT pm_record_pkey PRIMARY KEY (jdoid);

ALTER TABLE ONLY public.pm_serie
    ADD CONSTRAINT pm_serie_pkey PRIMARY KEY (id_serie);

ALTER TABLE ONLY public.pm_template_identifier
    ADD CONSTRAINT pm_template_identifier_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.pm_template
    ADD CONSTRAINT pm_template_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.ptpclockmtieresultdbimpl
    ADD CONSTRAINT ptpclockmtieresultdbimpl_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.re_ha_properites
    ADD CONSTRAINT re_ha_properites_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.reflector_fam_record
    ADD CONSTRAINT reflector_fam_record_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.rp_report_custom_fields
    ADD CONSTRAINT rp_report_custom_fields_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.rp_report
    ADD CONSTRAINT rp_report_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.se_action_children
    ADD CONSTRAINT se_action_children_pkey PRIMARY KEY (id, children_id);

ALTER TABLE ONLY public.se_action
    ADD CONSTRAINT se_action_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.se_group
    ADD CONSTRAINT se_group_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.se_group_user
    ADD CONSTRAINT se_group_user_pkey PRIMARY KEY (id, users_id);

ALTER TABLE ONLY public.se_keystore
    ADD CONSTRAINT se_keystore_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.se_properties
    ADD CONSTRAINT se_properties_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.se_role_children
    ADD CONSTRAINT se_role_children_pkey PRIMARY KEY (id, children_id);

ALTER TABLE ONLY public.se_role
    ADD CONSTRAINT se_role_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.se_sab_event
    ADD CONSTRAINT se_sab_event_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.se_user
    ADD CONSTRAINT se_user_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.se_view
    ADD CONSTRAINT se_view_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.se_viewpoints
    ADD CONSTRAINT se_viewpoints_pkey PRIMARY KEY (thread_id, id);

ALTER TABLE ONLY public.sm_bw_restrict
    ADD CONSTRAINT sm_bw_restrict_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.sm_bw_restrict
    ADD CONSTRAINT sm_bw_restrict_unique UNIQUE (name);

ALTER TABLE ONLY public.sm_contacts
    ADD CONSTRAINT sm_contacts_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.sm_customer_contact_link
    ADD CONSTRAINT sm_customer_contact_link_pkey PRIMARY KEY (contact_id, customer_id);

ALTER TABLE ONLY public.sm_customer_service_group
    ADD CONSTRAINT sm_customer_service_group_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.sm_customers
    ADD CONSTRAINT sm_customers_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.sm_diversity
    ADD CONSTRAINT sm_diversity_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.sm_operational_status
    ADD CONSTRAINT sm_operational_status_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.sm_properties
    ADD CONSTRAINT sm_properties_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.sm_service_param
    ADD CONSTRAINT sm_service_param_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.sm_service
    ADD CONSTRAINT sm_service_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.sm_service_refs
    ADD CONSTRAINT sm_service_refs_pkey PRIMARY KEY (service_id, serviceref_id);

ALTER TABLE ONLY public.sm_service_tp
    ADD CONSTRAINT sm_service_tp_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.sm_tags
    ADD CONSTRAINT sm_tags_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.snt_sensors
    ADD CONSTRAINT snt_sensors_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.snt_setup
    ADD CONSTRAINT snt_setup_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.span_loss_record
    ADD CONSTRAINT span_loss_record_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.sr_build
    ADD CONSTRAINT sr_build_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.sr_db_version_info
    ADD CONSTRAINT sr_db_version_info_pkey PRIMARY KEY (jdoid);

ALTER TABLE ONLY public.sr_email_pops
    ADD CONSTRAINT sr_email_pops_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.sr_multi_server
    ADD CONSTRAINT sr_multi_server_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.sso_user
    ADD CONSTRAINT sso_user_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.sync_ncd
    ADD CONSTRAINT sync_ncd_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.sync_node_masters
    ADD CONSTRAINT sync_node_masters_pkey PRIMARY KEY (sync_node, sync_master);

ALTER TABLE ONLY public.sync_node
    ADD CONSTRAINT sync_node_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.sync_perf_data
    ADD CONSTRAINT sync_perf_data_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.sync_properties
    ADD CONSTRAINT sync_properties_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.sync_route_member_nodes
    ADD CONSTRAINT sync_route_member_nodes_pkey PRIMARY KEY (route_id, node_id);

ALTER TABLE ONLY public.sync_route
    ADD CONSTRAINT sync_route_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.sync_subnet_migration
    ADD CONSTRAINT sync_subnet_migration_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.sync_tc_location
    ADD CONSTRAINT sync_tc_location_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.sync_test_nms_creation_params
    ADD CONSTRAINT sync_test_nms_creation_params_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.sync_test
    ADD CONSTRAINT sync_test_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.ui_templates
    ADD CONSTRAINT ui_templates_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.ui_usr
    ADD CONSTRAINT ui_usr_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.ui_usr_sec
    ADD CONSTRAINT ui_usr_sec_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_unmanaged_cross_connect
    ADD CONSTRAINT unq_a_entity_b_entity_channel UNIQUE (a_entity, b_entity, channel);

ALTER TABLE ONLY public.nc_assigned_snmp_profile_info
    ADD CONSTRAINT unq_assigned_snmp_profile_ne_id UNIQUE (neid);

ALTER TABLE ONLY public.cn_croma_conn_map
    ADD CONSTRAINT unq_cn_croma_conn_map_0 UNIQUE (id);

ALTER TABLE ONLY public.cn_croma_entity_ec
    ADD CONSTRAINT unq_cn_croma_entity_ec_0 UNIQUE (id);

ALTER TABLE ONLY public.cn_croma_service_path
    ADD CONSTRAINT unq_cn_croma_service_path_0 UNIQUE (id);

ALTER TABLE ONLY public.cn_fiber_connection_ec
    ADD CONSTRAINT unq_cn_fiber_connection_ec_0 UNIQUE (id);

ALTER TABLE ONLY public.cn_master_profile_file
    ADD CONSTRAINT unq_cn_master_profile_file_0 UNIQUE (id);

ALTER TABLE ONLY public.cn_master_profile_file
    ADD CONSTRAINT unq_cn_master_profile_file_1 UNIQUE (filename);

ALTER TABLE ONLY public.cn_port_endpoint
    ADD CONSTRAINT unq_cn_port_endpoint_0 UNIQUE (id);

ALTER TABLE ONLY public.cn_profile_ne
    ADD CONSTRAINT unq_cn_profile_ne_0 UNIQUE (ne_id, id, mp_id);

ALTER TABLE ONLY public.cn_spectrum_inventory
    ADD CONSTRAINT unq_cn_spectrum_inventory_0 UNIQUE (id);

ALTER TABLE ONLY public.ml_connection_point
    ADD CONSTRAINT unq_cp_ne_location_identityindex UNIQUE (parent_node_id, location);

ALTER TABLE ONLY public.els_sso_account
    ADD CONSTRAINT unq_els_sso_account_0 UNIQUE (account_type);

ALTER TABLE ONLY public.cn_fdfr
    ADD CONSTRAINT unq_fdfrname UNIQUE (fdfrname);

ALTER TABLE ONLY public.ec_template_files
    ADD CONSTRAINT unq_file_nameftp_server_type UNIQUE (file_name, ftp_server_type);

ALTER TABLE ONLY public.cn_ftp
    ADD CONSTRAINT unq_ftp_pgid UNIQUE (pg_id);

ALTER TABLE ONLY public.nc_ftp_preferences
    ADD CONSTRAINT unq_ftp_type UNIQUE (ftp_type);

ALTER TABLE ONLY public.cn_network_element
    ADD CONSTRAINT unq_local_idneindex UNIQUE (local_id, neindex);

ALTER TABLE ONLY public.cn_cross_connect
    ADD CONSTRAINT unq_moneid_entityfrom_idxentityto_idx UNIQUE (crs_ne_id, entityfrom_index, entityto_index);

ALTER TABLE ONLY public.sync_ncd
    ADD CONSTRAINT unq_ncd_name UNIQUE (ncd_name);

ALTER TABLE ONLY public.cn_ne_backup
    ADD CONSTRAINT unq_ne_id UNIQUE (ne_id);

ALTER TABLE ONLY public.cn_esa_probe_f3
    ADD CONSTRAINT unq_ne_id_esaname_protocol UNIQUE (ne_id, esaname, protocol);

ALTER TABLE ONLY public.cn_entity_cp
    ADD CONSTRAINT unq_ne_idcpentityindex UNIQUE (ne_id, cpentityindex);

ALTER TABLE ONLY public.cn_entity
    ADD CONSTRAINT unq_ne_identityindex UNIQUE (ne_id, entityindex);

ALTER TABLE ONLY public.cn_logicalport_f3
    ADD CONSTRAINT unq_ne_idifdesc UNIQUE (ne_id, ifdesc);

ALTER TABLE ONLY public.cn_ma
    ADD CONSTRAINT unq_ne_idmdindexmaindex UNIQUE (ne_id, mdindex, maindex);

ALTER TABLE ONLY public.cn_eth_flow_policer_f3
    ADD CONSTRAINT unq_ne_ineinshelslotaccpflowtypepoli UNIQUE (ne_id, neindex, shelfindex, slotindex, portindex, flow_index, typeindex, policerindex);

ALTER TABLE ONLY public.cn_shaper_cm
    ADD CONSTRAINT unq_ne_ineinshelslotaccpflowtypeshap UNIQUE (ne_id, neindex, shelfindex, slotindex, portindex, flowindex, typeindex, shaperindex);

ALTER TABLE ONLY public.cn_flow_f3
    ADD CONSTRAINT unq_neidneindexflowtypeindex UNIQUE (neid, neindex, flowindex, flowtype);

ALTER TABLE ONLY public.cn_f3sync_ref
    ADD CONSTRAINT unq_neidneindshelfslotisyncirefin UNIQUE (neid, neindex, shelfindex, slotindex, syncindex, refindex);

ALTER TABLE ONLY public.cn_f3timeclock_ref
    ADD CONSTRAINT unq_neidneindshelfslotitimeirefin UNIQUE (neid, neindex, shelfindex, slotindex, timeindex, refindex);

ALTER TABLE ONLY public.ni_res_settings
    ADD CONSTRAINT unq_ocs_id UNIQUE (ocs_id);

ALTER TABLE ONLY public.pd_connection_point
    ADD CONSTRAINT unq_pd_cp_ne_location_identityindex UNIQUE (parent_node_id, location);

ALTER TABLE ONLY public.cn_secure_flow_template
    ADD CONSTRAINT unq_profile_name UNIQUE (profilename);

ALTER TABLE ONLY public.cn_key_exchange_template
    ADD CONSTRAINT unq_profile_name_key_ex UNIQUE (profilename);

ALTER TABLE ONLY public.nc_profile_snmp
    ADD CONSTRAINT unq_profile_snmp_name UNIQUE (name);

ALTER TABLE ONLY public.cn_pwr_consumption
    ADD CONSTRAINT unq_pwr_neid UNIQUE (ne_id);

ALTER TABLE ONLY public.sync_route
    ADD CONSTRAINT unq_route_name UNIQUE (route_name);

ALTER TABLE ONLY public.se_keystore
    ADD CONSTRAINT unq_se_keystore_0 UNIQUE (name);

ALTER TABLE ONLY public.ec_service_templates
    ADD CONSTRAINT unq_service_id_ne_id UNIQUE (service_id, ne_id);

ALTER TABLE ONLY public.ml_te_property
    ADD CONSTRAINT unq_te_prop UNIQUE (te_id, key);

ALTER TABLE ONLY public.cn_unmanaged_object
    ADD CONSTRAINT unq_uno_business_id UNIQUE (uno_business_id);

CREATE UNIQUE INDEX ix_unq_cn_fallback_ne_pwd_ne_id ON public.cn_fallback_ne_pwd USING btree (ne_id);

CREATE UNIQUE INDEX unq_mac ON public.cn_network_element USING btree (mac) WHERE (identkey = 2);

CREATE UNIQUE INDEX unq_mo_neid_entityindex ON public.cn_managed_object USING btree (ne_id, entityindex) WHERE (entityindex IS NOT NULL);

CREATE UNIQUE INDEX unq_ne_id_onn_id_mo_aid ON public.cn_conn_info USING btree (ne_id, conn_id, mo_aid) WHERE (parent_conn_info_id IS NULL);

CREATE UNIQUE INDEX unq_ne_id_onn_id_mo_aid_parent_conn_info_id ON public.cn_conn_info USING btree (ne_id, conn_id, mo_aid, parent_conn_info_id) WHERE (parent_conn_info_id IS NOT NULL);

CREATE UNIQUE INDEX unq_ne_idaidstring ON public.cn_entity USING btree (ne_id, aidstring);

CREATE UNIQUE INDEX unq_serial ON public.cn_network_element USING btree (serial) WHERE (identkey = 1);

CREATE INDEX pdl3pvpnsrvceentitylistfkpdl3pvpnsrviceentitylistipvpnserviceid ON mnc_mpd_layer3.pdl3_ipvpn_service_entity_list USING btree (ipvpn_service_id);
CREATE INDEX ON public.bk_bookmark USING btree (jdoclass);
CREATE INDEX ix_bk_bookmark_fk_bk_bookmark_parent_id ON public.bk_bookmark USING btree (parent_id);
CREATE INDEX i_bk_bookmark_user_id ON public.bk_bookmark USING btree (user_id);
CREATE INDEX ON public.bk_bookmark_group USING btree (jdoclass);
CREATE INDEX ix_bk_bookmark_group_fk_bk_bookmark_group_parent_id ON public.bk_bookmark_group USING btree (parent_id);
CREATE INDEX i_bk_bookmark_group_user_id ON public.bk_bookmark_group USING btree (user_id);
CREATE INDEX ix_cm_cua_ne_ids_fk_cm_cua_ne_ids_id ON public.cm_cua_ne_ids USING btree (id);
CREATE INDEX ix_cm_cua_subnet_ids_fk_cm_cua_subnet_ids_id ON public.cm_cua_subnet_ids USING btree (id);
CREATE INDEX ix_cm_pca_ne_ids_fk_cm_pca_ne_ids_id ON public.cm_pca_ne_ids USING btree (id);
CREATE INDEX ix_cm_pca_subnet_ids_fk_cm_pca_subnet_ids_id ON public.cm_pca_subnet_ids USING btree (id);
CREATE INDEX ix_cn_apsgroup_fk_cn_apsgroup_clientport_id ON public.cn_apsgroup USING btree (clientport_id);
CREATE INDEX ix_cn_apsgroup_fk_cn_apsgroup_protectionmodule_id ON public.cn_apsgroup USING btree (protectionmodule_id);
CREATE INDEX i_cn_apsgrp_protportaid ON public.cn_apsgroup USING btree (protectionport_aid);
CREATE INDEX ix_cn_apsgroup_fk_cn_apsgroup_protectionvch_id ON public.cn_apsgroup USING btree (protectionvch_id);
CREATE INDEX ix_cn_apsgroup_fk_cn_apsgroup_workingmodule_id ON public.cn_apsgroup USING btree (workingmodule_id);
CREATE INDEX i_cn_apsgrp_workportaid ON public.cn_apsgroup USING btree (workingport_aid);
CREATE INDEX ix_cn_apsgroup_fk_cn_apsgroup_workingvch_id ON public.cn_apsgroup USING btree (workingvch_id);
CREATE INDEX cn_boundary_clock_fk_cn_boundary_clock_ptp_clock_timing_source ON public.cn_boundary_clock USING btree (ptp_clock_timing_source);
CREATE INDEX ix_cn_boundary_clock_fk_cn_boundary_clock_timing_source ON public.cn_boundary_clock USING btree (timing_source);
CREATE INDEX cncrdclusterf8cardlistfkcncrdclusterf8cardlistcncardclusterf8id ON public.cn_card_cluster_f8_card_list USING btree (cn_card_cluster_f8_id);
CREATE INDEX ON public.cn_config_ctrl USING btree (jdoclass);
CREATE INDEX ix_cn_conn_comp_restn_route_fk_cn_conn_comp_restn_route_conn_id ON public.cn_conn_comp_restn_route USING btree (conn_id);
CREATE INDEX ix_cn_conn_comp_restn_route_fk_cn_conn_comp_restn_route_line_id ON public.cn_conn_comp_restn_route USING btree (line_id);
CREATE INDEX ix_cn_conn_crossconnects_fk_cn_conn_crossconnects_crossconn_id ON public.cn_conn_crossconnects USING btree (crossconn_id);
CREATE INDEX ix_cn_conn_crossconnects_fk_cn_conn_crossconnects_subchconn_id ON public.cn_conn_crossconnects USING btree (subchconn_id);
CREATE INDEX ix_cn_conn_info_fk_cn_conn_info_conn_id ON public.cn_conn_info USING btree (conn_id);
CREATE INDEX ix_cn_conn_info_fk_cn_conn_info_entity_id ON public.cn_conn_info USING btree (entity_id);
CREATE INDEX ON public.cn_conn_info USING btree (jdoclass);
CREATE INDEX ix_cn_conn_info_fk_cn_conn_info_line_id ON public.cn_conn_info USING btree (line_id);
CREATE INDEX ix_cn_conn_info_fk_cn_conn_info_module_id ON public.cn_conn_info USING btree (module_id);
CREATE INDEX i_conn_info_ne_id_mo_aid ON public.cn_conn_info USING btree (ne_id, mo_aid);
CREATE INDEX ix_cn_conn_info_fk_cn_conn_info_ne_id ON public.cn_conn_info USING btree (ne_id);
CREATE INDEX ix_cn_conn_info_fk_cn_conn_info_parent_conn_info_id ON public.cn_conn_info USING btree (parent_conn_info_id);
CREATE INDEX ix_cn_conn_info_fk_cn_conn_info_subnet_id ON public.cn_conn_info USING btree (subnet_id);
CREATE INDEX cn_conn_nominal_prot_routefk_cn_conn_nominal_prot_route_conn_id ON public.cn_conn_nominal_prot_route USING btree (conn_id);
CREATE INDEX cn_conn_nominal_prot_routefk_cn_conn_nominal_prot_route_line_id ON public.cn_conn_nominal_prot_route USING btree (line_id);
CREATE INDEX ix_cn_conn_nominal_route_fk_cn_conn_nominal_route_conn_id ON public.cn_conn_nominal_route USING btree (conn_id);
CREATE INDEX ix_cn_conn_nominal_route_fk_cn_conn_nominal_route_line_id ON public.cn_conn_nominal_route USING btree (line_id);
CREATE INDEX ix_cn_conn_ochconns_fk_cn_conn_ochconns_optchconn_id ON public.cn_conn_ochconns USING btree (optchconn_id);
CREATE INDEX ix_cn_conn_ochconns_fk_cn_conn_ochconns_subchconn_id ON public.cn_conn_ochconns USING btree (subchconn_id);
CREATE INDEX cn_conn_ochvirtchannels_fk_cn_conn_ochvirtchannels_optchconn_id ON public.cn_conn_ochvirtchannels USING btree (optchconn_id);
CREATE INDEX i_cn_virtch_opt ON public.cn_conn_ochvirtchannels USING btree (optchconn_virtch_id);
CREATE INDEX i_cn_virtch_virtid ON public.cn_conn_ochvirtchannels USING btree (virtch_id);
CREATE INDEX ix_cn_conn_peer_vc_fk_cn_conn_peer_vc_conn_id ON public.cn_conn_peer_vc USING btree (conn_id);
CREATE INDEX i_cn_peer_vc ON public.cn_conn_peer_vc USING btree (vc_index);
CREATE INDEX ix_cn_conn_prot_route_fk_cn_conn_prot_route_conn_id ON public.cn_conn_prot_route USING btree (conn_id);
CREATE INDEX ix_cn_conn_prot_route_fk_cn_conn_prot_route_line_id ON public.cn_conn_prot_route USING btree (line_id);
CREATE INDEX ix_cn_conn_prot_trailconn_fk_cn_conn_prot_trailconn_conn_id ON public.cn_conn_prot_trailconn USING btree (conn_id);
CREATE INDEX cn_conn_prot_trailconn_fk_cn_conn_prot_trailconn_connection_id ON public.cn_conn_prot_trailconn USING btree (connection_id);
CREATE INDEX ix_cn_conn_restn_route_fk_cn_conn_restn_route_conn_id ON public.cn_conn_restn_route USING btree (conn_id);
CREATE INDEX ix_cn_conn_restn_route_fk_cn_conn_restn_route_line_id ON public.cn_conn_restn_route USING btree (line_id);
CREATE INDEX ix_cn_conn_route_fk_cn_conn_route_conn_id ON public.cn_conn_route USING btree (conn_id);
CREATE INDEX ix_cn_conn_route_fk_cn_conn_route_line_id ON public.cn_conn_route USING btree (line_id);
CREATE INDEX ix_cn_conn_start_vc_fk_cn_conn_start_vc_conn_id ON public.cn_conn_start_vc USING btree (conn_id);
CREATE INDEX i_cn_start_vc ON public.cn_conn_start_vc USING btree (vc_index);
CREATE INDEX ix_cn_connection_line_aend ON public.cn_conn_to_line_end_points USING btree (aend_neid, aend_port_index);
CREATE INDEX ix_cn_conn_to_line_conn_label ON public.cn_conn_to_line_end_points USING btree (conn_id);
CREATE INDEX ix_cn_conn_to_line_line_id ON public.cn_conn_to_line_end_points USING btree (line_id);
CREATE INDEX ix_cn_connection_line_zend ON public.cn_conn_to_line_end_points USING btree (zend_neid, zend_port_index);
CREATE INDEX ix_cn_conn_trailconn_fk_cn_conn_trailconn_conn_id ON public.cn_conn_trailconn USING btree (conn_id);
CREATE INDEX ix_cn_conn_trailconn_fk_cn_conn_trailconn_connection_id ON public.cn_conn_trailconn USING btree (connection_id);
CREATE INDEX cn_connect_guard_config_fk_cn_connect_guard_config_snmppropsid ON public.cn_connect_guard_config USING btree (snmppropsid);
CREATE INDEX ix_cn_connection_fk_cn_connection_containingochconnection_id ON public.cn_connection USING btree (containingochconnection_id);
CREATE INDEX ix_cn_connection_fk_cn_connection_cptunnel_id ON public.cn_connection USING btree (cptunnel_id);
CREATE INDEX ix_cn_connection_fk_cn_connection_customer_id ON public.cn_connection USING btree (customer_id);
CREATE INDEX ON public.cn_connection USING btree (jdoclass);
CREATE INDEX ix_cn_connection_label ON public.cn_connection USING btree (label);
CREATE INDEX ix_cn_connection_fk_cn_connection_ni_service_id ON public.cn_connection USING btree (ni_service_id);
CREATE INDEX ix_cn_connection_fk_cn_connection_oper_state ON public.cn_connection USING btree (oper_state);
CREATE INDEX ix_cn_connection_fk_cn_connection_peerapsgroup_id ON public.cn_connection USING btree (peerapsgroup_id);
CREATE INDEX ix_cn_connection_fk_cn_connection_peermodule_id ON public.cn_connection USING btree (peermodule_id);
CREATE INDEX ix_cn_connection_fk_cn_connection_peerpm_id ON public.cn_connection USING btree (peerpm_id);
CREATE INDEX ix_cn_connection_fk_cn_connection_peerport_id ON public.cn_connection USING btree (peerport_id);
CREATE INDEX ix_cn_connection_fk_cn_connection_protpeercm_id ON public.cn_connection USING btree (protpeercm_id);
CREATE INDEX ix_cn_connection_fk_cn_connection_protpeerport_id ON public.cn_connection USING btree (protpeerport_id);
CREATE INDEX ix_cn_connection_fk_cn_connection_protstartcm_id ON public.cn_connection USING btree (protstartcm_id);
CREATE INDEX ix_cn_connection_fk_cn_connection_protstartport_id ON public.cn_connection USING btree (protstartport_id);
CREATE INDEX ix_cn_connection_fk_cn_connection_servicegroup_id ON public.cn_connection USING btree (servicegroup_id);
CREATE INDEX ix_cn_connection_fk_cn_connection_serviceintent_id ON public.cn_connection USING btree (serviceintent_id);
CREATE INDEX ix_cn_connection_fk_cn_connection_startapsgroup_id ON public.cn_connection USING btree (startapsgroup_id);
CREATE INDEX ix_cn_connection_fk_cn_connection_startmodule_id ON public.cn_connection USING btree (startmodule_id);
CREATE INDEX ix_cn_connection_startne_id_peerne_id ON public.cn_connection USING btree (startne_id, peerne_id);
CREATE INDEX ix_cn_connection_fk_cn_connection_startpm_id ON public.cn_connection USING btree (startpm_id);
CREATE INDEX ix_cn_connection_fk_cn_connection_startport_id ON public.cn_connection USING btree (startport_id);
CREATE INDEX cncromadegreesecfk_cn_croma_degrees_ec_cn_croma_service_path_id ON public.cn_croma_degrees_ec USING btree (cn_croma_service_path_id);
CREATE INDEX cncromaportendpointsecfkcncromaportendpointseccncromaentityecid ON public.cn_croma_port_endpoints_ec USING btree (cn_croma_entity_ec_id);
CREATE INDEX cn_croma_service_path_fk_cn_croma_service_path_croma_entity_id ON public.cn_croma_service_path USING btree (croma_entity_id);
CREATE INDEX cn_croma_slc_bandwidthfk_cn_croma_slc_bandwidth_cn_croma_slc_id ON public.cn_croma_slc_bandwidth USING btree (cn_croma_slc_id);
CREATE INDEX cn_croma_slc_frequencyfk_cn_croma_slc_frequency_cn_croma_slc_id ON public.cn_croma_slc_frequency USING btree (cn_croma_slc_id);
CREATE INDEX ix_cn_croma_uris_ec_fk_cn_croma_uris_ec_cn_croma_entity_ec_id ON public.cn_croma_uris_ec USING btree (cn_croma_entity_ec_id);
CREATE INDEX cncrossconnectectplistfkcncrossconnectectplist_cross_connect_id ON public.cn_cross_connect_ec_tplist USING btree (cross_connect_id);
CREATE INDEX ix_cn_cross_connects_ref_fk_cn_cross_connects_ref_roadm_id ON public.cn_cross_connects_ref USING btree (roadm_id);
CREATE INDEX ix_cn_ec_ctp_fk_cn_ec_ctp_sk_id ON public.cn_ec_ctp USING btree (sk_id);
CREATE INDEX ix_cn_ec_ctp_fk_cn_ec_ctp_so_id ON public.cn_ec_ctp USING btree (so_id);
CREATE INDEX ix_cn_ec_ptp_otsi_fk_cn_ec_ptp_otsi_ec_ctp_id ON public.cn_ec_ptp_otsi USING btree (ec_ctp_id);
CREATE INDEX ix_cn_ec_ptp_otsi_fk_cn_ec_ptp_otsi_ec_ctp_otsi_id ON public.cn_ec_ptp_otsi USING btree (ec_ctp_otsi_id);
CREATE INDEX cn_elan_termination_point_fk_cn_elan_termination_point_conn_id ON public.cn_elan_termination_point USING btree (conn_id);
CREATE INDEX ix_cn_elp_group_fk_cn_elp_group_protectportid ON public.cn_elp_group USING btree (protectportid);
CREATE INDEX ix_cn_elp_group_fk_cn_elp_group_workingportid ON public.cn_elp_group USING btree (workingportid);
CREATE INDEX ix_cn_elp_protflow_fk_cn_elp_protflow_elpid ON public.cn_elp_protflow USING btree (elpid);
CREATE INDEX ix_cn_elp_unit_fk_cn_elp_unit_elpid ON public.cn_elp_unit USING btree (elpid);
CREATE INDEX cn_encryption_status_fk_cn_encryption_status_encryptionstatusid ON public.cn_encryption_status USING btree (encryptionstatusid);
CREATE INDEX i_cn_entity_assignedtype ON public.cn_entity USING btree (assignedtype);
CREATE INDEX i_cn_ntty_entityindex ON public.cn_entity USING btree (entityindex);
CREATE INDEX ix_cn_entity_fk_cn_entity_lifid ON public.cn_entity USING btree (lifid);
CREATE INDEX i_cn_entity_ne_id_contained_in ON public.cn_entity USING btree (ne_id, containedin);
CREATE INDEX i_cn_ntty_ne_id ON public.cn_entity USING btree (ne_id);
CREATE INDEX ix_cn_entity_fk_cn_entity_parent_id ON public.cn_entity USING btree (parent_id);
CREATE INDEX i_cn_n_cp_cpentityindex ON public.cn_entity_cp USING btree (cpentityindex);
CREATE INDEX i_cn_n_cp_ne_id ON public.cn_entity_cp USING btree (ne_id);
CREATE INDEX ix_cn_erp_f3_fk_cn_erp_f3_interconnection_erp ON public.cn_erp_f3 USING btree (interconnection_erp);
CREATE INDEX ix_cn_erp_f3_fk_cn_erp_f3_ringport0erpunitid ON public.cn_erp_f3 USING btree (ringport0erpunitid);
CREATE INDEX ix_cn_erp_f3_fk_cn_erp_f3_ringport0id ON public.cn_erp_f3 USING btree (ringport0id);
CREATE INDEX ix_cn_erp_f3_fk_cn_erp_f3_ringport1erpunitid ON public.cn_erp_f3 USING btree (ringport1erpunitid);
CREATE INDEX ix_cn_erp_f3_fk_cn_erp_f3_ringport1id ON public.cn_erp_f3 USING btree (ringport1id);
CREATE INDEX ix_cn_erp_protflow_f3_fk_cn_erp_protflow_f3_erpid ON public.cn_erp_protflow_f3 USING btree (erpid);
CREATE INDEX i_flowref_index ON public.cn_erp_protflow_f3 USING btree (flowref_index);
CREATE INDEX i_cn_esa_probe_f3_sourceport ON public.cn_esa_probe_f3 USING btree (sourceport);
CREATE INDEX ix_cn_eth_flow_policer_f3_fk_cn_eth_flow_policer_f3_flowpointid ON public.cn_eth_flow_policer_f3 USING btree (flowpointid);
CREATE INDEX i_cn_eth_ring_assoc_obj_entityindex ON public.cn_eth_ring_associated_object USING btree (assoc_obj_entityindex);
CREATE INDEX i_cn_eth_ring_assoc_obj_ne_id ON public.cn_eth_ring_associated_object USING btree (ne_id);
CREATE INDEX cnethringassociatedobjectfkcnethringassociated_object_parent_id ON public.cn_eth_ring_associated_object USING btree (parent_id);
CREATE INDEX ix_cn_eth_ring_group_fk_cn_eth_ring_group_parent ON public.cn_eth_ring_group USING btree (parent);
CREATE INDEX ix_cn_eth_ring_path_fk_cn_eth_ring_path_eth_ring_id ON public.cn_eth_ring_path USING btree (eth_ring_id);
CREATE INDEX ix_cn_eth_service_fk_cn_eth_service_portfsp150ccdbimpl_id ON public.cn_eth_service USING btree (portfsp150ccdbimpl_id);
CREATE INDEX ix_cn_evpn_flows_f4_fk_cn_evpn_flows_f4_evpn_id ON public.cn_evpn_flows_f4 USING btree (evpn_id);
CREATE INDEX cnevpnlocalroutetargetf4fkcn_evpn_local_route_target_f4_evpn_id ON public.cn_evpn_local_route_target_f4 USING btree (evpn_id);
CREATE INDEX ix_cn_f3sync_ref_fk_cn_f3sync_ref_ref_id ON public.cn_f3sync_ref USING btree (ref_id);
CREATE INDEX ix_cn_f3timeclock_ref_fk_cn_f3timeclock_ref_ref_id ON public.cn_f3timeclock_ref USING btree (ref_id);
CREATE INDEX ix_cn_f8_spsl_fk_cn_f8_spsl_ec_ctp_id ON public.cn_f8_spsl USING btree (ec_ctp_id);
CREATE INDEX ix_cn_fallback_ne_pwd_fk_cn_fallback_ne_pwd_ne_id ON public.cn_fallback_ne_pwd USING btree (ne_id);
CREATE INDEX ix_cn_fdfr_fk_cn_fdfr_aend_jdoid ON public.cn_fdfr USING btree (aend_jdoid);
CREATE INDEX ix_cn_fdfr_fk_cn_fdfr_aendcrs_jdoid ON public.cn_fdfr USING btree (aendcrs_jdoid);
CREATE INDEX ix_cn_fdfr_fk_cn_fdfr_evc_id ON public.cn_fdfr USING btree (evc_id);
CREATE INDEX ix_cn_fdfr_fk_cn_fdfr_zend_jdoid ON public.cn_fdfr USING btree (zend_jdoid);
CREATE INDEX ix_cn_fdfr_fk_cn_fdfr_zendcrs_jdoid ON public.cn_fdfr USING btree (zendcrs_jdoid);
CREATE INDEX ix_cn_fdfr_end_fk_cn_fdfr_end_flowdbimpl ON public.cn_fdfr_end USING btree (flowdbimpl);
CREATE INDEX ix_cn_fdfr_end_fk_cn_fdfr_end_ftpdbimpl ON public.cn_fdfr_end USING btree (ftpdbimpl);
CREATE INDEX ix_cn_fdfr_end_fk_cn_fdfr_end_portdbimpl ON public.cn_fdfr_end USING btree (portdbimpl);
CREATE INDEX ix_cn_fdfr_to_crs_fk_cn_fdfr_to_crs_crs_jdoid ON public.cn_fdfr_to_crs USING btree (crs_jdoid);
CREATE INDEX ix_cn_fdfr_to_crs_fk_cn_fdfr_to_crs_fdfr_jdoid ON public.cn_fdfr_to_crs USING btree (fdfr_jdoid);
CREATE INDEX i_cn_flow_cm_port ON public.cn_flow_cm USING btree (portfsp150cmaccdbimpl);
CREATE INDEX ix_cn_flow_f3_fk_cn_flow_f3_flowpointa ON public.cn_flow_f3 USING btree (flowpointa);
CREATE INDEX ix_cn_flow_f3_fk_cn_flow_f3_flowpointb ON public.cn_flow_f3 USING btree (flowpointb);
CREATE INDEX i_cn_flowhn_port ON public.cn_flow_hn USING btree (porthn4000dbimpl);
CREATE INDEX ix_cn_flowpoint_f3_fk_cn_flowpoint_f3_elpid ON public.cn_flowpoint_f3 USING btree (elpid);
CREATE INDEX ix_cn_flowpoint_f3_fk_cn_flowpoint_f3_flowid ON public.cn_flowpoint_f3 USING btree (flowid);
CREATE INDEX ix_cn_flowpoint_f3_fk_cn_flowpoint_f3_flowpointcpdid ON public.cn_flowpoint_f3 USING btree (flowpointcpdid);
CREATE INDEX ix_cn_flowpoint_f3_fk_cn_flowpoint_f3_fpoamowner ON public.cn_flowpoint_f3 USING btree (fpoamowner);
CREATE INDEX ix_cn_flowpoint_f3_fk_cn_flowpoint_f3_fpowner ON public.cn_flowpoint_f3 USING btree (fpowner);
CREATE INDEX ix_cn_flowpoint_f3_fk_cn_flowpoint_f3_mpflow ON public.cn_flowpoint_f3 USING btree (mpflow);
CREATE INDEX cnflowpointf4rngprofsfkcn_flowpoint_f4_rngprofs_flowpoint_f4_id ON public.cn_flowpoint_f4_rngprofs USING btree (flowpoint_f4_id);
CREATE INDEX i_cn_ftp_aport ON public.cn_ftp USING btree (aport_id);
CREATE INDEX i_cn_ftp_bport ON public.cn_ftp USING btree (bport_id);
CREATE INDEX ix_cn_intermediate_neids_fk_cn_intermediate_neids_conn_id ON public.cn_intermediate_neids USING btree (conn_id);
CREATE INDEX ix_cn_intra_connection_ref_fk_cn_intra_connection_ref_roadm_id ON public.cn_intra_connection_ref USING btree (roadm_id);
CREATE INDEX ix_cn_l2_remote_slave_fk_cn_l2_remote_slave_port_clock ON public.cn_l2_remote_slave USING btree (port_clock);
CREATE INDEX index_cn_l3_port_clock_ip_v4_address_ip_v6_address ON public.cn_l3_port_clock USING btree (ip_v4_address, ip_v6_address);
CREATE INDEX ix_cn_l3_port_clock_fk_cn_l3_port_clock_ptp_clock_id ON public.cn_l3_port_clock USING btree (ptp_clock_id);
CREATE INDEX ix_cn_lag_f3_fk_cn_lag_f3_logicalport_id ON public.cn_lag_f3 USING btree (logicalport_id);
CREATE INDEX ix_cn_lagport_f3_fk_cn_lagport_f3_lagid ON public.cn_lagport_f3 USING btree (lagid);
CREATE INDEX ix_cn_lagport_f3_fk_cn_lagport_f3_lagmember_id ON public.cn_lagport_f3 USING btree (lagmember_id);
CREATE INDEX ix_cn_lagservicemap_f3_fk_cn_lagservicemap_f3_lagid ON public.cn_lagservicemap_f3 USING btree (lagid);
CREATE INDEX ix_cn_lambda_info_fk_cn_lambda_info_conn_id ON public.cn_lambda_info USING btree (conn_id);
CREATE INDEX ix_cn_lambda_info_fk_cn_lambda_info_line_id ON public.cn_lambda_info USING btree (line_id);
CREATE INDEX ix_cn_line_fk_cn_line_aendalmport_id ON public.cn_line USING btree (aendalmport_id);
CREATE INDEX ix_cn_line_fk_cn_line_aendrsm_id ON public.cn_line USING btree (aendrsm_id);
CREATE INDEX i_cn_line_creationdate ON public.cn_line USING btree (creationdate);
CREATE INDEX index_cn_line_eastne_id_a_entityindex ON public.cn_line USING btree (eastne_id, a_entityindex);
CREATE INDEX ix_cn_line_fk_cn_line_eastne_id ON public.cn_line USING btree (eastne_id);
CREATE INDEX ix_cn_line_fk_cn_line_ethbandwidthdata ON public.cn_line USING btree (ethbandwidthdata);
CREATE INDEX ON public.cn_line USING btree (jdoclass);
CREATE INDEX ix_cn_line_fk_cn_line_protline_id ON public.cn_line USING btree (protline_id);
CREATE INDEX index_cn_line_westne_id_z_entityindex ON public.cn_line USING btree (westne_id, z_entityindex);
CREATE INDEX ix_cn_line_fk_cn_line_westne_id ON public.cn_line USING btree (westne_id);
CREATE INDEX ix_cn_line_fk_cn_line_zendalmport_id ON public.cn_line USING btree (zendalmport_id);
CREATE INDEX ix_cn_line_fk_cn_line_zendrsm_id ON public.cn_line USING btree (zendrsm_id);
CREATE INDEX ix_cn_line_bw_restrict_fk_cn_line_bw_restrict_bw_restrict_id ON public.cn_line_bw_restrict USING btree (bw_restrict_id);
CREATE INDEX ix_cn_line_bw_restrict_fk_cn_line_bw_restrict_line_id ON public.cn_line_bw_restrict USING btree (line_id);
CREATE INDEX ix_cn_line_conns_fk_cn_line_conns_conn_id ON public.cn_line_conns USING btree (conn_id);
CREATE INDEX ix_cn_line_conns_fk_cn_line_conns_line_id ON public.cn_line_conns USING btree (line_id);
CREATE INDEX ix_cn_line_stby_conns_fk_cn_line_stby_conns_conn_id ON public.cn_line_stby_conns USING btree (conn_id);
CREATE INDEX ix_cn_line_stby_conns_fk_cn_line_stby_conns_line_id ON public.cn_line_stby_conns USING btree (line_id);
CREATE INDEX ix_cn_lldp_local_port_fk_cn_lldp_local_port_lldpsystemdatadb_id ON public.cn_lldp_local_port USING btree (lldpsystemdatadb_id);
CREATE INDEX cnlldpremtableentryfkcnlldp_rem_table_entry_lldpsystemdatadb_id ON public.cn_lldp_rem_table_entry USING btree (lldpsystemdatadb_id);
CREATE INDEX cn_lldp_rem_table_entry_fk_cn_lldp_rem_table_entry_localport_id ON public.cn_lldp_rem_table_entry USING btree (localport_id);
CREATE INDEX i_cn_l_f3_ifdesc ON public.cn_logicalport_f3 USING btree (ifdesc);
CREATE INDEX i_cn_l_f3_ne_id ON public.cn_logicalport_f3 USING btree (ne_id);
CREATE INDEX ix_cn_ma_fk_cn_ma_mdfsp150cc825dbimpl ON public.cn_ma USING btree (mdfsp150cc825dbimpl);
CREATE INDEX i_cn_managed_entityindex ON public.cn_managed_object USING btree (entityindex);
CREATE INDEX ON public.cn_managed_object USING btree (jdoclass);
CREATE INDEX index_cn_managed_object_ne_id_shortdescription ON public.cn_managed_object USING btree (ne_id, shortdescription);
CREATE INDEX i_cn_mjct_ne_id ON public.cn_managed_object USING btree (ne_id);
CREATE INDEX i_cn_mjct_pmid ON public.cn_managed_object USING btree (pmid);
CREATE INDEX ix_cn_master_clock_fk_cn_master_clock_redundancy_group ON public.cn_master_clock USING btree (redundancy_group);
CREATE INDEX ix_cn_master_clock_fk_cn_master_clock_selectedtimeref ON public.cn_master_clock USING btree (selectedtimeref);
CREATE INDEX ix_cn_master_clock_fk_cn_master_clock_timeclock ON public.cn_master_clock USING btree (timeclock);
CREATE INDEX cnmasterclockinterfacefkcnmaster_clock_interface_boundary_clock ON public.cn_master_clock_interface USING btree (boundary_clock);
CREATE INDEX cnmasterclockinterfacefk_cn_master_clock_interface_master_clock ON public.cn_master_clock_interface USING btree (master_clock);
CREATE INDEX cnmasterclockinterfacefkcnmasterclockinterfacemastervirtualport ON public.cn_master_clock_interface USING btree (master_virtual_port);
CREATE INDEX ix_cn_master_clock_vlan_ip_fk_cn_master_clock_vlan_ip_master_id ON public.cn_master_clock_vlan_ip USING btree (master_id);
CREATE INDEX cnmastervirtualportfkcnmastervirtualport_master_clock_interface ON public.cn_master_virtual_port USING btree (master_clock_interface);
CREATE INDEX cn_master_virtual_port_fk_cn_master_virtual_port_ptpflowpoint ON public.cn_master_virtual_port USING btree (ptpflowpoint);
CREATE INDEX ix_cn_mep_fk_cn_mep_mafsp150cc825dbimpl ON public.cn_mep USING btree (mafsp150cc825dbimpl);
CREATE INDEX ix_cn_mep_fk_cn_mep_mdfsp150cc825dbimpl ON public.cn_mep USING btree (mdfsp150cc825dbimpl);
CREATE INDEX ix_cn_mo_attr_fk_cn_mo_attr_managedobjectdb ON public.cn_mo_attr USING btree (managedobjectdb);
CREATE INDEX ix_cn_module_fk_cn_module_cmdbimplextension_jdoid ON public.cn_module USING btree (cmdbimplextension_jdoid);
CREATE INDEX ON public.cn_module_cm_ext USING btree (jdoclass);
CREATE INDEX ix_cn_module_cm_ext_fk_cn_module_cm_ext_module_id ON public.cn_module_cm_ext USING btree (module_id);
CREATE INDEX ix_cn_module_f7_fk_cn_module_f7_cmdbimplextension_jdoid ON public.cn_module_f7 USING btree (cmdbimplextension_jdoid);
CREATE INDEX ix_cn_module_f7_fk_cn_module_f7_osdbextension_jdoid ON public.cn_module_f7 USING btree (osdbextension_jdoid);
CREATE INDEX ON public.cn_module_os_ext USING btree (jdoclass);
CREATE INDEX ix_cn_module_os_ext_fk_cn_module_os_ext_module_id ON public.cn_module_os_ext USING btree (module_id);
CREATE INDEX ix_cn_msppgport_f3_fk_cn_msppgport_f3_msppg_id ON public.cn_msppgport_f3 USING btree (msppg_id);
CREATE INDEX ix_cn_msppgport_f3_fk_cn_msppgport_f3_port_id ON public.cn_msppgport_f3 USING btree (port_id);
CREATE INDEX ix_cn_ne_branches_fk_cn_ne_branches_line_id ON public.cn_ne_branches USING btree (line_id);
CREATE INDEX ix_cn_ne_branches_fk_cn_ne_branches_ne_id ON public.cn_ne_branches USING btree (ne_id);
CREATE INDEX ON public.cn_ne_events USING btree (jdoclass);
CREATE INDEX ON public.cn_nemi_sw_file USING btree (jdoclass);
CREATE INDEX ix_cn_network_element_fk_cn_network_element_cliprops_id ON public.cn_network_element USING btree (cliprops_id);
CREATE INDEX i_cn_nmnt_creationdate ON public.cn_network_element USING btree (creationdate);
CREATE INDEX ix_cn_network_element_fk_cn_network_element_httpprops_id ON public.cn_network_element USING btree (httpprops_id);
CREATE INDEX i_cn_nmnt_ipaddress ON public.cn_network_element USING btree (ipaddress);
CREATE INDEX ON public.cn_network_element USING btree (jdoclass);
CREATE INDEX ix_cn_network_element_fk_cn_network_element_local_id ON public.cn_network_element USING btree (local_id);
CREATE INDEX ix_cn_network_element_fk_cn_network_element_managementstatusid ON public.cn_network_element USING btree (managementstatusid);
CREATE INDEX i_cn_network_element_name ON public.cn_network_element USING btree (lower((name0)::text));
CREATE INDEX ix_cn_network_element_fk_cn_network_element_ne_capabilities_id ON public.cn_network_element USING btree (ne_capabilities_id);
CREATE INDEX ix_cn_network_element_fk_cn_network_element_netconfprops_id ON public.cn_network_element USING btree (netconfprops_id);
CREATE INDEX i_cn_network_element_pm_id ON public.cn_network_element USING btree (performancemanagerid);
CREATE INDEX ix_cn_network_element_fk_cn_network_element_snmpprops_id ON public.cn_network_element USING btree (snmpprops_id);
CREATE INDEX ix_cn_network_element_fk_cn_network_element_subnet_id ON public.cn_network_element USING btree (subnet_id);
CREATE INDEX ix_cn_network_element_fk_cn_network_element_swupgradene_id ON public.cn_network_element USING btree (swupgradene_id);
CREATE INDEX ix_cn_ni_controller_fk_cn_ni_controller_nictrlcommons_id ON public.cn_ni_controller USING btree (nictrlcommons_id);
CREATE INDEX ON public.cn_ni_tunnel USING btree (jdoclass);
CREATE INDEX cnntficationnetypelistfkcnntificationnetypelistnotificationecid ON public.cn_notification_ne_type_list USING btree (notification_ec_id);
CREATE INDEX ix_cn_ntp_clock_interface_fk_cn_ntp_clock_interface_ntp_clock ON public.cn_ntp_clock_interface USING btree (ntp_clock);
CREATE INDEX cnntpremoteclientfk_cn_ntp_remote_client_cn_ntp_clock_interface ON public.cn_ntp_remote_client USING btree (cn_ntp_clock_interface);
CREATE INDEX ix_cn_ntp_remote_server_fk_cn_ntp_remote_server_ntp_clock ON public.cn_ntp_remote_server USING btree (ntp_clock);
CREATE INDEX cnntptrackedclientfkcnntp_tracked_client_cn_ntp_clock_interface ON public.cn_ntp_tracked_client USING btree (cn_ntp_clock_interface);
CREATE INDEX cnptrouterportfrequencyslotfkcnoptrouterportfrequencyslotportid ON public.cn_optrouterport_frequencyslot USING btree (port_id);
CREATE INDEX cnoptrouterportwavelengthfk_cn_optrouterport_wavelength_port_id ON public.cn_optrouterport_wavelength USING btree (port_id);
CREATE INDEX ix_cn_path_elements_fk_cn_path_elements_path_id ON public.cn_path_elements USING btree (path_id);
CREATE INDEX ix_cn_pgport_f3_fk_cn_pgport_f3_pg_id ON public.cn_pgport_f3 USING btree (pg_id);
CREATE INDEX ix_cn_pgport_f3_fk_cn_pgport_f3_port_id ON public.cn_pgport_f3 USING btree (port_id);
CREATE INDEX index_cn_planner_pm_ne_id ON public.cn_planner_pm USING btree (ne_id);
CREATE INDEX ix_cn_plug_ec_rates_fk_cn_plug_ec_rates_plug_ec_id ON public.cn_plug_ec_rates USING btree (plug_ec_id);
CREATE INDEX ix_cn_port_fk_cn_port_containing_module_id ON public.cn_port USING btree (containing_module_id);
CREATE INDEX ix_cn_port_fk_cn_port_containing_plug_id ON public.cn_port USING btree (containing_plug_id);
CREATE INDEX index_cn_port_physaddress ON public.cn_port USING btree (physaddress);
CREATE INDEX ix_cn_port_fk_cn_port_ttpdbimplextension_jdoid ON public.cn_port USING btree (ttpdbimplextension_jdoid);
CREATE INDEX cn_port_bits_group_member_fk_cn_port_bits_group_member_group_id ON public.cn_port_bits_group_member USING btree (group_id);
CREATE INDEX ix_cn_port_clock_fk_cn_port_clock_ptp_clock_id ON public.cn_port_clock USING btree (ptp_clock_id);
CREATE INDEX ix_cn_port_cm_fk_cn_port_cm_embedded_gps_receiver ON public.cn_port_cm USING btree (embedded_gps_receiver);
CREATE INDEX ix_cn_port_cm_fk_cn_port_cm_embedded_stl_receiver ON public.cn_port_cm USING btree (embedded_stl_receiver);
CREATE INDEX ix_cn_port_cm_fk_cn_port_cm_redundancy_group ON public.cn_port_cm USING btree (redundancy_group);
CREATE INDEX ON public.cn_port_edfa_ext USING btree (jdoclass);
CREATE INDEX ix_cn_port_edfa_ext_fk_cn_port_edfa_ext_port_id ON public.cn_port_edfa_ext USING btree (port_id);
CREATE INDEX ON public.cn_port_ext USING btree (jdoclass);
CREATE INDEX i_cn_port_ne_id ON public.cn_port_ext USING btree (ne_id);
CREATE INDEX i_cn_port_port_id ON public.cn_port_ext USING btree (portdbimpl_id);
CREATE INDEX ix_cn_port_f7_fk_cn_port_f7_port_client_ext_jdoid ON public.cn_port_f7 USING btree (port_client_ext_jdoid);
CREATE INDEX ix_cn_port_f7_fk_cn_port_f7_port_net_ext_jdoid ON public.cn_port_f7 USING btree (port_net_ext_jdoid);
CREATE INDEX ix_cn_port_f7_fk_cn_port_f7_portedfadbimplextension_jdoid ON public.cn_port_f7 USING btree (portedfadbimplextension_jdoid);
CREATE INDEX ix_cn_port_f7_fk_cn_port_f7_ttpdbimplextension_jdoid ON public.cn_port_f7 USING btree (ttpdbimplextension_jdoid);
CREATE INDEX ix_cn_port_hn_fk_cn_port_hn_unidbimpl ON public.cn_port_hn USING btree (unidbimpl);
CREATE INDEX ON public.cn_port_tt_ext USING btree (jdoclass);
CREATE INDEX ix_cn_port_tt_ext_fk_cn_port_tt_ext_port_id ON public.cn_port_tt_ext USING btree (port_id);
CREATE INDEX ix_cn_profile_ne_fk_cn_profile_ne_mp_id ON public.cn_profile_ne USING btree (mp_id);
CREATE INDEX ix_cn_profile_ne_fk_cn_profile_ne_ne_id ON public.cn_profile_ne USING btree (ne_id);
CREATE INDEX cnprtctdflwpntlistecfkcnprtctdflwpntlistecprotectionflowpointid ON public.cn_protected_flowpoint_list_ec USING btree (protection_flowpoint_id);
CREATE INDEX ix_cn_ptp_clock_fk_cn_ptp_clock_active_slave_port ON public.cn_ptp_clock USING btree (active_slave_port);
CREATE INDEX ix_cn_ptp_clock_fk_cn_ptp_clock_redundancy_group ON public.cn_ptp_clock USING btree (redundancy_group);
CREATE INDEX ix_cn_ptp_clock_fk_cn_ptp_clock_sync_eid ON public.cn_ptp_clock USING btree (sync_eid);
CREATE INDEX ix_cn_ptp_clock_fk_cn_ptp_clock_time_source ON public.cn_ptp_clock USING btree (time_source);
CREATE INDEX index_cn_ptp_clock_probe_slave_ip_master_ip_direction ON public.cn_ptp_clock_probe USING btree (slave_ip, master_ip, direction);
CREATE INDEX index_cn_ptp_clock_probe_history_slave_ip_master_ip_direction ON public.cn_ptp_clock_probe_history USING btree (slave_ip, master_ip, direction);
CREATE INDEX index_cn_ptp_network_probe_slave_ip ON public.cn_ptp_network_probe USING btree (slave_ip);
CREATE INDEX cn_redundancy_group_fk_cn_redundancy_group_active_card_entity ON public.cn_redundancy_group USING btree (active_card_entity);
CREATE INDEX cn_redundancy_groupfk_cn_redundancy_group_active_managed_object ON public.cn_redundancy_group USING btree (active_managed_object);
CREATE INDEX cn_redundancy_group_fk_cn_redundancy_group_standby_card_entity ON public.cn_redundancy_group USING btree (standby_card_entity);
CREATE INDEX ix_cn_remote_slave_fk_cn_remote_slave_mci ON public.cn_remote_slave USING btree (mci);
CREATE INDEX index_cn_remote_slave_capacity_master_function_id ON public.cn_remote_slave_capacity USING btree (master_function_id);
CREATE INDEX index_cn_remote_slave_connectivity_ip_address ON public.cn_remote_slave_connectivity USING btree (ip_address);
CREATE INDEX index_cn_remote_slave_connectivity_port_identity ON public.cn_remote_slave_connectivity USING btree (port_identity);
CREATE INDEX ix_cn_satop_tdm_entity_fk_cn_satop_tdm_entity_satopdbimpl_id ON public.cn_satop_tdm_entity USING btree (satopdbimpl_id);
CREATE INDEX ix_cn_secure_flow_fk_cn_secure_flow_flow_id ON public.cn_secure_flow USING btree (flow_id);
CREATE INDEX ix_cn_shaper_cm_fk_cn_shaper_cm_ethid ON public.cn_shaper_cm USING btree (ethid);
CREATE INDEX i_cn_shaper_flow ON public.cn_shaper_cm USING btree (flowfsp150cmdbimpl);
CREATE INDEX ix_cn_shaper_cm_fk_cn_shaper_cm_flowpointid ON public.cn_shaper_cm USING btree (flowpointid);
CREATE INDEX i_cn_shaper_port ON public.cn_shaper_cm USING btree (portfsp150cmaccdbimpl);
CREATE INDEX ix_cn_shg_members_f3_fk_cn_shg_members_f3_member_id ON public.cn_shg_members_f3 USING btree (member_id);
CREATE INDEX i_memberref_index ON public.cn_shg_members_f3 USING btree (memberref_index);
CREATE INDEX ix_cn_shg_members_f3_fk_cn_shg_members_f3_shg ON public.cn_shg_members_f3 USING btree (shg);
CREATE INDEX ix_cn_sj_probe_fk_cn_sj_probe_scheduler_id ON public.cn_sj_probe USING btree (scheduler_id);
CREATE INDEX cnsjprobemtieresultfkcnsj_probe_mtie_result_owner_probe_history ON public.cn_sj_probe_mtie_result USING btree (owner_probe_history);
CREATE INDEX index_cn_slave_vp_port_identity ON public.cn_slave_vp USING btree (port_identity);
CREATE INDEX ix_cn_soam_xg3xx_fk_cn_soam_xg3xx_serviceindex ON public.cn_soam_xg3xx USING btree (serviceindex);
CREATE INDEX ix_cn_sooc_fk_cn_sooc_slave ON public.cn_sooc USING btree (slave);
CREATE INDEX index_cn_sooc_slave_ip_slave_ipv6 ON public.cn_sooc USING btree (slave_ip, slave_ipv6);
CREATE INDEX ix_cn_spectrum_inventory_fk_cn_spectrum_inventory_croma_conn_id ON public.cn_spectrum_inventory USING btree (croma_conn_id);
CREATE INDEX cn_spectrum_inventory_fk_cn_spectrum_inventory_croma_entity_id ON public.cn_spectrum_inventory USING btree (croma_entity_id);
CREATE INDEX cn_spectrum_inventory_fk_cn_spectrum_inventory_degree_entity_id ON public.cn_spectrum_inventory USING btree (degree_entity_id);
CREATE INDEX ON public.cn_subnet USING btree (jdoclass);
CREATE INDEX ix_cn_subnet_fk_cn_subnet_parent_id ON public.cn_subnet USING btree (parent_id);
CREATE INDEX ix_cn_telecom_slave_fk_cn_telecom_slave_selectedclock ON public.cn_telecom_slave USING btree (selectedclock);
CREATE INDEX index_cn_telecom_slave_slave_index_ne_index ON public.cn_telecom_slave USING btree (slave_index, ne_index);
CREATE INDEX cnunmanaged_cross_connectfk_cn_unmanaged_cross_connect_a_entity ON public.cn_unmanaged_cross_connect USING btree (a_entity);
CREATE INDEX cnunmanaged_cross_connectfk_cn_unmanaged_cross_connect_b_entity ON public.cn_unmanaged_cross_connect USING btree (b_entity);
CREATE INDEX ix_cn_unmanaged_object_fk_cn_unmanaged_object_handover_port_id ON public.cn_unmanaged_object USING btree (handover_port_id);
CREATE INDEX ix_cn_unmanaged_object_fk_cn_unmanaged_object_tp_parent_id ON public.cn_unmanaged_object USING btree (tp_parent_id);
CREATE INDEX cnunsupportednetworkelementfkcnunsupportednetwork_element_ne_id ON public.cn_unsupported_network_element USING btree (ne_id);
CREATE INDEX ix_cn_vrf_tplist_f4_fk_cn_vrf_tplist_f4_vrf_id ON public.cn_vrf_tplist_f4 USING btree (vrf_id);
CREATE INDEX ix_cn_vtep_ip_interface_fk_cn_vtep_ip_interface_protectportid ON public.cn_vtep_ip_interface USING btree (protectportid);
CREATE INDEX ix_cn_vtep_ip_interface_fk_cn_vtep_ip_interface_vtepid ON public.cn_vtep_ip_interface USING btree (vtepid);
CREATE INDEX ix_cn_vx_lan_segment_fk_cn_vx_lan_segment_vtepid ON public.cn_vx_lan_segment USING btree (vtepid);
CREATE INDEX ix_ec_ne_types_fk_ec_ne_types_file_id ON public.ec_ne_types USING btree (file_id);
CREATE INDEX ix_efd_trace_template_fk_efd_trace_template_loss_guidance_id ON public.efd_trace_template USING btree (loss_guidance_id);
CREATE INDEX ix_efd_trace_template_fk_efd_trace_template_trace_attribute_id ON public.efd_trace_template USING btree (trace_attribute_id);
CREATE INDEX ON public.ev_counter USING btree (jdoclass);
CREATE INDEX i_v_event_detectiontype ON public.ev_event USING btree (detectiontype);
CREATE INDEX i_v_event_dtype ON public.ev_event USING btree (dtype);
CREATE INDEX ne_timestamp_1 ON public.ev_event USING btree (netimestamp, id);
CREATE INDEX ne_timestamp_2 ON public.ev_event USING btree (netimestamp DESC, id DESC);
CREATE INDEX nms_timestamp_1 ON public.ev_event USING btree (nmstimestamp, id);
CREATE INDEX nms_timestamp_2 ON public.ev_event USING btree (nmstimestamp, id DESC);
CREATE INDEX i_v_event_nmstimestamp ON public.ev_event USING btree (nmstimestamp);
CREATE INDEX i_v_event_objectindex ON public.ev_event USING btree (objectindex);
CREATE INDEX i_v_event_sourcene ON public.ev_event USING btree (sourcene);
CREATE INDEX i_v_event_sourceneid ON public.ev_event USING btree (sourceneid);
CREATE INDEX ix_ev_event_fk_ev_event_sourceneid ON public.ev_event USING btree (sourceneid);
CREATE INDEX i_v_event_subnetid ON public.ev_event USING btree (subnetid);
CREATE INDEX i_v_event_trapid ON public.ev_event USING btree (trapid);
CREATE INDEX i_v_event_typ ON public.ev_event USING btree (typ);
CREATE INDEX ix_ev_event_assoc_fk_ev_event_assoc_id ON public.ev_event_assoc USING btree (id);
CREATE INDEX ix_ev_event_assoc_fk_ev_event_assoc_id2 ON public.ev_event_assoc USING btree (objectid);
CREATE INDEX ix_ev_event_conns_fk_ev_event_conns_id ON public.ev_event_conns USING btree (id);
CREATE INDEX i_ev_con_val ON public.ev_event_conns USING btree (val);
CREATE INDEX ix_ev_event_hist_assoc_fk_ev_event_hist_assoc_id ON public.ev_event_hist_assoc USING btree (id);
CREATE INDEX i_v_event_hist_id ON public.ev_event_history USING btree (id);
CREATE INDEX i_v_event_hist_sourceneid ON public.ev_event_history USING btree (sourceneid);
CREATE INDEX i_v_event_hist_subnetid ON public.ev_event_history USING btree (subnetid);
CREATE INDEX i_v_event_hist_trapid ON public.ev_event_history USING btree (trapid);
CREATE INDEX ix_ev_event_multivar_fk_ev_event_multivar_event_id ON public.ev_event_multivar USING btree (event_id);
CREATE INDEX i_ev_muvar_order ON public.ev_event_multivar USING btree (event_multivar_order);
CREATE INDEX ix_ev_event_sounds_fk_ev_event_sounds_owner ON public.ev_event_sounds USING btree (owner);
CREATE INDEX ix_ev_event_sync_ncd_fk_ev_event_sync_ncd_event_id ON public.ev_event_sync_ncd USING btree (event_id);
CREATE INDEX ix_ev_event_sync_node_fk_ev_event_sync_node_event_id ON public.ev_event_sync_node USING btree (event_id);
CREATE INDEX ON public.ev_filter USING btree (jdoclass);
CREATE INDEX i_ev_filter_type ON public.ev_filter USING btree (type);
CREATE INDEX ix_ev_filter_node_fk_ev_filter_node_filter_id ON public.ev_filter_node USING btree (filter_id);
CREATE INDEX i_ev_filter_node ON public.ev_filter_node USING btree (node_id);
CREATE INDEX ON public.ev_latency_monitor USING btree (jdoclass);
CREATE INDEX ON public.ev_nm_settings USING btree (jdoclass);
CREATE INDEX ix_ev_severity_fk_ev_severity_jdoid ON public.ev_severity USING btree (jdoid);
CREATE INDEX i_ev_sev ON public.ev_severity USING btree (severity);
CREATE INDEX ix_ev_subscription_filter_fk_ev_subscription_filter_id ON public.ev_subscription_filter USING btree (id);
CREATE INDEX ix_ev_transport_fk_ev_transport_filter ON public.ev_transport USING btree (filter);
CREATE INDEX ON public.ev_transport USING btree (jdoclass);
CREATE INDEX idx_enterprise_id ON public.ev_trap_severity USING btree (enterpriseid);
CREATE INDEX ON public.ev_trap_severity USING btree (jdoclass);
CREATE INDEX index_ev_trap_severity_netype_trapid ON public.ev_trap_severity USING btree (netype, trapid);
CREATE INDEX ix_ev_trash_fk_ev_trash_jdoid ON public.ev_trash USING btree (jdoid);
CREATE INDEX i_ev_trash ON public.ev_trash USING btree (trash);
CREATE INDEX ix_fam_event_fk_fam_event_owner_id ON public.fam_event USING btree (owner_id);
CREATE INDEX ix_fam_link_properties_fk_fam_link_properties_id ON public.fam_link_properties USING btree (id);
CREATE INDEX ix_fam_point_fk_fam_point_fa_record ON public.fam_point USING btree (fa_record);
CREATE INDEX ix_fam_point_fk_fam_point_fp_record ON public.fam_point USING btree (fp_record);
CREATE INDEX ix_fam_trace_fk_fam_trace_owner_id ON public.fam_trace USING btree (owner_id);
CREATE INDEX ix_fam_trace_data_fk_fam_trace_data_owner_id ON public.fam_trace_data USING btree (owner_id);
CREATE INDEX ix_fam_trace_settings_fk_fam_trace_settings_owner_id ON public.fam_trace_settings USING btree (owner_id);
CREATE INDEX index_hm_alarm_samples_history_time_stamp ON public.hm_alarm_samples_history USING btree (time_stamp);
CREATE INDEX index_hm_health_monitoring_sample_time_stamp_host_id ON public.hm_health_monitoring_sample USING btree (time_stamp, host_id);
CREATE INDEX index_hm_ne_samples_history_time_stamp ON public.hm_ne_samples_history USING btree (time_stamp);
CREATE INDEX index_hm_pwr_consump_samples_history_time_stamp ON public.hm_pwr_consump_samples_history USING btree (time_stamp);
CREATE INDEX index_hm_service_samples_history_time_stamp ON public.hm_service_samples_history USING btree (time_stamp);
CREATE INDEX mdsrdefinedtextfieldsfkmduserdefinedtextfieldsglobaltextfieldid ON public.md_userdefined_text_fields USING btree (globaltextfield_id);
CREATE INDEX i_md_udtf_tfh_id ON public.md_userdefined_text_fields USING btree (text_fieldholder_id);
CREATE INDEX ix_ml_adaptation_client_fk_ml_adaptation_client_adapt_id ON public.ml_adaptation_client USING btree (adapt_id);
CREATE INDEX ix_ml_adaptation_client_fk_ml_adaptation_client_topo_element_id ON public.ml_adaptation_client USING btree (topo_element_id);
CREATE INDEX ix_ml_adaptation_server_fk_ml_adaptation_server_adapt_id ON public.ml_adaptation_server USING btree (adapt_id);
CREATE INDEX ix_ml_adaptation_server_fk_ml_adaptation_server_topo_element_id ON public.ml_adaptation_server USING btree (topo_element_id);
CREATE INDEX ix_ml_connection_fk_ml_connection_a_end_id ON public.ml_connection USING btree (a_end_id);
CREATE INDEX ix_ml_connection_fk_ml_connection_tot_resource_id ON public.ml_connection USING btree (tot_resource_id);
CREATE INDEX ix_ml_connection_fk_ml_connection_z_end_id ON public.ml_connection USING btree (z_end_id);
CREATE INDEX i_ml_co_location ON public.ml_connection_point USING btree (location);
CREATE INDEX ix_ml_connection_point_fk_ml_connection_point_parent_cp_id ON public.ml_connection_point USING btree (parent_cp_id);
CREATE INDEX ix_ml_connection_point_fk_ml_connection_point_parent_id ON public.ml_connection_point USING btree (parent_id);
CREATE INDEX i_ml_cp_parent_module ON public.ml_connection_point USING btree (parent_module_id);
CREATE INDEX index_ml_connpoint_ne_id_location ON public.ml_connection_point USING btree (parent_node_id, location);
CREATE INDEX i_ml_cp_parent_node ON public.ml_connection_point USING btree (parent_node_id);
CREATE INDEX ml_connection_reservation_fk_ml_connection_reservation_conn_id ON public.ml_connection_reservation USING btree (conn_id);
CREATE INDEX mlcnnctionreservationfkmlcnnectionreservationreservedresourceid ON public.ml_connection_reservation USING btree (reserved_resource_id);
CREATE INDEX ml_connection_reservation_fk_ml_connection_reservation_trail_id ON public.ml_connection_reservation USING btree (trail_id);
CREATE INDEX ix_ml_connection_resource_fk_ml_connection_resource_resource_id ON public.ml_connection_resource USING btree (resource_id);
CREATE INDEX ix_ml_connection_resource_fk_ml_connection_resource_tp_pair_id ON public.ml_connection_resource USING btree (tp_pair_id);
CREATE INDEX ix_ml_cp_mon_layers_fk_ml_cp_mon_layers_topo_id ON public.ml_cp_mon_layers USING btree (topo_id);
CREATE INDEX ix_ml_cp_path_seq_fk_ml_cp_path_seq_cp_id ON public.ml_cp_path_seq USING btree (cp_id);
CREATE INDEX ix_ml_cp_path_seq_fk_ml_cp_path_seq_path_id ON public.ml_cp_path_seq USING btree (path_id);
CREATE INDEX ix_ml_cp_term_layers_fk_ml_cp_term_layers_topo_id ON public.ml_cp_term_layers USING btree (topo_id);
CREATE INDEX ix_ml_end_point_fk_ml_end_point_cp_id ON public.ml_end_point USING btree (cp_id);
CREATE INDEX mllayerextensionfkmllayer_extension_associated_connection_point ON public.ml_layer_extension USING btree (associated_connection_point);
CREATE INDEX ml_layer_extension_fk_ml_layer_extension_egress_bw_profile_id ON public.ml_layer_extension USING btree (egress_bw_profile_id);
CREATE INDEX ml_layer_extension_fk_ml_layer_extension_ingress_bw_profile_id ON public.ml_layer_extension USING btree (ingress_bw_profile_id);
CREATE INDEX ml_log_link_dev_endpointfk_ml_log_link_dev_endpoint_log_link_id ON public.ml_log_link_dev_endpoint USING btree (log_link_id);
CREATE INDEX ix_ml_log_link_oms_prt_path_fk_ml_log_link_oms_prt_path_id ON public.ml_log_link_oms_prt_path USING btree (id);
CREATE INDEX ix_ml_log_link_oms_wkg_path_fk_ml_log_link_oms_wkg_path_id ON public.ml_log_link_oms_wkg_path USING btree (id);
CREATE INDEX mlloglinksuplinkresourcefkmlloglinksuplink_resource_log_link_id ON public.ml_log_link_sup_link_resource USING btree (log_link_id);
CREATE INDEX i_ml_mod_location ON public.ml_module USING btree (location);
CREATE INDEX index_ml_module_ne_id_location ON public.ml_module USING btree (node_id, location);
CREATE INDEX ix_ml_module_fk_ml_module_node_id ON public.ml_module USING btree (node_id);
CREATE INDEX mlmonitoringsectionfk_ml_monitoring_section_ml_parent_entity_id ON public.ml_monitoring_section USING btree (ml_parent_entity_id);
CREATE INDEX ix_ml_monitoring_section_fk_ml_monitoring_section_path ON public.ml_monitoring_section USING btree (path);
CREATE INDEX ix_ml_monitoring_section_fk_ml_monitoring_section_reverse_path ON public.ml_monitoring_section USING btree (reverse_path);
CREATE INDEX mlmnitoringsectionentitiesfkmlmonitoringsectionentitiesentityid ON public.ml_monitoring_section_entities USING btree (entity_id);
CREATE INDEX mlmntrngsctnentitiesfkmlmntrngsctionentitiesmonitoringsectionid ON public.ml_monitoring_section_entities USING btree (monitoring_section_id);
CREATE INDEX ix_ml_mtp_conn_point_fk_ml_mtp_conn_point_conn_id ON public.ml_mtp_conn_point USING btree (conn_id);
CREATE INDEX ix_ml_mtp_conn_point_fk_ml_mtp_conn_point_connpoint_id ON public.ml_mtp_conn_point USING btree (connpoint_id);
CREATE INDEX ix_ml_ni_link_fk_ml_ni_link_a_end_id ON public.ml_ni_link USING btree (a_end_id);
CREATE INDEX ix_ml_ni_link_fk_ml_ni_link_z_end_id ON public.ml_ni_link USING btree (z_end_id);
CREATE INDEX ix_ml_path_fk_ml_path_a_end_id ON public.ml_path USING btree (a_end_id);
CREATE INDEX ix_ml_path_fk_ml_path_z_end_id ON public.ml_path USING btree (z_end_id);
CREATE INDEX ix_ml_path_seq_fk_ml_path_seq_connection_id ON public.ml_path_seq USING btree (connection_id);
CREATE INDEX ix_ml_path_seq_fk_ml_path_seq_path_id ON public.ml_path_seq USING btree (path_id);
CREATE INDEX ix_ml_res_ts_pair_fk_ml_res_ts_pair_res_ref ON public.ml_res_ts_pair USING btree (res_ref);
CREATE INDEX ix_ml_segment_client_fk_ml_segment_client_seg_id ON public.ml_segment_client USING btree (seg_id);
CREATE INDEX ix_ml_segment_client_fk_ml_segment_client_topo_element_id ON public.ml_segment_client USING btree (topo_element_id);
CREATE INDEX ix_ml_segment_entities_fk_ml_segment_entities_entity_id ON public.ml_segment_entities USING btree (entity_id);
CREATE INDEX ix_ml_segment_entities_fk_ml_segment_entities_seg_id ON public.ml_segment_entities USING btree (seg_id);
CREATE INDEX ix_ml_segment_path_fk_ml_segment_path_seg_id ON public.ml_segment_path USING btree (seg_id);
CREATE INDEX ix_ml_segment_path_fk_ml_segment_path_seg_p_id ON public.ml_segment_path USING btree (seg_p_id);
CREATE INDEX ix_ml_ser_view_lines_fk_ml_ser_view_lines_line_id ON public.ml_ser_view_lines USING btree (line_id);
CREATE INDEX ix_ml_ser_view_lines_fk_ml_ser_view_lines_view_id ON public.ml_ser_view_lines USING btree (view_id);
CREATE INDEX ix_ml_ser_view_nes_fk_ml_ser_view_nes_ne_id ON public.ml_ser_view_nes USING btree (ne_id);
CREATE INDEX ix_ml_ser_view_nes_fk_ml_ser_view_nes_view_id ON public.ml_ser_view_nes USING btree (view_id);
CREATE INDEX ix_ml_ser_view_subnets_fk_ml_ser_view_subnets_subnet_id ON public.ml_ser_view_subnets USING btree (subnet_id);
CREATE INDEX ix_ml_ser_view_subnets_fk_ml_ser_view_subnets_view_id ON public.ml_ser_view_subnets USING btree (view_id);
CREATE INDEX ix_ml_service_fk_ml_service_a_end_id ON public.ml_service USING btree (a_end_id);
CREATE INDEX ix_ml_service_fk_ml_service_bk_w_id ON public.ml_service USING btree (bk_w_id);
CREATE INDEX ix_ml_service_fk_ml_service_contract_id ON public.ml_service USING btree (contract_id);
CREATE INDEX ix_ml_service_fk_ml_service_customer_id ON public.ml_service USING btree (customer_id);
CREATE INDEX ix_ml_service_fk_ml_service_fw_w_id ON public.ml_service USING btree (fw_w_id);
CREATE INDEX index_ml_service_layer ON public.ml_service USING btree (layer);
CREATE INDEX ix_ml_service_fk_ml_service_ni_link_id ON public.ml_service USING btree (ni_link_id);
CREATE INDEX ix_ml_service_fk_ml_service_reserved_resource_id ON public.ml_service USING btree (reserved_resource_id);
CREATE INDEX ix_ml_service_fk_ml_service_servicegroup_id ON public.ml_service USING btree (servicegroup_id);
CREATE INDEX ix_ml_service_fk_ml_service_z_end_id ON public.ml_service USING btree (z_end_id);
CREATE INDEX ix_ml_service_endpoint_fk_ml_service_endpoint_connpoint_id ON public.ml_service_endpoint USING btree (connpoint_id);
CREATE INDEX ix_ml_service_endpoint_fk_ml_service_endpoint_service_id ON public.ml_service_endpoint USING btree (service_id);
CREATE INDEX ix_ml_service_entities_fk_ml_service_entities_entity_id ON public.ml_service_entities USING btree (entity_id);
CREATE INDEX ix_ml_service_entities_fk_ml_service_entities_service_id ON public.ml_service_entities USING btree (service_id);
CREATE INDEX ix_ml_te_property_fk_ml_te_property_te_id ON public.ml_te_property USING btree (te_id);
CREATE INDEX i_ml_topo_mo_ref_descr ON public.ml_topo_mo_ref USING btree (descr);
CREATE INDEX i_ml_topo_ref_entityindex ON public.ml_topo_mo_ref USING btree (entityindex);
CREATE INDEX ix_ml_topo_mo_ref_fk_ml_topo_mo_ref_te_id ON public.ml_topo_mo_ref USING btree (te_id);
CREATE INDEX ON public.ml_topology_element USING btree (jdoclass);
CREATE INDEX index_ml_topology_element_label ON public.ml_topology_element USING btree (label);
CREATE INDEX ix_ml_vlan_values_fk_ml_vlan_values_resource_id ON public.ml_vlan_values USING btree (resource_id);
CREATE INDEX i_nc_assigned_snmp_profile_info_neid ON public.nc_assigned_snmp_profile_info USING btree (neid);
CREATE INDEX ncssignedsnmpprofileinfofkncssignedsnmpprofileinfosnmpprofileid ON public.nc_assigned_snmp_profile_info USING btree (snmpprofile_id);
CREATE INDEX ON public.nc_cliprops USING btree (jdoclass);
CREATE INDEX ON public.nc_preferences USING btree (jdoclass);
CREATE INDEX ON public.nc_snmpprops USING btree (jdoclass);
CREATE INDEX ix_pd_cfm_entry_fk_pd_cfm_entry_layer_extension_id ON public.pd_cfm_entry USING btree (layer_extension_id);
CREATE INDEX ix_pd_cfmentry_pvids_fk_pd_cfmentry_pvids_cfm_entry_id ON public.pd_cfmentry_pvids USING btree (cfm_entry_id);
CREATE INDEX ix_pd_cfmentry_rmeps_fk_pd_cfmentry_rmeps_cfm_entry_id ON public.pd_cfmentry_rmeps USING btree (cfm_entry_id);
CREATE INDEX ix_pd_connection_fk_pd_connection_a_end_id ON public.pd_connection USING btree (a_end_id);
CREATE INDEX ix_pd_connection_fk_pd_connection_tot_resource_id ON public.pd_connection USING btree (tot_resource_id);
CREATE INDEX ix_pd_connection_fk_pd_connection_z_end_id ON public.pd_connection USING btree (z_end_id);
CREATE INDEX i_pd_co_location ON public.pd_connection_point USING btree (location);
CREATE INDEX ix_pd_connection_point_fk_pd_connection_point_parent_cp_id ON public.pd_connection_point USING btree (parent_cp_id);
CREATE INDEX ix_pd_connection_point_fk_pd_connection_point_parent_id ON public.pd_connection_point USING btree (parent_id);
CREATE INDEX i_pd_cp_parent_module ON public.pd_connection_point USING btree (parent_module_id);
CREATE INDEX index_pd_connpoint_ne_id_location ON public.pd_connection_point USING btree (parent_node_id, location);
CREATE INDEX i_pd_cp_parent_node ON public.pd_connection_point USING btree (parent_node_id);
CREATE INDEX pdconnectionresourcefkpdconnection_resource_child_vlan_resource ON public.pd_connection_resource USING btree (child_vlan_resource);
CREATE INDEX ix_pd_connection_resource_fk_pd_connection_resource_resource_id ON public.pd_connection_resource USING btree (resource_id);
CREATE INDEX ix_pd_cp_path_seq_fk_pd_cp_path_seq_cp_id ON public.pd_cp_path_seq USING btree (cp_id);
CREATE INDEX ix_pd_cp_path_seq_fk_pd_cp_path_seq_path_id ON public.pd_cp_path_seq USING btree (path_id);
CREATE INDEX ix_pd_cp_term_layers_fk_pd_cp_term_layers_topo_id ON public.pd_cp_term_layers USING btree (topo_id);
CREATE INDEX pdeth_service_intent_paramfk_pd_eth_service_intent_param_ref_id ON public.pd_eth_service_intent_param USING btree (ref_id);
CREATE INDEX pd_eth_service_intent_tp_fk_pd_eth_service_intent_tp_service_id ON public.pd_eth_service_intent_tp USING btree (service_id);
CREATE INDEX pdevpnserviceentitylistfkpdevpnserviceentitylistevpn_service_id ON public.pd_evpn_service_entity_list USING btree (evpn_service_id);
CREATE INDEX pdlayerextpdconnpointfkpdlayerextpdconnpointconnection_point_id ON public.pd_layer_ext_pd_conn_point USING btree (connection_point_id);
CREATE INDEX pdlayerext_pd_conn_pointfk_pd_layer_ext_pd_conn_point_ml_lag_id ON public.pd_layer_ext_pd_conn_point USING btree (ml_lag_id);
CREATE INDEX pdlayerextensionfkpdlayer_extension_associated_connection_point ON public.pd_layer_extension USING btree (associated_connection_point);
CREATE INDEX pd_layer_extension_fk_pd_layer_extension_egress_bw_profile_id ON public.pd_layer_extension USING btree (egress_bw_profile_id);
CREATE INDEX pd_layer_extension_fk_pd_layer_extension_ingress_bw_profile_id ON public.pd_layer_extension USING btree (ingress_bw_profile_id);
CREATE INDEX i_pd_mod_location ON public.pd_module USING btree (location);
CREATE INDEX index_pd_module_ne_id_location ON public.pd_module USING btree (node_id, location);
CREATE INDEX ix_pd_module_fk_pd_module_node_id ON public.pd_module USING btree (node_id);
CREATE INDEX ix_pd_mtp_conn_point_fk_pd_mtp_conn_point_conn_id ON public.pd_mtp_conn_point USING btree (conn_id);
CREATE INDEX ix_pd_mtp_conn_point_fk_pd_mtp_conn_point_connpoint_id ON public.pd_mtp_conn_point USING btree (connpoint_id);
CREATE INDEX ix_pd_path_fk_pd_path_a_end_id ON public.pd_path USING btree (a_end_id);
CREATE INDEX ix_pd_path_fk_pd_path_z_end_id ON public.pd_path USING btree (z_end_id);
CREATE INDEX ix_pd_path_seq_fk_pd_path_seq_connection_id ON public.pd_path_seq USING btree (connection_id);
CREATE INDEX ix_pd_path_seq_fk_pd_path_seq_path_id ON public.pd_path_seq USING btree (path_id);
CREATE INDEX ix_pd_pm_profile_fk_pd_pm_profile_owner ON public.pd_pm_profile USING btree (owner);
CREATE INDEX ix_pd_pm_profile_counter_fk_pd_pm_profile_counter_profile ON public.pd_pm_profile_counter USING btree (profile);
CREATE INDEX ix_pd_ser_view_nes_fk_pd_ser_view_nes_view_id ON public.pd_ser_view_nes USING btree (view_id);
CREATE INDEX ix_pd_ser_view_subnets_fk_pd_ser_view_subnets_view_id ON public.pd_ser_view_subnets USING btree (view_id);
CREATE INDEX ix_pd_service_fk_pd_service_a_end_id ON public.pd_service USING btree (a_end_id);
CREATE INDEX ix_pd_service_fk_pd_service_bk_w_id ON public.pd_service USING btree (bk_w_id);
CREATE INDEX ix_pd_service_fk_pd_service_fw_w_id ON public.pd_service USING btree (fw_w_id);
CREATE INDEX index_pd_service_layer ON public.pd_service USING btree (layer);
CREATE INDEX ix_pd_service_fk_pd_service_reserved_resource_id ON public.pd_service USING btree (reserved_resource_id);
CREATE INDEX ix_pd_service_fk_pd_service_z_end_id ON public.pd_service USING btree (z_end_id);
CREATE INDEX ix_pd_service_endpoint_fk_pd_service_endpoint_connpoint_id ON public.pd_service_endpoint USING btree (connpoint_id);
CREATE INDEX ix_pd_service_endpoint_fk_pd_service_endpoint_service_id ON public.pd_service_endpoint USING btree (service_id);
CREATE INDEX ix_pd_service_entities_fk_pd_service_entities_entity_id ON public.pd_service_entities USING btree (entity_id);
CREATE INDEX ix_pd_service_entities_fk_pd_service_entities_service_id ON public.pd_service_entities USING btree (service_id);
CREATE INDEX ix_pd_te_property_fk_pd_te_property_te_id ON public.pd_te_property USING btree (te_id);
CREATE INDEX i_pd_topo_mo_ref_descr ON public.pd_topo_mo_ref USING btree (descr);
CREATE INDEX i_pd_topo_ref_entityindex ON public.pd_topo_mo_ref USING btree (entityindex);
CREATE INDEX ix_pd_topo_mo_ref_fk_pd_topo_mo_ref_te_id ON public.pd_topo_mo_ref USING btree (te_id);
CREATE INDEX ON public.pd_topology_element USING btree (jdoclass);
CREATE INDEX index_pd_topology_element_label ON public.pd_topology_element USING btree (label);
CREATE INDEX i_pm_manager_cfg_neid ON public.pm_manager_cfg USING btree (id_ne);
CREATE INDEX idx_pm_mapping_ent_tt ON public.pm_mapping USING btree (id_man, timetype);
CREATE INDEX ix_pm_profile_fk_pm_profile_owner ON public.pm_profile USING btree (owner);
CREATE INDEX ix_pm_profile_counter_fk_pm_profile_counter_profile ON public.pm_profile_counter USING btree (profile);
CREATE INDEX idx_pm_record_ts_tt ON public.pm_record USING btree ("timestamp", timetype);
CREATE INDEX idx_pm_record_ent_tt_ ON public.pm_record USING btree (id_entity, timetype);
CREATE INDEX idx_pm_record_neid ON public.pm_record USING btree (id_ne);
CREATE INDEX ON public.pm_template USING btree (jdoclass);
CREATE INDEX ix_pm_template_data_map_fk_pm_template_data_map_id ON public.pm_template_data_map USING btree (id);
CREATE INDEX ON public.pm_template_identifier USING btree (jdoclass);
CREATE INDEX ON public.re_ha_properites USING btree (jdoclass);
CREATE INDEX i_se_action_object_id ON public.se_action USING btree (objectid);
CREATE INDEX ix_se_action_children_fk_se_action_children_children_id ON public.se_action_children USING btree (children_id);
CREATE INDEX ix_se_action_children_fk_se_action_children_id ON public.se_action_children USING btree (id);
CREATE INDEX ix_se_group_user_fk_se_group_user_id ON public.se_group_user USING btree (id);
CREATE INDEX ix_se_group_user_fk_se_group_user_users_id ON public.se_group_user USING btree (users_id);
CREATE INDEX ix_se_mountedtreenodes_fk_se_mountedtreenodes_id ON public.se_mountedtreenodes USING btree (id);
CREATE INDEX ix_se_restrictedtreenodes_fk_se_restrictedtreenodes_id ON public.se_restrictedtreenodes USING btree (id);
CREATE INDEX ix_se_role_children_fk_se_role_children_children_id ON public.se_role_children USING btree (children_id);
CREATE INDEX ix_se_role_children_fk_se_role_children_id ON public.se_role_children USING btree (id);
CREATE INDEX i_se_user_name ON public.se_user USING btree (name0);
CREATE INDEX index_se_viewpoints_id ON public.se_viewpoints USING btree (id);
CREATE INDEX ix_se_visibletreenodes_fk_se_visibletreenodes_id ON public.se_visibletreenodes USING btree (id);
CREATE INDEX ON public.sm_contacts USING btree (jdoclass);
CREATE INDEX sm_customer_contact_link_fk_sm_customer_contact_link_contact_id ON public.sm_customer_contact_link USING btree (contact_id);
CREATE INDEX sm_customer_contact_linkfk_sm_customer_contact_link_customer_id ON public.sm_customer_contact_link USING btree (customer_id);
CREATE INDEX sm_customer_service_group_fk_sm_customer_service_group_customer ON public.sm_customer_service_group USING btree (customer);
CREATE INDEX ON public.sm_customer_service_group USING btree (jdoclass);
CREATE INDEX smcustomerservicegroupfk_sm_customer_service_group_parent_group ON public.sm_customer_service_group USING btree (parent_group);
CREATE INDEX i_sm_csg_parent_id ON public.sm_customer_service_group USING btree (parentid);
CREATE INDEX ix_sm_customers_fk_sm_customers_customer_group ON public.sm_customers USING btree (customer_group);
CREATE INDEX ON public.sm_customers USING btree (jdoclass);
CREATE INDEX ix_sm_service_param_fk_sm_service_param_ref_id ON public.sm_service_param USING btree (ref_id);
CREATE INDEX ix_sm_service_refs_fk_sm_service_refs_service_id ON public.sm_service_refs USING btree (service_id);
CREATE INDEX ix_sm_service_refs_fk_sm_service_refs_serviceref_id ON public.sm_service_refs USING btree (serviceref_id);
CREATE INDEX ix_sm_service_tp_fk_sm_service_tp_service_id ON public.sm_service_tp USING btree (service_id);
CREATE INDEX ON public.sr_build USING btree (jdoclass);
CREATE INDEX ON public.sr_db_version_info USING btree (jdoclass);
CREATE INDEX i_sr_ms_ordering ON public.sr_multi_server USING btree (ordering);
CREATE INDEX syncdupidentity_portfk_sync_dup_identity_port_syncnodedbimpl_id ON public.sync_dup_identity_port USING btree (syncnodedbimpl_id);
CREATE INDEX ix_sync_dup_masters_fk_sync_dup_masters_syncnodedbimpl_id ON public.sync_dup_masters USING btree (syncnodedbimpl_id);
CREATE INDEX ON public.sync_ncd USING btree (jdoclass);
CREATE INDEX ix_sync_node_fk_sync_node_active_freq_master ON public.sync_node USING btree (active_freq_master);
CREATE INDEX ix_sync_node_fk_sync_node_active_master ON public.sync_node USING btree (active_master);
CREATE INDEX ix_sync_node_fk_sync_node_bc ON public.sync_node USING btree (bc);
CREATE INDEX ON public.sync_node USING btree (jdoclass);
CREATE INDEX ix_sync_node_fk_sync_node_mc ON public.sync_node USING btree (mc);
CREATE INDEX ix_sync_node_fk_sync_node_ncd_id ON public.sync_node USING btree (ncd_id);
CREATE INDEX ix_sync_node_ne_id ON public.sync_node USING btree (ne_id);
CREATE INDEX ix_sync_node_fk_sync_node_oc_slave ON public.sync_node USING btree (oc_slave);
CREATE INDEX ix_sync_node_fk_sync_node_ptp_clock ON public.sync_node USING btree (ptp_clock);
CREATE INDEX ix_sync_node_fk_sync_node_sync_entity ON public.sync_node USING btree (sync_entity);
CREATE INDEX ix_sync_node_fk_sync_node_tc ON public.sync_node USING btree (tc);
CREATE INDEX ix_sync_node_masters_fk_sync_node_masters_sync_master ON public.sync_node_masters USING btree (sync_master);
CREATE INDEX ix_sync_node_masters_fk_sync_node_masters_sync_node ON public.sync_node_masters USING btree (sync_node);
CREATE INDEX ix_sync_route_member_nodes_fk_sync_route_member_nodes_node_id ON public.sync_route_member_nodes USING btree (node_id);
CREATE INDEX ix_sync_route_member_nodes_fk_sync_route_member_nodes_route_id ON public.sync_route_member_nodes USING btree (route_id);
CREATE INDEX i_syncctn_dtype ON public.sync_tc_location USING btree (dtype);
CREATE INDEX ix_sync_tc_location_fk_sync_tc_location_master ON public.sync_tc_location USING btree (master);
CREATE INDEX ix_sync_tc_location_fk_sync_tc_location_parent_tc ON public.sync_tc_location USING btree (parent_tc);
CREATE INDEX ix_sync_tc_location_fk_sync_tc_location_slave ON public.sync_tc_location USING btree (slave);
CREATE INDEX ix_sync_tc_location_fk_sync_tc_location_tc_id ON public.sync_tc_location USING btree (tc_id);
CREATE INDEX i_synctst_dtype ON public.sync_test USING btree (dtype);
CREATE INDEX ix_sync_test_fk_sync_test_ncd ON public.sync_test USING btree (ncd);
CREATE INDEX ix_sync_test_fk_sync_test_ne ON public.sync_test USING btree (ne);
CREATE INDEX ix_sync_test_fk_sync_test_nms_probe_history ON public.sync_test USING btree (nms_probe_history);
CREATE INDEX ix_sync_test_fk_sync_test_owner ON public.sync_test USING btree (owner);
CREATE INDEX ix_sync_test_fk_sync_test_probe ON public.sync_test USING btree (probe);
CREATE INDEX ix_sync_test_fk_sync_test_probe_history ON public.sync_test USING btree (probe_history);
CREATE INDEX synctestnms_creation_paramsfk_sync_test_nms_creation_params_ncd ON public.sync_test_nms_creation_params USING btree (ncd);
CREATE INDEX synctestnmscreationparamsfk_sync_test_nms_creation_params_owner ON public.sync_test_nms_creation_params USING btree (owner);
CREATE INDEX ix_ui_template_props_fk_ui_template_props_templatedatadbimpl_id ON public.ui_template_props USING btree (templatedatadbimpl_id);
CREATE INDEX ix_ui_usr_sec_fk_ui_usr_sec_usrid ON public.ui_usr_sec USING btree (usrid);
CREATE INDEX ix_ui_usr_sec_props_fk_ui_usr_sec_props_sectiondatadbimpl_id ON public.ui_usr_sec_props USING btree (sectiondatadbimpl_id);
ALTER TABLE ONLY public.cn_entity
    ADD CONSTRAINT cn_entity_ibfk_1 FOREIGN KEY (parent_id) REFERENCES public.cn_entity(id);

ALTER TABLE ONLY public.cn_network_element
    ADD CONSTRAINT cn_network_element_fk_p2l FOREIGN KEY (local_id) REFERENCES public.cn_network_element(id);

ALTER TABLE ONLY public.ec_ne_types
    ADD CONSTRAINT ec_ne_types_file_id_fkey FOREIGN KEY (file_id) REFERENCES public.ec_template_files(file_id) DEFERRABLE;
