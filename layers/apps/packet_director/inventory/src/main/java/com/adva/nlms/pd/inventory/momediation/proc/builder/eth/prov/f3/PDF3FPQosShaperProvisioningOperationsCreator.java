/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: tomaszd
 */
package com.adva.nlms.pd.inventory.momediation.proc.builder.eth.prov.f3;

import com.adva.nlms.common.config.EntityIndex;
import com.adva.nlms.common.config.f3.EntityIndexPrefix;
import com.adva.nlms.common.mltopologymodel.pce.layerextensions.ProvisioningEthLayerExtensionDTO;
import com.adva.nlms.mediation.config.dto.DTO;
import com.adva.nlms.mediation.config.dto.DTOBuilder;
import com.adva.nlms.mediation.config.dto.attr.F3FpQosShaperAttr;
import com.adva.nlms.mediation.config.dto.attr.ManagedObjectAttr;
import com.adva.nlms.mediation.config.dto.attr.QOSAccFlowPointShaperF3Attr;
import com.adva.nlms.mediation.config.dto.attr.QOSAccPortFPShaperF3Attr;
import com.adva.nlms.mediation.config.dto.attr.QOSNetFlowPointShaperF3Attr;
import com.adva.nlms.mediation.config.dto.attr.QOSNetPortFPShaperF3Attr;
import com.adva.nlms.mediation.config.dto.attr.QOSTrafficPortShaperF3Attr;
import com.adva.nlms.mediation.config.f3.entity.shaper.accportfpshaper.QOSAccPortFPShaperF3DAO;
import com.adva.nlms.mediation.config.f3.entity.shaper.accportfpshaper.QOSAccPortFPShaperF3DBImpl;
import com.adva.nlms.mediation.config.f3.entity.shaper.netportfpshaper.QOSNetPortFPShaperF3DBImpl;
import com.adva.nlms.mediation.config.f3.entity.shaper.qostrafficportshaper.QOSTrafficPortShaperF3DBImpl;
import com.adva.nlms.mediation.config.nettransaction.Operation;
import com.adva.nlms.mediation.config.nettransaction.OperationType;
import com.adva.nlms.pd.inventory.momediation.proc.builder.eth.prov.PDEthCrossConnectOperationStateMachine;
import com.adva.nlms.pd.inventory.momediation.proc.builder.eth.utils.PDOperationEssentials;
import com.adva.nlms.pd.inventory.momediation.proc.builder.eth.utils.PDRollbackDTO;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import jakarta.validation.constraints.NotNull;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

public class PDF3FPQosShaperProvisioningOperationsCreator {

  private static final Logger log = LogManager.getLogger(PDF3FPQosShaperProvisioningOperationsCreator.class);

  private final PDBufferSizeCalculator bsCalculator;
  private final int neId;
  private final PDEthCrossConnectOperationStateMachine ethStateMachine;
  private final PDF3QosProvisioningHelper f3QosProvisioningHelper;
  private final QOSAccPortFPShaperF3DAO qosAccPortFPShaperF3DAO;


  public PDF3FPQosShaperProvisioningOperationsCreator(PDBufferSizeCalculator bsCalculator, int neId,
                                                      PDEthCrossConnectOperationStateMachine ethStateMachine,
                                                      QOSAccPortFPShaperF3DAO qosAccPortFPShaperF3DAO) {
    this.bsCalculator = bsCalculator;
    this.neId = neId;
    this.ethStateMachine = ethStateMachine;
    this.qosAccPortFPShaperF3DAO = qosAccPortFPShaperF3DAO;
    f3QosProvisioningHelper = new PDF3QosProvisioningHelper(this.qosAccPortFPShaperF3DAO);
  }

  void addFPShaperOperations(List<Operation> operations, EntityIndex flowPointEI,
                             ProvisioningEthLayerExtensionDTO ethExtension) {
    DTO<? extends ManagedObjectAttr> qosShaperDtoToUpdate = prepareQosShaperDto(ethExtension,flowPointEI);
    operations.add(new Operation(OperationType.Modify, qosShaperDtoToUpdate, neId));
  }

  DTO<? extends ManagedObjectAttr> prepareQosShaperDto(ProvisioningEthLayerExtensionDTO extension, EntityIndex fpEI) {

    int[] qosShaperIndexArray = f3QosProvisioningHelper.createShaperOrPolicerIndexArray(fpEI);
    if (fpEI.getCategory() == EntityIndexPrefix.AccessFlowPoint.getCategory()) {
      return createAccessFpShaperOperations(extension, qosShaperIndexArray);
    } else if (fpEI.getCategory() == EntityIndexPrefix.NetworkFlowPoint.getCategory()) {
      return createNetworkFpShaperOperations(extension, qosShaperIndexArray);
    } else {
      return createTrafficFpShaperOperations(extension, qosShaperIndexArray);
    }
  }

  @NotNull
  private DTO<F3FpQosShaperAttr> createTrafficFpShaperOperations(ProvisioningEthLayerExtensionDTO extension, int[] qosShaperIndexArray) {
    DTO<F3FpQosShaperAttr> dto = DTOBuilder.getInstance().newDTO(F3FpQosShaperAttr.class);
    dto.putOrReplace(F3FpQosShaperAttr.ENTITY_INDEX, new EntityIndex(EntityIndexPrefix.F3FpQosShaperTable.getCategory(),
            qosShaperIndexArray));
    if (extension != null) {
      dto.putOrReplace(F3FpQosShaperAttr.CIR_COMPOSITE, extension.getEgressBwProfile().getCir());
      dto.putOrReplace(F3FpQosShaperAttr.CBS, extension.getEgressBwProfile().getCbs());
      dto.putOrReplace(F3FpQosShaperAttr.EIR_COMPOSITE, extension.getEgressBwProfile().getEir());
      dto.putOrReplace(F3FpQosShaperAttr.EBS, extension.getEgressBwProfile().getEbs());
      dto.putOrReplace(F3FpQosShaperAttr.BUFFER_SIZE, extension.getEgressBwProfile().getBufferSize());
    }
    return dto;
  }

  @NotNull
  private DTO<QOSNetFlowPointShaperF3Attr> createNetworkFpShaperOperations(ProvisioningEthLayerExtensionDTO extension, int[] qosShaperIndexArray) {
    DTO<QOSNetFlowPointShaperF3Attr> dto = DTOBuilder.getInstance().newDTO(QOSNetFlowPointShaperF3Attr.class);
    dto.putOrReplace(QOSNetFlowPointShaperF3Attr.ENTITY_INDEX,
            new EntityIndex(EntityIndexPrefix.NetworkFlowPointShaper.getCategory(),
                    qosShaperIndexArray));
    if (extension != null) {
      dto.putOrReplace(QOSNetFlowPointShaperF3Attr.CIR_COMPOSITE, extension.getEgressBwProfile().getCir());
      dto.putOrReplace(QOSNetFlowPointShaperF3Attr.CBS, extension.getEgressBwProfile().getCbs());
      dto.putOrReplace(QOSNetFlowPointShaperF3Attr.EIR_COMPOSITE, extension.getEgressBwProfile().getEir());
      dto.putOrReplace(QOSNetFlowPointShaperF3Attr.EBS, extension.getEgressBwProfile().getEbs());
      dto.putOrReplace(QOSNetFlowPointShaperF3Attr.BUFFER_SIZE, extension.getEgressBwProfile().getBufferSize());
    }
    return dto;
  }

  @NotNull
  private DTO<QOSAccFlowPointShaperF3Attr> createAccessFpShaperOperations(ProvisioningEthLayerExtensionDTO extension, int[] qosShaperIndexArray) {
    DTO<QOSAccFlowPointShaperF3Attr> dto = DTOBuilder.getInstance().newDTO(QOSAccFlowPointShaperF3Attr.class);
    dto.putOrReplace(QOSAccFlowPointShaperF3Attr.ENTITY_INDEX,
            new EntityIndex(EntityIndexPrefix.AccessFlowPointShaper.getCategory(), qosShaperIndexArray));
    if (extension != null) {
      dto.putOrReplace(QOSAccFlowPointShaperF3Attr.CIR_COMPOSITE, extension.getEgressBwProfile().getCir());
      dto.putOrReplace(QOSAccFlowPointShaperF3Attr.CBS, extension.getEgressBwProfile().getCbs());
      dto.putOrReplace(QOSAccFlowPointShaperF3Attr.EIR_COMPOSITE, extension.getEgressBwProfile().getEir());
      dto.putOrReplace(QOSAccFlowPointShaperF3Attr.EBS, extension.getEgressBwProfile().getEbs());
      dto.putOrReplace(QOSAccFlowPointShaperF3Attr.BUFFER_SIZE,
              extension.getEgressBwProfile().getBufferSize());
    }
    return dto;
  }

  void addPortShaperOperations(List<Operation> operations, EntityIndex flowPointEI,
                                                         ProvisioningEthLayerExtensionDTO extensionDTO){

    final int SHAPER_INDEX = 1;
    int [] qosShaperIndexArray = new int[]
            {flowPointEI.get(0), flowPointEI.get(1),flowPointEI.get(2),flowPointEI.get(3), SHAPER_INDEX};
    if (flowPointEI.getCategory() == EntityIndexPrefix.AccessFlowPoint.getCategory()) {
      addAccPortFpProvisioningAndRollbackOperations(operations, extensionDTO, qosShaperIndexArray);
    } else if (flowPointEI.getCategory() == EntityIndexPrefix.FlowPoint.getCategory()){
      addTrafficPortFpProvisioningAndRollbackOperations(operations, extensionDTO, qosShaperIndexArray);
    } else if (flowPointEI.getCategory() == EntityIndexPrefix.NetworkFlowPoint.getCategory()){
      addNetPortFpProvisioningAndRollbackOperations(operations, extensionDTO, qosShaperIndexArray);
    }
  }

  private void addTrafficPortFpProvisioningAndRollbackOperations(List<Operation> operations, ProvisioningEthLayerExtensionDTO extensionDTO, int[] qosShaperIndexArray) {
    DTO<QOSTrafficPortShaperF3Attr> dto = DTOBuilder.getInstance().newDTO(QOSTrafficPortShaperF3Attr.class);
    EntityIndex shaperEI = new EntityIndex(EntityIndexPrefix.TrafficPortQOSShaper.getCategory(), qosShaperIndexArray);
    dto.putOrReplace(ManagedObjectAttr.ENTITY_INDEX, shaperEI);
    Optional<QOSTrafficPortShaperF3DBImpl> shaper = f3QosProvisioningHelper.getQOSTrafficPortFPShaperForPort(shaperEI, neId);
    if (shaper.isPresent()) {
      modifyExistingTrafficPortShaperDto(dto, shaper.get(), extensionDTO);
      operations.add(new Operation(OperationType.Modify, dto, neId));
      ethStateMachine.addToOperationCache(dto.getValue(ManagedObjectAttr.ENTITY_INDEX).toString(),
              new PDOperationEssentials(prepareModifyTrafficFpShaperRollbackDto(dto, shaper.get())));
    } else {
      createNewTrafficFpShaperDto(dto, extensionDTO);
      operations.add(new Operation(OperationType.Create, dto, neId));
      ethStateMachine.addToOperationCache(dto.getValue(ManagedObjectAttr.ENTITY_INDEX).toString(),
              new PDOperationEssentials(prepareCreateTrafficFpShaperRollbackDto(dto)));
    }
  }

  private void addNetPortFpProvisioningAndRollbackOperations(List<Operation> operations, ProvisioningEthLayerExtensionDTO extensionDTO, int[] qosShaperIndexArray) {
    DTO<QOSNetPortFPShaperF3Attr> dto = DTOBuilder.getInstance().newDTO(QOSNetPortFPShaperF3Attr.class);
    EntityIndex shaperEI = new EntityIndex(EntityIndexPrefix.NetworkPortFPShaper.getCategory(), qosShaperIndexArray);
    dto.putOrReplace(ManagedObjectAttr.ENTITY_INDEX, shaperEI);
    Optional<QOSNetPortFPShaperF3DBImpl> shaper = f3QosProvisioningHelper.getQOSNetPortFPShaperForPort(shaperEI, neId, log);
    if (shaper.isPresent()) {
      modifyExistingNetShaperDto(dto, shaper.get(), extensionDTO);
      operations.add(new Operation(OperationType.Modify, dto, neId));
      ethStateMachine.addToOperationCache(dto.getValue(ManagedObjectAttr.ENTITY_INDEX).toString(),
              new PDOperationEssentials(prepareModifyNetPortFpShaperRollbackDto(dto, shaper.get())));
    } else {
      createNewNetFpShaperDto(dto, extensionDTO);
      operations.add(new Operation(OperationType.Create, dto, neId));
      ethStateMachine.addToOperationCache(dto.getValue(ManagedObjectAttr.ENTITY_INDEX).toString(),
              new PDOperationEssentials(prepareCreateNetFpShaperRollbackDto(dto)));
    }
  }

  private void addAccPortFpProvisioningAndRollbackOperations(List<Operation> operations, ProvisioningEthLayerExtensionDTO extensionDTO, int[] qosShaperIndexArray) {
    DTO<QOSAccPortFPShaperF3Attr> dto = DTOBuilder.getInstance().newDTO(QOSAccPortFPShaperF3Attr.class);
    EntityIndex shaperEI = new EntityIndex(EntityIndexPrefix.AccessPortFPShaper.getCategory(), qosShaperIndexArray);
    dto.putOrReplace(QOSAccPortFPShaperF3Attr.ENTITY_INDEX, shaperEI);
    Optional<QOSAccPortFPShaperF3DBImpl> shaper = f3QosProvisioningHelper.getQOSAccPortFPShaperForPort(shaperEI, neId, log);
    if (shaper.isPresent()) {
      modifyExistingAccShaperDto(dto, shaper.get(), extensionDTO);
      operations.add(new Operation(OperationType.Modify, dto, neId));
      ethStateMachine.addToOperationCache(dto.getValue(ManagedObjectAttr.ENTITY_INDEX).toString(),
              new PDOperationEssentials(prepareModifyAccPortFpShaperRollbackDto(dto, shaper.get())));
    } else {
      createNewAccFpShaperDto(dto, extensionDTO);
      operations.add(new Operation(OperationType.Create, dto, neId));
      ethStateMachine.addToOperationCache(dto.getValue(ManagedObjectAttr.ENTITY_INDEX).toString(),
              new PDOperationEssentials(prepareCreateAccFpShaperRollbackDto(dto)));
    }
  }

  private List<PDRollbackDTO> prepareModifyAccPortFpShaperRollbackDto(DTO<QOSAccPortFPShaperF3Attr> dto, QOSAccPortFPShaperF3DBImpl shaper) {
    List<PDRollbackDTO> rollbackDTOs = new ArrayList<>();
    DTO<? extends QOSAccPortFPShaperF3Attr> shaperRollbackDto = DTOBuilder.getInstance().newDTO(dto.getAttributesGroupClass());
    shaperRollbackDto.putOrReplace(ManagedObjectAttr.ENTITY_INDEX, dto.getValue(ManagedObjectAttr.ENTITY_INDEX));
    shaperRollbackDto.putOrReplace(QOSAccPortFPShaperF3Attr.CIR_COMPOSITE, shaper.getCIR());
    shaperRollbackDto.putOrReplace(QOSAccPortFPShaperF3Attr.EIR_COMPOSITE, shaper.getEIR());
    shaperRollbackDto.putOrReplace(QOSAccPortFPShaperF3Attr.BUFFER_SIZE, shaper.getBufferSize());
    rollbackDTOs.add(new PDRollbackDTO(OperationType.Modify, shaperRollbackDto));
    return rollbackDTOs;
  }

  private List<PDRollbackDTO> prepareModifyNetPortFpShaperRollbackDto(DTO<QOSNetPortFPShaperF3Attr> dto, QOSNetPortFPShaperF3DBImpl shaper) {
    List<PDRollbackDTO> rollbackDTOs = new ArrayList<>();
    DTO<? extends QOSNetPortFPShaperF3Attr> shaperRollbackDto = DTOBuilder.getInstance().newDTO(dto.getAttributesGroupClass());
    shaperRollbackDto.putOrReplace(ManagedObjectAttr.ENTITY_INDEX, dto.getValue(ManagedObjectAttr.ENTITY_INDEX));
    shaperRollbackDto.putOrReplace(QOSNetPortFPShaperF3Attr.CIR_COMPOSITE, shaper.getCIR());
    shaperRollbackDto.putOrReplace(QOSNetPortFPShaperF3Attr.EIR_COMPOSITE, shaper.getEIR());
    shaperRollbackDto.putOrReplace(QOSNetPortFPShaperF3Attr.BUFFER_SIZE, shaper.getBufferSize());
    rollbackDTOs.add(new PDRollbackDTO(OperationType.Modify, shaperRollbackDto));
    return rollbackDTOs;
  }


  private List<PDRollbackDTO> prepareModifyTrafficFpShaperRollbackDto(DTO<QOSTrafficPortShaperF3Attr> dto, QOSTrafficPortShaperF3DBImpl shaper) {
    List<PDRollbackDTO> rollbackDTOs = new ArrayList<>();
    DTO<? extends QOSTrafficPortShaperF3Attr> shaperRollbackDto = DTOBuilder.getInstance().newDTO(dto.getAttributesGroupClass());
    shaperRollbackDto.putOrReplace(ManagedObjectAttr.ENTITY_INDEX, dto.getValue(ManagedObjectAttr.ENTITY_INDEX));
    shaperRollbackDto.putOrReplace(QOSTrafficPortShaperF3Attr.CIR_COMPOSITE, shaper.getCIR());
    shaperRollbackDto.putOrReplace(QOSTrafficPortShaperF3Attr.EIR_COMPOSITE, shaper.getEIR());
    shaperRollbackDto.putOrReplace(QOSTrafficPortShaperF3Attr.BUFFER_SIZE, shaper.getBufferSize());
    rollbackDTOs.add(new PDRollbackDTO(OperationType.Modify, shaperRollbackDto));
    return rollbackDTOs;
  }

  private List<PDRollbackDTO> prepareCreateAccFpShaperRollbackDto(DTO<QOSAccPortFPShaperF3Attr> dto) {
    List<PDRollbackDTO> rollbackDTOs = new ArrayList<>();
    DTO<? extends QOSAccPortFPShaperF3Attr> shaperRollbackDto = DTOBuilder.getInstance().newDTO(dto.getAttributesGroupClass());
    shaperRollbackDto.putOrReplace(ManagedObjectAttr.ENTITY_INDEX, dto.getValue(ManagedObjectAttr.ENTITY_INDEX));
    rollbackDTOs.add(new PDRollbackDTO(OperationType.Delete, shaperRollbackDto));
    return rollbackDTOs;
  }

  private List<PDRollbackDTO> prepareCreateNetFpShaperRollbackDto(DTO<QOSNetPortFPShaperF3Attr> dto) {
    List<PDRollbackDTO> rollbackDTOs = new ArrayList<>();
    DTO<? extends QOSNetPortFPShaperF3Attr> shaperRollbackDto = DTOBuilder.getInstance().newDTO(dto.getAttributesGroupClass());
    shaperRollbackDto.putOrReplace(ManagedObjectAttr.ENTITY_INDEX, dto.getValue(ManagedObjectAttr.ENTITY_INDEX));
    rollbackDTOs.add(new PDRollbackDTO(OperationType.Delete, shaperRollbackDto));
    return rollbackDTOs;
  }

  private List<PDRollbackDTO> prepareCreateTrafficFpShaperRollbackDto(DTO<QOSTrafficPortShaperF3Attr> dto) {
    List<PDRollbackDTO> rollbackDTOs = new ArrayList<>();
    DTO<? extends QOSTrafficPortShaperF3Attr> shaperRollbackDto = DTOBuilder.getInstance().newDTO(dto.getAttributesGroupClass());
    shaperRollbackDto.putOrReplace(ManagedObjectAttr.ENTITY_INDEX, dto.getValue(ManagedObjectAttr.ENTITY_INDEX));
    rollbackDTOs.add(new PDRollbackDTO(OperationType.Delete, shaperRollbackDto));
    return rollbackDTOs;
  }


  private void createNewAccFpShaperDto(DTO<QOSAccPortFPShaperF3Attr> dto, ProvisioningEthLayerExtensionDTO extension) {
    dto.putOrReplace(QOSAccPortFPShaperF3Attr.CIR_COMPOSITE, extension.getEgressBwProfile().getCir());
    dto.putOrReplace(QOSAccPortFPShaperF3Attr.EIR_COMPOSITE, extension.getEgressBwProfile().getEir());
    dto.putOrReplace(QOSAccPortFPShaperF3Attr.BUFFER_SIZE, extension.getEgressBwProfile().getBufferSize());
  }

  private void createNewNetFpShaperDto(DTO<QOSNetPortFPShaperF3Attr> dto, ProvisioningEthLayerExtensionDTO extension) {
    dto.putOrReplace(QOSNetPortFPShaperF3Attr.CIR_COMPOSITE, extension.getEgressBwProfile().getCir());
    dto.putOrReplace(QOSNetPortFPShaperF3Attr.EIR_COMPOSITE, extension.getEgressBwProfile().getEir());
    dto.putOrReplace(QOSNetPortFPShaperF3Attr.BUFFER_SIZE, extension.getEgressBwProfile().getBufferSize());
  }

  private void createNewTrafficFpShaperDto(DTO<QOSTrafficPortShaperF3Attr> dto, ProvisioningEthLayerExtensionDTO extension) {
    dto.putOrReplace(QOSTrafficPortShaperF3Attr.CIR_COMPOSITE, extension.getEgressBwProfile().getCir());
    dto.putOrReplace(QOSTrafficPortShaperF3Attr.EIR_COMPOSITE, extension.getEgressBwProfile().getEir());
    dto.putOrReplace(QOSTrafficPortShaperF3Attr.BUFFER_SIZE, extension.getEgressBwProfile().getBufferSize());
  }

  private void modifyExistingTrafficPortShaperDto(DTO<QOSTrafficPortShaperF3Attr> dto, QOSTrafficPortShaperF3DBImpl shaperF3DB,
                                                  ProvisioningEthLayerExtensionDTO extension) {
    dto.putOrReplace(QOSTrafficPortShaperF3Attr.CIR_COMPOSITE, shaperF3DB.getCIR() + extension.getEgressBwProfile().getCir());
    dto.putOrReplace(QOSTrafficPortShaperF3Attr.EIR_COMPOSITE, shaperF3DB.getEIR() + extension.getEgressBwProfile().getEir());
    dto.putOrReplace(QOSTrafficPortShaperF3Attr.BUFFER_SIZE, extension.getEgressBwProfile().getBufferSize());
  }

  private void modifyExistingAccShaperDto(DTO<QOSAccPortFPShaperF3Attr> dto, QOSAccPortFPShaperF3DBImpl qosAccPortFPShaperF3DB,
                                                  ProvisioningEthLayerExtensionDTO extension) {
    dto.putOrReplace(QOSAccPortFPShaperF3Attr.CIR_COMPOSITE, qosAccPortFPShaperF3DB.getCIR() + extension.getEgressBwProfile().getCir());
    dto.putOrReplace(QOSAccPortFPShaperF3Attr.EIR_COMPOSITE, qosAccPortFPShaperF3DB.getEIR() + extension.getEgressBwProfile().getEir());
    dto.putOrReplace(QOSAccPortFPShaperF3Attr.BUFFER_SIZE, extension.getEgressBwProfile().getBufferSize());
  }

  private void modifyExistingNetShaperDto(DTO<QOSNetPortFPShaperF3Attr> dto, QOSNetPortFPShaperF3DBImpl qosNetPortFPShaperF3DB,
                                          ProvisioningEthLayerExtensionDTO extension) {
    dto.putOrReplace(QOSNetPortFPShaperF3Attr.CIR_COMPOSITE, qosNetPortFPShaperF3DB.getCIR() + extension.getEgressBwProfile().getCir());
    dto.putOrReplace(QOSNetPortFPShaperF3Attr.EIR_COMPOSITE, qosNetPortFPShaperF3DB.getEIR() + extension.getEgressBwProfile().getEir());
    dto.putOrReplace(QOSNetPortFPShaperF3Attr.BUFFER_SIZE, extension.getEgressBwProfile().getBufferSize());
  }
}
