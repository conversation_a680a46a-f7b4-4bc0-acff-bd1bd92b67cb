/*
 * Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 * Owner: tomaszw
 */

package com.adva.nlms.pd.inventory.api.gui.entities;

import com.adva.apps.sm.Definition;
import com.adva.nlms.pd.api.in.dto.enums.PDTEPropertyKey;
import com.adva.nlms.pd.api.in.gui.dto.PDEntitiesPageDTO;
import com.adva.nlms.pd.inventory.model.db.PDEvpnServiceDBImpl;
import com.adva.nlms.pd.inventory.model.dao.PDTopologyElementDAO;
import com.adva.nlms.pd.inventory.model.db.PDConnectionDBImpl;
import com.adva.nlms.pd.inventory.model.db.PDConnectionPointDBImpl;
import com.adva.nlms.pd.inventory.model.db.PDElanServiceDBImpl;
import com.adva.nlms.pd.inventory.model.db.PDServiceDBImpl;
import com.adva.nlms.pd.inventory.model.db.PDTopologyElementDBImpl;
import com.adva.nlms.pd.inventory.model.db.PDTrailDBImpl;
import com.adva.nlms.pd.inventory.model.interfaces.PDConnection;
import com.adva.nlms.pd.inventory.model.interfaces.PDConnectionPoint;
import com.adva.nlms.pd.inventory.model.interfaces.PDTopologyElement;
import com.adva.nlms.pd.inventory.mofacade.PDMoReferenceHelper;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Component;

import jakarta.annotation.Nullable;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;


@Component
public class PDEntitiesModelTabBuilder {

  private static final Logger log = LogManager.getLogger(PDEntitiesModelTabBuilder.class);
  protected final PDTopologyElementDAO pdTopologyElementDAO;
  protected final PDMoReferenceHelper pdMoReferenceHelper;
  protected final PDEntitiesBuilderImpl pdEntitiesBuilder;

  public PDEntitiesModelTabBuilder(PDTopologyElementDAO pdTopologyElementDAO,
                                   PDMoReferenceHelper pdMoReferenceHelper,
                                   PDEntitiesBuilderImpl pdEntitiesBuilder){
    this.pdTopologyElementDAO = pdTopologyElementDAO;
    this.pdMoReferenceHelper = pdMoReferenceHelper;
    this.pdEntitiesBuilder = pdEntitiesBuilder;
  }

  enum EndPointType {
    AEND,
    ZEND,
    END
  }

  public List<PDEntitiesPageDTO> constructData(int serviceId) {
    PDServiceDBImpl pdServiceDB = getService(serviceId);
    List<PDEntitiesPageDTO> data=new ArrayList<>();
    if (pdServiceDB instanceof PDTrailDBImpl) {
      data = new PDTrailEntitiesModelTabBuilder(pdTopologyElementDAO, pdMoReferenceHelper, pdEntitiesBuilder)
              .build(pdServiceDB);
    } else if (pdServiceDB instanceof PDElanServiceDBImpl) {
      data = new PDElanEntitiesModelTabBuilder(pdTopologyElementDAO, pdMoReferenceHelper, pdEntitiesBuilder)
              .build(pdServiceDB);
    } else if(pdServiceDB instanceof PDEvpnServiceDBImpl) {
      data = new PDEvpnEntitiesModelTabBuilder(pdTopologyElementDAO, pdMoReferenceHelper, pdEntitiesBuilder)
              .build(pdServiceDB);
    }
    return data;
  }


  protected Definition.ServiceType retrieveServiceType(PDServiceDBImpl pdServiceDB) {
      return Optional.ofNullable(pdServiceDB.getPropertyOrNull(PDTEPropertyKey.SERVICE_TYPE, String.class))
              .map(Definition.ServiceType::valueOfString).orElse(Definition.ServiceType.ETHERNET);
  }

  protected void addLagMembers(List<PDTopologyElementDBImpl> entitiesList, Map<Integer, EndPointType> endpoints) {
    List<PDTopologyElementDBImpl> result = new LinkedList<>();
    for (PDTopologyElementDBImpl elementDB : entitiesList)  {
      result.add(elementDB);
      if (elementDB instanceof PDConnectionPointDBImpl) {
        Set<PDConnectionPointDBImpl> lagMembers = pdMoReferenceHelper.getLagMembers((PDConnectionPointDBImpl)elementDB);
        result.addAll(lagMembers);
        if (!lagMembers.isEmpty() && endpoints.containsKey(elementDB.getId())) {
          EndPointType typeToFill = endpoints.get(elementDB.getId());
          lagMembers.forEach(lagMember -> endpoints.putIfAbsent(lagMember.getId(), typeToFill));
        }
      }
    }
    entitiesList.clear();
    entitiesList.addAll(result);
  }

  protected boolean isRelatedToAccessFlow(PDConnectionPoint mlConnectionPoint) {
    return pdTopologyElementDAO.getConnectionsByEndpoint((PDConnectionPointDBImpl) mlConnectionPoint, PDConnectionDBImpl.class, null)
            .stream().anyMatch(this::isAccessFlow);
  }

  protected boolean isAccessFlow(PDConnectionDBImpl crs) {
    return crs.getPropertyOrNull(PDTEPropertyKey.ACCESS_FLOW_MODULE) != null;
  }

  protected PDConnection getRelatedConnection(PDConnectionPoint mlConnectionPoint, List<PDConnection> mlConnections) {
    PDConnection relatedConnection = null;
    for (PDConnection connection : mlConnections) {
      if (isEthCrs(connection) && connection.allConnectionPoints().contains(mlConnectionPoint)) {
        relatedConnection = connection;
      }
    }
    return relatedConnection;
  }

  @Nullable
  private PDServiceDBImpl getService(int serviceId) {
    PDServiceDBImpl serviceByID = pdTopologyElementDAO.getServiceByID(serviceId);
    if (serviceByID == null) {
      log.debug("Could not get PD service with ID {}", serviceId);
    }
    return serviceByID;
  }

  private boolean isEthCrs(PDTopologyElement pdTopologyElement) {
    return pdTopologyElement instanceof PDConnectionDBImpl pdConnectionDB && pdConnectionDB.isCrossConnect();
  }

}
