/*
 * Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 * Owner: PawelKr
 */

package com.adva.nlms.pd.inventory.momediation.proc.builder.services;

import com.adva.nlms.pd.inventory.model.db.PDEvpnServiceDBImpl;
import com.adva.nlms.pd.inventory.model.dao.PDTopologyElementDAO;
import com.adva.nlms.pd.inventory.model.db.PDElanServiceDBImpl;
import com.adva.nlms.pd.inventory.model.db.PDServiceDBImpl;
import com.adva.nlms.pd.inventory.model.db.PDTrailDBImpl;
import com.adva.nlms.pd.inventory.momediation.proc.builder.services.elan.PDDetailedElanDTOBuilder;
import com.adva.nlms.pd.inventory.momediation.proc.builder.services.evpn.PDDetailedEvpnDTOBuilder;
import com.adva.nlms.pd.inventory.momediation.proc.builder.services.trail.PDDetailedTrailDTOBuilder;

public class PDDetailedServiceDTOBuilder {

    private final PDTopologyElementDAO pdTopologyElementDAO;

    public PDDetailedServiceDTOBuilder(PDTopologyElementDAO pdTopologyElementDAO) {
        this.pdTopologyElementDAO = pdTopologyElementDAO;
    }

    public PDDetailedServiceDTO build(PDServiceDBImpl pdServiceDB) throws NotSupportedDetailedServiceException {
        if (pdServiceDB instanceof PDTrailDBImpl) {
            return new PDDetailedTrailDTOBuilder(pdTopologyElementDAO).build((PDTrailDBImpl) pdServiceDB);
        } else if (pdServiceDB instanceof PDElanServiceDBImpl) {
            return new PDDetailedElanDTOBuilder(pdTopologyElementDAO).build((PDElanServiceDBImpl) pdServiceDB);
        } else if (pdServiceDB instanceof PDEvpnServiceDBImpl evpnServiceDb) {
            return new PDDetailedEvpnDTOBuilder(pdTopologyElementDAO).build(evpnServiceDb);
        }
        throw new NotSupportedDetailedServiceException(pdServiceDB.getClass().getName() +
                " is not supported as detailed service dto");
    }
}
