/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: okumar
 */

package com.adva.nlms.pd.api.in.discovery.dto;

import com.adva.nlms.common.AdministrationStateType;
import com.adva.nlms.common.sm.ServiceAdminStateDTO;
import com.adva.nlms.pd.api.in.dto.PDOperationalStatusDTO;
import com.adva.nlms.pd.api.in.dto.PDTopologyElementDTO;
import com.adva.nlms.pd.api.in.dto.PDTransportDTO;
import com.adva.nlms.pd.api.in.dto.enums.PDEntityResourceType;
import com.adva.nlms.pd.api.in.dto.enums.PDLifeCycleState;
import com.adva.nlms.pd.api.in.dto.enums.PDNetworkLayer;
import com.adva.nlms.pd.api.in.dto.enums.PDResourceType;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

public class PDEvpnServiceDTO extends PDTopologyElementDTO implements PDTransportDTO {


    private int customerId;                // ref to CustomerDBImpl
    private String customerName;
    private int serviceGroupId;            // ref to CustomerServiceGroupDBImpl
    private int serviceIntent;
    private String remarks;
    private AdministrationStateType pdServiceAdminState;
    private PDOperationalStatusDTO operationalStatus;

    private List<Integer> nodeList;    // ref to PDNodeDBImpl
    private PDNetworkLayer layer;
    private boolean isManaged;
    private PDResourceType resourceType;
    private PDLifeCycleState lifeCycleState;

    private boolean dataService;
    private List<Integer> entitiesList = new ArrayList<>();


    public int getCustomerId() {
        return customerId;
    }

    public void setCustomerId(int customerId) {
        this.customerId = customerId;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public int getServiceGroupId() {
        return serviceGroupId;
    }

    public void setServiceGroupId(int serviceGroupId) {
        this.serviceGroupId = serviceGroupId;
    }

    public void setServiceIntent(int serviceIntent) {
        this.serviceIntent = serviceIntent;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public AdministrationStateType getPdServiceAdminState() {
        return pdServiceAdminState;
    }

    public void setPdServiceAdminState(AdministrationStateType pdServiceAdminState) {
        this.pdServiceAdminState = pdServiceAdminState;
    }

    public void setOperationalStatus(PDOperationalStatusDTO operationalStatus) {
        this.operationalStatus = operationalStatus;
    }



    public List<Integer> getNodeList() {
        return nodeList;
    }

    public void setNodeList(List<Integer> nodeList) {
        this.nodeList = nodeList;
    }

    public void setLayer(PDNetworkLayer layer) {
        this.layer = layer;
    }

    public boolean isManaged() {
        return isManaged;
    }

    public void setManaged(boolean managed) {
        isManaged = managed;
    }

    public PDResourceType getResourceType() {
        return resourceType;
    }

    public void setResourceType(PDResourceType resourceType) {
        this.resourceType = resourceType;
    }

    public void setLifeCycleState(PDLifeCycleState lifeCycleState) {
        this.lifeCycleState = lifeCycleState;
    }

    public boolean isDataService() {
        return dataService;
    }

    public void setDataService(boolean dataService) {
        this.dataService = dataService;
    }

    public List<Integer> getEntitiesList() {
        return entitiesList;
    }

    public void setEntitiesList(List<Integer> entitiesList) {
        this.entitiesList = entitiesList;
    }

    @Override
    public PDEntityResourceType getEntityResourceType() {
        return null;
    }

    @Override
    public PDNetworkLayer getLayer() {
        return layer;
    }

    @Override
    public PDLifeCycleState getLifeCycleState() {
        return lifeCycleState;
    }

    @Override
    public AdministrationStateType getAdminState() {
        return null;
    }

    @Override
    public PDOperationalStatusDTO getOperationalStatus() {
        return operationalStatus;
    }

    @Override
    public Set<ServiceAdminStateDTO.IllegalAdminState> getIllegalAdminStates() {
        return PDTransportDTO.super.getIllegalAdminStates();
    }

    @Override
    public int getServiceIntent() {
        return serviceIntent;
    }
}
