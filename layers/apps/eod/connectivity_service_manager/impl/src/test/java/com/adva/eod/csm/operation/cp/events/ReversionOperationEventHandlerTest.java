/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: spyrosm
 */

package com.adva.eod.csm.operation.cp.events;

import com.adva.eod.csm.api.dto.operation.OperationType;
import com.adva.eod.csm.lockservice.ResourceLockRegistry;
import com.adva.eod.csm.operation.OperationRepository;
import com.adva.eod.csm.operation.entity.Operation;
import com.adva.eod.csm.reversion.ReversionOperationManager;
import com.adva.eod.sie.api.dto.AutoReversionOperationDto;
import com.adva.eod.sie.api.dto.OperationProgressDto;
import com.adva.eod.sie.api.dto.Status;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.integration.support.locks.DefaultLockRegistry;
import org.springframework.integration.support.locks.LockRegistry;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.time.Duration;
import java.util.UUID;

import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(SpringExtension.class)
@ContextConfiguration(classes = ReversionOperationEventHandler.class)
class ReversionOperationEventHandlerTest {
  @Autowired
  private ReversionOperationEventHandler reversionOperationEventHandler;
  @MockitoBean
  private OperationRepository operationRepository;
  @MockitoBean
  private ResourceLockRegistry resourceLockRegistry;
  @MockitoBean
  private ReversionOperationManager reversionOperationManager;

  private final LockRegistry lockRegistry = new DefaultLockRegistry();
  private final Duration duration = Duration.ofMinutes(1);

  @Test
  void handleEventWhenOperationFound() {
    String shortMessage = "shortMessage";
    OperationProgressDto operationProgressDto = new OperationProgressDto("message", shortMessage, 100);
    AutoReversionOperationDto operationDto = new AutoReversionOperationDto(
            UUID.randomUUID(), UUID.randomUUID(), Status.FAILED, operationProgressDto, null);
    Operation operation = getOperation();
    when(operationRepository.findByCpOperationId(operationDto.id())).thenReturn(operation);
    when(resourceLockRegistry.getLockRegistry()).thenReturn(lockRegistry);
    when(resourceLockRegistry.getLockDuration()).thenReturn(duration);
    reversionOperationEventHandler.handleEvent(operationDto);
    Mockito.verify(operationRepository, never()).save(operation);
  }

  @Test
  void findOrCreateOperation() {
    UUID cpOperationId = UUID.randomUUID();
    UUID csId = UUID.randomUUID();
    when(operationRepository.findByCpOperationId(cpOperationId)).thenReturn(null);
    Operation operation = getOperation();
    when(reversionOperationManager.create(csId, OperationType.REVERSION)).thenReturn(operation);
    reversionOperationEventHandler.findOrCreateOperation(cpOperationId, csId);
    verify(operationRepository).save(operation);
    Assertions.assertEquals(cpOperationId, operation.getCpOperationId());
  }

  @Test
  void getExecFailureRunnable() {
    Operation operation = getOperation();
    String errorMessage = "errorMessage";
    OperationProgressDto operationProgressDto = new OperationProgressDto("message", errorMessage, 100);
    reversionOperationEventHandler.getExecFailureRunnable(operation, errorMessage, operationProgressDto).call();
    verify(reversionOperationManager).operationWithProgressFailed(
            operation, errorMessage, operationProgressDto.shortMessage(), operationProgressDto.percentOfCompletion());
  }

  @Test
  void getExecCompletedRunnable() {
    Operation operation = getOperation();
    String message = "message";
    OperationProgressDto operationProgressDto = new OperationProgressDto(message, "shortMessage", 100);
    reversionOperationEventHandler.getExecCompletedRunnable(operation, message, operationProgressDto).call();
    verify(reversionOperationManager).operationWithProgressCompleted(
            operation, message, operationProgressDto.shortMessage(), operationProgressDto.percentOfCompletion());
  }

  @Test
  void getExecInProgressRunnable() {
    Operation operation = getOperation();
    String message = "message";
    OperationProgressDto operationProgressDto = new OperationProgressDto(message, "shortMessage", 50);
    reversionOperationEventHandler.getExecInProgressRunnable(operation, operationProgressDto).call();
    verify(reversionOperationManager).operationWithProgressInProgress(
            operation, message, operationProgressDto.shortMessage(), operationProgressDto.percentOfCompletion());
  }

  private Operation getOperation() {
    Operation operation = new Operation();
    operation.setId(UUID.randomUUID());
    operation.setType(OperationType.REVERSION);
    operation.setConnectivityServiceId(UUID.randomUUID());
    operation.setInfo("info");
    operation.setProgressPercentage(100);
    return operation;
  }
}