/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: ricardos
 */

package com.adva.nlms.mediation.apps.sm.intTest.f8;

import com.adva.nlms.mediation.apps.sm.testCommon.service.TestTopologyHdlr;
import org.junit.Test;

public class F8MultiTopoTest {
  private static final String F8_TOPOLOGY_DIR = "f8";
  private static final TestTopologyHdlr testTopologyHdlr = new TestTopologyHdlr();

  @Test
  public void createAllTopologies() {
    testTopologyHdlr.createAllTopologiesFromDirectory(F8_TOPOLOGY_DIR);
  }

  @Test
  public void deleteAllTopologies() {
    testTopologyHdlr.deleteAllTopologiesFromDirectory(F8_TOPOLOGY_DIR);
  }
}
