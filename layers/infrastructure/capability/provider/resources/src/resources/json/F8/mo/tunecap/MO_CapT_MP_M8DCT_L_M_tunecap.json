[{"attribute": "tuned-frequency", "moduleType": "T-MP-M8DCT-L", "portId": "C1|C2|C3|C4|C5|C6|C7|C8", "caplq": ".*", "plugType": "QSFP28-112G-ZR+-SM-LC", "moclass": "ptp", "path": "opt", "tunecap": {"basefreq": 196100000, "slprtcl": 6250, "slwp": 12500, "maxslp": 777, "maxslwp": 3, "mode": ".*", "wavelength": false, "integrated": false}}, {"attribute": "tuned-frequency", "moduleType": "T-MP-M8DCT-L", "portId": "C1|C2|C3|C4|C5|C6|C7|C8", "caplq": ".*", "plugType": "QSFP28-112G-ZR+-SM-LC-TIN", "moclass": "ptp", "path": "opt", "tunecap": {"basefreq": 196100000, "slprtcl": 50000, "slwp": 12500, "maxslp": 98, "maxslwp": 3, "mode": ".*", "wavelength": false, "integrated": false}}, {"attribute": "tuned-frequency", "moduleType": "T-MP-M8DCT-L", "portId": "C1|C5", "caplq": ".*", "plugType": "QSFP56-DD-425G-OZR+-SM-LC|QSFP56-DD-448G-OZRE-SM-LC", "moclass": "ptp", "path": "opt", "tunecap": {"basefreq": 196125000, "slprtcl": 6250, "slwp": 12500, "maxslp": 777, "maxslwp": 6, "mode": ".*", "wavelength": false, "integrated": false}}, {"attribute": "tuned-frequency", "moduleType": "T-MP-M8DCT-L", "portId": "C1|C5", "caplq": ".*", "plugType": "QSFP56-DD-425G-ZR-SM-LC", "moclass": "ptp", "path": "opt", "tunecap": {"basefreq": 196100000, "slprtcl": 75000, "slwp": 12500, "maxslp": 64, "maxslwp": 6, "mode": ".*", "wavelength": false, "integrated": false}}, {"attribute": "tuned-frequency", "moduleType": "T-MP-M8DCT-L", "portId": "N", "caplq": ".*", "plugType": ".*", "moclass": "ptp", "path": "opt", "tunecap": {"basefreq": 190812500, "slprtcl": 6250, "slwp": 0, "maxslp": 749, "maxslwp": 0, "slwidth": [100000, 112500, 125000, 150000], "mode": ".*", "wavelength": false, "integrated": true}}]