/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: asowa
 */

package com.adva.infrastructure.capabilityprovider.capabilities;

import com.adva.nlms.opticalparameters.api.OpticalParameters;
import com.adva.nlms.opticalparameters.api.Value;
import com.adva.nlms.opticalparameters.api.enums.ParameterName;
import com.adva.nlms.opticalparameters.cim.api.CimValue;
import com.adva.nlms.opticalparameters.cim.api.OpticalParametersCim;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInfo;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.fail;

class CapabilityFilesTest {

  private static final Logger log = LogManager.getLogger(CapabilityFilesTest.class);
  private static final String SINGLE_MODULE_FILE = "classpath*:json/F8/capabilities/singlemodule/*";
  private static final String SINGLE_MODULE_TEST_FILE = "file:../resources/src/test/resources/json/F8/capabilities/mo/test/*";
  private static final ObjectMapper mapper = new ObjectMapper();
  // File name patterns for capability file validation
  private static final Pattern SOURCE_FILE_PATTERN = Pattern.compile("^Cap(.+)_M\\.json$");
  private static final String TEST_FILE_TEMPLATE = "MO_Cap%s_test.json";
  private static final String PLUG_FILE_TEMPLATE = "Cap%s_M_plugs.json";

  @BeforeEach
  void setUp(TestInfo testInfo) {
    String currentTestName = testInfo.getDisplayName();
    log.info("=== Starting test: {} ===", currentTestName);
  }

  @Test
  @DisplayName("Test All Single Module Capabilities")
  void testAllSingleModuleCapabilities() throws IOException {
    log.info("Starting comprehensive capability files test with pattern-based discovery");
    List<TestResult> results = new ArrayList<>();

    // Discover test cases using pattern matching instead of static map
    Map<String, String> testCases = discoverTestCases();

    for (Map.Entry<String, String> testCase : testCases.entrySet()) {
      String sourceFile = testCase.getKey();
      String testFile = testCase.getValue();
      String plugFile = generatePlugFileName(sourceFile);

      log.info("Testing capability pair: {} -> {} (plug: {})", sourceFile, testFile, plugFile);
      testSingleModuleCapability(sourceFile, testFile);
      results.add(new TestResult(sourceFile + " -> " + testFile, true, "Test passed"));
      log.info("PASSED: {} -> {}", sourceFile, testFile);
    }

    printTestSummary(results);

    long failedCount = results.stream().filter(r -> !r.isPassed()).count();
    if (failedCount > 0) {
      throw new AssertionError(String.format("%d out of %d capability tests failed. See logs for details.",
        failedCount, results.size()));
    }
  }

  private void testSingleModuleCapability(String source, String test) throws IOException {

    log.info("=== Testing capability files: {} -> {} ===", source, test);

    // Load the source file
    Resource sourceResource = getFilesFromResourceFolder(SINGLE_MODULE_FILE, source);
    List<CapabilityFileRecord> sourceRecords = loadFromResource(sourceResource, CapabilityFileRecord[].class).stream().filter(r -> "Forward Error Correction".equals(r.parameter())).toList();
    log.info("Loaded {} source records from {}", sourceRecords.size(), source);

    // Load the test file
    Resource testResource = getFilesFromResourceFolder(SINGLE_MODULE_TEST_FILE, test);
    List<TestCapabilityRecord> testRecords = loadFromResource(testResource, TestCapabilityRecord[].class).stream().filter(r -> "fec-type".equals(r.attribute())).toList();
    log.info("Loaded {} test records from {}", testRecords.size(), test);

    // Load the plug test file
    Resource plugTestResource = getFilesFromResourceFolder(SINGLE_MODULE_TEST_FILE, test);
    List<PlugTestRecord> plugTestRecords = loadFromResource(plugTestResource, PlugTestRecord[].class).stream().filter(PlugTestRecord::isExternalFecSupported).toList();
    log.info("Loaded {} test records from {}", testRecords.size(), test);

    validateTestRecordMatches(sourceRecords, testRecords, plugTestRecords);
  }

  private void validateTestRecordMatches(List<CapabilityFileRecord> sourceRecords, List<TestCapabilityRecord> testRecords, List<PlugTestRecord> plugTestRecords) {
    log.info("Finding matches for ALL test records by comparing plugs, portId, and caplq");
    int totalTestRecords = testRecords.size();
    int matchedTestRecords = 0;
    List<TestCapabilityRecord> unmatchedTestRecords = new ArrayList<>();
    List<String> matchDetails = new ArrayList<>();

    for (TestCapabilityRecord testRecord : testRecords) {
      List<CapabilityFileRecord> matchingSourceRecords = findMatchingSourceRecords(sourceRecords, plugTestRecords, testRecord);
      if (!matchingSourceRecords.isEmpty()) {
        matchedTestRecords++;
        for (CapabilityFileRecord sourceRecord : matchingSourceRecords) {
          String matchDetail = String.format("MATCH: %s -> %s", testRecord, sourceRecord);
          matchDetails.add(matchDetail);
        }
      } else {
        unmatchedTestRecords.add(testRecord);
      }
    }

    if (!matchDetails.isEmpty()) {
      matchDetails.forEach(detail -> log.info("  {}", detail));
    }

    log.info("Validation completed. {}/{} test records matched",
      matchedTestRecords, totalTestRecords);
    if (!CollectionUtils.isEmpty(unmatchedTestRecords)) {
      log.info("Unmatched test records: {}", unmatchedTestRecords);
      fail("Test failed");
    }
  }

  private List<CapabilityFileRecord> findMatchingSourceRecords(List<CapabilityFileRecord> sourceRecords, List<PlugTestRecord> plugTestRecords, TestCapabilityRecord testRecord) {
    return sourceRecords
      .stream()
      .filter(s -> isRecordMatch(s, testRecord, plugTestRecords))
      .toList();
  }

  private boolean isRecordMatch(CapabilityFileRecord sourceRecord, TestCapabilityRecord testRecord, List<PlugTestRecord> plugTestRecords) {
    // Check lpq pattern match (source lpq is a pattern, test caplq is a string)
    if (!validateLpqMatch(sourceRecord.lpq(), testRecord.caplq())) {
      log.error("LPQ pattern mismatch: pattern '{}' does not match '{}' | {} -> {}",
        sourceRecord.lpq(), testRecord.caplq(), sourceRecord, testRecord);
      return false;
    }

//     Check portid pattern match (source portid is a pattern, test portId is a string)
    if (!validatePortIdMatch(sourceRecord.portid(), testRecord.portId())) {
      log.info("PortID pattern mismatch: pattern '{}' does not match '{}' | {} -> {}",
        sourceRecord.portid(), testRecord.portId(), sourceRecord, sourceRecord);
      return false;
    }

//     Check plugtype/plugType match (with | splitting)
    if (sourceRecord.plugtype() != null && testRecord.plugType() != null) {
      if (!validatePlugTypeMatch(sourceRecord.plugtype(), testRecord.plugType())) {
        log.error("PlugType mismatch: pattern '{}' does not match '{}' | {} -> {}", sourceRecord.plugtype(), testRecord.plugType(), sourceRecord, sourceRecord);
        return false;
      }
    } else if (!(sourceRecord.plugtype() == null && testRecord.plugType() == null)) {
      return false;
    }

    // Check default value and values match
    if (!checkValuesMatch(sourceRecord, testRecord, plugTestRecords)) {
      log.error("Default value/values mismatch | {} -> {}", sourceRecord, sourceRecord);
      return false;
    }
    return true;
  }

  private boolean validateLpqMatch(String sourceLpqPattern, String testCaplq) {
    if (sourceLpqPattern == null || testCaplq == null) {
      return false;
    }
    try {
      // Compile source lpq as a pattern and match against test caplq
      Pattern lpqPattern = Pattern.compile(sourceLpqPattern);
      return lpqPattern.matcher(testCaplq).matches();
    } catch (Exception e) {
      // Fall back to exact string match
      return sourceLpqPattern.equals(testCaplq);
    }
  }

  private boolean validatePortIdMatch(String sourcePortIdPattern, String testPortId) {
    if (sourcePortIdPattern == null && testPortId == null) {
      return true;
    }
    if (sourcePortIdPattern == null || testPortId == null) {
      return false;
    }

    try {
      // Compile source portid as a pattern and match against test portId
      Pattern portIdPattern = Pattern.compile(sourcePortIdPattern);
      return portIdPattern.matcher(testPortId).matches();
    } catch (Exception e) {
      // Fall back to exact string match
      return sourcePortIdPattern.equals(testPortId);
    }
  }

  private boolean validatePlugTypeMatch(String sourcePlugTypePattern, String testPlugType) {
    // Split test plugType by | to get individual plug types
    String[] testPlugTypes = testPlugType.split("\\|");

    for (String testPlug : testPlugTypes) {
      testPlug = testPlug.trim().replace("-", "/");

      // Special case: source "Integrated" matches test ".*"
      if ("Integrated".equals(sourcePlugTypePattern) && ".*".equals(testPlug)) {
        return true;
      }

      if (matchesPattern(sourcePlugTypePattern, testPlug)) {
        return true;
      } else {
        log.info("Pattern mismatch: source pattern '{}' does NOT match test plug '{}'", sourcePlugTypePattern, testPlug);
      }
    }
    return false;
  }

  private boolean checkValuesMatch(CapabilityFileRecord capabilityFileRecord, TestCapabilityRecord testCapabilityRecord, List<PlugTestRecord> plugTestRecords) {
    if (testCapabilityRecord.attributeValue().size() == 1) {
      checkValuesMatch(capabilityFileRecord.values(), testCapabilityRecord.attributeValue().get(0).getValuesList());
    }

    Set<String> fecExtPlugs = Arrays
      .stream(testCapabilityRecord.plugType().split("\\|"))
      .filter(plugType -> plugTestRecords.stream().anyMatch(plugTestRecord -> plugTestRecord.plug().equals(plugType) && plugTestRecord.isExternalFecSupported()))
      .collect(Collectors.toSet());

    Set<String> nonFecExtPlugs = Arrays
      .stream(testCapabilityRecord.plugType().split("\\|"))
      .filter(plugType -> plugTestRecords.stream().anyMatch(plugTestRecord -> plugTestRecord.plug().equals(plugType) && !plugTestRecord.isExternalFecSupported()))
      .collect(Collectors.toSet());


    if (!fecExtPlugs.isEmpty()) {
      Optional<TestCapabilityRecord.AttributeValueRecord> attributeValueRecord = testCapabilityRecord.attributeValue().stream()
        .filter(r -> Boolean.getBoolean(r.condValue()))
        .findFirst();
      if (attributeValueRecord.isPresent() && !checkValuesMatch(capabilityFileRecord.values(), attributeValueRecord.get().getValuesList())) {
        return false;
      }
    }

    if (!nonFecExtPlugs.isEmpty()) {
      Optional<TestCapabilityRecord.AttributeValueRecord> attributeValueRecord = testCapabilityRecord.attributeValue().stream()
        .filter(r -> !Boolean.getBoolean(r.condValue()))
        .findFirst();
      return attributeValueRecord.isEmpty() || checkValuesMatch(capabilityFileRecord.values(), attributeValueRecord.get().getValuesList());
    }
    return true;
  }

  private boolean checkValuesMatch(String sourceValues, List<String> testValues) {
    Set<String> sourceValueSet = Arrays.stream(sourceValues.split("[,|]"))
      .map(String::trim)
      .collect(Collectors.toSet());

    for (String sourceValue : sourceValueSet) {
      CimValue fromSourceValue = OpticalParametersCim.fromCommon(List.of(new OpticalParameters.SelectedParameter(ParameterName.FEC, new Value.Enum(sourceValue)))).get(0).value();
      if (fromSourceValue instanceof com.adva.nlms.opticalparameters.cim.api.CimValue.Enum fromSourceEnum) {
        sourceValue = fromSourceEnum.value();
        return testValues.contains(sourceValue);
      }
      throw new MOValuesException();
    }
    return true;
  }


  private boolean matchesPattern(String pattern, String testValue) {
    if (pattern == null || testValue == null) {
      return false;
    }

    if ("Integrated".equals(pattern) && ".*".equals(testValue)) {
      return true;
    }

    if (pattern.contains(",")) {
      String[] patterns = pattern.split(",");
      for (String singlePattern : patterns) {
        singlePattern = singlePattern.trim();
        if (matchesSinglePattern(singlePattern, testValue)) {
          return true;
        }
      }
      log.info("No pattern match found in list: '{}' against '{}'", pattern, testValue);
      return false;
    }
    return matchesSinglePattern(pattern, testValue);
  }

  private boolean matchesSinglePattern(String singlePattern, String testValue) {
    if (singlePattern.equals(testValue)) {
      return true;
    }
    if (singlePattern.contains(".*")) {
      return true;
    }
    return testValue.contains(singlePattern) || singlePattern.contains(testValue);
  }

  private Resource getFilesFromResourceFolder(String folder, String filename) throws IOException {
    ResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
    Resource[] resources = resolver.getResources(folder);

    Optional<Resource> foundResource = Arrays.stream(resources)
      .filter(resource -> resource.getFilename().equals(filename))
      .findFirst();

    if (foundResource.isEmpty()) {
      String availableFiles = Arrays.stream(resources)
        .map(Resource::getFilename)
        .collect(Collectors.joining(", "));
      String errorMsg = String.format("No resource found with filename: '%s' in folder: '%s'. Available files: [%s]", filename, folder, availableFiles);
      log.error(errorMsg);
      throw new IOException(errorMsg);
    }
    log.info("Successfully found resource: {}", foundResource.get().getFilename());
    return foundResource.get();
  }

  <T> List<T> loadFromResource(Resource resource, Class<T[]> arrayType) {
    log.info("Loading resource: {} as type: {}", resource.getFilename(), arrayType.getSimpleName());

    try (InputStream is = resource.getInputStream()) {
      List<T> result = Arrays.asList(mapper.readValue(is, arrayType));
      log.info("Successfully loaded {} records from file: {}", result.size(), resource.getFilename());
      return result;
    } catch (Exception e) {
      String errorMsg = String.format("Failed to read file '%s': %s", resource.getFilename(), e.getMessage());
      log.error(errorMsg, e);
      throw new RuntimeException(errorMsg, e);
    }
  }

  private Map<String, String> discoverTestCases() throws IOException {
    Map<String, String> testCases = new HashMap<>();
    PathMatchingResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
    List<Resource> sourceResources = Arrays.stream(resolver.getResources(SINGLE_MODULE_FILE))
      // remove this line after fixing of eqh/id for Access Flex
      .filter(resource -> !"CapAF_4X4XGT_M.json".equals(resource.getFilename())).toList();
    Resource[] testResources = resolver.getResources(SINGLE_MODULE_TEST_FILE);
    Set<String> testFileNames = Arrays.stream(testResources)
      .map(Resource::getFilename)
      .filter(Objects::nonNull)
      .collect(Collectors.toSet());

    sourceResources
      .stream()
      .map(Resource::getFilename)
      .filter(Objects::nonNull)
      .filter(fileName -> SOURCE_FILE_PATTERN.matcher(fileName).matches())
      .forEach(sourceFileName -> {
        String testFileName = generateTestFileName(sourceFileName);
        if (testFileName != null) {
          if (testFileNames.contains(testFileName)) {
            testCases.put(sourceFileName, testFileName);
            log.debug("Discovered test case: {} -> {}", sourceFileName, testFileName);
          } else {
            log.warn("Test file not found for source file: {} (expected: {})", sourceFileName, testFileName);
          }
        }
      });
    return testCases;
  }


  private static String generateTestFileName(String sourceFileName) {
    Matcher matcher = SOURCE_FILE_PATTERN.matcher(sourceFileName);
    if (matcher.matches()) {
      String modulePattern = matcher.group(1);
      modulePattern = transformModulePattern(modulePattern);
      return String.format(TEST_FILE_TEMPLATE, modulePattern);
    }
    return null;
  }

  private static String generatePlugFileName(String sourceFileName) {
    Matcher matcher = SOURCE_FILE_PATTERN.matcher(sourceFileName);
    if (matcher.matches()) {
      String modulePattern = matcher.group(1);
      return String.format(PLUG_FILE_TEMPLATE, modulePattern);
    }
    return null;
  }

  private static String transformModulePattern(String modulePattern) {
    return modulePattern
      .replace("_", "-")  // Convert underscores to hyphens (e.g., "AF_4X4XGT" -> "AF-4X4XGT")
      .replace("--", "_"); // Convert double hyphens back to single underscore for specific cases
  }


  private void printTestSummary(List<TestResult> results) {
    log.info("=== TEST SUMMARY ===");
    log.info("Total tests: {}", results.size());

    long passedCount = results.stream().filter(TestResult::isPassed).count();
    long failedCount = results.size() - passedCount;

    log.info("Passed: {}", passedCount);
    log.info("Failed: {}", failedCount);

    if (failedCount > 0) {
      log.error("=== FAILED TESTS ===");
      results.stream()
        .filter(r -> !r.isPassed())
        .forEach(r -> log.error("{}: {}", r.getTestName(), r.getMessage()));
    }
    log.info("=== END SUMMARY ===");
  }
}
