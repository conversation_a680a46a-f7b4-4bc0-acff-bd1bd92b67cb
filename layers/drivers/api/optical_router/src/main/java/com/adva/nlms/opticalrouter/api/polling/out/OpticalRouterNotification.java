/*
 *  Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 *  Owner: bkaminsk
 */

package com.adva.nlms.opticalrouter.api.polling.out;

import java.util.UUID;

public record OpticalRouterNotification(
  UUID neId,
  String entityLabel, // label like the one used in OpticalPort or OpticalPlug
  EntityType entityType,
  long timestamp,  // epoch time in ms
  OpticalRouterEvent event
) {
  public enum EntityType {
    PORT, PLUG
  }
}
