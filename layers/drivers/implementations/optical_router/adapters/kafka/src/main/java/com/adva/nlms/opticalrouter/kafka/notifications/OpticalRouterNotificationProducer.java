/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: bkaminsk
 */

package com.adva.nlms.opticalrouter.kafka.notifications;

import com.adva.nlms.opticalrouter.api.polling.out.OpticalRouterNotification;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;

import java.util.concurrent.CompletableFuture;

/**
 * Kafka producer for OpticalRouter notifications.
 * Sends notifications to a dedicated Kafka topic for downstream processing.
 */
public class OpticalRouterNotificationProducer {
    
    private static final Logger LOGGER = LogManager.getLogger(OpticalRouterNotificationProducer.class);
    
    private final KafkaTemplate<String, OpticalRouterNotification> kafkaTemplate;
    private final String topicName;
    
    public OpticalRouterNotificationProducer(String topicName, 
                                           KafkaTemplate<String, OpticalRouterNotification> kafkaTemplate) {
        this.topicName = topicName;
        this.kafkaTemplate = kafkaTemplate;
    }
    
    /**
     * Sends an OpticalRouter notification to Kafka.
     * Uses the device ID as the message key for proper partitioning.
     *
     * @param notification the notification to send
     * @return CompletableFuture for async handling
     */
    public CompletableFuture<SendResult<String, OpticalRouterNotification>> sendNotification(OpticalRouterNotification notification) {
        String messageKey = notification.neId().toString();
        
        LOGGER.debug("Sending OpticalRouter notification to Kafka topic '{}' with key '{}': {}", 
                    topicName, messageKey, notification);
        
        return kafkaTemplate.send(topicName, messageKey, notification)
            .whenComplete((result, throwable) -> {
                if (throwable != null) {
                    LOGGER.error("Failed to send OpticalRouter notification to Kafka topic '{}' with key '{}': {}", 
                               topicName, messageKey, notification, throwable);
                } else {
                    LOGGER.info("Successfully sent OpticalRouter notification to Kafka topic '{}' with key '{}' at offset {}", 
                              topicName, messageKey, result.getRecordMetadata().offset());
                }
            });
    }
    
    /**
     * Sends notification synchronously with timeout handling.
     *
     * @param notification the notification to send
     * @throws OpticalRouterNotificationKafkaException if sending fails
     */
    public void sendNotificationSync(OpticalRouterNotification notification) {
        try {
            sendNotification(notification).get();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new OpticalRouterNotificationKafkaException(
                "Interrupted while sending notification to Kafka", e);
        } catch (Exception e) {
            throw new OpticalRouterNotificationKafkaException(
                "Failed to send notification to Kafka", e);
        }
    }
    
    /**
     * Sends notification asynchronously with callback handling.
     *
     * @param notification the notification to send
     * @param successCallback callback for successful sends
     * @param errorCallback callback for failed sends
     */
    public void sendNotificationAsync(OpticalRouterNotification notification,
                                    Runnable successCallback,
                                    java.util.function.Consumer<Throwable> errorCallback) {
        sendNotification(notification)
            .whenComplete((result, throwable) -> {
                if (throwable != null) {
                    if (errorCallback != null) {
                        errorCallback.accept(throwable);
                    }
                } else {
                    if (successCallback != null) {
                        successCallback.run();
                    }
                }
            });
    }
    
    /**
     * Gets the configured topic name.
     *
     * @return the Kafka topic name
     */
    public String getTopicName() {
        return topicName;
    }
}
