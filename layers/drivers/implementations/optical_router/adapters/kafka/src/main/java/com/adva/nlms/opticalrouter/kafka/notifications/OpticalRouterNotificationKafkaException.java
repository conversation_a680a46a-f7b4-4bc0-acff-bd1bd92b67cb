/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: bkaminsk
 */

package com.adva.nlms.opticalrouter.kafka.notifications;

/**
 * Exception thrown when Kafka operations for OpticalRouter notifications fail.
 */
public class OpticalRouterNotificationKafkaException extends RuntimeException {
    
    public OpticalRouterNotificationKafkaException(String message) {
        super(message);
    }
    
    public OpticalRouterNotificationKafkaException(String message, Throwable cause) {
        super(message, cause);
    }
    
    public OpticalRouterNotificationKafkaException(Throwable cause) {
        super(cause);
    }
}
