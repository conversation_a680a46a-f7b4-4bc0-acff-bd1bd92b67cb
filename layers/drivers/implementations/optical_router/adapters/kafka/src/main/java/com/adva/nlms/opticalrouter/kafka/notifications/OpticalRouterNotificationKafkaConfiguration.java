/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: bkaminsk
 */

package com.adva.nlms.opticalrouter.kafka.notifications;

import com.adva.nlms.opticalrouter.api.polling.out.OpticalRouterNotification;
import com.adva.nlms.opticalrouter.kafka.OpticalRouterKafkaConfiguration;
import org.apache.kafka.clients.admin.NewTopic;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.config.TopicBuilder;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.core.ProducerFactory;

/**
 * Kafka configuration for OpticalRouter notifications.
 * Creates the necessary beans for producing notifications to Kafka.
 */
@Configuration
@ConditionalOnProperty(name = "optical.router.kafka.notifications.enabled", havingValue = "true", matchIfMissing = true)
public class OpticalRouterNotificationKafkaConfiguration {
    
    public static final String OPTICAL_ROUTER_NOTIFICATIONS_TOPIC = "optical-router-notifications";
    
    @Value("${optical.router.kafka.notifications.topic.name:" + OPTICAL_ROUTER_NOTIFICATIONS_TOPIC + "}")
    private String notificationTopicName;
    
    @Value("${optical.router.kafka.notifications.topic.partitions:3}")
    private int notificationTopicPartitions;
    
    @Value("${optical.router.kafka.notifications.topic.replicas:1}")
    private short notificationTopicReplicas;
    
    /**
     * Creates the Kafka topic for OpticalRouter notifications.
     *
     * @return NewTopic configuration
     */
    @Bean
    public NewTopic opticalRouterNotificationsTopic() {
        return TopicBuilder.name(notificationTopicName)
            .partitions(notificationTopicPartitions)
            .replicas(notificationTopicReplicas)
            .build();
    }
    
    /**
     * Creates the producer factory for OpticalRouter notifications.
     *
     * @return ProducerFactory for OpticalRouterNotification
     */
    @Bean
    public ProducerFactory<String, OpticalRouterNotification> opticalRouterNotificationProducerFactory() {
        return new DefaultKafkaProducerFactory<>(OpticalRouterKafkaConfiguration.getProducerConfiguration());
    }
    
    /**
     * Creates the Kafka template for OpticalRouter notifications.
     *
     * @return KafkaTemplate for OpticalRouterNotification
     */
    @Bean
    public KafkaTemplate<String, OpticalRouterNotification> opticalRouterNotificationKafkaTemplate() {
        return new KafkaTemplate<>(opticalRouterNotificationProducerFactory());
    }
    
    /**
     * Creates the OpticalRouter notification producer.
     *
     * @return OpticalRouterNotificationProducer
     */
    @Bean
    public OpticalRouterNotificationProducer opticalRouterNotificationProducer() {
        return new OpticalRouterNotificationProducer(
            notificationTopicName, 
            opticalRouterNotificationKafkaTemplate()
        );
    }
}
