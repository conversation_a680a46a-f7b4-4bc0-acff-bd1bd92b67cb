/*
 *  Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 *  Owner: michalo
 */

package com.adva.nlms.opticalrouter.rest.server;

import com.adva.nlms.infrastucture.security.permission.api.Authorization;
import com.adva.nlms.infrastucture.security.permission.api.PermissionAction;
import com.adva.nlms.mediation.common.rest.MDRestComponent;
import com.adva.nlms.opticalrouter.api.polling.in.CommunicationSettings;
import com.adva.nlms.opticalrouter.api.polling.in.OpticalRouterCommandException;
import com.adva.nlms.opticalrouter.api.polling.in.OpticalRouterDriverManager;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;

import java.util.UUID;

@MDRestComponent
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@Path("v1/drv/optical-router/manager")
public class OpticalRouterDriverManagerController {
  private final OpticalRouterDriverManager opticalRouterDriverManager;

  public OpticalRouterDriverManagerController(OpticalRouterDriverManager opticalRouterDriverManager) {
    this.opticalRouterDriverManager = opticalRouterDriverManager;
  }

  @POST
  @Path("{neId}/connect")
  @Authorization(anyOfPermissions = {PermissionAction.GetNetworkElement})
  public Response connect(@PathParam("neId") UUID neId, CommunicationSettings settings) throws OpticalRouterCommandException {
    opticalRouterDriverManager.connect(neId, settings);
    return Response.ok().build();
  }

  @POST
  @Path("{neId}/disconnect")
  @Authorization(anyOfPermissions = {PermissionAction.GetNetworkElement})
  public Response disconnect(@PathParam("neId") UUID neId) throws OpticalRouterCommandException {
    opticalRouterDriverManager.disconnect(neId);
    return Response.ok().build();
  }

  @POST
  @Path("{neId}/test-connection")
  @Authorization(anyOfPermissions = {PermissionAction.GetNetworkElement})
  public Response testConnection(@PathParam("neId") UUID neId, CommunicationSettings settings) throws OpticalRouterCommandException {
    opticalRouterDriverManager.testConnection(neId, settings);
    return Response.ok().build();
  }

  @GET
  @Path("{neId}/is-connected")
  @Authorization(anyOfPermissions = {PermissionAction.GetNetworkElement})
  public Response isConnected(@PathParam("neId") UUID neId) throws OpticalRouterCommandException {
    boolean isConnected = opticalRouterDriverManager.isConnected(neId);
    return Response.ok(isConnected).status(Response.Status.ACCEPTED).build();
  }

  @GET
  @Path("{neId}/cold-start-time")
  @Authorization(anyOfPermissions = {PermissionAction.GetNetworkElement})
  public Response getColdStartTime(@PathParam("neId") UUID neId) throws OpticalRouterCommandException {
    long coldStartTime = opticalRouterDriverManager.getColdStartTime(neId);
    return Response.ok(coldStartTime).status(Response.Status.ACCEPTED).build();
  }

  @GET
  @Path("{neId}/synchronize-device-info")
  @Authorization(anyOfPermissions = {PermissionAction.GetNetworkElement})
  public Response synchronizeDeviceInfo(@PathParam("neId") UUID neId) throws OpticalRouterCommandException {
    opticalRouterDriverManager.synchronizeDeviceInfo(neId);
    return Response.status(Response.Status.ACCEPTED).build();
  }

  @GET
  @Path("{neId}/synchronize-inventory")
  @Authorization(anyOfPermissions = {PermissionAction.GetNetworkElement})
  public Response synchronizeInventory(@PathParam("neId") UUID neId) throws OpticalRouterCommandException {
    opticalRouterDriverManager.synchronizeInventory(neId);
    return Response.status(Response.Status.ACCEPTED).build();
  }

  @GET
  @Path("{neId}/synchronize-status")
  @Authorization(anyOfPermissions = {PermissionAction.GetNetworkElement})
  public Response synchronizeStatus(@PathParam("neId") UUID neId) throws OpticalRouterCommandException {
    opticalRouterDriverManager.synchronizeStatus(neId);
    return Response.status(Response.Status.ACCEPTED).build();
  }
}
