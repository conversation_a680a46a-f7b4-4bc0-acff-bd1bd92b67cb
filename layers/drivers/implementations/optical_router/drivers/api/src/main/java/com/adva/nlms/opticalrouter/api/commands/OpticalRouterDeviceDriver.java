/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: bkaminsk
 */

package com.adva.nlms.opticalrouter.api.commands;

import java.util.UUID;

public interface OpticalRouterDeviceDriver {
  /**
   * Establish a connection with device.
   * Must be invoked as a precondition for any other method reading data from the device.
   *
   * @param neId     device identifier
   * @param settings set of data needed to initiate the session
   * @throws OpticalRouterCommandException for other error
   */
  void connect(UUID neId, CommunicationSettings settings) throws OpticalRouterCommandException;

  /**
   * Disconnect from the device.
   * After it is invoked, no method reading data from the device will succeed and no notifications should come.
   *
   * @param neId device identifier
   * @throws OpticalRouterCommandException for other error
   */
  void disconnect(UUID neId) throws OpticalRouterCommandException;

  /**
   * Check if device is connected.
   *
   * @param neId device identifier
   * @return true if device is connected, false otherwise
   * @throws OpticalRouterCommandException for other error
   */
  boolean isConnected(UUID neId) throws OpticalRouterCommandException;

  /**
   * Get time of the last cold start of the device.
   *
   * @param neId device identifier
   * @return posix timestamp of the last cold start of the device (**seconds** since 1970-01-01 UTC)
   * @throws OpticalRouterCommandException for other error
   */
  long getColdStartTime(UUID neId) throws OpticalRouterCommandException;

  /**
   * Trigger synchronization of inventory with the device.
   * <p>
   * Result of the synchronization is returned via OpticalRouterDriversManagerNotifications
   * - deviceInfoReceived - returns basic information about the device
   * - synchronizationOfDeviceInfoCompleted - invoked once processing of device info has been completed.
   * </p>
   *
   * @param neId device identifier
   */
  void synchronizeDeviceInfo(UUID neId);

  /**
   * Trigger synchronization of inventory with the device.
   * <p>
   * Result of the synchronization is returned via OpticalRouterDriversManagerNotifications
   * - deviceInfoReceived - returns basic information about the device, it shall be invoked prior to processing
   * inventory data.
   * - synchronizationOfInventoryCompleted - invoked once processing of inventory data has been completed.
   * </p>
   *
   * @param neId device identifier
   */
  void synchronizeInventory(UUID neId);

  /**
   * Trigger synchronization of status with the device.
   * <p>
   * Result of the synchronization is returned via OpticalRouterDriversManagerNotifications
   * - synchronizationOfStatusCompleted - invoked once processing of inventory data has been completed.
   * </p>
   *
   * @param neId device identifier
   */
  void synchronizeStatus(UUID neId);

  /**
   * Establish  and close a connection with device regardless of the current connection state.
   * Request is successful if driver successfully connected to the device.
   *
   * @param neId     device identifier
   * @param settings set of data needed to initiate the session
   * @throws OpticalRouterCommandException for other error
   */
  void testConnection(UUID neId, CommunicationSettings settings);
}
