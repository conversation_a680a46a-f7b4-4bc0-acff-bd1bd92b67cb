/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: bkaminsk
 */

package com.adva.nlms.opticalrouter.impl.polling;

import com.adva.nlms.opticalrouter.api.polling.out.OpticalRouterEvent;
import com.adva.nlms.opticalrouter.api.polling.out.OpticalRouterNotification;
import org.springframework.kafka.core.KafkaTemplate;

import java.util.UUID;

/**
 * Manual test class for testing Kafka notification sending.
 * This can be used to manually verify that notifications are sent to Kafka.
 */
public class ManualKafkaNotificationTest {
    
    /**
     * Creates test notifications and sends them to Kafka.
     * This method can be called from a test or main method to verify Kafka integration.
     *
     * @param kafkaTemplate the Kafka template to use for sending
     */
    public static void sendTestNotifications(KafkaTemplate<String, Object> kafkaTemplate) {
        OpticalRouterNotificationKafkaProducer producer = 
            new OpticalRouterNotificationKafkaProducer(kafkaTemplate);
        
        // Test 1: EntityAlarmed notification
        UUID deviceId1 = UUID.fromString("d0baf626-c566-47c8-9ba5-390437d0913a");
        OpticalRouterEvent.EntityAlarmed alarmedEvent = new OpticalRouterEvent.EntityAlarmed(
            "POWER_LOSS",
            OpticalRouterEvent.Direction.RX,
            OpticalRouterEvent.Severity.CRITICAL,
            System.currentTimeMillis()
        );
        
        OpticalRouterNotification alarmedNotification = new OpticalRouterNotification(
            deviceId1,
            "port-1-1-1",
            OpticalRouterNotification.EntityType.PORT,
            System.currentTimeMillis(),
            alarmedEvent
        );
        
        System.out.println("Sending EntityAlarmed notification...");
        producer.sendNotification(alarmedNotification);
        
        // Test 2: EntityCreated notification
        UUID deviceId2 = UUID.fromString("b14bcdc0-c29f-4fed-8afe-7308ed0a8acf");
        OpticalRouterEvent.EntityCreated createdEvent = new OpticalRouterEvent.EntityCreated(null);
        
        OpticalRouterNotification createdNotification = new OpticalRouterNotification(
            deviceId2,
            "plug-1-2-1",
            OpticalRouterNotification.EntityType.PLUG,
            System.currentTimeMillis(),
            createdEvent
        );
        
        System.out.println("Sending EntityCreated notification...");
        producer.sendNotification(createdNotification);
        
        // Test 3: EntityStateChanged notification
        UUID deviceId3 = UUID.fromString("a1b2c3d4-e5f6-7890-abcd-ef1234567890");
        OpticalRouterEvent.EntityStateChanged stateChangedEvent = new OpticalRouterEvent.EntityStateChanged(
            null, null
        );
        
        OpticalRouterNotification stateChangedNotification = new OpticalRouterNotification(
            deviceId3,
            "port-2-1-1",
            OpticalRouterNotification.EntityType.PORT,
            System.currentTimeMillis(),
            stateChangedEvent
        );
        
        System.out.println("Sending EntityStateChanged notification...");
        producer.sendNotification(stateChangedNotification);
        
        // Test 4: Multiple notifications from same device
        for (int i = 1; i <= 3; i++) {
            OpticalRouterEvent.EntityAlarmed multipleAlarm = new OpticalRouterEvent.EntityAlarmed(
                "SIGNAL_LOSS_" + i,
                OpticalRouterEvent.Direction.TX,
                OpticalRouterEvent.Severity.MINOR,
                System.currentTimeMillis()
            );
            
            OpticalRouterNotification multipleNotification = new OpticalRouterNotification(
                deviceId1, // Same device
                "port-1-1-" + i,
                OpticalRouterNotification.EntityType.PORT,
                System.currentTimeMillis(),
                multipleAlarm
            );
            
            System.out.println("Sending multiple notification " + i + "...");
            producer.sendNotification(multipleNotification);
        }
        
        System.out.println("All test notifications sent!");
    }
    
    /**
     * Creates a sample notification for testing purposes.
     *
     * @return a sample OpticalRouterNotification
     */
    public static OpticalRouterNotification createSampleNotification() {
        UUID deviceId = UUID.randomUUID();
        OpticalRouterEvent.EntityAlarmed alarmedEvent = new OpticalRouterEvent.EntityAlarmed(
            "TEST_ALARM",
            OpticalRouterEvent.Direction.BI,
            OpticalRouterEvent.Severity.WARNING,
            System.currentTimeMillis()
        );
        
        return new OpticalRouterNotification(
            deviceId,
            "test-port",
            OpticalRouterNotification.EntityType.PORT,
            System.currentTimeMillis(),
            alarmedEvent
        );
    }
}
