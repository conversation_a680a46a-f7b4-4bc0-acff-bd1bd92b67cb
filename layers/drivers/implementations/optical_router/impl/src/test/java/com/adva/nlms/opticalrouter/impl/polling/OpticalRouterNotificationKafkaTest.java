/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: bkaminsk
 */

package com.adva.nlms.opticalrouter.impl.polling;

import com.adva.nlms.opticalrouter.api.polling.out.OpticalRouterEvent;
import com.adva.nlms.opticalrouter.api.polling.out.OpticalRouterNotification;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;

import java.util.UUID;
import java.util.concurrent.CompletableFuture;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class OpticalRouterNotificationKafkaTest {

    @Mock
    private KafkaTemplate<String, Object> kafkaTemplate;

    @Mock
    private SendResult<String, Object> sendResult;

    private OpticalRouterNotificationKafkaProducer kafkaProducer;
    private OpticalRouterNotification testNotification;

    @BeforeEach
    void setUp() {
        kafkaProducer = new OpticalRouterNotificationKafkaProducer(kafkaTemplate);
        
        // Create test notification
        UUID deviceId = UUID.randomUUID();
        OpticalRouterEvent.EntityAlarmed alarmedEvent = new OpticalRouterEvent.EntityAlarmed(
            "POWER_LOSS",
            OpticalRouterEvent.Direction.RX,
            OpticalRouterEvent.Severity.CRITICAL,
            System.currentTimeMillis()
        );
        
        testNotification = new OpticalRouterNotification(
            deviceId,
            "port-1",
            OpticalRouterNotification.EntityType.PORT,
            System.currentTimeMillis(),
            alarmedEvent
        );
    }

    @Test
    void sendNotification_success() {
        // Arrange
        CompletableFuture<SendResult<String, Object>> future = CompletableFuture.completedFuture(sendResult);
        when(kafkaTemplate.send(any(String.class), any(String.class), any(Object.class)))
            .thenReturn(future);

        // Act
        kafkaProducer.sendNotification(testNotification);

        // Assert
        ArgumentCaptor<String> topicCaptor = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> keyCaptor = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Object> valueCaptor = ArgumentCaptor.forClass(Object.class);
        
        verify(kafkaTemplate).send(topicCaptor.capture(), keyCaptor.capture(), valueCaptor.capture());
        
        assertEquals("optical-router-notifications", topicCaptor.getValue());
        assertEquals(testNotification.neId().toString(), keyCaptor.getValue());
        assertEquals(testNotification, valueCaptor.getValue());
    }

    @Test
    void sendNotification_withEntityCreatedEvent() {
        // Arrange
        OpticalRouterEvent.EntityCreated createdEvent = new OpticalRouterEvent.EntityCreated(null);
        OpticalRouterNotification createdNotification = new OpticalRouterNotification(
            UUID.randomUUID(),
            "port-2",
            OpticalRouterNotification.EntityType.PORT,
            System.currentTimeMillis(),
            createdEvent
        );
        
        CompletableFuture<SendResult<String, Object>> future = CompletableFuture.completedFuture(sendResult);
        when(kafkaTemplate.send(any(String.class), any(String.class), any(Object.class)))
            .thenReturn(future);

        // Act
        kafkaProducer.sendNotification(createdNotification);

        // Assert
        verify(kafkaTemplate).send(eq("optical-router-notifications"), 
                                 eq(createdNotification.neId().toString()), 
                                 eq(createdNotification));
    }

    @Test
    void sendNotification_withEntityStateChangedEvent() {
        // Arrange
        OpticalRouterEvent.EntityStateChanged stateChangedEvent = new OpticalRouterEvent.EntityStateChanged(
            null, null
        );
        OpticalRouterNotification stateChangedNotification = new OpticalRouterNotification(
            UUID.randomUUID(),
            "plug-1",
            OpticalRouterNotification.EntityType.PLUG,
            System.currentTimeMillis(),
            stateChangedEvent
        );
        
        CompletableFuture<SendResult<String, Object>> future = CompletableFuture.completedFuture(sendResult);
        when(kafkaTemplate.send(any(String.class), any(String.class), any(Object.class)))
            .thenReturn(future);

        // Act
        kafkaProducer.sendNotification(stateChangedNotification);

        // Assert
        verify(kafkaTemplate).send(eq("optical-router-notifications"), 
                                 eq(stateChangedNotification.neId().toString()), 
                                 eq(stateChangedNotification));
    }

    @Test
    void sendNotification_handlesKafkaException() {
        // Arrange
        CompletableFuture<SendResult<String, Object>> future = new CompletableFuture<>();
        future.completeExceptionally(new RuntimeException("Kafka connection failed"));
        when(kafkaTemplate.send(any(String.class), any(String.class), any(Object.class)))
            .thenReturn(future);

        // Act & Assert - should not throw exception
        assertDoesNotThrow(() -> kafkaProducer.sendNotification(testNotification));
        
        verify(kafkaTemplate).send(any(String.class), any(String.class), any(Object.class));
    }
}
