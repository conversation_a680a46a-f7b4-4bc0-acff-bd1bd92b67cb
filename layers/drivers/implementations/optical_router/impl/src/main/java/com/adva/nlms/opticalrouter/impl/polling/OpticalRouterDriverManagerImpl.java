/*
 *  Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 *  Owner: michalo
 */

package com.adva.nlms.opticalrouter.impl.polling;

import com.adva.nlms.opticalrouter.api.commands.OpticalRouterDeviceDriver;
import com.adva.nlms.opticalrouter.api.commands.PasswordAuthenticationData;
import com.adva.nlms.opticalrouter.api.polling.in.CommunicationSettings;
import com.adva.nlms.opticalrouter.api.polling.in.CommandStatus;
import com.adva.nlms.opticalrouter.api.polling.in.OpticalRouterCommandException;
import com.adva.nlms.opticalrouter.api.polling.in.OpticalRouterDriverManager;
import com.adva.nlms.opticalrouter.persistence.OpticalRouterInventoryPersistenceAdapter;

import java.util.UUID;
import java.util.concurrent.Callable;

class OpticalRouterDriverManagerImpl implements OpticalRouterDriverManager {
  private final OpticalRouterDeviceDriver opticalRouterDeviceDriver;
  private final OpticalRouterInventoryPersistenceAdapter persistenceAdapter;

  OpticalRouterDriverManagerImpl(
    OpticalRouterDeviceDriver opticalRouterDeviceDriver,
    OpticalRouterInventoryPersistenceAdapter persistenceAdapter
  ) {
    this.opticalRouterDeviceDriver = opticalRouterDeviceDriver;
    this.persistenceAdapter = persistenceAdapter;
  }

  @Override
  public void connect(UUID neId, CommunicationSettings settings) throws OpticalRouterCommandException {
    final var driverSettings = new com.adva.nlms.opticalrouter.api.commands.CommunicationSettings(
      settings.deviceType(),
      settings.ipAddress(),
      settings.port(),
      settings.timeoutSeconds(),
      new PasswordAuthenticationData(settings.username(), settings.password())
    );
    invoke(() -> opticalRouterDeviceDriver.connect(neId, driverSettings));
  }

  @Override
  public void disconnect(UUID neId) throws OpticalRouterCommandException {
    invoke(() -> opticalRouterDeviceDriver.disconnect(neId));
    persistenceAdapter.removeData(neId);
  }

  @Override
  public boolean isConnected(UUID neId) throws OpticalRouterCommandException {
    return invoke(() -> opticalRouterDeviceDriver.isConnected(neId));
  }

  @Override
  public long getColdStartTime(UUID neId) throws OpticalRouterCommandException {
    return invoke(() -> opticalRouterDeviceDriver.getColdStartTime(neId));
  }

  @Override
  public void synchronizeDeviceInfo(UUID neId) {
    invoke(() -> opticalRouterDeviceDriver.synchronizeDeviceInfo(neId));
  }

  @Override
  public void synchronizeInventory(UUID neId) {
    invoke(() -> {
      opticalRouterDeviceDriver.synchronizeDeviceInfo(neId);
      opticalRouterDeviceDriver.synchronizeInventory(neId);
    });
  }

  @Override
  public void synchronizeStatus(UUID neId) {
    invoke(() -> opticalRouterDeviceDriver.synchronizeStatus(neId));
  }

  @Override
  public void testConnection(UUID neId, CommunicationSettings settings){
    final var driverSettings = new com.adva.nlms.opticalrouter.api.commands.CommunicationSettings(
      settings.deviceType(),
      settings.ipAddress(),
      settings.port(),
      settings.timeoutSeconds(),
      new PasswordAuthenticationData(settings.username(), settings.password())
    );
    invoke(() -> opticalRouterDeviceDriver.testConnection(neId, driverSettings));
  }

  private void invoke(Runnable runnable) {
    try {
      runnable.run();
    } catch (com.adva.nlms.opticalrouter.api.commands.OpticalRouterCommandException e) {
      throw new OpticalRouterCommandException(map(e.getCommandStatus()), e.getMessage(), e.getCause());
    } catch (Exception e) {
      throw new OpticalRouterCommandException(CommandStatus.FATAL_ERROR, e.getMessage(), e);
    }
  }

  private <T> T invoke(Callable<T> callable) {
    try {
      return callable.call();
    } catch (com.adva.nlms.opticalrouter.api.commands.OpticalRouterCommandException e) {
      throw new OpticalRouterCommandException(map(e.getCommandStatus()), e.getMessage(), e.getCause());
    } catch (Exception e) {
      throw new OpticalRouterCommandException(CommandStatus.FATAL_ERROR, e.getMessage(), e);
    }
  }

  private CommandStatus map(com.adva.nlms.opticalrouter.api.commands.CommandStatus status) {
    return switch (status) {
      case SUCCESS -> CommandStatus.SUCCESS;
      case UNSUPPORTED_DEVICE -> CommandStatus.UNSUPPORTED_DEVICE;
      case TIMEOUT -> CommandStatus.TIMEOUT;
      case DRIVER_ERROR -> CommandStatus.DRIVER_ERROR;
      case FATAL_ERROR -> CommandStatus.FATAL_ERROR;
    };
  }

}