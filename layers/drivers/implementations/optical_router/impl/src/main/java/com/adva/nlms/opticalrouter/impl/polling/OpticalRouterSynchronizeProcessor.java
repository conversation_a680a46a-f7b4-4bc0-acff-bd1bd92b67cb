/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: bkaminsk
 */

package com.adva.nlms.opticalrouter.impl.polling;

import com.adva.nlms.opticalrouter.api.commands.CommandResponder;
import com.adva.nlms.opticalrouter.api.commands.CommandResponse;
import com.adva.nlms.opticalrouter.api.commands.Status;
import com.adva.nlms.opticalrouter.api.polling.in.CommandStatus;
import com.adva.nlms.opticalrouter.api.polling.out.OpticalRouterDriverManagerNotifications;
import com.adva.nlms.opticalrouter.api.polling.out.OpticalRouterNotificationHandler;
import com.adva.nlms.opticalrouter.api.polling.out.SynchronizationCommandResult;
import com.adva.nlms.opticalrouter.api.resources.OpticalRouterConnectionFaultCause;
import com.adva.nlms.opticalrouter.api.resources.OpticalRouterDeviceInfo;
import com.adva.nlms.opticalrouter.api.resources.OpticalRouterInventory;
import com.adva.nlms.opticalrouter.api.resources.OpticalRouterDriverNotification;
import com.adva.nlms.opticalrouter.api.resources.OpticalRouterStatus;
import com.adva.nlms.opticalrouter.api.resources.OpticalRouterSynchronizationData;
import com.adva.nlms.opticalrouter.persistence.OpticalRouterInventoryPersistenceAdapter;
import com.adva.nlms.opticalrouter.persistence.OpticalRouterStatusPersistenceAdapter;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.kafka.core.KafkaTemplate;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.function.Consumer;

class OpticalRouterSynchronizeProcessor implements CommandResponder {

  private static final Logger LOGGER = LogManager.getLogger(OpticalRouterSynchronizeProcessor.class);

  private final OpticalRouterDriverManagerNotifications opticalRouterDriverManagerNotifications;
  private final OpticalRouterInventoryPersistenceAdapter opticalRouterInventoryPersistenceAdapter;
  private final OpticalRouterStatusPersistenceAdapter opticalRouterStatusPersistenceAdapter;
  private final OpticalRouterNotificationHandler opticalRouterNotificationHandler;
  private final OpticalRouterNotificationKafkaProducer kafkaProducer;

  private final Map<Class<? extends OpticalRouterSynchronizationData>, Consumer<CommandResponse>> handlers = new HashMap<>();

  public OpticalRouterSynchronizeProcessor(final OpticalRouterDriverManagerNotifications opticalRouterDriverManagerNotifications,
                                           final OpticalRouterInventoryPersistenceAdapter opticalRouterInventoryPersistenceAdapter,
                                           final OpticalRouterStatusPersistenceAdapter opticalRouterStatusPersistenceAdapter,
                                           final OpticalRouterNotificationHandler opticalRouterNotificationHandler,
                                           final KafkaTemplate<String, Object> kafkaTemplate) {
    handlers.put(OpticalRouterDeviceInfo.class, this::handleDeviceInfo);
    handlers.put(OpticalRouterInventory.class, this::handleInventory);
    handlers.put(OpticalRouterStatus.class, this::handleStatus);
    handlers.put(OpticalRouterConnectionFaultCause.class, this::handleConnectionFault);
    handlers.put(OpticalRouterDriverNotification.class, this::handleNotification);

    this.opticalRouterDriverManagerNotifications = opticalRouterDriverManagerNotifications;
    this.opticalRouterInventoryPersistenceAdapter = opticalRouterInventoryPersistenceAdapter;
    this.opticalRouterStatusPersistenceAdapter = opticalRouterStatusPersistenceAdapter;
    this.opticalRouterNotificationHandler = opticalRouterNotificationHandler;
    this.kafkaProducer = new OpticalRouterNotificationKafkaProducer(kafkaTemplate);
  }

  @Override
  public void process(final CommandResponse response) {
    Optional.ofNullable(response)
      .map(CommandResponse::data)
      .ifPresentOrElse(data -> {
        Consumer<CommandResponse> handler = handlers.get(data.getClass());
        if (handler != null) {
          handler.accept(response);
        } else {
          LOGGER.error("Unexpected data type received: {}", data.getClass().getName());
        }
      }, () -> LOGGER.info("Received null CommandResponse or null data"));
  }

  private void handleConnectionFault(CommandResponse commandResponse) {
    LOGGER.info("Handling ConnectionFault response: {}", commandResponse);
    opticalRouterDriverManagerNotifications.deviceDisconnected(commandResponse.deviceId());
  }

  private void handleDeviceInfo(final CommandResponse response) {
    LOGGER.info("Handling device info synchronization response: {}", response);
    if (isValid(response)) {
      final OpticalRouterDeviceInfo opticalRouterDeviceInfo = (OpticalRouterDeviceInfo) response.data();
      opticalRouterDriverManagerNotifications.deviceInfoReceived(buildOpticalRouterDeviceInfo(response.deviceId(), opticalRouterDeviceInfo));
    }
    opticalRouterDriverManagerNotifications.synchronizationOfDeviceInfoCompleted(buildSynchronizationCommandResult(response));
  }

  private void handleInventory(final CommandResponse response) {
    LOGGER.info("Handling device inventory synchronization response: {}", response);
    if (isValid(response)) {
      final OpticalRouterInventory opticalRouterInventory = (OpticalRouterInventory) response.data();
      opticalRouterInventoryPersistenceAdapter.processInventory(response.deviceId(), opticalRouterInventory);
    }
    opticalRouterDriverManagerNotifications.synchronizationOfInventoryCompleted(buildSynchronizationCommandResult(response));
  }

  private void handleStatus(final CommandResponse response) {
    LOGGER.info("Handling device status response: {}", response);
    if (isValid(response)) {
      final OpticalRouterStatus opticalRouterStatus = (OpticalRouterStatus) response.data();
      opticalRouterStatusPersistenceAdapter.processOpticalRouterStatus(opticalRouterStatus);
    }
    opticalRouterDriverManagerNotifications.synchronizationOfStatusCompleted(buildSynchronizationCommandResult(response));
  }

  private void handleNotification(final CommandResponse commandResponse) {
    LOGGER.info("Handling notification response: {}", commandResponse);
    final OpticalRouterDriverNotification opticalRouterDriverNotification = (OpticalRouterDriverNotification) commandResponse.data();

    // Build the notification object
    final OpticalRouterNotification notification = OpticalRouterEventMapper.buildOpticalRouterNotification(
        commandResponse.deviceId(), opticalRouterDriverNotification);

    // Send to Kafka for testing
    try {
      kafkaProducer.sendNotification(notification);
      LOGGER.info("Successfully sent notification to Kafka: {}", notification);
    } catch (Exception e) {
      LOGGER.error("Failed to send notification to Kafka: {}", notification, e);
    }

    // Continue with existing processing
    opticalRouterNotificationHandler.handleNotification(notification);
  }

  private boolean isValid(final CommandResponse response) {
    return Status.SUCCESS.equals(response.status());
  }

  private com.adva.nlms.opticalrouter.api.polling.out.OpticalRouterDeviceInfo buildOpticalRouterDeviceInfo(final UUID deviceID, final OpticalRouterDeviceInfo opticalRouterDeviceInfo) {
    return new com.adva.nlms.opticalrouter.api.polling.out.OpticalRouterDeviceInfo(
      deviceID,
      opticalRouterDeviceInfo.deviceType(),
      opticalRouterDeviceInfo.name(),
      opticalRouterDeviceInfo.serialNumber(),
      opticalRouterDeviceInfo.softwareVersion(),
      opticalRouterDeviceInfo.semanticSoftwareVersion()
    );
  }

  private SynchronizationCommandResult buildSynchronizationCommandResult(final CommandResponse response) {
    return new SynchronizationCommandResult(response.deviceId(), mapCommandStatus(response.status()), response.message());
  }

  private CommandStatus mapCommandStatus(final Status status) {
    return switch (status) {
      case SUCCESS -> CommandStatus.SUCCESS;
      case TIMEOUT -> CommandStatus.TIMEOUT;
      case UNSUPPORTED_DEVICE -> CommandStatus.UNSUPPORTED_DEVICE;
      case FAILURE -> CommandStatus.DRIVER_ERROR;
    };
  }

}