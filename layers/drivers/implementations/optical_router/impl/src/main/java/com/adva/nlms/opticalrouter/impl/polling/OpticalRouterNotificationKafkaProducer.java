/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: bkaminsk
 */

package com.adva.nlms.opticalrouter.impl.polling;

import com.adva.nlms.opticalrouter.api.polling.out.OpticalRouterNotification;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.kafka.core.KafkaTemplate;

/**
 * Simple Kafka producer for testing OpticalRouter notifications.
 */
public class OpticalRouterNotificationKafkaProducer {
    
    private static final Logger LOGGER = LogManager.getLogger(OpticalRouterNotificationKafkaProducer.class);
    private static final String TOPIC_NAME = "optical-router-notifications";
    
    private final KafkaTemplate<String, Object> kafkaTemplate;
    
    public OpticalRouterNotificationKafkaProducer(KafkaTemplate<String, Object> kafkaTemplate) {
        this.kafkaTemplate = kafkaTemplate;
    }
    
    /**
     * Sends an OpticalRouter notification to <PERSON>f<PERSON> for testing.
     *
     * @param notification the notification to send
     */
    public void sendNotification(OpticalRouterNotification notification) {
        try {
            String messageKey = notification.neId().toString();
            
            LOGGER.info("Sending OpticalRouter notification to Kafka topic '{}' with key '{}': {}", 
                       TOPIC_NAME, messageKey, notification);
            
            kafkaTemplate.send(TOPIC_NAME, messageKey, notification)
                .whenComplete((result, throwable) -> {
                    if (throwable != null) {
                        LOGGER.error("Failed to send OpticalRouter notification to Kafka: {}", 
                                   notification, throwable);
                    } else {
                        LOGGER.info("Successfully sent OpticalRouter notification to Kafka at offset {}: {}", 
                                  result.getRecordMetadata().offset(), notification);
                    }
                });
                
        } catch (Exception e) {
            LOGGER.error("Error sending OpticalRouter notification to Kafka: {}", notification, e);
        }
    }
}
