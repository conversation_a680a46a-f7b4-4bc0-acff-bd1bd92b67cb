/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: piotrno
 */

package com.adva.sdn.enums;


public enum ColorMode {
  UNKNOWN(0, "Unknown"),
  COLOR_AWARE(1, "Color Aware"),
  COLOR_BLIND(2, "Color Blind"),
  COLOR_AWARE_DROP_RED(3, "Color Aware Drop Red"),
  COLOR_BLIND_DROP_RED(4, "Color Blind Drop Red");

  ColorMode(int intValue, String stringValue) {
    this.intValue = intValue;
    this.stringValue = stringValue;
  }

  public static ColorMode fromInt(int intValue) {
    for (ColorMode value : ColorMode.values()) {
      if (value.getInt() == intValue) {
        return value;
      }
    }
    return UNKNOWN;
  }
  public int getInt() {
    return intValue;
  }
  public String getString() {
    return stringValue;
  }
  private final int intValue;
  private final String stringValue;

  @Override
  public String toString() {
    return stringValue;
  }
}
