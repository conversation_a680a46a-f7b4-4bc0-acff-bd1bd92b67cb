/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: tomaszw
 */
package com.adva.nlms.mediation.ne_comm.snmp.enumeration.fsp150MXCP;

import com.adva.nlms.common.snmp.ValueOutOfEnumException;

public enum AdminState {
    UP("Up", 1), DOW<PERSON>("Down", 2), TESTING("Testing", 3), LINK_DOWN("Link down", 4), NA("N/A", -1);

    private final String desc;
    private final int intValue;

    AdminState(String desc, Integer intValue) {
        this.desc = desc;
        this.intValue = intValue;
    }

    public String getString() {
        return desc;
    }

    public Integer getIntValue() {
        return intValue;
    }

    public static AdminState fromInt(int intValue) {
        for (AdminState state : AdminState.values()) {
            if (state.getIntValue() == intValue) {
                return state;
            }
        }
        throw new ValueOutOfEnumException("Unknown value: "+ intValue);
    }
    public int getInt () { return intValue; }
}
