/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: msteiner
 */

package com.adva.nlms.mediation.ne_comm.snmpscan.scanplan.iterator;

import com.adva.nlms.mediation.ne_comm.snmpscan.scanplan.adapter.ScanEntity;
import com.adva.nlms.mediation.ne_comm.snmpscan.scanplan.adapter.ScanEntityAdapter;
import com.adva.nlms.mediation.ne_comm.snmpscan.scanplan.manager.DefaultScanPlanResultSet;
import com.adva.nlms.mediation.ne_comm.snmpscan.scanplan.manager.ScanPlanResultSet;
import com.adva.nlms.mediation.ne_comm.snmpscan.scantable.DefaultScanEntityFactory;
import com.adva.nlms.mediation.ne_comm.snmpscan.scantable.util.ScanPlanSNMPAdapter;
import org.snmp4j.smi.OID;

import java.util.Iterator;
import java.util.List;
import java.util.Map;

public class ScanPlanResultSetIterator implements ScanPlanIterator {

  private ScanPlanResultSet            scanPlanResultSet;
  private Map<Integer, List<OID>>      entityTypeMap;
  private ScanPlanSNMPAdapter          scanPlanSNMPAdapter;  
  private Iterator<ScanEntityAdapter>  iterator;

  public ScanPlanResultSetIterator(ScanPlanResultSet scanPlanResultSet) {
    this.scanPlanResultSet = scanPlanResultSet;
  }

  @Override
  public Iterator<ScanEntityAdapter> iterator() {
    ScanPlanResultSet scanPlanResultSetCopy = new DefaultScanPlanResultSet();
    for (ScanEntityAdapter entity : scanPlanResultSet) {
      if (entity.isScanEntity()) {
        ScanEntity newEntity = DefaultScanEntityFactory.createDefaultScanEntity((ScanEntity) entity);
        scanPlanResultSetCopy.addEntry(newEntity);
      
      } else {
        scanPlanResultSetCopy.addEntry(entity);
      }

    }
    iterator = scanPlanResultSetCopy.getValues().iterator();
    return this;
  }

  @Override
  public boolean hasNext() {
    return iterator.hasNext();
  }

  @Override
  public ScanEntityAdapter next() {
    return iterator.next();
  }

  @Override
  public void remove() {
    iterator.remove();
  }

  @Override  
  public Map<Integer, List<OID>> getEntityTypeMap() {
    return entityTypeMap;
  }

  @Override  
  public void setEntityTypeMap(Map<Integer, List<OID>> entityTypeMap) {
    this.entityTypeMap = entityTypeMap;
  }

  @Override  
  public ScanPlanSNMPAdapter getScanPlanSNMPAdapter() {
    return scanPlanSNMPAdapter;
  }

  @Override  
  public void setScanPlanSNMPAdapter(ScanPlanSNMPAdapter scanPlanSNMPAdapter) {
    this.scanPlanSNMPAdapter = scanPlanSNMPAdapter;
  }

  @Override
  public int getSize() {
    return scanPlanResultSet.size();
  }  
}
