/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: msteiner
 */
package com.adva.nlms.mediation.ne_comm.snmpscan.scantable.manager;

/**
  *
  *      Generated by the Scan Table Generator.
  * !! Please, do not change the content of class !!
  *
  */
public interface ScanTableIndexManager extends ScanTableManager {

      public int getIfIndex();                                        
      public int getPrimaryIndex();                                   
      public int getNativeIndex();                                    
      public int getCompositeSlotIndex();                             
      public int getCompositePortIndex();                             
      public int getCompositePlugIndex();                             
      public int getCompositeModuleIndex();                           
      public int getCompositeF3LagIndex();                            
      public int getCompositeF3LagPortIndex();                        
      public int getF3LagPortPrimaryIndex();                          
      public int getContainedInFK();                                  
      public int getNeIndex();                                        
      public int getEntityIndex();                                    
      public int getInventoryIndex();                                 
      public int getShelfIndex();                                     
      public int getAccPortIndex();                                   
      public int getSlotIndex();                                      
      public int getPortIndex();                                      
      public int getPlugIndex();                                      
      public int getModuleIndex();                                    
      public int getFlowIndex();                                      
      public int getTypeIndex();                                      
      public int getShaperIndex();                                    
      public int getPolicerIndex();                                   
      public int getPolicerTypeIndex();                               
      public int getF3SyncIndex();                                    
      public int getF3RefIndex();                                     
      public int getF3LagIndex();                                     
      public int getF3LagPortIndex();
      public int getF3LagServiceMapIndex();
      public int getMdIndex();                                        
      public int getMaIndex();                                        
      public int getMaCompIndex();                                    
      public int getMepIndex();                                       
      public int getEsaIndex();                                       
      public int getEdfaIndex();                                      
      public int getPtpIndex();                                       
      public int getPtpTableIndex();                                  
      public int getWdmTunnelIndex();                                 
      public int getVchIndex();                                       
      public int getApsIndex();                                       
      public int getFanIndex();                                       
      public int getRackIndex();                                      
      public int getOpticalLinkIndex();                               
      public int getLogicalCPIndex();                                 
      public int getPowerSupplyIndex();                               
      public int getCrossConnectIndex();                              
      public int getCrossConnectFromIndex();                          
      public int getCrossConnectToIndex();                            
      public int getCnxConnectionIndex();                             
      public int getIntraNeConnectionIndex();                         
      public int getNumberedTeLinkIndex();                            
      public int getUnnumberedTeLinkIndex();
      public int getEthTrafficPortVlanLpbkIndex();
      public int getPtpClockIndex();
      public int getPtpPortIndex();
      public int getRedundancyIndex();
      public int getFeatureIndex();
      public int getAccFlowPointIndex();
      public int getNetFlowPointIndex();
      public int getPolicerEnvelopeIndex();
      public int getUnitIndex();
      public int getErpIndex();
      public int getGroupIndex();
      public int getSlaveIndex();
      public int getPgIndex();
}
