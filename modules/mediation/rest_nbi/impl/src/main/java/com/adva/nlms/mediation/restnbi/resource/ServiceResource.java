/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: shait
 */

package com.adva.nlms.mediation.restnbi.resource;

import com.adva.nlms.infrastucture.security.permission.api.PermissionAction;
import com.adva.nlms.common.redundancy.WorkMode;
import com.adva.nlms.common.rest.MDRestPath;
import com.adva.nlms.mediation.common.rest.MDRestComponent;
import com.adva.nlms.mediation.restnbi.ctrl.ServicesCtrl;
import com.adva.nlms.mediation.restnbi.exception.ServiceException;
import com.adva.nlms.mediation.restnbi.model.Service;
import com.adva.nlms.mediation.restnbi.model.ServiceIdToName;
import com.adva.nlms.infrastucture.security.permission.api.Authorization;
import com.adva.nlms.mediation.server.ServerCtrl;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Component;
import jakarta.ws.rs.core.Response;

import jakarta.inject.Singleton;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import java.util.List;

@Component
@Path(MDRestPath.REST_NBI.PATH)
@Produces({MediaType.APPLICATION_JSON})
@Singleton
@MDRestComponent(basicAuthentication = true)
public class ServiceResource {
    private static final Logger log = LogManager.getLogger(ServiceResource.class);

    private final ServicesCtrl servicesCtrl;

    private final ServerCtrl serverCtrl;

    /**
     * A constructor injection
     *
     * @param servicesCtrl - ServicesCtrl
     * @param serverCtrl - ServerCtrl
     */
    public ServiceResource(ServicesCtrl servicesCtrl,ServerCtrl serverCtrl) {
        this.servicesCtrl = servicesCtrl;
        this.serverCtrl = serverCtrl;
    }


//    /**
//     * return Services List.
//     *
//     * @return String.
//     */
//    @GET
//    @Path(MDRestPath.REST_NBI.SERVICES)
//    @Produces({MediaType.APPLICATION_JSON})
//    public List<Service> getServices() {
//        try {
//            long start = -1L;
//            if (log.isDebugEnabled()) {
//                start = System.currentTimeMillis();
//            }
//            List<Service> services = this.servicesCtrl.getServices();
//            log.debug("getServices take:" + (System.currentTimeMillis() - start) + "ms ,count:" + services.size());
//            return services;
//        } catch (Exception e) {
//            throw new InternalServerErrorException("Internal Server Error getServices", e);
//        }
//    }

    /**
     * return Service with entity list.
     *
     * @return String.
     */
    @GET
    @Path(MDRestPath.REST_NBI.SERVICES + "/{serviceName}")
    @Authorization(permissions = PermissionAction.BrowseServices)
    @Produces({MediaType.APPLICATION_JSON})
    public Response getServiceByName(@PathParam("serviceName") String serviceName, @QueryParam("pwr") Boolean pwr) {
        try {
            if(isMaster()){
                Boolean _pwr = pwr == Boolean.TRUE ? Boolean.TRUE : Boolean.FALSE;
                Service service = this.servicesCtrl.getServicesByName(serviceName.replaceAll("[\n\r]",""), _pwr);
                return Response.ok(service, MediaType.APPLICATION_JSON).build();
            }
            else{
                return Response.status(409).entity(String.format("Service %s cant be return from slave", serviceName)).build();
            }
        } catch (ServiceException e) {
            return Response.status(404).entity(String.format("Service %s not found", serviceName)).build();
        } catch (Exception e) {
            throw new InternalServerErrorException("Internal Server Error getServiceByName", e);
        }
    }

    @GET
    @Path(MDRestPath.REST_NBI.SERVICES)
    @Authorization(permissions = PermissionAction.BrowseServices)
    @Produces({MediaType.APPLICATION_JSON})
    public Response getServicesIdAndNameByRegexServiceName(@DefaultValue("*") @QueryParam("search") String search){
        try {
            if(isMaster()){
                List<ServiceIdToName> services = servicesCtrl.getServicesIdAndNameByRegexServiceName(search);
                return Response.ok(services, MediaType.APPLICATION_JSON).build();
            }
            else{
                return Response.status(409).entity(String.format("Service %s cant be return from slave", search)).build();
            }
        } catch (Exception e){
            throw new InternalServerErrorException("Internal Server Error getServicesIdAndNameByRegexServiceName", e);
        }
    }

    private boolean isMaster(){
        boolean isMaster = (serverCtrl.getWorkMode() == WorkMode.MASTER);
        if (log.isDebugEnabled()) {
            log.debug("getServiceById isMaster : "+isMaster);
        }
        return isMaster;
    }
}
