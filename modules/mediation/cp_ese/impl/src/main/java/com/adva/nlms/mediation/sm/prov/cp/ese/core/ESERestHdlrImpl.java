/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: jiaxiuz
 */
package com.adva.nlms.mediation.sm.prov.cp.ese.core;

import com.adva.nlms.common.cp.ese.CssCreate;
import com.adva.nlms.common.cp.ese.Equalize;
import com.adva.nlms.common.cp.ese.OssCreate;
import com.adva.nlms.common.cp.ese.SegmentID;
import com.adva.nlms.common.cp.ese.SegmentInventory;
import com.adva.nlms.common.cp.policymodel.ErrorMessage;
import com.adva.nlms.common.snmp.MDOperationFailedException;
import com.adva.nlms.mediation.common.AuthorizationException;
import com.adva.nlms.mediation.common.ThrowableReference;
import com.adva.nlms.mediation.common.UntrustedCertificateException;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.NetworkElementHdlrLocal;
import com.adva.nlms.mediation.config.fsp_r7.NetworkElementFSP_R7;
import com.adva.nlms.mediation.config.fsp_r7.NetworkElementFSP_R7Impl;
import com.adva.nlms.mediation.ne_comm.f7.cp.rest.CpRestClient;
import com.adva.nlms.mediation.ne_comm.f7.cp.rest.CpRestClientFactory;
import com.adva.nlms.mediation.sm.prov.cp.ese.api.ESERestHdlr;
import com.adva.nlms.mediation.sm.prov.cp.ese.api.MessageListener;
import com.adva.nlms.mediation.sm.prov.cp.ese.enums.Operation;
import com.adva.nlms.mediation.sm.prov.cp.ese.enums.SegmentType;
import jakarta.ws.rs.client.Entity;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.net.URISyntaxException;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;

@Component
public class ESERestHdlrImpl implements ESERestHdlr {
    private static final Logger log = LoggerFactory.getLogger(ESERestHdlrImpl.class);

    private static final int STATUS_OK = Response.Status.OK.getStatusCode();
    private static final int STATUS_BAD_REQUEST = Response.Status.BAD_REQUEST.getStatusCode();
    private static final String STREAM_CHARSET = "UTF-8";

    @SuppressWarnings("FieldCanBeLocal")
    private String URL_BASE = "/cprest/public/services/%s/%s";

    private CpRestClientFactory cpRestClientFactory;
    private NetworkElementHdlrLocal networkElementHdlrLocal;
    private ESEConfiguration eseConfiguration;

    @Autowired
    public ESERestHdlrImpl(CpRestClientFactory cpRestClientFactory,
                           NetworkElementHdlrLocal networkElementHdlrLocal,
                           ESEConfiguration eseConfiguration) {
        this.cpRestClientFactory = cpRestClientFactory;
        this.networkElementHdlrLocal = networkElementHdlrLocal;
        this.eseConfiguration = eseConfiguration;
    }


    @Override
    public void createOSS(int neId, OssCreate params, MessageListener messageListener) throws AuthorizationException, MDOperationFailedException, UntrustedCertificateException, URISyntaxException {
        log.info("createOSS({},{},{})", neId, params, (messageListener == null ? "N/A" : messageListener.getClass()));
        operateAsync(neId, params, Operation.CREATE, SegmentType.OSS, messageListener);
        log.info("createOSS completed");
    }

    @Override
    public void destroyOSS(int neId, SegmentID params, MessageListener messageListener) throws AuthorizationException, MDOperationFailedException, UntrustedCertificateException, URISyntaxException {
        log.info("destroyOSS({},{},{})", neId, params, (messageListener == null ? "N/A" : messageListener.getClass()));
        operateAsync(neId, params, Operation.DESTROY, SegmentType.OSS, messageListener);
        log.info("destroyOSS completed");
    }

    @Override
    public void disableOSS(int neId, SegmentID params, MessageListener messageListener) throws AuthorizationException, MDOperationFailedException, UntrustedCertificateException, URISyntaxException {
        log.info("disableOSS({},{},{})", neId, params, (messageListener == null ? "N/A" : messageListener.getClass()));
        operateAsync(neId, params, Operation.DISABLE, SegmentType.OSS, messageListener);
        log.info("disableOSS completed");
    }


    @Override
    public void enableOSS(int neId, SegmentID params, MessageListener messageListener) throws AuthorizationException, MDOperationFailedException, UntrustedCertificateException, URISyntaxException {
        log.info("enableOSS({},{},{})", neId, params, (messageListener == null ? "N/A" : messageListener.getClass()));
        operateAsync(neId, params, Operation.ENABLE, SegmentType.OSS, messageListener);
        log.info("enableOSS completed");
    }

    @Override
    public void equalize(int neId, Equalize params, MessageListener messageListener) throws AuthorizationException, MDOperationFailedException, UntrustedCertificateException, URISyntaxException {
        log.info("equalizeOSS({},{},{})", neId, params, (messageListener == null ? "N/A" : messageListener.getClass()));
        operateAsync(neId, params, Operation.EQUALIZE, SegmentType.OSS, messageListener);
        log.info("equalizeOSS completed");
    }

    @Override
    public void createCSS(int neId, CssCreate params, MessageListener messageListener) throws AuthorizationException, MDOperationFailedException, UntrustedCertificateException, URISyntaxException {
        log.info("createCSS({},{},{})", neId, params, (messageListener == null ? "N/A" : messageListener.getClass()));
        operateAsync(neId, params, Operation.CREATE, SegmentType.CSS, messageListener);
        log.info("createCSS completed");
    }

    @Override
    public void destroyCSS(int neId, SegmentID params, MessageListener messageListener) throws AuthorizationException, MDOperationFailedException, UntrustedCertificateException, URISyntaxException {
        log.info("destroyCSS({},{},{})", neId, params, (messageListener == null ? "N/A" : messageListener.getClass()));
        operateAsync(neId, params, Operation.DESTROY, SegmentType.CSS, messageListener);
        log.info("destroyCSS completed");
    }

    @Override
    public void disableCSS(int neId, SegmentID params, MessageListener messageListener) throws AuthorizationException, MDOperationFailedException, UntrustedCertificateException, URISyntaxException {
        log.info("disableCSS({},{},{})", neId, params, (messageListener == null ? "N/A" : messageListener.getClass()));
        operateAsync(neId, params, Operation.DISABLE, SegmentType.CSS, messageListener);
        log.info("disableCSS completed");
    }

    @Override
    public void enableCSS(int neId, SegmentID params, MessageListener messageListener) throws AuthorizationException, MDOperationFailedException, UntrustedCertificateException, URISyntaxException {
        log.info("enableCSS({},{},{})", neId, params, (messageListener == null ? "N/A" : messageListener.getClass()));
        operateAsync(neId, params, Operation.ENABLE, SegmentType.CSS, messageListener);
        log.info("enableCSS completed");
    }

    @Override
    public SegmentInventory getInventory(int neId, SegmentID crsMessage, String type) throws AuthorizationException, MDOperationFailedException, UntrustedCertificateException, URISyntaxException {
        log.info("getInventory({},{})", neId, crsMessage);
        try {
            NetworkElement ne = networkElementHdlrLocal.getNetworkElement(neId);
            if (ne instanceof NetworkElementFSP_R7Impl) {
                String url = String.format(URL_BASE, SegmentType.valueOf(type).getAction(), Operation.GET_INVENTORY.getAction());
                Response response;
                Optional<CpRestClient> cpRestClient = cpRestClientFactory.getResource(neId);
                if (cpRestClient.isPresent()) {
                    response = cpRestClient.get().post(url, Entity.entity(crsMessage, MediaType.APPLICATION_JSON_TYPE));
                    validateResponse(response);
                } else {
                    throw new MDOperationFailedException("Failed to retrieve cp rest client");
                }

                try {
                    return response.readEntity(SegmentInventory.class);
                } catch (RuntimeException x) {
                    throw new MDOperationFailedException("Network element returned unexpected response");
                }
            } else {
                throw new IllegalArgumentException("Network element has no compatible interface");
            }
        } finally {
            log.info("getInventory completed");
        }
    }

    private void operateAsync(int neid, Serializable crsMessage, Operation operation, SegmentType segmentType,
                              MessageListener messageListener) throws MDOperationFailedException, UntrustedCertificateException, AuthorizationException, URISyntaxException {
        NetworkElement ne = networkElementHdlrLocal.getNetworkElement(neid);
        if (ne instanceof NetworkElementFSP_R7Impl) {
            String url = String.format(URL_BASE, segmentType.getAction(), operation.getAction());
            Optional<CpRestClient> cpRestClient = cpRestClientFactory.getResource(neid);
            if (cpRestClient.isPresent()) {
                upgradeRestRequest(url, crsMessage, messageListener, ne, cpRestClient.get());
            } else {
                throw new MDOperationFailedException("Failed to retrieve cp rest client");
            }
        } else {
            throw new IllegalArgumentException("Network element has no compatible interface");
        }
    }


    private void upgradeRestRequest(String url, Serializable crsMessage, MessageListener messageListener,
                                    NetworkElement ne, CpRestClient cpRestClient)
            throws URISyntaxException, UntrustedCertificateException, MDOperationFailedException, AuthorizationException {
        if (messageListener != null) {
            SegmentID segmentID;
            if (crsMessage instanceof OssCreate) {
                segmentID = ((OssCreate) crsMessage).getId();
            } else if (crsMessage instanceof CssCreate) {
                segmentID = ((CssCreate) crsMessage).getId();
            } else if (crsMessage instanceof SegmentID) {
                segmentID = (SegmentID) crsMessage;
            } else if(crsMessage instanceof Equalize){
                segmentID = ((Equalize) crsMessage).getId();
            }
            else {
                throw new IllegalArgumentException("Unsupported crsMessage message type: " + crsMessage.getClass().getName());
            }
            String systemId = ((NetworkElementFSP_R7) ne).getSystemId();
            //cpRestClient.get("",)
            String sessionId = getSessionId(cpRestClient);
            CommunicationHandler communicationHandler = new CommunicationHandler(eseConfiguration, segmentID, crsMessage, url, cpRestClient, messageListener);
            CPClientWebSocket cpClientWebSocket = new CPClientWebSocket(eseConfiguration, communicationHandler, systemId, ne.getIPAddress(), sessionId);
            cpClientWebSocket.connect();
        } else {
            Response response = cpRestClient.post(url, Entity.entity(crsMessage, MediaType.APPLICATION_JSON_TYPE));
            validateResponse(response);
        }
    }

    private String getSessionId(CpRestClient cpRestClient) throws UntrustedCertificateException, AuthorizationException {
        AtomicReference<String> resultRef = new AtomicReference<>();
        ThrowableReference<UntrustedCertificateException> tRef = new ThrowableReference<>();
        cpRestClient.get("/scripts/session_refresh", response -> {
            if (response.getStatusInfo().getFamily() == Response.Status.OK.getFamily()) {
                resultRef.set(cpRestClient.getSessionId());
            } else {
                try {
                    cpRestClient.ensureSessionIsOpen();
                    resultRef.set(cpRestClient.getSessionId());
                } catch (UntrustedCertificateException e) {
                    tRef.set(e);
                }
            }
        });
        tRef.throwIfPresent();
        return resultRef.get();
    }


    private void validateResponse(Response response) throws MDOperationFailedException {
        if (response != null) {
            if (response.getStatusInfo().getStatusCode() != STATUS_OK) {
                String error;
                if (response.getStatusInfo().getStatusCode() == STATUS_BAD_REQUEST) {
                    ErrorMessage errorMessage = response.readEntity(ErrorMessage.class);
                    if (errorMessage != null && errorMessage.getMsg() != null) {
                        error = String.join(", ", errorMessage.getMsg());
                        if (error != null && !error.isEmpty()) {
                            throw new MDOperationFailedException(error);
                        }
                    }
                }
                error = response.getStatusInfo().getReasonPhrase();
                if (error != null && !error.isEmpty()) {
                    throw new MDOperationFailedException(error);
                }
                throw new MDOperationFailedException("Unknown response");
            }
        }
    }

}
