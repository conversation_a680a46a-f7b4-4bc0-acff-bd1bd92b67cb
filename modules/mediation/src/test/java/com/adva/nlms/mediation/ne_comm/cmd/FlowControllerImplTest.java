/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: twitting
 */
package com.adva.nlms.mediation.ne_comm.cmd;

import com.adva.nlms.mediation.ne_comm.SNMPCtrl;
import com.adva.nlms.mediation.ne_comm.cmd.action.Action;
import com.adva.nlms.mediation.ne_comm.cmd.action.CommandAction;
import com.adva.nlms.mediation.ne_comm.cmd.action.CoreActions;
import org.junit.Test;

import static junit.framework.Assert.assertEquals;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class FlowControllerImplTest extends AbstractSNMPCommanderTest {


  @Test
  public void testProcessNextCommandAction() throws Exception {
    //boolean processNextCommandAction(final SNMPCommand command, final CommandAction nextAction, final Object ... actionParams) throws SNMPCommandException {
    SNMPCtrl snmpCtrl = getSNMPCtrlMock();
    Commander commanderMock = snmpCtrl.getCommander();

    final SNMPAdapter snmpAdapterMock = mock(SNMPAdapter.class);
    final SNMPCommandFactoryImpl commandFactoryMock = mock(SNMPCommandFactoryImpl.class);
    final CommandActionExecutor executorMock = CommandActionExecutorImpl.getInstance();
    final ActionRequestProcessor actionRequestProcessorMock = mock(ActionRequestProcessor.class);

    final FlowController flowController = new FlowControllerImpl(commanderMock, commandFactoryMock, executorMock, actionRequestProcessorMock, snmpAdapterMock);

    SNMPCommand command = new TestSimpleSNMPCommand(snmpCtrl);
    Action nextAction = new Action(SNMPCommand.Actions.STEP1);


    // increment retry counter artificially
    command.getCommandParams().incRetryCounter();

    // return same command
    when(commandFactoryMock.getNextCommand(command, nextAction.getAction(), true)).thenReturn(command);
    when(commandFactoryMock.getNextCommand(command, nextAction.getAction(), false)).thenReturn(command);

    boolean continueProcessing = flowController.processNextCommandAction(command, nextAction);

    verify(commandFactoryMock, times(1)).getNextCommand(command, nextAction.getAction(), true);
    verify(commandFactoryMock, times(1)).getNextCommand(command, nextAction.getAction(), false);

    // check history - if FlowHistoryEvent correctly added
    assertEquals(3, command.getCommandParams().getActionHistory().getFlowHistory().size());
    assertEquals(0, command.getCommandParams().getRetryCounter());

  }

  private static class TestSimpleSNMPCommand extends SNMPCommand<CommandParams> {
    public TestSimpleSNMPCommand(SNMPCtrl snmpCtrl) {
      super(snmpCtrl);
    }

    @ActionMethod(Actions.START)
    public CommandAction start() {
      return null;
    }

    @ActionMethod(Actions.STEP1)
    public CommandAction step1() {
      return CoreActions.FINISH;
    }
  }
}
