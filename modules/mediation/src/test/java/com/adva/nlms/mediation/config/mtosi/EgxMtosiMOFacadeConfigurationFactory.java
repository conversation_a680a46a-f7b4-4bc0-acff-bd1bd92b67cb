/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: tomaszm
 */

package com.adva.nlms.mediation.config.mtosi;

import com.adva.nlms.common.config.netypes.NEType;
import com.adva.nlms.mediation.config.AbstractNetworkElementPollingWorkerBasisImpl;
import com.adva.nlms.mediation.config.f3.NetworkElementPollingWorkerF3BeanContainer;
import com.adva.nlms.mediation.config.fsp150egx.NetworkElementFSP150EGXImpl;
import com.adva.nlms.mediation.config.fsp150egx.NetworkElementPollingWorkerFSP150EGXImpl;
import com.adva.nlms.mediation.config.fsp150egx.factory.impl.DBObjectFactoryFSP150EGXImpl;
import com.adva.nlms.mediation.config.fsp150egx.mtosi.MtosiMOFacadeEgxImpl;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPCommFailure;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import static org.mockito.Mockito.mock;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration({
        "classpath:**/egx-mtosi-mo-facade-test-additions-context.xml",
        "/META-INF/config/mtosi/egx-mtosi-mo-facade-context.xml",
})
public class EgxMtosiMOFacadeConfigurationFactory extends MtosiMOFacadeConfigurationFactory<NetworkElementFSP150EGXImpl> {

  private static final Logger LOG = LoggerFactory.getLogger(EgxMtosiMOFacadeConfigurationFactory.class);

  @Autowired
  private MtosiMOFacadeEgxImpl mtosiMOFacade;

  public MtosiMOFacadeEgxImpl getMtosiMOFacade () {
    return mtosiMOFacade;
  }

  public MtosiMOFacadeConfiguration createConfiguration(int neId, String ipAddr) throws SNMPCommFailure {
    return new MtosiMOFacadeConfiguration(
            this,
            NetworkElementFSP150EGXImpl.class,
            neId,
            ipAddr,
            NEType.FSP_150EGX,
            new DBObjectFactoryFSP150EGXImpl(),
            new PollinWorkerInitializer<NetworkElementFSP150EGXImpl>() {
              @Override
              AbstractNetworkElementPollingWorkerBasisImpl init (NetworkElementFSP150EGXImpl ne) {
                return new NetworkElementPollingWorkerFSP150EGXImpl(ne, mock(NetworkElementPollingWorkerF3BeanContainer.class));
              }
            });
    }


}
