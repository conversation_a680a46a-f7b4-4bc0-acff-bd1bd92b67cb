package com.adva.nlms.mediation.opticalrouter;

import com.adva.nlms.common.AlarmTrapType;
import com.adva.nlms.common.AlarmTypeHandler;
import com.adva.nlms.mediation.event.EventDTO;
import com.adva.nlms.opticalrouter.api.polling.out.OpticalRouterEvent;
import com.adva.nlms.opticalrouter.api.polling.out.OpticalRouterNotification;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.Method;
import java.util.NoSuchElementException;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class TrapProcessorOpticalRouterFixedTest {

    private TrapProcessorOpticalRouter trapProcessor;
    
    @Mock
    private AlarmTypeHandler mockAlarmTypeHandler;
    
    @Mock
    private AlarmTrapType mockAlarmTrapType;
    
    private EventDTO testEvent;
    private static final int TEST_NE_TYPE = 12345;

    @BeforeEach
    void setUp() throws Exception {
        trapProcessor = new TrapProcessorOpticalRouter();
        
        // Use reflection to inject the mock alarm type handler
        java.lang.reflect.Field field = TrapProcessorOpticalRouter.class.getDeclaredField("alarmTypeHandler");
        field.setAccessible(true);
        field.set(trapProcessor, mockAlarmTypeHandler);
        
        testEvent = new EventDTO();
        testEvent.sourceNEType = TEST_NE_TYPE;
    }

    @Test
    void getAlarmTrapType_successWithEntityAlarmedEvent() throws Exception {
        // Arrange
        OpticalRouterEvent.EntityAlarmed alarmedEvent = new OpticalRouterEvent.EntityAlarmed(
            "POWER_LOSS",
            OpticalRouterEvent.Direction.RX,
            OpticalRouterEvent.Severity.CRITICAL,
            System.currentTimeMillis()
        );
        
        OpticalRouterNotification notification = new OpticalRouterNotification(
            UUID.randomUUID(),
            "port-1",
            OpticalRouterNotification.EntityType.PORT,
            System.currentTimeMillis(),
            alarmedEvent
        );
        
        when(mockAlarmTypeHandler.getTrapDescriptorByTrapName(TEST_NE_TYPE, "POWER-LOSS"))
            .thenReturn(mockAlarmTrapType);

        // Act
        AlarmTrapType result = invokeGetAlarmTrapType(testEvent, notification);

        // Assert
        assertSame(mockAlarmTrapType, result);
        verify(mockAlarmTypeHandler).getTrapDescriptorByTrapName(TEST_NE_TYPE, "POWER-LOSS");
        // Should not try other methods since first one succeeded
        verify(mockAlarmTypeHandler, never()).getTrapDescriptorByTrapShortName(anyInt(), anyString());
    }

    @Test
    void getAlarmTrapType_successWithEntityLabelFallback() throws Exception {
        // Arrange
        OpticalRouterEvent.EntityAlarmed alarmedEvent = new OpticalRouterEvent.EntityAlarmed(
            "UNKNOWN_ALARM",
            OpticalRouterEvent.Direction.RX,
            OpticalRouterEvent.Severity.MINOR,
            System.currentTimeMillis()
        );
        
        OpticalRouterNotification notification = new OpticalRouterNotification(
            UUID.randomUUID(),
            "port-2",
            OpticalRouterNotification.EntityType.PORT,
            System.currentTimeMillis(),
            alarmedEvent
        );
        
        when(mockAlarmTypeHandler.getTrapDescriptorByTrapName(TEST_NE_TYPE, "UNKNOWN-ALARM"))
            .thenThrow(new RuntimeException("Not found"));
        when(mockAlarmTypeHandler.getTrapDescriptorByTrapShortName(TEST_NE_TYPE, "port-2"))
            .thenReturn(mockAlarmTrapType);

        // Act
        AlarmTrapType result = invokeGetAlarmTrapType(testEvent, notification);

        // Assert
        assertSame(mockAlarmTrapType, result);
        verify(mockAlarmTypeHandler).getTrapDescriptorByTrapName(TEST_NE_TYPE, "UNKNOWN-ALARM");
        verify(mockAlarmTypeHandler).getTrapDescriptorByTrapShortName(TEST_NE_TYPE, "port-2");
    }

    @Test
    void getAlarmTrapType_successWithNonAlarmEvent() throws Exception {
        // Arrange
        OpticalRouterEvent.EntityCreated createdEvent = new OpticalRouterEvent.EntityCreated(null);
        
        OpticalRouterNotification notification = new OpticalRouterNotification(
            UUID.randomUUID(),
            "port-3",
            OpticalRouterNotification.EntityType.PORT,
            System.currentTimeMillis(),
            createdEvent
        );
        
        // For non-alarm events, extractAlarmType returns entityLabel
        when(mockAlarmTypeHandler.getTrapDescriptorByTrapName(TEST_NE_TYPE, "port-3"))
            .thenReturn(mockAlarmTrapType);

        // Act
        AlarmTrapType result = invokeGetAlarmTrapType(testEvent, notification);

        // Assert
        assertSame(mockAlarmTrapType, result);
        verify(mockAlarmTypeHandler).getTrapDescriptorByTrapName(TEST_NE_TYPE, "port-3");
    }

    @Test
    void getAlarmTrapType_throwsExceptionWhenAllStrategiesFail() throws Exception {
        // Arrange
        OpticalRouterEvent.EntityAlarmed alarmedEvent = new OpticalRouterEvent.EntityAlarmed(
            "UNKNOWN_ALARM",
            OpticalRouterEvent.Direction.TX,
            OpticalRouterEvent.Severity.WARNING,
            System.currentTimeMillis()
        );
        
        OpticalRouterNotification notification = new OpticalRouterNotification(
            UUID.randomUUID(),
            "unknown-port",
            OpticalRouterNotification.EntityType.PORT,
            System.currentTimeMillis(),
            alarmedEvent
        );
        
        when(mockAlarmTypeHandler.getTrapDescriptorByTrapName(anyInt(), anyString()))
            .thenThrow(new RuntimeException("Not found"));
        when(mockAlarmTypeHandler.getTrapDescriptorByTrapShortName(anyInt(), anyString()))
            .thenThrow(new RuntimeException("Not found"));

        // Act & Assert
        NoSuchElementException exception = assertThrows(NoSuchElementException.class, 
            () -> invokeGetAlarmTrapType(testEvent, notification));
        
        assertTrue(exception.getMessage().contains("Trap not found"));
        assertTrue(exception.getMessage().contains("NE-type=" + TEST_NE_TYPE));
        assertTrue(exception.getMessage().contains("UNKNOWN-ALARM"));
        assertTrue(exception.getMessage().contains("unknown-port"));
    }

    @Test
    void extractAlarmType_returnsAlarmTypeForEntityAlarmed() throws Exception {
        // Arrange
        OpticalRouterEvent.EntityAlarmed alarmedEvent = new OpticalRouterEvent.EntityAlarmed(
            "SIGNAL_LOSS",
            OpticalRouterEvent.Direction.RX,
            OpticalRouterEvent.Severity.MAJOR,
            System.currentTimeMillis()
        );
        
        OpticalRouterNotification notification = new OpticalRouterNotification(
            UUID.randomUUID(),
            "port-4",
            OpticalRouterNotification.EntityType.PORT,
            System.currentTimeMillis(),
            alarmedEvent
        );

        // Act
        String result = invokeExtractAlarmType(notification);

        // Assert
        assertEquals("SIGNAL_LOSS", result);
    }

    @Test
    void extractAlarmType_returnsEntityLabelForNonAlarmEvent() throws Exception {
        // Arrange
        OpticalRouterEvent.EntityStateChanged stateChangedEvent = new OpticalRouterEvent.EntityStateChanged(
            null, null
        );
        
        OpticalRouterNotification notification = new OpticalRouterNotification(
            UUID.randomUUID(),
            "port-5",
            OpticalRouterNotification.EntityType.PORT,
            System.currentTimeMillis(),
            stateChangedEvent
        );

        // Act
        String result = invokeExtractAlarmType(notification);

        // Assert
        assertEquals("port-5", result);
    }

    @Test
    void isAlarmEvent_returnsTrueForEntityAlarmed() throws Exception {
        // Arrange
        OpticalRouterEvent.EntityAlarmed alarmedEvent = new OpticalRouterEvent.EntityAlarmed(
            "TEST_ALARM",
            OpticalRouterEvent.Direction.RX,
            OpticalRouterEvent.Severity.CRITICAL,
            System.currentTimeMillis()
        );
        
        OpticalRouterNotification notification = new OpticalRouterNotification(
            UUID.randomUUID(),
            "port-6",
            OpticalRouterNotification.EntityType.PORT,
            System.currentTimeMillis(),
            alarmedEvent
        );

        // Act
        boolean result = invokeIsAlarmEvent(notification);

        // Assert
        assertTrue(result);
    }

    @Test
    void isAlarmEvent_returnsFalseForNonAlarmEvent() throws Exception {
        // Arrange
        OpticalRouterEvent.EntityCreated createdEvent = new OpticalRouterEvent.EntityCreated(null);
        
        OpticalRouterNotification notification = new OpticalRouterNotification(
            UUID.randomUUID(),
            "port-7",
            OpticalRouterNotification.EntityType.PORT,
            System.currentTimeMillis(),
            createdEvent
        );

        // Act
        boolean result = invokeIsAlarmEvent(notification);

        // Assert
        assertFalse(result);
    }

    @Test
    void normalizeAlarmName_replacesSpacesWithHyphens() throws Exception {
        // Act
        String result = invokeNormalizeAlarmName("Signal Loss Alarm");

        // Assert
        assertEquals("Signal-Loss-Alarm", result);
    }

    @Test
    void normalizeAlarmName_handlesNullInput() throws Exception {
        // Act
        String result = invokeNormalizeAlarmName(null);

        // Assert
        assertEquals("", result);
    }

    // Helper methods to invoke private methods using reflection
    
    private AlarmTrapType invokeGetAlarmTrapType(EventDTO event, OpticalRouterNotification notification) throws Exception {
        Method method = TrapProcessorOpticalRouter.class.getDeclaredMethod("getAlarmTrapType", EventDTO.class, OpticalRouterNotification.class);
        method.setAccessible(true);
        return (AlarmTrapType) method.invoke(trapProcessor, event, notification);
    }

    private String invokeExtractAlarmType(OpticalRouterNotification notification) throws Exception {
        Method method = TrapProcessorOpticalRouter.class.getDeclaredMethod("extractAlarmType", OpticalRouterNotification.class);
        method.setAccessible(true);
        return (String) method.invoke(trapProcessor, notification);
    }

    private boolean invokeIsAlarmEvent(OpticalRouterNotification notification) throws Exception {
        Method method = TrapProcessorOpticalRouter.class.getDeclaredMethod("isAlarmEvent", OpticalRouterNotification.class);
        method.setAccessible(true);
        return (Boolean) method.invoke(trapProcessor, notification);
    }

    private String invokeNormalizeAlarmName(String description) throws Exception {
        Method method = TrapProcessorOpticalRouter.class.getDeclaredMethod("normalizeAlarmName", String.class);
        method.setAccessible(true);
        return (String) method.invoke(trapProcessor, description);
    }
}
