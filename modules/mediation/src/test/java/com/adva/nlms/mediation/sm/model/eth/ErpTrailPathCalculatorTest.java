/*
 * Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 * Owner: TomaszSi
 *
 * $Id: $
 */

package com.adva.nlms.mediation.sm.model.eth;

import com.adva.nlms.common.config.EntityIndex;
import com.adva.nlms.common.snmp.MDOperationFailedException;
import com.adva.nlms.common.snmp.MIBFSP150CM;
import com.adva.nlms.mediation.config.ManagedObjectDAO;
import com.adva.nlms.mediation.config.ManagedObjectDBImpl;
import com.adva.nlms.mediation.config.NetworkElementDBImpl;
import com.adva.nlms.mediation.config.ec.entity.facility.ctp.ConnectionTerminationPointEcDBImpl;
import com.adva.nlms.mediation.config.ec.entity.facility.ptp.PhysicalTerminationPointEcDBImpl;
import com.adva.nlms.mediation.config.f3.entity.erp.ErpGroupF3DBImpl;
import com.adva.nlms.mediation.config.f3.entity.erpunit.ErpUnitF3DBImpl;
import com.adva.nlms.mediation.config.f3.entity.lag.LagF3DAO;
import com.adva.nlms.mediation.config.f3.entity.lag.LagF3DBImpl;
import com.adva.nlms.mediation.config.f3.entity.lagport.LagPortF3DBImpl;
import com.adva.nlms.mediation.config.f3.entity.port.AbstractEthernetPortF3DBImpl;
import com.adva.nlms.mediation.config.f3.entity.port.acc.PortF3AccDBImpl;
import com.adva.nlms.mediation.config.f3.entity.port.net.PortF3NetDBImpl;
import com.adva.nlms.mediation.config.f4.F4EntityIndexHelper;
import com.adva.nlms.mediation.config.f4.NetworkElementF4DBImpl;
import com.adva.nlms.mediation.config.f4.entity.lag.LagF4DAO;
import com.adva.nlms.mediation.config.f4.entity.lag.LagF4DBImpl;
import com.adva.nlms.mediation.ec.support.EcEntityIndex;
import com.adva.nlms.mediation.ne_comm.snmp.enumeration.f3.ErpPortStatus;
import com.adva.nlms.mediation.topology.LineDBImpl;
import com.adva.nlms.mediation.topology.LinkEndpointEmbedded;
import com.adva.ethernet.ring.api.in.dto.EthernetRingPathElementDTO;
import com.adva.nlms.mediation.topology.ethernet.ring.api.dto.builders.EthernetRingPathElementDTOBuilder;
import com.adva.nlms.mediation.topology.ethernet.ring.business.exception.RingRestrictionException;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;


class ErpTrailPathCalculatorTest {

    List<LineDBImpl> lineDBImplList;
    List<LineDBImpl> lineDBImplListWithLag;
    List<ErpGroupF3DBImpl> erpGroupF3DBImplList;
    List<ErpGroupF3DBImpl> erpGroupF3DBImplListWithLag;

    //constants
    int ne1 = 45012;
    int ne2 = 45015;
    int ne3 = 45018;

    int line1 = 46243;
    int line2 = 46163;
    int line3 = 46249;

    int erp1 = 46200;
    int erp2 = 46106;
    int erp3 = 45980;

    String erp1Name = "ERP-ERP-15";
    String erp2Name = "ERP-ERP-16";
    String erp3Name = "ERP-1-1";

    String tp1 = "1";
    String tp2 = "2";
    String tp3 = "3";
    String tp4 = "4";
    String tp5 = "5";
    String tp6 = "6";

    String tp7 = "7";
    String tp8 = "8";


//lag case
    static final int lag1Id = 60001;
    static final int lag2Id = 60002;
    static final int lag1No = 1;
    static final int lag2No = 2;

    static final int lag1LogicalPortId = 70001;
    static final int lag2LogicalPortId = 70002;

    static final int lag2Member1 = 80001;
    static final int lag2Member2 = 80002;
    LagF4DAO lagF4DAO = mock(LagF4DAO.class);
    LagF3DAO lagF3DAO = mock(LagF3DAO.class);
    ManagedObjectDAO managedObjectDAO = mock(ManagedObjectDAO.class);

    private ErpTrailPathCalculator erpTrailPathCalculator =  new ErpTrailPathCalculator(lagF3DAO, lagF4DAO, managedObjectDAO);;

    void setUpDataForTest1() {
        lineDBImplList = new ArrayList<>();
        lineDBImplList.add(mockLineDBImpl(line1, ne1, ne2, createPTPShortDescription(tp2), createPTPShortDescription(tp3)));
        lineDBImplList.add(mockLineDBImpl(line2, ne2, ne3, createPTPShortDescription(tp4), createPTPShortDescription(tp5)));
        lineDBImplList.add(mockLineDBImpl(line3, ne1, ne3, createPTPShortDescription(tp1), createPTPShortDescription(tp6)));

        erpGroupF3DBImplList = new ArrayList<>();

        erpGroupF3DBImplList.add(mockErpGroupF3DBImpl(ne1, erp1, erp1Name, 15, 1,
                mockCtp(ne1, tp1),
                mockCtp(ne1, tp2)));
        erpGroupF3DBImplList.add(mockErpGroupF3DBImpl(ne2, erp2, erp2Name, 15, 1,
                mockCtp(ne2, tp3),
                mockCtp(ne2, tp4)));
        erpGroupF3DBImplList.add(mockErpGroupF3DBImpl(ne3, erp3, erp3Name, 15, 1,
                mockCtp(ne3, tp5),
                mockCtp(ne3, tp6)));
    }

    void setUpDataForLagCase () {
        //ne1(f4) tp1 tp2+tp3 - ne2(f3) tp4+tp5 tp6 - ne3(f4) tp7 tp8

        ConnectionTerminationPointEcDBImpl tp1Ctp = mockCtp(ne1, tp1);
        ConnectionTerminationPointEcDBImpl tp2Ctp = mockCtp(ne1, tp2);
        ConnectionTerminationPointEcDBImpl tp3Ctp = mockCtp(ne1, tp3);
        LagF4DBImpl lagF4DB = mockLagF4(ne1, lag1Id, tp2Ctp.getEntityIndex(), tp3Ctp.getEntityIndex());
        ConnectionTerminationPointEcDBImpl lag1LogicalPort = mockLagF4LogicalPort(ne1, lag1LogicalPortId, lag1No);

        PortF3NetDBImpl tp4Net = mockNetPort(ne2, tp4);
        PortF3NetDBImpl tp5Net = mockNetPort(ne2, tp5);
        PortF3AccDBImpl tp6Acc = mockAccPort(ne2, tp6);
        PortF3NetDBImpl lag2LogicalPort = mockF3NetLogicalPort(ne2, lag2LogicalPortId);
        LagPortF3DBImpl lagMember1 = mockF3LagMember(lag2Member1, tp4Net);
        LagPortF3DBImpl lagMember2 = mockF3LagMember(lag2Member2, tp5Net);
        LagF3DBImpl lagF3DB = mockLagF3(ne2, lag2Id, lag2No, lag2LogicalPort, lagMember1, lagMember2);

        ConnectionTerminationPointEcDBImpl tp7Ctp = mockCtp(ne3, tp7);
        ConnectionTerminationPointEcDBImpl tp8Ctp = mockCtp(ne3, tp8);
        lineDBImplListWithLag = new ArrayList<>();

        //links tp2-tp4, tp6-tp7, tp8 -tp1
        lineDBImplListWithLag.add(mockLineDBImpl(line1, ne1, ne2, createPTPShortDescription(tp2), createF3NetPortShortDescription(tp4)));
        lineDBImplListWithLag.add(mockLineDBImpl(line2, ne2, ne3, createF3AccPortShortDescription(tp6), createPTPShortDescription(tp7)));
        lineDBImplListWithLag.add(mockLineDBImpl(line3, ne1, ne3, createPTPShortDescription(tp1), createPTPShortDescription(tp8)));

        erpGroupF3DBImplListWithLag = new ArrayList<>();

        erpGroupF3DBImplListWithLag.add(mockErpGroupF3DBImpl(ne1, erp1, erp1Name, 15,1,
                tp1Ctp,
                lag1LogicalPort));
        erpGroupF3DBImplListWithLag.add(mockErpGroupF3DBImpl(ne2, erp2, erp2Name, 15,1,
                lag2LogicalPort,
                tp6Acc));
        erpGroupF3DBImplListWithLag.add(mockErpGroupF3DBImpl(ne3, erp3, erp3Name, 15,1,
                tp7Ctp,
                tp8Ctp));

        //set up queries
        //f4
        //LagF4DBImpl lagF4DB = lagF4DAO.getByEntityIndex(ctpRingPort.getNeID(), F4EntityIndexHelper.getLagPart(ctpRingPort.getEntityIndex()));
        when(lagF4DAO.getByEntityIndex(eq(ne1),eq(F4EntityIndexHelper.getLagPart(lag1LogicalPort.getEntityIndex())))).thenReturn(lagF4DB);
        //ManagedObjectDBImpl possibleLagPort = managedObjectDAO.get(lagF4DB.getNeID(), ei);
        when(managedObjectDAO.get(eq(ne1), eq(tp1Ctp.getEntityIndex()))).thenReturn(tp1Ctp);
        when(managedObjectDAO.get(eq(ne1), eq(tp2Ctp.getEntityIndex()))).thenReturn(tp2Ctp);
        when(managedObjectDAO.get(eq(ne1), eq(tp3Ctp.getEntityIndex()))).thenReturn(tp3Ctp);
        //f3
        //LagF3DBImpl lagF3DB = LagF3DAO.getLagF3DBImplByLogicalPort(f3RingPort.getNeID(), f3RingPort.getId());
        when(lagF3DAO.getLagF3DBImplByLogicalPort(eq(ne2), eq(lag2LogicalPortId))).thenReturn(lagF3DB);
    }

    private String createF3AccPortShortDescription(String name) {
        return "ACC-" + name;
    }

    private String createF3NetPortShortDescription(String tpName) {
        return "NET-" + tpName;
    }

    private PortF3NetDBImpl mockF3NetLogicalPort(int neId, int id) {
        PortF3NetDBImpl result = new PortF3NetDBImpl();
        result.setNeID(neId);
        result.setId(id);
        result.setSlotIndex(MIBFSP150CM.Entity.SlotTable.SLOTNUM_DUMMY_LAG);
        return result;
    }

    private PortF3AccDBImpl mockAccPort(int neId, String name) {
        PortF3AccDBImpl result = new PortF3AccDBImpl();
        result.setNeID(neId);
        result.setShortDescription(createF3AccPortShortDescription(name));
        return result;
    }

    private PortF3NetDBImpl mockNetPort(int neId, String name) {
        PortF3NetDBImpl result = new PortF3NetDBImpl();
        result.setNeID(neId);
        result.setShortDescription(createF3NetPortShortDescription(name));
        return result;
    }

    @Test
    void createOrderedNesAndLinksList() throws MDOperationFailedException, RingRestrictionException {
        setUpDataForTest1();
        List<EthernetRingPathElementDTO> result = erpTrailPathCalculator.createPath(ne1, lineDBImplList, erpGroupF3DBImplList);

        assertEquals(erp1Name, result.get(0).getAid());
        assertEquals(line1, result.get(1).getEntityMoId());
        assertEquals(erp2Name, result.get(2).getAid());
        assertEquals(line2, result.get(3).getEntityMoId());
        assertEquals(erp3Name, result.get(4).getAid());
        assertEquals(line3, result.get(5).getEntityMoId());
    }

    @Test
    void createOrderedNesAndLinksListWithLag() throws RingRestrictionException, MDOperationFailedException {
        setUpDataForLagCase();
        List<EthernetRingPathElementDTO> result = erpTrailPathCalculator.createPath(ne1, lineDBImplListWithLag, erpGroupF3DBImplListWithLag);

        assertEquals(erp1Name, result.get(0).getAid());
        assertEquals(line1, result.get(1).getEntityMoId());
        assertEquals(erp2Name, result.get(2).getAid());
        assertEquals(line2, result.get(3).getEntityMoId());
        assertEquals(erp3Name, result.get(4).getAid());
        assertEquals(line3, result.get(5).getEntityMoId());
    }

    LagF4DBImpl mockLagF4(int neId, int lagId, EntityIndex... entityIndexes) {
        LagF4DBImpl result = new LagF4DBImpl();
        result.setNeID(neId);
        result.setId(lagId);
        result.setTerminationPoints(Arrays.asList(entityIndexes));
        return result;
    }

    ConnectionTerminationPointEcDBImpl mockLagF4LogicalPort(int neId, int id, int lagNo)   {
        ConnectionTerminationPointEcDBImpl result = new ConnectionTerminationPointEcDBImpl();
        result.setNeID(neId);
        result.setId(id);
        ////EC_CAT^/mit/me/1/fltp/lag-11/ctp/eth
        result.setEntityIndex(EcEntityIndex.getEcEntityIndex("/mit/me/1/fltp/lag-" + lagNo + "/ctp/eth"));
        return result;
    }

    LagF3DBImpl mockLagF3(int neId, int lagId, int lagNo, AbstractEthernetPortF3DBImpl logicalPort, LagPortF3DBImpl... lagMembers)  {
        LagF3DBImpl result = new LagF3DBImpl();
        result.setNeID(neId);
        result.setId(lagId);
        result.setLagIndex(lagNo);
        result.setLagMembers(Arrays.stream(lagMembers).collect(Collectors.toSet()));
        result.setLogicalPort(logicalPort);
        return result;
    }

    LagPortF3DBImpl mockF3LagMember(int id, AbstractEthernetPortF3DBImpl port)   {
        LagPortF3DBImpl result = new LagPortF3DBImpl();
        result.setNeID(port.getNeID());
        result.setId(id);
        result.setLagMember(port);
        return result;
    }

    LineDBImpl mockLineDBImpl(int id, int aEndNEId, int zEndNEId, String aEndLabel, String zEndLabel){
        LineDBImpl lineDB = new LineDBImpl();
        lineDB.setId(id);
        NetworkElementDBImpl element1 = new NetworkElementF4DBImpl();
        NetworkElementDBImpl element2 = new NetworkElementF4DBImpl();
        element1.setId(aEndNEId);
        element2.setId(zEndNEId);
        lineDB.setAEndNE(element1);
        lineDB.setZEndNE(element2);
        LinkEndpointEmbedded aEnd = new LinkEndpointEmbedded();
        aEnd.setLabel(aEndLabel);
        lineDB.setAEndpoint(aEnd);
        LinkEndpointEmbedded zEnd = new LinkEndpointEmbedded();
        zEnd.setLabel(zEndLabel);
        lineDB.setZEndpoint(zEnd);
        return lineDB;
    }

    ErpGroupF3DBImpl mockErpGroupF3DBImpl(int neId, int id, String shortDescription, int ringId, int rapsVlan, ManagedObjectDBImpl ringPort0, ManagedObjectDBImpl ringPort1) {
        ErpGroupF3DBImpl erpGroupF3DBmocked = new ErpGroupF3DBImpl();
        erpGroupF3DBmocked.setNeID(neId);
        erpGroupF3DBmocked.setId(id);
        erpGroupF3DBmocked.setShortDescription(shortDescription);
        erpGroupF3DBmocked.setRapsRingId(ringId);
        erpGroupF3DBmocked.setRapsVlanId(rapsVlan);
        erpGroupF3DBmocked.setRingPort0(ringPort0);
        erpGroupF3DBmocked.setRingPort1(ringPort1);

        ErpUnitF3DBImpl erpUnitF3DB0 = new ErpUnitF3DBImpl();
        ErpUnitF3DBImpl erpUnitF3DB1 = new ErpUnitF3DBImpl();
        erpUnitF3DB0.setPortStatus(ErpPortStatus.UNBLOCKED.getIntValue());
        erpUnitF3DB1.setPortStatus(ErpPortStatus.UNBLOCKED.getIntValue());
        erpGroupF3DBmocked.setRingPort0ErpUnit(erpUnitF3DB0);
        erpGroupF3DBmocked.setRingPort1ErpUnit(erpUnitF3DB1);
        return  erpGroupF3DBmocked;
    }

    private ConnectionTerminationPointEcDBImpl mockCtp(int neId, String shortDescription)    {
        ConnectionTerminationPointEcDBImpl terminationPointEcDB = new ConnectionTerminationPointEcDBImpl();
        terminationPointEcDB.setNeID(neId);
        terminationPointEcDB.setShortDescription(createCTPshortDescription(shortDescription));
        terminationPointEcDB.setEntityIndex(createCTPEI(shortDescription));
        PhysicalTerminationPointEcDBImpl ptp = new PhysicalTerminationPointEcDBImpl();
        ptp.setShortDescription(createPTPShortDescription(shortDescription));
        ptp.setNeID(neId);
        ptp.setEntityIndex(createPTPEI(shortDescription));
        terminationPointEcDB.setParent(ptp);
        return terminationPointEcDB;
    }

    private LagF4DBImpl mockLag(int neId, EntityIndex lagMember1, EntityIndex lagMember2) {
        LagF4DBImpl result = new LagF4DBImpl();
        result.setNeID(neId);
        List<EntityIndex> terminationPoints = new ArrayList<>();
        terminationPoints.add(lagMember1);
        terminationPoints.add(lagMember2);
        result.setTerminationPoints(terminationPoints);
        return result;
    }

    private String createCTPshortDescription(String shortDescription) {
        return "CTP-" + shortDescription;
    }

    //EC_CAT^/mit/me/1/eqh/shelf,1/eqh/slot,1/eq/card/ptp/port,51/ctp/eth
    private EntityIndex createCTPEI(String shortDescription) {
        return createPTPEI(shortDescription).append("/ctp/eth");
    }

    private String createPTPShortDescription(String shortDescription) {
        return "PTP-" + shortDescription;
    }

    private EntityIndex createPTPEI(String shortDescription) {
        StringBuilder sb = new StringBuilder("/mit/me/1/eqh/shelf,1/eqh/slot,1/eq/card/ptp/port,");
        sb.append(shortDescription);
        return EcEntityIndex.getEcEntityIndex(sb.toString());
    }

    EthernetRingPathElementDTO mockEthernetRingPathElementDTO(int entityMoId, int moNeId, String aid){
        return EthernetRingPathElementDTOBuilder.anEthernetRingPathElementDTO()
                .entityMoId(entityMoId)
                .moNeId(moNeId)
                .aid(aid)
                .build();
    }
}