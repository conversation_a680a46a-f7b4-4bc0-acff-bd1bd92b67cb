package com.adva.nlms.mediation.opticalrouter;

import com.adva.nlms.mediation.event.EventDTO;
import com.adva.nlms.opticalrouter.api.polling.out.OpticalRouterEvent;
import com.adva.nlms.opticalrouter.api.polling.out.OpticalRouterNotification;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.snmp4j.smi.VariableBinding;

import java.util.Collections;
import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

class TrapProcessorOpticalRouterTest {

    private TrapProcessorOpticalRouter trapProcessor;
    private List<VariableBinding> emptyVbl;

    @BeforeEach
    void setUp() {
        trapProcessor = new TrapProcessorOpticalRouter();
        emptyVbl = Collections.emptyList();
    }

    @Test
    void isNESpecificAlarm_withEntityAlarmedEvent_returnsTrue() {
        // Arrange
        OpticalRouterEvent.EntityAlarmed alarmedEvent = new OpticalRouterEvent.EntityAlarmed(
            "TEST_ALARM",
            OpticalRouterEvent.Direction.RX,
            OpticalRouterEvent.Severity.CRITICAL,
            System.currentTimeMillis()
        );
        
        OpticalRouterNotification notification = new OpticalRouterNotification(
            UUID.randomUUID(),
            "test-port",
            OpticalRouterNotification.EntityType.PORT,
            System.currentTimeMillis(),
            alarmedEvent
        );
        
        EventDTO event = new EventDTO();
        event.setOpticalRouterNotification(notification);

        // Act
        boolean result = trapProcessor.isNESpecificAlarm(event, emptyVbl);

        // Assert
        assertTrue(result, "Should return true for EntityAlarmed events");
    }

    @Test
    void isNESpecificAlarm_withEntityCreatedEvent_returnsFalse() {
        // Arrange
        OpticalRouterEvent.EntityCreated createdEvent = new OpticalRouterEvent.EntityCreated(null);
        
        OpticalRouterNotification notification = new OpticalRouterNotification(
            UUID.randomUUID(),
            "test-port",
            OpticalRouterNotification.EntityType.PORT,
            System.currentTimeMillis(),
            createdEvent
        );
        
        EventDTO event = new EventDTO();
        event.setOpticalRouterNotification(notification);

        // Act
        boolean result = trapProcessor.isNESpecificAlarm(event, emptyVbl);

        // Assert
        assertFalse(result, "Should return false for EntityCreated events");
    }

    @Test
    void isNESpecificAlarm_withEntityStateChangedEvent_returnsFalse() {
        // Arrange
        OpticalRouterEvent.EntityStateChanged stateChangedEvent = new OpticalRouterEvent.EntityStateChanged(
            null, // AdminState
            null  // OperationalState
        );
        
        OpticalRouterNotification notification = new OpticalRouterNotification(
            UUID.randomUUID(),
            "test-port",
            OpticalRouterNotification.EntityType.PORT,
            System.currentTimeMillis(),
            stateChangedEvent
        );
        
        EventDTO event = new EventDTO();
        event.setOpticalRouterNotification(notification);

        // Act
        boolean result = trapProcessor.isNESpecificAlarm(event, emptyVbl);

        // Assert
        assertFalse(result, "Should return false for EntityStateChanged events");
    }

    @Test
    void isNESpecificAlarm_withNullNotification_returnsFalse() {
        // Arrange
        EventDTO event = new EventDTO();
        event.setOpticalRouterNotification(null);

        // Act
        boolean result = trapProcessor.isNESpecificAlarm(event, emptyVbl);

        // Assert
        assertFalse(result, "Should return false when notification is null");
    }

    @Test
    void isNESpecificAlarm_withNoOpticalRouterNotification_returnsFalse() {
        // Arrange
        EventDTO event = new EventDTO();
        // Don't set any optical router notification

        // Act
        boolean result = trapProcessor.isNESpecificAlarm(event, emptyVbl);

        // Assert
        assertFalse(result, "Should return false when no optical router notification is set");
    }
}
