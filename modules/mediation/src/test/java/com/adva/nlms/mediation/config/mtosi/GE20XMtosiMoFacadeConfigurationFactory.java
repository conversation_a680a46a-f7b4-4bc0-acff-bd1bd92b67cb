/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: tmalesinski
 */

package com.adva.nlms.mediation.config.mtosi;

import com.adva.nlms.common.config.netypes.NEType;
import com.adva.nlms.mediation.config.AbstractNetworkElementPollingWorkerBasisImpl;
import com.adva.nlms.mediation.config.f3.NetworkElementPollingWorkerF3BeanContainer;
import com.adva.nlms.mediation.config.f3.entity.factory.impl.DBObjectFactoryF3Impl;
import com.adva.nlms.mediation.config.fsp20X.NetworkElementFSPGE20XImpl;
import com.adva.nlms.mediation.config.fsp20X.NetworkElementPollingWorkerFSPGE20XImpl;
import com.adva.nlms.mediation.config.fsp20X.mtosi.MtosiMOFacadeFSPGE20XImpl;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPCommFailure;
import org.springframework.beans.factory.annotation.Autowired;

import static org.mockito.Mockito.mock;

public class GE20XMtosiMoFacadeConfigurationFactory extends MtosiMOFacadeConfigurationFactory<NetworkElementFSPGE20XImpl> {

  @Autowired
  private MtosiMOFacadeFSPGE20XImpl mtosiMOFacade;
  @Autowired
  private DBObjectFactoryF3Impl dbObjectFactoryF3;

  public MtosiMOFacadeFSPGE20XImpl getMtosiMOFacade () {
    return mtosiMOFacade;
  }

  public MtosiMOFacadeConfiguration createConfiguration(int neId, String ipAddr) throws SNMPCommFailure {
    return new MtosiMOFacadeConfiguration(
            this,
            NetworkElementFSPGE20XImpl.class,
            neId,
            ipAddr,
            NEType.GE201SE,
            dbObjectFactoryF3,
            new PollinWorkerInitializer<NetworkElementFSPGE20XImpl>() {
              @Override
              AbstractNetworkElementPollingWorkerBasisImpl init(NetworkElementFSPGE20XImpl ne) {
                return new NetworkElementPollingWorkerFSPGE20XImpl(ne, mock(NetworkElementPollingWorkerF3BeanContainer.class));
              }
            }
    );
  }


}
