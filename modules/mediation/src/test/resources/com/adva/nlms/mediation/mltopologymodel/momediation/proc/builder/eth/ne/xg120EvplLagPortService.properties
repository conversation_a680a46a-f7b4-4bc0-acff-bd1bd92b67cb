test.customerId=43999
test.mlNodeId=44693
test.intendDbId=51076
test.serviceName=Service-XG120-EVPL-FLOWPOINT-LAG
test.alias=Service-XG120-EVPL-FLOWPOINT-LAG
test.singleNodeService=true
test.mlMeId=44693
test.neId=44427
test.mlNeId=44427
test.neType=FSP 150-XG120Pro-SH
test.ipAddress=*************

test.conn.ingress.mlMeId=44693
test.conn.ingress.label=LAG-1-1-TP
test.conn.ingress.portlocation=pt:TRAFFIC12541
test.conn.ingress.adminstate=Up

test.conn.ingress.layer.eth=true
test.conn.ingress.layer.eth.egressBwAccounting=false
test.conn.ingress.layer.eth.egressCir=1024000
test.conn.ingress.layer.eth.egressEir=1024000
test.conn.ingress.layer.eth.egressCbs=32
test.conn.ingress.layer.eth.egressEbs=32
test.conn.ingress.layer.eth.egressBufferSize=64
test.conn.ingress.layer.eth.ingressBwAccounting=false
test.conn.ingress.layer.eth.ingressCir=1024000
test.conn.ingress.layer.eth.ingressEir=1024000
test.conn.ingress.layer.eth.ingressCbs=32
test.conn.ingress.layer.eth.ingressEbs=32

test.conn.ingress.layer.vlan=true
test.conn.ingress.layer.vlan.vlanMemberList=0:4095
test.conn.ingress.layer.vlan.defaultMemberEnabled=false
test.conn.ingress.layer.vlan.untaggedFramesEnabled=false
test.conn.ingress.layer.vlan.ctagControl=1
test.conn.ingress.layer.vlan.ctagVlanId=0
test.conn.ingress.layer.vlan.ctagVlanPriority=0
test.conn.ingress.layer.vlan.stagControl=1
test.conn.ingress.layer.vlan.stagVlanId=1
test.conn.ingress.layer.vlan.stagVlanPriority=0
test.conn.ingress.layer.vlan.portServiceType=0

test.conn.ingress.layer.cfm=true

test.conn.egress.mlMeId=44693
test.conn.egress.label=ETH PORT-1-1-1-10
test.conn.egress.portlocation=pt:TRAFFIC1110
test.conn.egress.adminstate=Up

test.conn.egress.layer.eth=true
test.conn.egress.layer.eth.egressBwAccounting=false
test.conn.egress.layer.eth.egressCir=1024000
test.conn.egress.layer.eth.egressEir=1024000
test.conn.egress.layer.eth.egressCbs=32
test.conn.egress.layer.eth.egressEbs=32
test.conn.egress.layer.eth.egressBufferSize=64
test.conn.egress.layer.eth.ingressBwAccounting=false
test.conn.egress.layer.eth.ingressCir=1024000
test.conn.egress.layer.eth.ingressEir=1024000
test.conn.egress.layer.eth.ingressCbs=32
test.conn.egress.layer.eth.ingressEbs=32

test.conn.egress.layer.vlan=true
test.conn.egress.layer.vlan.vlanMemberList=0:4095
test.conn.egress.layer.vlan.defaultMemberEnabled=false
test.conn.egress.layer.vlan.untaggedFramesEnabled=false
test.conn.egress.layer.vlan.ctagControl=1
test.conn.egress.layer.vlan.ctagVlanId=0
test.conn.egress.layer.vlan.ctagVlanPriority=0
test.conn.egress.layer.vlan.stagControl=1
test.conn.egress.layer.vlan.stagVlanId=1
test.conn.egress.layer.vlan.stagVlanPriority=0
test.conn.egress.layer.vlan.portServiceType=0

test.conn.egress.layer.cfm=true

test.provision.ingress.portEndpointType=0
test.provision.ingress.portSvcType=2
test.provision.ingress.portTrafficModel=FlowPoint
test.provision.ingress.entityIndex=47^1.1.254.1
test.provision.ingress.mlTopologyAidMoRef.class=EthernetTrafficPortF3DBImpl
test.provision.ingress.mlTopologyAidMoRef.string=LAG-1-1-TP
test.provision.ingress.mlConnPointDbImpl=1000
test.provision.ingress.moPortDbImpl=In-Service

test.provision.egress.portEndpointType=1
test.provision.egress.portSvcType=2
test.provision.egress.portTrafficModel=FlowPoint
test.provision.egress.entityIndex=47^1.1.1.10
test.provision.egress.mlTopologyAidMoRef.class=EthernetTrafficPortF3DBImpl
test.provision.egress.mlTopologyAidMoRef.string=ETH PORT-1-1-1-10
test.provision.egress.mlConnPointDbImpl=1001
test.provision.egress.moPortDbImpl=In-Service

test.operation.max.operation.order=10

test.operation.type.0=Create
test.operation.class.0=FlowPointF3Attr
test.operation.attr.EntityIndex.0=49^1.1.254.1.1
test.operation.attr.circuitName.0=Service-XG120-EVPL-FLOWPOINT-LAG
test.operation.attr.ctagControl.0=1
test.operation.attr.stagControl.0=1
test.operation.attr.defaultMemberEnabled.0=2
test.operation.attr.adminState.0=1

test.operation.type.1=Modify
test.operation.class.1=F3FpQosShaperAttr
test.operation.attr.EntityIndex.1=206^1.1.254.1.1.1
test.operation.attr.CIR.1=1024000
test.operation.attr.CIRHi.1=0
test.operation.attr.CBS.1=32
test.operation.attr.EIR.1=1024000
test.operation.attr.EIRHi.1=0
test.operation.attr.EBS.1=32
test.operation.attr.bufferSize.1=64

test.operation.type.2=Modify
test.operation.class.2=F3FpQosPolicerAttr
test.operation.attr.EntityIndex.2=207^1.1.254.1.1.1
test.operation.attr.CIR.2=1024000
test.operation.attr.CIRHi.2=CIRHi
test.operation.attr.CBS.2=32
test.operation.attr.EIR.2=1024000
test.operation.attr.EIRHi.2=0
test.operation.attr.EBS.2=32
test.operation.attr.couplingFlag.2=1
test.operation.attr.colorMode.2=1

test.operation.type.3=Create
test.operation.class.3=FlowPointF3Attr
test.operation.attr.EntityIndex.3=49^1.1.1.10.1
test.operation.attr.circuitName.3=Service-XG120-EVPL-FLOWPOINT-LAG
test.operation.attr.ctagControl.3=1
test.operation.attr.stagControl.3=1
test.operation.attr.defaultMemberEnabled.3=2
test.operation.attr.adminState.3=1
test.operation.attr.adminState.3.vlanMemberList=0:4095
test.operation.attr.adminState.3.untaggedFrameEnabled=2

test.operation.type.4=Modify
test.operation.class.4=F3FpQosShaperAttr
test.operation.attr.EntityIndex.4=206^1.1.1.10.1.1
test.operation.attr.CIR.4=1024000
test.operation.attr.CIRHi.4=0
test.operation.attr.CBS.4=32
test.operation.attr.EIR.4=1024000
test.operation.attr.EIRHi.4=0
test.operation.attr.EBS.4=32
test.operation.attr.bufferSize.4=64

test.operation.type.5=Modify
test.operation.class.5=F3FpQosPolicerAttr
test.operation.attr.EntityIndex.5=207^1.1.1.10.1.1
test.operation.attr.CIR.5=1024000
test.operation.attr.CIRHi.5=0
test.operation.attr.CBS.5=32
test.operation.attr.EIR.5=1024000
test.operation.attr.EIRHi.5=0
test.operation.attr.EBS.5=32
test.operation.attr.couplingFlag.5=1
test.operation.attr.colorMode.5=1

test.operation.type.6=Create
test.operation.class.6=MPFlowF3Attr
test.operation.attr.EntityIndex.6=110^1.1
test.operation.attr.entityAlias.6=Service-XG120-EVPL-FLOWPOINT-LAG

test.operation.type.7=Create
test.operation.class.7=MPFlowF3MemberAttr
test.operation.attr.EntityIndex.7=1.1.19.1.3.6.1.4.1.2544.1.12.4.1.29.1.2.1.1.254.1.1
test.operation.attr.neIndex.7=1
test.operation.attr.mpFlowIndex.7=1
test.operation.attr.MP_FLOW_MEMBER_INDEX.7=19.1.3.6.1.4.1.2544.1.12.4.1.29.1.2.1.1.254.1.1


test.operation.type.8=Create
test.operation.class.8=MPFlowF3MemberAttr
test.operation.attr.EntityIndex.8=1.1.19.1.3.6.1.4.1.2544.1.12.4.1.29.1.2.1.1.1.10.1
test.operation.attr.neIndex.8=1
test.operation.attr.mpFlowIndex.8=1
test.operation.attr.MP_FLOW_MEMBER_INDEX.8=19.1.3.6.1.4.1.2544.1.12.4.1.29.1.2.1.1.1.10.1

test.operation.type.9=Modify
test.operation.class.9=MPFlowF3Attr
test.operation.attr.EntityIndex.9=110^1.1
test.operation.attr.adminState.9=1

test.operation.type.10=Modify
test.operation.class.10=FlowPointF3Attr
test.operation.attr.EntityIndex.10=49^1.1.254.1.1
test.operation.attr.adminState.10=1


test.rb.operation.max.operation.order=4

test.rb.operation.type.0=ModifyAndDelete
test.rb.operation.class.0=MPFlowF3Attr
test.rb.operation.entityIndex.0=110^1.1
test.rb.operation.adminState.0=5

test.rb.operation.type.1=Modify
test.rb.operation.class.1=FlowPointF3Attr
test.rb.operation.entityIndex.1=49^1.1.254.1.1
test.rb.operation.adminState.1=2

test.rb.operation.type.2=ModifyAndDelete
test.rb.operation.class.2=FlowPointF3Attr
test.rb.operation.entityIndex.2=49^1.1.1.10.1
test.rb.operation.adminState.2=2

test.rb.operation.type.3=Modify
test.rb.operation.class.3=EthernetTrafficPortF3Attr
test.rb.operation.entityIndex.3=47^1.1.254.1
test.rb.operation.adminState.3=1
test.rb.operation.neId.3=44427


test.rb.operation.type.4=Modify
test.rb.operation.class.4=EthernetTrafficPortF3Attr
test.rb.operation.entityIndex.4=47^1.1.1.10
test.rb.operation.adminState.4=1
test.rb.operation.neId.4=44427

