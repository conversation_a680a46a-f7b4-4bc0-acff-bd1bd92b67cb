/*
 *   Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *   Owner:
 */

package com.adva.nlms.mediation.polling.testPerformers;

import com.adva.nlms.common.snmp.MDOperationFailedException;
import com.adva.nlms.mediation.config.polling.InventoryPollingParameters;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPCommFailure;
import com.adva.nlms.mediation.polling.core.Polling;
import org.snmp4j.smi.OID;

import java.util.Arrays;

public class TestPollingPerformerWithChangedPP extends TestPollingPerformer
{

  public TestPollingPerformerWithChangedPP(int id, String identifierStr, long delayTime, boolean performFailure)
  {
    super(id, identifierStr, delayTime, performFailure);
  }

  @Override
  public void doPolling(Polling polling, boolean firstRun) throws MDOperationFailedException, SNMPCommFailure
  {
    ((InventoryPollingParameters) polling.getParameters()).getEntityTypeMap().put(2, Arrays.asList(new OID("1.1.3")));

    super.doPolling(polling, firstRun);
  }
}
