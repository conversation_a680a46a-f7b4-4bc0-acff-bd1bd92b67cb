/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: <PERSON><PERSON><PERSON><PERSON><PERSON>
 */
package com.adva.nlms.mediation.ne_comm;

import com.adva.nlms.common.snmp.MIBFSP150CM;
import com.adva.nlms.mediation.event.EventDTO;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPCommFailure;
import org.snmp4j.util.TableEvent;

import java.util.ArrayList;
import java.util.List;

public class StatusPollingOSA540XSNMPCtrlImpl extends StatusPollingF3SNMPCtrlImpl {

  @Override
  public List<EventDTO> getCurrentAlarms(int neIndex) throws SNMPCommFailure {
    final List<EventDTO> currentAlarmsMap = new ArrayList<EventDTO>();  // the return value
    scanNEAlarms(neIndex, currentAlarmsMap);
    return currentAlarmsMap;
  }

  @Override
  protected void scanNEAlarms(int neIndex, List<EventDTO> currentAlarmsMap) throws SNMPCommFailure {
    final List<TableEvent> table = snmpCtrl.getSnmpAdapter().getTable(new int[]{neIndex, 0}, new int[]{neIndex + 1},
        MIBFSP150CM.Alarm.AlarmObjects.NetworkElementAlmTable.OID_OBJECT,
        MIBFSP150CM.Alarm.AlarmObjects.NetworkElementAlmTable.OID_TYPE,
        MIBFSP150CM.Alarm.AlarmObjects.NetworkElementAlmTable.OID_NOTIF_CODE,
        MIBFSP150CM.Alarm.AlarmObjects.NetworkElementAlmTable.OID_LOCATION,
        MIBFSP150CM.Alarm.AlarmObjects.NetworkElementAlmTable.OID_DIRECTION,
        MIBFSP150CM.Alarm.AlarmObjects.NetworkElementAlmTable.OID_SRV_EFF,
        MIBFSP150CM.Alarm.AlarmObjects.NetworkElementAlmTable.OID_TIME,
        MIBFSP150CM.Alarm.AlarmObjects.NetworkElementAlmTable.OID_OBJECT_NAME,
        MIBFSP150CM.Alarm.AlarmObjects.NetworkElementAlmTable.OID_ADDITIONAL_INFO_NAME
    );
    treatNEAlarms(table, currentAlarmsMap);
  }

  public void treatNEAlarmsOSA540x(final List<TableEvent> table, List<EventDTO> currentAlarmsMap){
    treatNEAlarms(table, currentAlarmsMap);
  }
}
