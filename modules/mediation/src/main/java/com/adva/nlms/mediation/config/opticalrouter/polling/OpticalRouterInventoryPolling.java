/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: krzysztofw
 */

package com.adva.nlms.mediation.config.opticalrouter.polling;

import com.adva.nlms.common.property.FNMPropertyConstants;
import com.adva.nlms.common.snmp.MDOperationFailedException;
import com.adva.nlms.mediation.common.transactions.InvalidPollingException;
import com.adva.nlms.mediation.config.AbstractInventoryPollingCommand;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.polling.AutowiredPollingCommand;
import com.adva.nlms.mediation.config.polling.InventoryPollingParameters;
import com.adva.nlms.mediation.config.polling.PollingCommandParameters;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPCommFailure;
import com.adva.nlms.mediation.polling.PollingType;
import com.adva.nlms.mediation.polling.api.PF;
import com.adva.nlms.opticalrouter.api.polling.in.OpticalRouterDriverManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

public class OpticalRouterInventoryPolling extends AbstractInventoryPollingCommand implements AutowiredPollingCommand {

    // ----------------------------------------------------------------------------------------------------------------

    private static final Logger log = LoggerFactory.getLogger(OpticalRouterInventoryPolling.class);

    // ----------------------------------------------------------------------------------------------------------------
    @Autowired
    RemoteOperationsMonitor remoteOperationsMonitor;

    @Autowired
    OpticalRouterDriverManager opticalRouterDriverManager;

    // ----------------------------------------------------------------------------------------------------------------

    @Override
    public void runPolling(PollingCommandParameters params) throws MDOperationFailedException, InvalidPollingException, SNMPCommFailure {
        try {
            super.runPolling(params);
        } catch (com.adva.nlms.opticalrouter.api.polling.in.OpticalRouterCommandException | com.adva.nlms.opticalrouter.api.commands.OpticalRouterCommandException e) {
            params.getNe().updateResponseStatus(false);
            throw e;
        }
    }

    // ----------------------------------------------------------------------------------------------------------------

    @Override
    protected boolean isCommunicationInitialized(PollingCommandParameters params) {
        return opticalRouterDriverManager.isConnected(params.getNe().neDBImpl().getUuid());
    }

    // ----------------------------------------------------------------------------------------------------------------

    @Override
    protected void handleNoCommunicationYet(PollingCommandParameters params) {
        // initialize communication first, then repeat inventory if successful
        PF.getPollingService().commission(params.getNe().getID(), PollingType.INIT_COMMUNICATION, t -> {
            if (t == null) {
                PF.getPollingService().commission(params.getNe().getID(), PollingType.INVENTORY);
            }
        });
    }

    @Override
    protected void doInventory(NetworkElement ne, InventoryPollingParameters inventoryPollingParameters, boolean useCurrentThread, boolean forced) throws InvalidPollingException, MDOperationFailedException, SNMPCommFailure {
        try {
            remoteOperationsMonitor.unregisterCurrentOnNotification(FNMPropertyConstants.REMOTE_OPERATION_TIMEOUT.getValue());
            invokeRemote(ne);
        } catch (IllegalStateException e) {
            log.error("Remote inventory on {} cannot be performed", ne.getIPAddress(), e);
        }
    }

    // ----------------------------------------------------------------------------------------------------------------

    private void invokeRemote(NetworkElement ne) {
        opticalRouterDriverManager.synchronizeInventory(ne.neDBImpl().getUuid());
    }

    // ----------------------------------------------------------------------------------------------------------------
}
