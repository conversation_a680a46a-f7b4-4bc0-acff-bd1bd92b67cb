/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: klub<PERSON><PERSON><PERSON>
 */
package com.adva.nlms.mediation.ne_comm.f7.cp.objects;


import java.util.Map;

/**
 * Interface for embedding SNMP derived datamaps.
 */
public interface ISnmpDataMapRepresentable {

  /**
   * Render as data map
   *
   * @return data map
   */
  Map<String, Object> getDataMap();

  /**
   * @param key
   * @param value
   */
  void storeInDataMap(final String key, final Object value);
}
