/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: tomaszsi
 */

package com.adva.nlms.mediation.config.nettransaction;

import com.adva.nlms.common.config.EntityIndex;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.dto.DTO;
import com.adva.nlms.mediation.config.dto.attr.ManagedObjectAttr;
import com.adva.nlms.mediation.config.mofacade.DtoTypesProvider;
import com.adva.nlms.mediation.config.mofacade.snmp.VarbindsCollection;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPCommFailure;
import com.adva.nlms.mediation.ne_comm.snmpscan.scantable.ScanTable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class IrreversibleOperationExecutor<T extends ManagedObjectAttr> extends ReversibleOperationExecutor<T> {
  private static final Logger LOG = LoggerFactory.getLogger(IrreversibleOperationExecutor.class);
  private DtoTypesProvider dtoTypesProvider;

  public IrreversibleOperationExecutor(NetworkElement ne, NetTransactionOperation operation, DTO<T> dto, EntityIndex parentIndex, DtoTypesProvider dtoTypesProvider) {
    super(ne, operation, dto, parentIndex, dtoTypesProvider);
    this.dtoTypesProvider = dtoTypesProvider;
  }

  @Override
  public void rollbackOperation () throws SNMPCommFailure {
    //this executor doesn't support rollback
  }

  private <T extends ManagedObjectAttr> void validateDTO(DTO<T> dto) {
    Class dbClass = dtoTypesProvider.getDBImplByProvAttr(dto.getAttributesGroupClass());
    if(dbClass == null ){
      ScanTable scanTable = dtoTypesProvider.getScanTableByProvAttr(dto.getAttributesGroupClass());
      if(scanTable == null){
        throw new F3NetTransactionException("DTO attributes["+dto.getAttributesGroupClass().getSimpleName()+"] wasn't registered in DTOTypes.");
      }
    }
  }

  @Override
  protected <T extends ManagedObjectAttr> VarbindsCollection prepareSnmpVarbinds(NetTransactionOperation operation, DTO<T> dto) {
    validateDTO(dto);
    Class dbClass = dtoTypesProvider.getDBImplByProvAttr(dto.getAttributesGroupClass());
    VarbindsCollection varbindsCollection = null;
    switch (operation.getOperationType()){
      case Create:
        varbindsCollection = getSnmpBuilder().prepareCreateCollection(ne.getID(), dbClass, dto);
        break;
      case Modify:
        varbindsCollection = getSnmpBuilder().prepareUpdateCollection(ne.getID(), dbClass, dto, null);
        break;
      case Delete:
        varbindsCollection = getSnmpBuilder().prepareDeleteCollection(ne.getID(), dbClass, dto, null);
        break;
      default:
        LOG.error("Operation["+operation+"] is not supported", new Exception());
        break;
    }

    return varbindsCollection;
  }
}
