/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: tomaszm
 */
package com.adva.nlms.mediation.ne_comm.discovery;

import com.adva.nlms.common.snmp.MDOperationFailedException;
import com.adva.nlms.mediation.config.ManagedObjectDBImpl;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPCommFailure;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.snmp4j.smi.OID;
import org.snmp4j.util.TableEvent;

import java.util.List;
import java.util.Map;

/**
 * An interface for discovery workers
 */
public interface DiscoveryWorker<DBO extends ManagedObjectDBImpl> {

  public static final Logger logger = LogManager.getLogger(DiscoveryWorker.class);

  public void executeResync() throws MDOperationFailedException;

  public void scanTable(final Map<Integer, List<OID>> entityTypeMap) throws SNMPCommFailure;

  public void treatTable(final List<TableEvent> tableEvents);

//  public Map<String, Object> scanOneRow(DBO dbObject) throws SNMPCommFailure;

  public Map<String, Object> scanOneRow(int... dbObjectIndices) throws SNMPCommFailure;

  public void clearMaps();

  public int getEntityType();

}

