/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: tomaszm
 */

package com.adva.nlms.mediation.config.hn4000;

import java.util.Arrays;
import java.util.List;

import com.adva.nlms.common.config.EntityIndex;
import com.adva.nlms.common.event.TrapParameterID;
import com.adva.nlms.common.event.types.CategoryType;
import com.adva.nlms.common.snmp.MIB;
import com.adva.nlms.common.snmp.MIBHN4000;
import com.adva.nlms.common.snmp.TestResultTranslation;
import com.adva.nlms.mediation.common.persistence.MDPersistenceContext;
import com.adva.nlms.mediation.common.persistence.MDPersistenceHelper;
import com.adva.nlms.mediation.common.serviceProvisioning.ContinuityTestResultPropertiesHN4000;
import com.adva.nlms.mediation.common.serviceProvisioning.FlowSPPropertiesHN4000;
import com.adva.nlms.mediation.common.serviceProvisioning.HnTrafficGenProperties;
import com.adva.nlms.mediation.common.serviceProvisioning.HnTrafficMonProperties;
import com.adva.nlms.mediation.common.serviceProvisioning.LayerDetailsSPProperties;
import com.adva.nlms.mediation.common.serviceProvisioning.PortHN4000EthernetSProperties;
import com.adva.nlms.mediation.common.transactions.NetTransactionException;
import com.adva.nlms.mediation.common.transactions.ObjectInUseException;
import com.adva.nlms.mediation.common.validation.SPCondition;
import com.adva.nlms.mediation.common.validation.SPValidationException;
import com.adva.nlms.mediation.config.ConfigDataCTRL;
import com.adva.nlms.mediation.config.DataManagerFactory;
import com.adva.nlms.mediation.config.DataManagerImpl;
import com.adva.nlms.mediation.config.IntDataCTRLImpl;
import com.adva.nlms.mediation.config.ManagedObjectDBImpl;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.StatusDataCTRL;
import com.adva.nlms.mediation.config.StringDataCTRLImpl;
import com.adva.nlms.mediation.config.VolatileDataCTRL;
import com.adva.nlms.mediation.config.hn4000.necomm.SNMPCtrlHN4000;
import com.adva.nlms.mediation.config.hn4000.polling.DiscoverFlowHN4000PollingParameters;
import com.adva.nlms.mediation.config.hn4000.polling.VolatileHN4000PollingParameters;
import com.adva.nlms.mediation.config.mtosi.FDFr;
import com.adva.nlms.mediation.config.mtosi.MtosiImplFactory;
import com.adva.nlms.mediation.config.mtosi.model.FDFrDAO;
import com.adva.nlms.mediation.config.polling.managedobject.MODescCommandParameters;
import com.adva.nlms.mediation.config.sr.MDSRContext;
import com.adva.nlms.mediation.config.sr.SRDictionaryHN4000;
import com.adva.nlms.mediation.config.sr.SRLogger;
import com.adva.nlms.mediation.config.sr.SRLoggerCarrier;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPCommFailure;
import com.adva.nlms.mediation.polling.PollingType;
import com.adva.nlms.mediation.polling.api.PF;

/**
 * Implementation class of a FSP 150CC virtual container.
 */
public class FlowHN4000Impl extends DataManagerImpl implements FlowHN4000 {
	/** JDO ID of the persistent object. */
	private final Object flowDBImplJDOID;

	//Recreates the transient HN4000 flow object.
	static FlowHN4000Impl
	recreate(final NetworkElementHN4000Impl neHN4000Impl, final FlowHN4000DBImpl flowDBImpl)
	{
		return new FlowHN4000Impl(neHN4000Impl, flowDBImpl);
	}


	//--------------------------------------------------------------------------------------------------------------------
	//Constructor for HN4000 flow object.
    public FlowHN4000Impl(final NetworkElement neHN4000Impl, final FlowHN4000DBImpl flowDBImpl)
	{
		super(neHN4000Impl, new EntityIndex(flowDBImpl.getObjectIndices()), FlowHN4000.NAME_STRING);
		this.flowDBImplJDOID = MDPersistenceHelper.getObjectId(flowDBImpl);
	}

	/**
	 * Returns the properties.
	 * @return the properties.
	 */
	@Override
  @MDPersistenceContext
  public FlowSPPropertiesHN4000 getFlowSPProperties()
	{
			return getFlowDBImpl().getFlowSPProperties();
	}

	/**
	 * Returns a reference to the persistent data.
	 */
  @Override
  public FlowHN4000DBImpl getFlowDBImpl() {
    return MDPersistenceHelper.getObjectById(FlowHN4000DBImpl.class, flowDBImplJDOID);
  }

	/**
	 * Return related fdfr to this flow.
	 * @return FDFr
	 */
  @Override
  public FDFr getFDFr() {
    FDFrHN4000DBImpl fdfrDBImpl = FDFrDAO.get(getNetworkElement().getID(), getFlowDBImpl().getEvcID());
    if (fdfrDBImpl != null) {
      return MtosiImplFactory.getInstance().recreateImpl(getNetworkElement(), fdfrDBImpl);
    }
    return null;
  }
	/**
	 * Initializes the configuration data CTRLs.
	 * @param configDataCTRLList List to add the CTRLs.
	 */
	@Override
	protected void initConfigDataCTRLs(final List<ConfigDataCTRL> configDataCTRLList)
	{
		// 1.) init for super class(es)!
		super.initConfigDataCTRLs(configDataCTRLList);
    EntityIndex indices = getIndex();
    configDataCTRLList.add(new AdminStateCTRL(indices));
    configDataCTRLList.add(new NameCTRL(indices));
    configDataCTRLList.add(new TypeCTRL(indices));
    configDataCTRLList.add(new VlanListCTRL(indices));
    configDataCTRLList.add(new VlanPreservationCTRL(indices));
    // 2.) init for this class!
  }

  /**
	 * Initializes the volatile data CTRLs.
	 * @param volatileDataCTRLList List to add the CTRLs.
	 */
	@Override
  protected void initVolatileDataCTRLs(final List<VolatileDataCTRL> volatileDataCTRLList){
    // 1.) init for super class(es)!
		super.initVolatileDataCTRLs(volatileDataCTRLList);
    EntityIndex indices = getIndex();
    // 2.) init for this class!
    volatileDataCTRLList.add(new SecondaryStateCTRL(indices));
  }

  /**
	 * Initializes the status data CTRLs.
	 * @param statusDataCTRLList List to add the CTRLs.
	 */
	@Override
	protected void initStatusDataCTRLs(final List<StatusDataCTRL> statusDataCTRLList)
	{
		// 1.) init for super class(es)!
		super.initStatusDataCTRLs(statusDataCTRLList);
    EntityIndex indices = getIndex();
    statusDataCTRLList.add(new OperStateCTRL(indices));
    statusDataCTRLList.add(new SecondaryStateCTRL(indices));
    // 2.) init for this class!
	}

	/**
	 * Return PortHN4000Acc related with flow.
	 * @return PortHN4000Acc
	 */
	@Override
  public PortHN4000 getPortHN4000()
	{
	    PortHN4000EthernetDBImpl portHN4000EthernetDB = PortHN4000EthernetDAO.getPortEthernetHN4000DBImpl(getFlowDBImpl().getPortHN4000DBId());
        return DataManagerFactory.getInstance().recreateImpl(getNetworkElement(), portHN4000EthernetDB);
	}

protected class NameCTRL extends StringDataCTRLImpl implements ConfigDataCTRL, SRLoggerCarrier
  {
    public NameCTRL(final EntityIndex indices)
    {
      super(getNetworkElement(),
            indices,
            MIBHN4000.Common.HnServices.EvcBindTable.OID_DESCRIPTION,
            "interfaceAttributeValueChange",
            TrapParameterID.hnEvcBindingDescription);
    }


    @Override
    public String
    getStringValueFromDB()
    {
      return getFlowDBImpl().getFlowName();
    }

    @Override
    @MDSRContext
    public void
    setValueInDB()
    {
      FlowHN4000DBImpl flowDBImpl = getFlowDBImpl();
      String oldFlowMtosiName = flowDBImpl.getMtosiName();
      flowDBImpl.setFlowName(value);
      String newFlowMtosiName = flowDBImpl.getMtosiName();
      String neName = getNetworkElement().getName();

      if(oldFlowMtosiName != null && newFlowMtosiName != null && !oldFlowMtosiName.equals(newFlowMtosiName)) {
        getSRLogger().logSuccess(SRDictionaryHN4000.EVCBindingMessages.EVCBindingSuccess.EVCBINDING_RENAMED, oldFlowMtosiName, neName, newFlowMtosiName);
        FDFrHN4000DBImpl fdfr = FDFrDAO.get(getNetworkElement().getID(), flowDBImpl.getEvcID());
        // todo sibmar check why we have such condition? and next log like this FLOW_WITHOUT_FDFR?? - asked Pawel Sz. as it was his request and he is checking :)
        if(getNetworkElement() instanceof NetworkElementHN4000Impl && value.isEmpty() && fdfr != null) {
          ((NetworkElementHN4000Impl)getNetworkElement()).getServiceDiscoveryWorker().ensureFlowInServiceNameUniqueness(flowDBImpl);
          getSRLogger().logException(SRDictionaryHN4000.FLOWMessages.FlowError.FLOW_WITHOUT_FDFR, newFlowMtosiName, fdfr.getFdfrName());
        }
      }
    }

    @Override
    public void postSetValue() {
      PF.getPollingService().commission(getNeId(), PollingType.SYNCH_MO_STRINGS,
              new MODescCommandParameters(getNetworkElement().getID(), Arrays.asList((ManagedObjectDBImpl) getFlowDBImpl())));
    }

    @Override
    public void
    notifyDataChange()
    {
      serverTrap.category      = CategoryType.FSPHN4000_EVCBINDING;
      serverTrap.objectIndexes = getFlowDBImpl().getIndices();
      super.notifyDataChange();
    }

    @Override
    public SRLogger getSRLogger() {
      return ((NetworkElement)getNetworkElement()).getSRLogger();
    }
  }

  protected class AdminStateCTRL extends IntDataCTRLImpl implements ConfigDataCTRL
  {
    public AdminStateCTRL(final EntityIndex indices)
    {
      super(getNetworkElement(),
            indices,
            MIBHN4000.Common.HnServices.EvcBindTable.OID_ADMIN_STATE,
            "interfaceAttributeValueChange",
            TrapParameterID.hnEvcBindingAdminState);
    }


    @Override
    public int
    getIntValueFromDB()
    {
      return getFlowDBImpl().getAdminState();
    }


    @Override
    public void
    setValueInDB()
    {
      getFlowDBImpl().setAdminState(value);
    }
      @Override
      public void
      notifyDataChange()
      {
        serverTrap.category      = CategoryType.FSPHN4000_EVCBINDING;
        serverTrap.objectIndexes = getFlowDBImpl().getIndices();
        super.notifyDataChange();
      }
  }
   protected class OperStateCTRL extends IntDataCTRLImpl implements StatusDataCTRL
  {
    public OperStateCTRL(final EntityIndex indices)
    {
      super(getNetworkElement(),
            indices,
            MIBHN4000.Common.HnServices.EvcBindTable.OID_OPER_STATE,
            "interfaceAttributeValueChange",
             TrapParameterID.hnEvcBindingOperState);
    }


    @Override
    public int
    getIntValueFromDB()
    {
      return getFlowDBImpl().getOperState();
    }


    @Override
    public void
    setValueInDB()
    {
      getFlowDBImpl().setOperState(value);
    }

    @Override
    public void
    notifyDataChange()
    {
      serverTrap.category      = CategoryType.FSPHN4000_EVCBINDING;
      serverTrap.objectIndexes = getFlowDBImpl().getIndices();
      super.notifyDataChange();
    }
  }

  protected class TypeCTRL extends IntDataCTRLImpl implements ConfigDataCTRL
  {
    public TypeCTRL(final EntityIndex indices)
    {
      super(getNetworkElement(),
            indices,
            MIBHN4000.Common.HnServices.EvcBindTable.OID_TYPE,
            "interfaceAttributeValueChange",
            TrapParameterID.hnEvcBindingType);
    }
    @Override
    public int
    getIntValueFromDB()
    {
      return getFlowDBImpl().getType();
    }

    @Override
    public void
    setValueInDB()
    {
      getFlowDBImpl().setType(value);
    }
    @Override
    public void
    notifyDataChange()
    {
      serverTrap.category      = CategoryType.FSPHN4000_EVCBINDING;
      serverTrap.objectIndexes = getFlowDBImpl().getIndices();
      super.notifyDataChange();
    }
  }

  protected class VlanPreservationCTRL extends IntDataCTRLImpl implements ConfigDataCTRL
    {
      public VlanPreservationCTRL(final EntityIndex indices)
      {
        super(getNetworkElement(),
              indices,
              MIBHN4000.Common.HnServices.EvcBindTable.OID_PRESERVATION,
              "interfaceAttributeValueChange",
              TrapParameterID.hnEvcBindingVlanPreservation);
      }


      @Override
      public int
      getIntValueFromDB()
      {
        return getFlowDBImpl().getVlanPreservation();
      }


      @Override
      public void
      setValueInDB()
      {
        getFlowDBImpl().setVlanPreservation(value);
      }
      @Override
      public void
      notifyDataChange()
      {
        serverTrap.category      = CategoryType.FSPHN4000_EVCBINDING;
        serverTrap.objectIndexes = getFlowDBImpl().getIndices();
        super.notifyDataChange();
      }
    }
  protected class VlanListCTRL extends StringDataCTRLImpl implements ConfigDataCTRL
    {
      public VlanListCTRL(final EntityIndex indices)
      {
        super(getNetworkElement(),
              indices,
              MIBHN4000.Common.HnServices.EvcBindTable.OID_VLAN_LIST,
              "interfaceAttributeValueChange",
              TrapParameterID.hnEvcBindingVlanList);
      }


      @Override
      public String
      getStringValueFromDB()
      {
        return getFlowDBImpl().getVlanList();
      }


      @Override
      public void
      setValueInDB()
      {
        getFlowDBImpl().setVlanList(value);
      }
      @Override
      public void
      notifyDataChange()
      {
        serverTrap.category      = CategoryType.FSPHN4000_EVCBINDING;
        serverTrap.objectIndexes = getFlowDBImpl().getIndices();
        super.notifyDataChange();
      }
    }

  protected class SecondaryStateCTRL extends IntDataCTRLImpl implements StatusDataCTRL, VolatileDataCTRL
  {
    public SecondaryStateCTRL(final EntityIndex indices)
    {
      super(getNetworkElement(),
              indices,
              MIBHN4000.Common.HnServices.EvcBindTable.OID_OBJECT_STATE,
              "interfaceAttributeValueChange",
              TrapParameterID.hnEvcBindingObjectState);
    }


    @Override
    public int
    getIntValueFromDB()
    {
			return getFlowDBImpl().getObjectState();
    }


    @Override
    public void
    setValueInDB()
    {
      getFlowDBImpl().setObjectState(value);
    }
    @Override
    public void
    notifyDataChange()
    {
      serverTrap.category      = CategoryType.FSPHN4000_EVCBINDING;
      serverTrap.objectIndexes = getFlowDBImpl().getIndices();
      super.notifyDataChange();
    }
  }
  //----------------------------------------------------------------------------
	// BEGIN: Implementation of interface PerformanceObject
	//----------------------------------------------------------------------------
	/**
	 * Returns the belonging database object.
	 */
	@Override
  public ManagedObjectDBImpl getManagedObjectDBImpl()
	{
		return getFlowDBImpl();
	}

  //----------------------------------------------------------------------------
	// END: Implementation of interface PerformanceObject
	//----------------------------------------------------------------------------

	/**
	 * Method used to set appropirate values for flow object on the device.
	 * @param flowSPProperties Properties for flow.
	 * @throws com.adva.nlms.mediation.common.validation.SPValidationException Validation exception
	 * @throws com.adva.nlms.mediation.common.transactions.ObjectInUseException NE reserved for other operation.
	 * @throws SNMPCommFailure SNMP Communication failure.
	 * @throws com.adva.nlms.mediation.common.transactions.NetTransactionException Transaction exception.
	 */
	@Override
  public void setFlowSettings(final FlowSPPropertiesHN4000 flowSPProperties) throws NetTransactionException, SPValidationException, SNMPCommFailure, ObjectInUseException//, boolean updateAll)
  {
    FlowSPPropertiesHN4000 currentFlowSPProperties = getFlowSPProperties();

  	final NetworkElementHN4000Impl neHN4000Impl = getNetworkElement();
    final SNMPCtrlHN4000 snmpCtrl = neHN4000Impl.getSNMPCtrlHN4000();

    if(neHN4000Impl.isPeer()){
      snmpCtrl.setFlowHN4xxName(flowSPProperties, currentFlowSPProperties.get(FlowSPPropertiesHN4000.VS.Desc));
    } else {
      snmpCtrl.setFlowProperties(flowSPProperties,currentFlowSPProperties);
    }

    neHN4000Impl.getPollingManager().preparePolling().forType(PollingType.DISCOVER_FLOW.getType()).
            withParameters(new DiscoverFlowHN4000PollingParameters(flowSPProperties)).useCurrentThread(true).execute();
  }


  @Override
  public void startContinuityTest()
           throws SNMPCommFailure, SPValidationException
   {
      checkEfmOamAdminState();
   }

   /**
   * Return result
   * @throws SNMPCommFailure
   */
  @Override
  public ContinuityTestResultPropertiesHN4000 getContinuityTest() throws SNMPCommFailure, SPValidationException
   {
     ContinuityTestResultPropertiesHN4000 continuityTestResultPropertiesHN4000 = new ContinuityTestResultPropertiesHN4000();
     checkEfmOamAdminState();
     //The TestResult would be Pass if the service state is Operational.  Any other value would be mapped to a TestResult of Fail.
     final int testResult =
             (PortHN4000EthernetDAO.getPortEthernetHN4000DBImpl(getFlowDBImpl().getPortHN4000DBId()).getEfmOamServiceState() == MIB.Dot3Oam.Objects.Oam.OperState.OPERATIONAL) ?
                     TestResultTranslation.PASS.getMIBValue() : TestResultTranslation.FAIL.getMIBValue();


     continuityTestResultPropertiesHN4000.set(ContinuityTestResultPropertiesHN4000.VI.TestResult, testResult);

     return continuityTestResultPropertiesHN4000;
   }
   /**
   * Return result
   * @throws SNMPCommFailure
   */
  @Override
  public void stopContinuityTest() throws SNMPCommFailure, SPValidationException
   {
     checkEfmOamAdminState();
   }


  /**
   * Start the Traffic Generator test on the HN400
   */
  @Override
  public void startTrafficGenTest(HnTrafficGenProperties properties)  throws SNMPCommFailure {
      final NetworkElementHN4000Impl neHN4000Impl = getNetworkElement();
	    final SNMPCtrlHN4000 snmpCtrl = neHN4000Impl.getSNMPCtrlHN4000();
	    properties.set(HnTrafficGenProperties.VI.Duration, 10);  //TODO DW: Create Constant for this Test, or retrieve it from fnm.properties ???
	    Integer ifIndex = this.getPortHN4000().getPortHN4000DBImpl().getIfIndex(); 
	    properties.set(HnTrafficGenProperties.VI.Index, ifIndex); 
	    snmpCtrl.startTrafficGen(properties);
  
  }

  /**
	 * Stop the Traffic Generator test on the HN400
	 */
	@Override
	public void stopTrafficGenTest() throws SNMPCommFailure {
    final NetworkElementHN4000Impl neHN4000Impl = getNetworkElement();
		HnTrafficGenProperties properties = new HnTrafficGenProperties();
		final SNMPCtrlHN4000 snmpCtrl = neHN4000Impl.getSNMPCtrlHN4000();
		Integer ifIndex = this.getPortHN4000().getPortHN4000DBImpl().getIfIndex();
		properties.set(HnTrafficGenProperties.VI.Index, ifIndex);
		snmpCtrl.stopTrafficGen(properties);

	}

  /**
	 * Get the Traffic Generator results on the HN400
	 */
	@Override
	public HnTrafficMonProperties getTrafficGenTest() throws SNMPCommFailure {
    final NetworkElementHN4000Impl neHN4000Impl = getNetworkElement();
		final HnTrafficGenProperties properties = new HnTrafficGenProperties();
		final SNMPCtrlHN4000 snmpCtrl = neHN4000Impl.getSNMPCtrlHN4000();
		Integer ifIndex = this.getPortHN4000().getPortHN4000DBImpl().getIfIndex();
		properties.set(HnTrafficGenProperties.VI.Index, ifIndex);
		return snmpCtrl.getTrafficGenResult(properties);

	}

  /**
   * throws an exception if OAM functions are disabled
   * @throws SPValidationException
   */
  private void checkEfmOamAdminState() throws SPValidationException
  {
     PortHN4000EthernetImpl port =  (PortHN4000EthernetImpl) getPortHN4000();
     //Prepare properties for polling
     PortHN4000EthernetSProperties portProperties = new PortHN4000EthernetSProperties();
     //We need to poll only OAM layer of ETH port
     portProperties.set(LayerDetailsSPProperties.VB.OAM, Boolean.TRUE);
     //synchronous layered polling
     port.startPolling(portProperties);

     if(port.getPortHN4000DBImpl().getEfmOamAdministration() != MIB.Dot3Oam.Objects.Oam.AdminState.ENABLED)
     {
      throw new SPValidationException("The test could not be run because OAM functions are disabled", SPCondition.INVALID_VALUE);
     }
  }

  /**
   * Runs volatile polling for objects related with flow.
   */
  @Override
  public void doPollingVolatile() {
    PF.getPollingService().inline(getNeId(), PollingType.VOLATILE,
            new VolatileHN4000PollingParameters(this));
  }

    //Recreates and returns the transient implementation of this entity.
    public static FlowHN4000Impl
    recreateImpl(final NetworkElementHN4000Impl ne, FlowHN4000DBImpl flowHN4000DB)
    {
        if(!ne.isPeer())
            return FlowHN4000Impl.recreate(ne, flowHN4000DB);
        else
            return FlowHN4xxImpl.recreate(ne, flowHN4000DB);
    }
}