/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: swiedeheft
 */
package com.adva.nlms.mediation.ne_comm;

import com.adva.nlms.common.HTTPProtocolType;

public class HTTPSettings {
  private HTTPProtocolType httpProtocolType;
  private int port;

  public HTTPSettings(HTTPProtocolType httpProtocolType, int port) {
    this.httpProtocolType = httpProtocolType;
    this.port = port;
  }

  public HTTPProtocolType getHttpProtocolType() {
    return httpProtocolType;
  }

  public int getPort() {
    return port;
  }
}
