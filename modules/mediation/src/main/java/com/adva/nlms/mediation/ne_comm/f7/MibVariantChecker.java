/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: msteiner
 */

package com.adva.nlms.mediation.ne_comm.f7;

public class MibVariantChecker {

  public static boolean isMibVariantSupportedByGivenClass(int mibVariant, Class<?> clazz) {
    MibVariantRequired m = clazz.getAnnotation(MibVariantRequired.class);
    return (m.sinceVersion() == MibVariantRequired.UNDEFINED || mibVariant >= m.sinceVersion())
            && (m.toVersion() == MibVariantRequired.UNDEFINED || mibVariant <= m.toVersion());
  }

}
