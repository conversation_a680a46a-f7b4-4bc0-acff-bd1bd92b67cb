/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: kkulaga
 */
package com.adva.nlms.mediation.config.f3.entity.esaprobeschedulegroup;

import com.adva.nlms.common.config.f3.EntityIndexPrefix;
import com.adva.nlms.mediation.common.MDOperationNotSupportedException;
import com.adva.nlms.common.config.f3.EntityIndexHelperF3;
import com.adva.nlms.mediation.config.EntityUpdaterFactory;
import com.adva.nlms.mediation.config.ManagedObjectDAO;
import com.adva.nlms.mediation.config.SnmpEntityAbstract;
import com.adva.nlms.mediation.config.f3.entity.factory.impl.DBImplCreateAndGetStrategy;
import com.adva.nlms.mediation.ne_comm.snmpscan.scanplan.adapter.ScanEntity;

import java.util.Set;

public class ESAProbeScheduleGroupF3DAO implements DBImplCreateAndGetStrategy<ESAProbeScheduleGroupF3DBImpl> {
    private static final ESAProbeScheduleGroupF3DAO instance = new ESAProbeScheduleGroupF3DAO();

    private ESAProbeScheduleGroupF3DAO() {
    }

    public static ESAProbeScheduleGroupF3DAO getInstance() {
        return instance;
    }

    @Override
    public ESAProbeScheduleGroupF3DBImpl create(int neId, ScanEntity scanEntity) throws MDOperationNotSupportedException {
        ESAProbeScheduleGroupF3DBImpl o = new ESAProbeScheduleGroupF3DBImpl(neId);
        EntityUpdaterFactory.getInstance().getEntityUpdater(o).initialize(o,new SnmpEntityAbstract( scanEntity.getAllDataMap()));
        return o;
    }

    @Override
    public ESAProbeScheduleGroupF3DBImpl get(int neId, int... objectIndices) throws MDOperationNotSupportedException {
        return ManagedObjectDAO.getInstance().getMoViaEntityIndex(
                ESAProbeScheduleGroupF3DBImpl.class,
                neId,
                EntityIndexHelperF3.getInstance().createEntityIndex(EntityIndexPrefix.esaProbScheduleGroup, objectIndices));
    }

    @Override
    public Set<ESAProbeScheduleGroupF3DBImpl> get(int neId) {
        return ManagedObjectDAO.getInstance().get(ESAProbeScheduleGroupF3DBImpl.class, neId);
    }

    @Override
    public Set<ESAProbeScheduleGroupF3DBImpl> getByParent(int neId, int... objectIndices) throws MDOperationNotSupportedException {
        throw new MDOperationNotSupportedException("ESASchedulerF3DAO.getByParent not supported.");
    }
}
