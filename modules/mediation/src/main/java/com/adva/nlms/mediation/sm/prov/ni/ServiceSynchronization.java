/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: spyrosm
 */

package com.adva.nlms.mediation.sm.prov.ni;

import com.adva.nlms.common.snmp.MDOperationFailedException;
import com.adva.nlms.common.snmp.NoSuchMDObjectException;
import com.adva.nlms.mediation.bean.provider.api.BeanProvider;
import com.adva.nlms.mediation.infrastructure.concurrent.AdvaExecutors;
import com.adva.nlms.mediation.infrastructure.concurrent.NamedThreadFactory;
import com.adva.nlms.mediation.common.persistence.MDPersistenceHelper;
import com.adva.nlms.mediation.common.persistence.MDTransactional;
import com.adva.nlms.mediation.common.transactions.InvalidPollingException;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.NetworkElementHdlrLocal;
import com.adva.nlms.mediation.event.message.MessageManager;
import com.adva.nlms.mediation.event.message.MessageManagerImpl;
import com.adva.nlms.mediation.mltopologymodel.resources.MLClassicAssociationHelper;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPCommFailure;
import com.adva.nlms.mediation.sm.SMServiceHelper;
import com.adva.nlms.mediation.sm.ServiceManagerCtrlImpl;
import com.adva.nlms.mediation.sm.dao.ConnectionDAO;
import com.adva.nlms.mediation.sm.dao.OChConnectionDAO;
import com.adva.nlms.mediation.sm.model.AbstractConnectionDBImpl;
import com.adva.nlms.mediation.sm.model.OChConnectionDBImpl;
import com.adva.nlms.mediation.sm.model.SubChConnectionDBImpl;
import com.adva.nlms.mediation.sm.prov.MlBasedCsProvisioningManager;
import com.adva.nlms.mediation.sm.prov.OpticalChannelService;
import com.adva.nlms.mediation.sm.prov.SMProvException;
import com.adva.nlms.mediation.sm.prov.TransportService;
import com.adva.nlms.mediation.sm.prov.cp.OCSAPSGroupHdlr;
import com.adva.nlms.mediation.sm.prov.cp.restoration.utils.ConfigurationProvider;
import com.adva.nlms.mediation.sm.prov.ni.model.NITunnelDAO;
import com.adva.nlms.mediation.sm.prov.ni.model.NITunnelDBImpl;
import com.adva.nlms.mediation.sm.prov.ni.model.NIWDMTunnelDBImpl;
import com.adva.nlms.mediation.sm.prov.ni.rest.NITunnelRestHandler;
import com.adva.nlms.mediation.sm.prov.ni.restoration.ClearMismatchUtil;
import com.adva.nlms.mediation.sm.prov.ni.serviceaction.ServiceActionCache;
import com.adva.nlms.mediation.sm.prov.ni.servicecreation.WdmTunnelCreator;
import com.adva.nlms.mediation.sm.prov.synchronize.ServiceSynchronize;
import com.adva.nlms.mediation.topology.LineDBImpl;
import com.adva.nlms.mediation.topology.LineHdlrImpl;
import ni.proto.external.services.service.ServiceOuterClass;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ExecutorService;

@Component
public class ServiceSynchronization implements ServiceSynchronize {
  private static final Logger LOGGER = LoggerFactory.getLogger(ServiceSynchronization.class);
  private static final String SERVICES_MSG = "Services";

  private final WdmTunnelCreator wdmTunnelCreator;
  private final NIOCSPathHandler niOCSPathHandler;
  private final NIOCSPortHandler niOCSPortHandler;
  private final OCSAPSGroupHdlr ocsAPSGroupHdlr;
  private final NetworkElementHdlrLocal neHlr;
  private final OChConnectionDAO ochDAO;
  private final ConnectionDAO connectionDAO;
  private final MessageManagerImpl messageManager;
  private final SMServiceHelper smServiceHelper;
  private final ServiceManagerCtrlImpl serviceManagerCtrl;
  private final NITunnelRestHandler niTunnelRestHandler;
  private final NITunnelDAO niTunnelDAO;
  private final ClearMismatchUtil clearMismatchUtil;
  private final LineHdlrImpl lineHdlr;

  private static final String SYNC_FOR_MOD_THREAD = "NISyncModThread";
  private static final ExecutorService syncModHandlerThreadPoolExecutor
          = AdvaExecutors.newSingleThreadExecutor(new NamedThreadFactory(SYNC_FOR_MOD_THREAD), true);

  @Autowired
  public ServiceSynchronization(WdmTunnelCreator wdmTunnelCreator, NIOCSPathHandler niOCSPathHandler, NIOCSPortHandler niOCSPortHandler,
                                OCSAPSGroupHdlr ocsAPSGroupHdlr, NetworkElementHdlrLocal neHlr, OChConnectionDAO ochDAO, ConnectionDAO connectionDAO,
                                MessageManagerImpl messageManager, SMServiceHelper smServiceHelper, ServiceManagerCtrlImpl serviceManagerCtrl,
                                NITunnelRestHandler niTunnelRestHandler, NITunnelDAO niTunnelDAO, ClearMismatchUtil clearMismatchUtil, LineHdlrImpl lineHdlr) {
    this.wdmTunnelCreator = wdmTunnelCreator;
    this.niOCSPathHandler = niOCSPathHandler;
    this.niOCSPortHandler = niOCSPortHandler;
    this.ocsAPSGroupHdlr = ocsAPSGroupHdlr;
    this.neHlr = neHlr;
    this.ochDAO = ochDAO;
    this.connectionDAO = connectionDAO;
    this.messageManager = messageManager;
    this.smServiceHelper = smServiceHelper;
    this.serviceManagerCtrl = serviceManagerCtrl;
    this.niTunnelRestHandler = niTunnelRestHandler;
    this.niTunnelDAO = niTunnelDAO;
    this.clearMismatchUtil = clearMismatchUtil;
    this.lineHdlr = lineHdlr;
  }

  @Override
  public void synchronizeService(int serviceId) throws MDOperationFailedException {
    synchronizeService(serviceId, false);
  }

  @Override
  public void synchronizeServiceWithoutRecreationOfExtLayers(int serviceId) throws MDOperationFailedException {
    synchronizeService(serviceId, true);
  }

  private void synchronizeService(int serviceId, boolean withoutRecreation) throws MDOperationFailedException {
    AbstractConnectionDBImpl connection = connectionDAO.getConnectionById(serviceId);
    if (connection == null)
      return;
    List<OpticalChannelService> opticalChannelServices = findValidServicesForAction(connection);
    for (OpticalChannelService ocs : opticalChannelServices)
      createServiceAndDoSynchronization(ocs, connection.getLabel(), withoutRecreation);
  }

  private List<OpticalChannelService> findValidServicesForAction(TransportService service) {
    List<OpticalChannelService> services = new ArrayList<>();
    if (service.isOChConnection() && service instanceof AbstractConnectionDBImpl connectionDB && !connectionDB.isSpurLink()) {
      services.add((OpticalChannelService) service);
    } else if (service instanceof SubChConnectionDBImpl connectionDB) {
      services.addAll(connectionDB.getAllContainingOChConnectionsDBs().stream().filter(ocs -> !(ocs.isSpurLink())).toList());
    }
    return services;
  }

  private void createServiceAndDoSynchronization(OpticalChannelService ocs, String label, boolean withoutRecreation) throws MDOperationFailedException {
    try {
      ServiceOuterClass.Service niService = wdmTunnelCreator.updateNiServiceForConnection(ocs.getNiServiceId(), ocs.getId());
      doSynchronization(niService, ocs, label, withoutRecreation);
    } catch (SMProvException e) {
      messageManager.addMessage(MessageManager.NO_LOGFILE, SERVICES_MSG, ocs.getId(), MessageManager.Error, e.getMessage(), label);
    }
  }

  private void doSynchronization(ServiceOuterClass.Service niService, OpticalChannelService ocs, String label, boolean withoutRecreation) throws MDOperationFailedException {
    try {
      //do polling first to give time for various threads
      if(!ocs.hasECHs()) {
        niOCSPortHandler.defineNetworkPorts(ocs, niService);
      }
      discoverAPSForProtectedService(ocs);
      niOCSPathHandler.handleOCSLines(ocs, niService);
      ochDAO.handleChannelCardProtOChConnectionDBImpl(ocs);
      synchronizeRestorationParameters(niService);
      doCrossConnectPolling(ocs);
      smServiceHelper.addIntermediateModules(ocs, false);
      // The parameter "withoutRecreation" is true only for the adoption use case. The assumption is, that adoption is
      // only the last step in a workflow of 1) Release, 2) Reconfigure (on the device), 3) ReExplore, 4) Adopt. In that
      // case, the classic and ML connections are all already properly recreated and correlated. Therefore, calling
      // "restoreExtLayers" is not necessary.
      if (!withoutRecreation)
        ConfigurationProvider.getConfigurationProvider().getServiceManagerFacade().restoreExtLayers(ocs.getId(), true);
      clearMismatchUtil.clearMismatch(ocs.getId());
      BeanProvider.get().getBean(MLClassicAssociationHelper.class).resyncFixOCSAssociation((OChConnectionDBImpl)ocs);
    } catch (SMProvException | InvalidPollingException | SNMPCommFailure e) {
      messageManager.addMessage(MessageManager.NO_LOGFILE, SERVICES_MSG, ocs.getId(), MessageManager.Error, e.getMessage(), label);
      throw new MDOperationFailedException(e);
    } finally {
      serviceManagerCtrl.notifyForServiceModification(ocs.getId(), (AbstractConnectionDBImpl) ocs);
    }
  }

  private void doCrossConnectPolling(OpticalChannelService ocs) {
    ocs = MDPersistenceHelper.refind(ocs,ocs.getId());
    Set<LineDBImpl> lines = new LinkedHashSet<>();
    lines.addAll(ocs.getRoute());
    lines.addAll(ocs.getProtRoute());

    try {
      Set<NetworkElement> allNeFromLines = lineHdlr.getAllNeFromLines(new ArrayList<>(lines));
      niOCSPortHandler.doCrossConnectPolling(allNeFromLines);
    } catch (NoSuchMDObjectException e) {
      LOGGER.warn("Unable to trigger cross connect polling Reason: {}",e.getMessage());
    }
  }

  private void discoverAPSForProtectedService(OpticalChannelService ocs) throws NoSuchMDObjectException, SNMPCommFailure {
    NIOCSProcessingParameters parameters = new NIOCSProcessingParameters();
    parameters.setOcs(ocs);
    parameters.setStartNe(neHlr.getNetworkElement(ocs.getStartNEID()));
    parameters.setPeerNe(neHlr.getNetworkElement(ocs.getPeerNEID()));
    ocsAPSGroupHdlr.discoverAPSForProtectedService(parameters);
  }

  private void synchronizeRestorationParameters(ServiceOuterClass.Service niService){
    updateNITunnelRestorationParameters(niService);
    updateOCSRestorationParameters(niService);
  }

  @MDTransactional
  private void updateNITunnelRestorationParameters(ServiceOuterClass.Service niService) {
    NITunnelDBImpl niTunnel = niTunnelDAO.getTunnelById(niService.getId().getId());
    if (niTunnel instanceof NIWDMTunnelDBImpl niwdmTunnelDB && niService.getConfiguration().hasRestoration()) {
      niwdmTunnelDB.setRestorationType(niService.getConfiguration().getRestoration().getRestorationTypeValue());
      niwdmTunnelDB.setRestorationMode(niService.getConfiguration().getRestoration().getRestorationModeValue());
      niwdmTunnelDB.setReversiontype(niService.getConfiguration().getRestoration().getReversionTypeValue());
    }
  }

  @MDTransactional
  private void updateOCSRestorationParameters(ServiceOuterClass.Service niService){
    OChConnectionDBImpl ocs = connectionDAO.getConnectionByNiTunnelID(niService.getId().getId());
    if(ocs!=null){
      ocs.setRestoreType(ServiceEnumMaps.mapDefinitionRestorationType(niService.getConfiguration().getRestoration().getRestorationMode()).getId());
      ocs.setRestorationMode(ServiceEnumMaps.mapDefinitionRestorationMode(niService.getConfiguration().getRestoration().getRestorationType()).getId());
      ocs.setReversionMode(ServiceEnumMaps.mapDefinitionReversionMode(niService.getConfiguration().getRestoration().getReversionType()).getId());
    }
  }

  public void synchronizeServiceF7(String serviceId) throws MDOperationFailedException {
    ServiceOuterClass.Service niService;
    try {
      niService = niTunnelRestHandler.getService(serviceId);
    } catch (SMProvException e) {
      throw new MDOperationFailedException(e);
    }

    if (niService != null) {
      AbstractConnectionDBImpl connection = connectionDAO.getConnectionByNiTunnelID(serviceId);
      if (connection != null) {
        List<OpticalChannelService> opticalChannelServices = findValidServicesForAction(connection);
        for (OpticalChannelService ocs : opticalChannelServices) {
          doSynchronization(niService, ocs, connection.getLabel(), false);
        }
      }
    } else {
      LOGGER.info("Service does not exist. Nothing to modify there.");
    }
  }

  public void synchronizeServiceF8(String serviceId) throws MDOperationFailedException {
    BeanProvider.get().getBean(MlBasedCsProvisioningManager.class).synchronizeServiceF8(serviceId);
  }

  void synchronizationForModificationF8(String serviceId){
    syncModHandlerThreadPoolExecutor.execute(() -> {
      try {
        synchronizeServiceF8(serviceId);
      } catch (MDOperationFailedException e) {
        LOGGER.info(e.getMessage(), e);
      }
    });
  }

  void synchronizationForModification(String serviceId){
    syncModHandlerThreadPoolExecutor.execute(() -> {
      AbstractConnectionDBImpl connection = connectionDAO.getConnectionByNiTunnelID(serviceId);
      try {
        if (connection != null) {
          ServiceOuterClass.Service niService = niTunnelRestHandler.getService(serviceId);
          List<OpticalChannelService> opticalChannelServices = findValidServicesForAction(connection);
          for (OpticalChannelService ocs : opticalChannelServices) {
            doModificationSynchronization(niService, ocs, connection.getLabel());
          }
        }
      } catch (SMProvException e) {
        LOGGER.info(e.getMessage(), e);
      } finally {
        ServiceActionCache.INSTANCE.removeServiceIdInUse(serviceId);
      }
    });
  }

  private void doModificationSynchronization(ServiceOuterClass.Service niService, OpticalChannelService ocs, String label) {
    try {
      niOCSPathHandler.handleOCSLines(ocs, niService);
      synchronizeRestorationParameters(niService);
    } catch (SMProvException e) {
      messageManager.addMessage(MessageManager.NO_LOGFILE, SERVICES_MSG, ocs.getId(), MessageManager.Error, e.getMessage(), label);
    }finally {
      serviceManagerCtrl.notifyForServiceModification(ocs.getId(), (AbstractConnectionDBImpl) ocs);
    }
  }
}
