/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: tomaszm
 */

package com.adva.nlms.mediation.config.fsp150cm;

import com.adva.nlms.mediation.config.f3.NetworkElementPollingWorkerF3BeanContainer;
import com.adva.nlms.mediation.polling.PollingType;
import com.adva.nlms.common.snmp.MDOperationFailedException;
import com.adva.nlms.mediation.common.transactions.InvalidPollingException;
import com.adva.nlms.mediation.config.AbstractNetworkElementPollingWorkerBasisImpl;
import com.adva.nlms.mediation.config.mtosi.FtpFSP150CMPollingParameters;
import com.adva.nlms.mediation.config.polling.PollingCommand;
import com.adva.nlms.mediation.config.polling.PollingCommandParameters;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPCommFailure;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

public abstract class AbstractNetworkElementPollingWorkerFSP150CMImpl<T extends NetworkElementFSP150CM> extends AbstractNetworkElementPollingWorkerBasisImpl<T> {

  /** The polling types that are configurable on the network element level. */
  private static final long[] CONFIGURABLE_POLLING_TYPES = new long[] {
          PollingType.PERFORMANCE_SHORT_TERM.getType(),
          PollingType.PERFORMANCE_LONG_TERM.getType(),
          PollingType.KEEP_ALIVE.getType(),
          PollingType.CONTINUOUS_DISCOVERY.getType(),
          PollingType.NE_CONFIG_BACKUP.getType(),
          PollingType.INVENTORY_REPORT_GENERATION.getType(),
          PollingType.ALARM_SEVERITY_COUNTER.getType()
  };

  public AbstractNetworkElementPollingWorkerFSP150CMImpl(T ne,
                                                         NetworkElementPollingWorkerF3BeanContainer networkElementPollingWorkerF3BeanContainer) {
    super(ne, networkElementPollingWorkerF3BeanContainer);
    setSysInfoPolling(networkElementPollingWorkerF3BeanContainer.getSysInfoPollingF3());
  }

  @Override
  protected void initializeNEPollings(Map<Long, PollingCommand> pollingCommands)
  {
    //override at subclasses
  }

  public class FTPPollingCommand implements PollingCommand {
    private final Logger LOG = LoggerFactory.getLogger(FTPPollingCommand.class);

    @Override
    public void runPolling (PollingCommandParameters params) throws MDOperationFailedException, InvalidPollingException, SNMPCommFailure {
      FtpFSP150CMPollingParameters pollingParams = (FtpFSP150CMPollingParameters)params.getPollingParameters();
      LOG.debug("FTPPollingCommand started with parameters ({})", pollingParams);
      getNe().getFtpf3Worker().synchronizeFTP(pollingParams.getFtpName(), pollingParams.getPgId());
    }
  }

  @Override
  public long[] getConfigurablePollingTypes() {
    return CONFIGURABLE_POLLING_TYPES;
  }

}
