/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: tomaszw
 */
package com.adva.nlms.mediation.config.f3;

import com.adva.nlms.common.NEUtils;
import com.adva.nlms.common.config.EntityIndex;
import com.adva.nlms.common.event.types.CategoryType;
import com.adva.nlms.common.snmp.MIB;
import com.adva.nlms.common.snmp.MIBFSP150CC825;
import com.adva.nlms.mediation.common.util.MtosiCapableDefinition;
import com.adva.nlms.mediation.config.EntityDescription;
import com.adva.nlms.mediation.config.NoSuchEntityException;
import com.adva.nlms.mediation.config.fsp150cm.MIBFSP150CMHelper;
import com.adva.nlms.mediation.event.EventDTO;
import com.adva.nlms.mediation.topology.TopologyNodeImpl;
import com.google.common.base.Function;
import com.google.common.base.Joiner;
import com.google.common.collect.Iterables;
import com.google.common.collect.Lists;
import com.google.common.primitives.Ints;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import jakarta.validation.constraints.NotNull;
import org.snmp4j.smi.OID;
import org.snmp4j.smi.VariableBinding;

import java.util.Arrays;
import java.util.List;

/**
 * Creates descriptions of entities which are not in db, based on category type and indexes.
 */
public class NeEntityNotInDbDescWorkerF3Impl {

  private Logger log = LogManager.getLogger(NeEntityNotInDbDescWorkerF3Impl.class);
  private static final int BITS_IN_TYPE = 1;
  private static final String SYS_BITS_OUT= "SYS BITS OUT";
  private static final String SYS_BITS_IN= "SYS BITS IN";
  private static final String CFM_MACOMP = "CFM MA";
  private static final String POWER_ETHERNET = "PSE GRP-1";
  private static final String POWER_ETHERNET_PORT = "PSE PORT-1-1";
  private static final String CFM_MANET = "CFM MANET";
  private static final String CFM_MD = "CFM MD";
  private static final String CFM_MEP = "CFM MEP";
  private static final String VTEP_IPV4_ROUTE_PATTERN = "VTEP IPv4 ROUTE-%d-%d-%s-%s";

  private static final String MIRROR_SESSION = "MIRROR SESSION";
  private static final String ENV_ALARM = "ENV ALARM INPUT";
  private static final String KEY_EXCHANGE_PROFILE = "KEY EXCHANGE PROFILE";
  private static final String OLM_CONTROLLER = "OLM CONTROLLER";

  private static final String ACCESS_FP_CPD = "ACCESS FLOWPOINT CPD";
  private static final String NETWORK_FP_CPD = "NETWORK FLOWPOINT CPD";
  private static final String CFM_A2N_VID_SHAPER = "CFM A2N VID SHAPER";
  private static final String CFM_N2A_VID_SHAPER = "CFM N2A VID SHAPER";

  private static final String NTP_VIRTUAL_PORT = "NTP VIRTUAL PORT";
  private static final String EO_MPLS_PW = "EOMPLS PW";
  private static final String F3_SYNC_DB_IMPL = "SYS SYNC";
  private static final String F3_TIMECLOCK_DB_IMPL = "TIME CLOCK";
  private static final String F3_SYNCREF_DB_IMPL = "SYNCREF";
  private static final String CLOCK_PROBE = "Clock Probe";
  private static final String VRRP_ROUTER = "VRRP ROUTER";
  private static final String F3_VRF = "L3 VRF";
  private static final String F3_TRAFFIC_OSPF_AREA = "TRAFFIC OSPF AREA";
  private static final String F3_L3_VRF_OSPF_ROUTER = "TRAFFIC OSPF";
  private static final String F3_VRF_LOOPBACK_INTERFACE = "LO";
  private static final String F3_L3_TRAFFIC_OSPF_INTERFACE = "TRAFFIC OSPF INTERFACE";
  private static final String F3_TWAMP_CONTROL_CLIENT = "F3 TWAMP CONTROL CLIENT";
  private static final String F3_TWAMP_IP_INTERFACE = "F3 TWAMP IP INTERFACE";
  private static final String F3_TWAMP_SERVER = "F3 TWAMP SERVER";
  private static final String F3_TWAMP_SESSION_REFLECTOR = "F3 TWAMP SESSION REFLECTOR";
  private static final String F3_TWAMP_SERVER_SESSION_REFLECTOR = "F3 TWAMP SERVER SESSION REFLECTOR";
  private static final String F3_TWAMP_DIST_STATS_CONFIG = "F3 TWAMP DIST STATS CONFIG";
  private static final String F3_TWAMP_SESSION = "F3 TWAMP SESSION";
  private static final String LLDP_MGMT_ADDR_TX_PORT = "LLDP MGMT ADDRESS TX PORT";
  private static final String SMTP_RECIPIENT = "SMTP RECIPIENT ";
  private static final String HTTPS_CERTIFICATE = "HTTPS CERTIFICATE";
  private static final String NTS_CERTIFICATE = "NTS CERTIFICATE";
  private static final String RADSEC_CERTIFICATE = "RADSEC CERTIFICATE";

  /**
   * Creates descriptions of entities which are not in db, based on category type and indexes.
   * @param event trap from device.
   * @return description of given event.
   * @throws NoSuchEntityException if given event is not handled.
   */
  public EntityDescription getEntityDescription(EventDTO event) throws NoSuchEntityException {
    EntityDescription desc = new EntityDescription();
    desc.alias = "";
    desc.fullDescription = "";
    desc.shortDescription = "";

    switch (event.category.getType()){
      case CategoryType.F3_BITSPORT_TYPE:
      case CategoryType.F3_BITSPORT_CREATE_TYPE:
      case CategoryType.F3_BITSPORT_DELETE_TYPE:
        abortIfMtosiNamingEnabled(event);
        int[] snmpIndices = event.getObjectIndex().toIntArray();
        if(checkSize(event, 5)){
          String bitsPortName = SYS_BITS_OUT;
          if(snmpIndices[3] == BITS_IN_TYPE){
            bitsPortName = SYS_BITS_IN;
          }

          desc.shortDescription = bitsPortName.concat(
                  getIndexString(snmpIndices[0], snmpIndices[1], NEUtils.getLocationString(TopologyNodeImpl.getNeType(event.getNeId()), snmpIndices[2]), snmpIndices[4]) );
        }
        break;

      case CategoryType.FSP150CC_MA_CREATE_TYPE:
      case CategoryType.FSP150CC_MA_DELETE_TYPE:
      case CategoryType.FSP150CC_MA_TYPE:
        setMADesc(event, desc);
        break;

      case CategoryType.FSP150CC_MD_CREATE_TYPE:
      case CategoryType.FSP150CC_MD_DELETE_TYPE:
      case CategoryType.FSP150CC_MD_TYPE:
        abortIfMtosiNamingEnabled(event);
        desc.shortDescription = CFM_MD.concat(getIndexString(event.objectIndex.toIntArray()[0]));
        break;

      case CategoryType.FSP150CC_MEP_TYPE:
      case CategoryType.FSP150CC_MEP_CREATE_TYPE:
      case CategoryType.FSP150CC_MEP_DELETE_TYPE:
        abortIfMtosiNamingEnabled(event);
        desc.shortDescription = CFM_MEP.concat(getIndexString(event.objectIndex.toIntArray()));
        break;
      case CategoryType.MA_MEP_LIST_TYPE:
      case CategoryType.MA_MEP_LIST_CREATE_TYPE:
      case CategoryType.MA_MEP_LIST_DELETE_TYPE:
        abortIfMtosiNamingEnabled(event);
        desc.shortDescription = CFM_MANET.concat(getIndexString(event.objectIndex.toIntArray()[0], event.objectIndex.toIntArray()[1]));
        break;
      case CategoryType.F3_CFM_DOWN_SHAPER_TYPE:
      case CategoryType.F3_CFM_DOWN_SHAPER_CREATE_TYPE:
      case CategoryType.F3_CFM_DOWN_SHAPER_DELETE_TYPE:
        setDescriptionForCfmDownMepQosShaper(event, desc);
        break;
      case CategoryType.F3_CFM_SYSDEF_MD_LVL_TYPE:
      case CategoryType.F3_CFM_SYSDEF_MD_LVL_CREATE_TYPE:
      case CategoryType.F3_CFM_SYSDEF_MD_LVL_DELETE_TYPE:
        setDescriptionForCfmMdLvlDefault(event, desc);
        break;
      case CategoryType.POWERETHERNET_TYPE:
        desc.shortDescription = POWER_ETHERNET.concat(getIndexString(event.objectIndex.toIntArray()));
        break;
      case CategoryType.POWERETHERNETPORT_TYPE:
        desc.shortDescription = POWER_ETHERNET_PORT.concat(getIndexString(event.objectIndex.toIntArray()));
        break;
      case CategoryType.F3_PORT_MIRROR_TYPE:
      case CategoryType.F3_PORT_MIRROR_CREATE_TYPE:
      case CategoryType.F3_PORT_MIRROR_DELETE_TYPE:
        desc.shortDescription = MIRROR_SESSION.concat(getIndexString(event.objectIndex.toIntArray()));
        break;
      case CategoryType.F3_ENV_ALM_CREATE_TYPE:
      case CategoryType.F3_ENV_ALM_DELETE_TYPE:
      case CategoryType.F3_ENV_ALM_TYPE:
        desc.shortDescription = ENV_ALARM.concat(getIndexString(event.objectIndex.toIntArray()));
        break;
      case CategoryType.FSP_PORT_WITH_IFINDEX_TYPE:
        setNamesForPortWithIfIndex(event, desc);
        break;
      case CategoryType.F3_KEY_EXCHANGE_PROFILE_CREATE_TYPE:
      case CategoryType.F3_KEY_EXCHANGE_PROFILE_DELETE_TYPE:
      case CategoryType.F3_KEY_EXCHANGE_PROFILE_TYPE:
        desc.shortDescription = KEY_EXCHANGE_PROFILE.concat(getIndexString(event.objectIndex.toIntArray()));
        break;
      case CategoryType.ENTITY_EVENT_TYPE:
        desc.shortDescription = event.entityDescription;
        break;
      case CategoryType.F3_USB_DCN_PORT_CREATE_TYPE:
      case CategoryType.F3_USB_DCN_PORT_DELETE_TYPE:
      case CategoryType.F3_USB_DCN_PORT_TYPE:
        desc.shortDescription = "3G DCN-1-1-1-1";
        break;
      case CategoryType.OLM_CONTROLLER_TYPE:
      case CategoryType.OLM_CONTROLLER_CREATE_TYPE:
      case CategoryType.OLM_CONTROLLER_DELETE_TYPE:
        desc.shortDescription = OLM_CONTROLLER.concat(getIndexString(event.objectIndex.toIntArray()));
        break;
      case CategoryType.F3_ACCESS_FP_CPD_TYPE:
      case CategoryType.F3_ACCESS_FP_CPD_CREATE_TYPE:
      case CategoryType.F3_ACCESS_FP_CPD_DELETE_TYPE:
        if(MtosiCapableDefinition.isMtosiNamingEnabled(event.getNeId(), event.getNeType()))
          desc.shortDescription = "/aid="+ACCESS_FP_CPD.concat(getIndexString(event.objectIndex.toIntArray()));
        else
          desc.shortDescription = ACCESS_FP_CPD.concat(getIndexString(event.objectIndex.toIntArray()));
        break;
      case CategoryType.F3_NETWORK_FP_CPD_TYPE:
      case CategoryType.F3_NETWORK_FP_CPD_CREATE_TYPE:
      case CategoryType.F3_NETWORK_FP_CPD_DELETE_TYPE:
        if(MtosiCapableDefinition.isMtosiNamingEnabled(event.getNeId(), event.getNeType()))
          desc.shortDescription = "/aid="+NETWORK_FP_CPD.concat(getIndexString(event.objectIndex.toIntArray()));
        else
          desc.shortDescription = NETWORK_FP_CPD.concat(getIndexString(event.objectIndex.toIntArray()));
        break;
      case CategoryType.F3_CFM_ACC_PORT_QOS_SHAPER_TYPE:
      case CategoryType.F3_CFM_ACC_PORT_QOS_SHAPER_CREATE_TYPE:
      case CategoryType.F3_CFM_ACC_PORT_QOS_SHAPER_DELETE_TYPE:
        if(MtosiCapableDefinition.isMtosiNamingEnabled(event.getNeId(), event.getNeType()))
          desc.shortDescription = "/aid="+CFM_A2N_VID_SHAPER.concat(getIndexString(event.objectIndex.toIntArray()));
        else
          desc.shortDescription = CFM_A2N_VID_SHAPER.concat(getIndexString(event.objectIndex.toIntArray()));
        break;
      case CategoryType.F3_CFM_NET_PORT_QOS_SHAPER_TYPE:
      case CategoryType.F3_CFM_NET_PORT_QOS_SHAPER_CREATE_TYPE:
      case CategoryType.F3_CFM_NET_PORT_QOS_SHAPER_DELETE_TYPE:
        if(MtosiCapableDefinition.isMtosiNamingEnabled(event.getNeId(), event.getNeType()))
          desc.shortDescription = "/aid="+CFM_N2A_VID_SHAPER.concat(getIndexString(event.objectIndex.toIntArray()));
        else
          desc.shortDescription = CFM_N2A_VID_SHAPER.concat(getIndexString(event.objectIndex.toIntArray()));
        break;
      case CategoryType.F3_PRIVILEGE_CHANGE_TYPE:
        desc.shortDescription = event.moduleTypeName;
        break;
        case CategoryType.NTP_VIRTUAL_PORT_TYPE:
      case CategoryType.NTP_VIRTUAL_PORT_CREATE_TYPE:
      case CategoryType.NTP_VIRTUAL_PORT_DELETE_TYPE:
        if(MtosiCapableDefinition.isMtosiNamingEnabled(event.getNeId(), event.getNeType()))
          desc.shortDescription = "/aid="+NTP_VIRTUAL_PORT.concat(getIndexString(event.objectIndex.toIntArray()));
        else
          desc.shortDescription = NTP_VIRTUAL_PORT.concat(getIndexString(event.objectIndex.toIntArray()));
        break;
      case CategoryType.F3_VTEP_IPV4_ROUTE_TYPE:
      case CategoryType.F3_VTEP_IPV4_ROUTE_CREATE_TYPE:
      case CategoryType.F3_VTEP_IPV4_ROUTE_DELETE_TYPE:
        desc.shortDescription = generateStaticRouteDescription(event.objectIndex.toIntArray());
        break;
      case CategoryType.EO_MPLS_PW_TYPE:
      case CategoryType.EO_MPLS_PW_CREATE_TYPE:
      case CategoryType.EO_MPLS_PW_DELETE_TYPE:
        if(MtosiCapableDefinition.isMtosiNamingEnabled(event.getNeId(), event.getNeType()))
          desc.shortDescription = "/aid="+EO_MPLS_PW.concat(getIndexString(event.objectIndex.toIntArray()));
        else
          desc.shortDescription = EO_MPLS_PW.concat(getIndexString(event.objectIndex.toIntArray()));
        break;
      case CategoryType.F3_SYNC_TYPE:
      case CategoryType.F3_SYNC_CREATE_TYPE:
      case CategoryType.F3_SYNC_DELETE_TYPE:
        desc.shortDescription = F3_SYNC_DB_IMPL.concat(getIndexString(event.objectIndex.toIntArray()));
        break;
      case CategoryType.F3_SYNC_TIMECLOCK_TYPE:
      case CategoryType.F3_SYNC_TIMECLOCK_CREATE_TYPE:
      case CategoryType.F3_SYNC_TIMECLOCK_DELETE_TYPE:
        desc.shortDescription = F3_TIMECLOCK_DB_IMPL.concat(getIndexString(event.objectIndex.toIntArray()));
        break;
      case CategoryType.F3_SYNCREF_TYPE:
      case CategoryType.F3_SYNCREF_CREATE_TYPE:
      case CategoryType.F3_SYNCREF_DELETE_TYPE:
        desc.shortDescription = F3_SYNCREF_DB_IMPL.concat(getIndexString(event.objectIndex.toIntArray()));
        break;
      case CategoryType.CLOCKPROBE_TYPE:
        desc.shortDescription = CLOCK_PROBE.concat(getIndexString(event.objectIndex.toIntArray()));
        break;
      case CategoryType.F3_FIPS_TYPE:
        desc.shortDescription = MIBFSP150CMHelper.SYSTEM_EVENT_STRING;
        break;
      case CategoryType.VRRP_ROUTER_CREATE_TYPE:
      case CategoryType.VRRP_ROUTER_DELETE_TYPE:
      case CategoryType.VRRP_ROUTER_TYPE:
        desc.shortDescription = VRRP_ROUTER.concat(getIndexString(event.objectIndex.toIntArray()));
        break;
      case CategoryType.F3_VRF_CREATE_TYPE:
      case CategoryType.F3_VRF_DELETE_TYPE:
      case CategoryType.F3_VRF_TYPE:
        desc.shortDescription = F3_VRF.concat(getIndexString(event.objectIndex.toIntArray()));
        break;
      case CategoryType.F3_TRAFFIC_OSPF_AREA_CREATE_TYPE:
      case CategoryType.F3_TRAFFIC_OSPF_AREA_DELETE_TYPE:
      case CategoryType.F3_TRAFFIC_OSPF_AREA_TYPE:
        desc.shortDescription = F3_TRAFFIC_OSPF_AREA.concat(getIndexString(event.objectIndex.toIntArray()));
        break;
      case CategoryType.F3_L3_VRF_OSPF_ROUTER_CREATE_TYPE:
      case CategoryType.F3_L3_VRF_OSPF_ROUTER_DELETE_TYPE:
      case CategoryType.F3_L3_VRF_OSPF_ROUTER_TYPE:
        desc.shortDescription = F3_L3_VRF_OSPF_ROUTER.concat(getIndexString(event.objectIndex.toIntArray()));
        break;
      case CategoryType.F3_VRF_LOOPBACK_INTERFACE_CREATE_TYPE:
      case CategoryType.F3_VRF_LOOPBACK_INTERFACE_DELETE_TYPE:
      case CategoryType.F3_VRF_LOOPBACK_INTERFACE_TYPE:
        desc.shortDescription = F3_VRF_LOOPBACK_INTERFACE.concat(getIndexString(event.objectIndex.toIntArray()));
        break;
      case CategoryType.F3_L3_TRAFFIC_OSPF_INTERFACE_CREATE_TYPE:
      case CategoryType.F3_L3_TRAFFIC_OSPF_INTERFACE_DELETE_TYPE:
      case CategoryType.F3_L3_TRAFFIC_OSPF_INTERFACE_TYPE:
        desc.shortDescription = F3_L3_TRAFFIC_OSPF_INTERFACE.concat(getIndexString(event.objectIndex.toIntArray()));
        break;
      case CategoryType.F3_TWAMP_CONTROL_CLIENT_CREATE_TYPE:
      case CategoryType.F3_TWAMP_CONTROL_CLIENT_DELETE_TYPE:
      case CategoryType.F3_TWAMP_CONTROL_CLIENT_TYPE:
        desc.shortDescription = F3_TWAMP_CONTROL_CLIENT.concat(getIndexString(event.objectIndex.toIntArray()));
        break;
      case CategoryType.F3_TWAMP_IP_INTERFACE_CREATE_TYPE:
      case CategoryType.F3_TWAMP_IP_INTERFACE_DELETE_TYPE:
      case CategoryType.F3_TWAMP_IP_INTERFACE_TYPE:
        desc.shortDescription = createTwampInterfaceDescription(event);
        break;
      case CategoryType.F3_TWAMP_SERVER_CREATE_TYPE:
      case CategoryType.F3_TWAMP_SERVER_DELETE_TYPE:
      case CategoryType.F3_TWAMP_SERVER_TYPE:
        desc.shortDescription = F3_TWAMP_SERVER.concat(getIndexString(event.objectIndex.toIntArray()));
        break;
      case CategoryType.F3_TWAMP_SESSION_REFLECTOR_CREATE_TYPE:
      case CategoryType.F3_TWAMP_SESSION_REFLECTOR_DELETE_TYPE:
      case CategoryType.F3_TWAMP_SESSION_REFLECTOR_TYPE:
        desc.shortDescription = F3_TWAMP_SESSION_REFLECTOR.concat(getIndexString(event.objectIndex.toIntArray()));
        break;
      case CategoryType.F3_TWAMP_SERVER_SESSION_REFLECTOR_CREATE_TYPE:
      case CategoryType.F3_TWAMP_SERVER_SESSION_REFLECTOR_DELETE_TYPE:
      case CategoryType.F3_TWAMP_SERVER_SESSION_REFLECTOR_TYPE:
        desc.shortDescription = F3_TWAMP_SERVER_SESSION_REFLECTOR.concat(getIndexString(event.objectIndex.toIntArray()));
        break;
      case CategoryType.F3_TWAMP_DIST_STATS_CONFIG_CREATE_TYPE:
      case CategoryType.F3_TWAMP_DIST_STATS_CONFIG_DELETE_TYPE:
      case CategoryType.F3_TWAMP_DIST_STATS_CONFIG_TYPE:
        desc.shortDescription = F3_TWAMP_DIST_STATS_CONFIG.concat(getIndexString(event.objectIndex.toIntArray()));
        break;
      case CategoryType.F3_TWAMP_SESSION_CREATE_TYPE:
      case CategoryType.F3_TWAMP_SESSION_DELETE_TYPE:
      case CategoryType.F3_TWAMP_SESSION_TYPE:
        desc.shortDescription = F3_TWAMP_SESSION.concat(getIndexString(event.objectIndex.toIntArray()));
        break;
      case CategoryType.LLDP_MGMT_ADDR_TX_PORT_ADVA_EXT_TYPE:
      case CategoryType.LLDP_MGMT_ADDR_TX_PORT_ADVA_EXT_CREATE_TYPE:
      case CategoryType.LLDP_MGMT_ADDR_TX_PORT_ADVA_EXT_DELETE_TYPE:
        desc.shortDescription = LLDP_MGMT_ADDR_TX_PORT;
        break;
      case CategoryType.F3_SMTP_RECIPIENT_TYPE, CategoryType.F3_SMTP_RECIPIENT_CREATE_TYPE, CategoryType.F3_SMTP_RECIPIENT_DELETE_TYPE:
        desc.shortDescription = SMTP_RECIPIENT.concat(getStringFromIndexArray(event.objectIndex.toIntArray()));
        break;
      case CategoryType.CERT_SERVICE_ASSIGNER_TYPE, CategoryType.CERT_SERVICE_ASSIGNER_CREATE_TYPE, CategoryType.CERT_SERVICE_ASSIGNER_DELETE_TYPE:
        desc.shortDescription = getCertificateNameFromIndexArray(event.objectIndex.toIntArray());
        break;
      default:
        return throwNoSuchEntity(event);
    }
    return desc;
  }

  @NotNull
  private String createTwampInterfaceDescription(EventDTO event) {
    //need substring to cut first character from event as it transports number of next characters in name of interface
    return F3_TWAMP_IP_INTERFACE.concat("-" + (getInterfaceName(event.objectIndex.toIntArray()).substring(1)));
  }

  private void setNamesForPortWithIfIndex(EventDTO event, EntityDescription desc){
    OID nameOID = new OID(MIB.IfX.OID_NAME);
    for(VariableBinding vb : event.getVbl()){
      if(vb.getOid().startsWith(nameOID)){
        desc.shortDescription = vb.getVariable().toString();
        break;
      }
    }
  }

  private void setDescriptionForCfmDownMepQosShaper(EventDTO event, EntityDescription desc) {
    boolean mtosiNamingEnabled = MtosiCapableDefinition.isMtosiNamingEnabled(event.getNeId(), event.getNeType());
    StringBuilder sb = new StringBuilder();
    if(mtosiNamingEnabled){
      sb.append("/aid=");
    }
    sb.append("CFM DOWN ");
    int[] indices = event.getObjectIndex().toIntArray();
    if(indices[4] == MIBFSP150CC825.CfmDownMEPQosShaper.DIRECTION_TX){
      sb.append("TX");
    }else if(indices[4] == MIBFSP150CC825.CfmDownMEPQosShaper.DIRECTION_RX){
      sb.append("RX");
    }
    sb.append(" SHAPER").append(getIndexString(indices[0], indices[1], indices[2], indices[3], indices[4]));
    desc.shortDescription = sb.toString();
  }

  private void setDescriptionForCfmMdLvlDefault(EventDTO event, EntityDescription desc) {
    boolean mtosiNamingEnabled = MtosiCapableDefinition.isMtosiNamingEnabled(event.getNeId(), event.getNeType());
    StringBuilder sb = new StringBuilder();
    if(mtosiNamingEnabled){
      sb.append("/aid=");
    }
    sb.append("CFM SYSDEF MDLVL");
    desc.shortDescription = sb.toString();
  }

  protected void setMADesc(EventDTO event, EntityDescription desc) throws NoSuchEntityException {
    // for ma comp notifications we don't want to have default name when mtosi is on but for ma net we should have default name.
    int[] indices = event.getObjectIndex().toIntArray();
    if(indices.length == 3 && indices[0]!=0){
      abortIfMtosiNamingEnabled(event);
    }
    if(indices.length == 2 || indices.length == 3 && indices[0]==0){
      int [] manetIndices;
      if(indices[0]==0)
        manetIndices = Arrays.copyOfRange(indices, 1, 3);
      else manetIndices = indices;

      desc.shortDescription = CFM_MANET.concat(getIndexString(manetIndices));
    } else if (indices.length == 3){
      desc.shortDescription = CFM_MACOMP.concat( getIndexString(indices[1], indices[2]) );
    }
  }

  /**
   * If mtosi naming is enabled and supported by ne and we cannot generate Mtosi name basing on simple event data then we should not
   * describe this event.
   * @param event
   * @throws NoSuchEntityException
   */
  private void abortIfMtosiNamingEnabled(EventDTO event) throws NoSuchEntityException {
    if(MtosiCapableDefinition.isMtosiNamingEnabled(event.getNeId(), event.getNeType())){
      throwNoSuchEntity(event);
    }
  }

  private EntityDescription throwNoSuchEntity(EventDTO event) throws NoSuchEntityException {
    throw new NoSuchEntityException("No such entity with category type: " + event.category.getType());
  }

    protected boolean checkSize(EventDTO event, int size){
    EntityIndex objectIndex = event.getObjectIndex();
    if(objectIndex != null && objectIndex.toIntArray().length == size){
      return true;
    }
    log.error("Event with category["+event.category+"] had wrong number of indexes!");
    return false;
  }

  private String getIndexString(int... indexes){
    StringBuilder sb = new StringBuilder();

    for (int index : indexes) {
      sb.append("-").append(index);
    }
    return sb.toString();
  }

  private String getStringFromIndexArray(int... indexes) {
    char[] charArray = new char[indexes.length];
    for (int i = 0; i < indexes.length; i++) {
      charArray[i] = (char) indexes[i];
    }
    return String.valueOf(charArray);
  }

  private String getCertificateNameFromIndexArray(int... indexes) {
    if (indexes.length > 0) {
      int last = indexes[indexes.length - 1];
      return switch (last) {
        case 1 -> HTTPS_CERTIFICATE;
        case 2 -> NTS_CERTIFICATE;
        case 3 -> RADSEC_CERTIFICATE;
        default -> "";
      };
      }
    return "";
  }

  String generateStaticRouteDescription(int[] staticRouteIndex) {

    try {
      int[] ipPart = Arrays.copyOfRange(staticRouteIndex, 2, 3 * 5 -1);
      int[] interfacePart = Arrays.copyOfRange(staticRouteIndex, 3*5, staticRouteIndex.length);

      return String.format(
          VTEP_IPV4_ROUTE_PATTERN, staticRouteIndex[0],staticRouteIndex[1],
              Joiner.on('-').join(
                      Iterables.transform(Lists.partition(Ints.asList(ipPart), 4),
                          new Function<List<Integer>, String>() {
                            @Override
                            public String apply(List<Integer> integers) {
                              return Joiner.on('.').join(integers);
                            }
                          }
                      )
              ),
              getInterfaceName(interfacePart)
      );
    } catch (Exception e) {
      log.error("Cannot generate AID for VTEP IPv4 ROUTE object", e);
      return "VTEP IPv4 ROUTE".concat(getIndexString(staticRouteIndex));
    }
  }

  private String getInterfaceName (int[] interfacePart) {
    StringBuilder sb = new StringBuilder();
    for (int i : interfacePart) {
      sb.append((char)i);
    }
    return sb.toString();
  }
}
