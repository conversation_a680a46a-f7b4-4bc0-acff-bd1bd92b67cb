/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: zyan<PERSON><PERSON><PERSON>
 */
package com.adva.nlms.mediation.ne_comm.f3.snmpscan.scanplan.definition;

import com.adva.nlms.mediation.ne_comm.snmpscan.scanplan.ScanPlan;
import com.adva.nlms.mediation.ne_comm.snmpscan.scanplan.builder.ScanPlanBuilder;
import com.adva.nlms.mediation.ne_comm.f3.snmpscan.scanplan.converter.EntityIndexConverter;
import com.adva.nlms.mediation.ne_comm.f3.snmpscan.scantable.definition.ScanSymmetricom;

public class ScanPlanSymmetricom {

    public static ScanPlan getSymmetricomScanPlan() {
        ScanPlanBuilder symmetricomScanPlan  = ScanPlanBuilder.createScanPlanBuilder();
        symmetricomScanPlan.select().all().from(ScanSymmetricom.PTP_COMMON_TABLE).convert(new EntityIndexConverter());
        return symmetricomScanPlan.getResult();
    }
}
