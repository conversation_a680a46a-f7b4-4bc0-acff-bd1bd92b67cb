/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: askar<PERSON><PERSON>
 */
package com.adva.nlms.mediation.housekeeping.swupgrade;

import com.adva.common.model.NEUpdateCsvDto;
import com.adva.common.util.collect.Lists;
import com.adva.nlms.common.NEUtils;
import com.adva.nlms.common.housekeeping.FTPConnectionType;
import com.adva.nlms.common.housekeeping.FTPServerFuncEnum;
import com.adva.nlms.common.housekeeping.FTPServerSettings;
import com.adva.nlms.common.housekeeping.ProtocolType;
import com.adva.nlms.common.housekeeping.SecurityMode;
import com.adva.nlms.common.housekeeping.ServerType;
import com.adva.nlms.common.housekeeping.TransportProtocol;
import com.adva.nlms.common.housekeeping.fmwupdate.FmwUpdateDTO;
import com.adva.nlms.common.housekeeping.swupgrade.NeSwUpgradeConfigDTO;
import com.adva.nlms.common.housekeeping.swupgrade.SwImageFileDTO;
import com.adva.nlms.common.housekeeping.swupgrade.SwRequestValidationResult;
import com.adva.nlms.common.housekeeping.swupgrade.SwUpgradeDTO;
import com.adva.nlms.common.housekeeping.swupgrade.SwUpgradeRequest;
import com.adva.nlms.common.housekeeping.swupgrade.SwUpgradeType;
import com.adva.nlms.common.rest.MDRestPath;
import com.adva.nlms.common.snmp.MDOperationFailedException;
import com.adva.nlms.common.snmp.NoSuchMDObjectException;
import com.adva.nlms.common.swupgrade.SWUpgradeNEFamilyUtils;
import com.adva.nlms.common.swupgrade.SWUpgradeStateEnum;
import com.adva.nlms.common.swupgrade.SWUpgradeTypeEnum;
import com.adva.nlms.common.swupgrade.SWUpgradeValidationResult;
import com.adva.nlms.infrastucture.security.permission.api.PermissionAction;
import com.adva.nlms.mediation.common.housekeeping.FileServerStateRepository;
import com.adva.nlms.mediation.common.housekeeping.RepositoryManagerException;
import com.adva.nlms.mediation.common.persistence.MDPersistenceContext;
import com.adva.nlms.mediation.common.persistence.MDPersistenceHelper;
import com.adva.nlms.mediation.common.persistence.MDTransactional;
import com.adva.nlms.mediation.common.rest.MDRestComponent;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.NetworkElementHdlrLocal;
import com.adva.nlms.mediation.config.NoSuchNetworkElementException;
import com.adva.nlms.mediation.event.message.MessageManager;
import com.adva.nlms.mediation.housekeeping.F7SoftwareVersionComparison;
import com.adva.nlms.mediation.housekeeping.FTPPreferencesDAO;
import com.adva.nlms.mediation.housekeeping.serverproperties.FTPServerPasswordMasker;
import com.adva.nlms.mediation.housekeeping.serverproperties.SWServerPropertiesSelector;
import com.adva.nlms.mediation.housekeeping.serverproperties.ServerPropertiesSelector;
import com.adva.nlms.mediation.housekeeping.swupgrade.api.NetworkElementVersionsUpdateProcess;
import com.adva.nlms.mediation.housekeeping.swupgrade.db.NEMISWFileDBImpl;
import com.adva.nlms.mediation.housekeeping.swupgrade.db.SWUpgradeDAO;
import com.adva.nlms.mediation.housekeeping.swupgrade.db.SWUpgradeNEDBImpl;
import com.adva.nlms.mediation.housekeeping.swupgrade.db.SWUpgradePropertiesDBImpl;
import com.adva.nlms.mediation.housekeeping.swupgrade.device.SWUpgradeFactory;
import com.adva.nlms.mediation.housekeeping.swupgrade.device.util.F7FullOrRequiredDownloadModeConfigurator;
import com.adva.nlms.mediation.housekeeping.swupgrade.exception.SWUpgradeException;
import com.adva.nlms.mediation.housekeeping.swupgrade.util.SWTypeConditionHelper;
import com.adva.nlms.mediation.housekeeping.swupgrade.util.SWUpgradeFileHelper;
import com.adva.nlms.mediation.infrastructure.server_infra.server_modules.api.out.AbstractModule;
import com.adva.nlms.mediation.ne_comm.NECommCtrl;
import com.adva.nlms.mediation.ne_comm.PreferencesDBImpl;
import com.adva.nlms.mediation.ne_comm.SNMPCtrl;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPCommFailure;
import com.adva.nlms.infrastucture.security.SecurityTools;
import com.adva.nlms.infrastucture.security.permission.api.Authorization;
import com.adva.nlms.mediation.servicelocator.QualifiedServicesRegistry;
import com.adva.nlms.mediation.topology.UnsupportedNetworkElementsHandler;
import com.adva.nlms.mediation.util.FTPSettingsUtils;
import jakarta.validation.constraints.NotNull;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.PUT;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Service
@Path(MDRestPath.SWDL.PATH)
@Produces(MediaType.APPLICATION_JSON)
@MDRestComponent
public class SwUpgradeHdlr extends AbstractModule implements SWHdlrLocal {

  private static final Logger log = LogManager.getLogger(SwUpgradeHdlr.class);

  @Autowired
  private SWUpgradeRunner runner;
  @Autowired
  private NECommCtrl neCommCtrl;
  @Autowired
  private MessageManager messageManager;
  @Autowired
  private SWUpgradeDAO dao;
  @Autowired
  private NetworkElementHdlrLocal neHdlr;
  @Autowired
  private SWUpgradeCtrl upgrader;
  @Autowired
  private SWUpgradeFactory factory;
  @Autowired
  private FileServerStateRepository stateRepository;
  @Autowired
  private FTPPreferencesDAO ftpPreferencesDAO;
  @Autowired
  private SWUpgradePageHdlrImpl swUpgradePageHdlr;
  @Autowired
  private UnsupportedNetworkElementsHandler unsupportedNetworkElementsHandler;
  @Autowired
  private FmwRequestValidator fmwRequestValidator;

  @Autowired
  @Qualifier("forSwUpdateHdlr")
  private QualifiedServicesRegistry<NetworkElementVersionsUpdateProcess, String> versionUpdateProcesses;


  /**
   * Lock for network element software upgrade configuration change operations
   */
  private final Object neSwUpgradeConfigChangeLock = new Object();

  private final SwUpgradeFtpCache ftpCache = new SwUpgradeFtpCache();

  private int numberOfParallelDownloads = SWUpgradeProperties.NUMBER_OF_PARALLEL_DOWNLOADS;

  @Override
  public void dependentInit() {
    numberOfParallelDownloads = dao.getSWNumberOfParallelDownloadsFromDatabase(neCommCtrl.getPreferencesObjID());
  }

  @Override
  public boolean isAnyUpgradePending() {
    return (runner.getUpgradesCountByState(SWUpgradeStateEnum.PENDING) > 0);
  }

  @Override
  public boolean isAnyUpgradeRunning() {
    /*
     * If in slave mode return false. It is still possible that there are some upgrades frozen during switchover,
     * which can start when slave become master again
     */
    if (!factory.isHAServerModeValid()) {
      return false;
    }
    return runner.getUpgradesCountByState(SWUpgradeStateEnum.ACTIVE) > 0 || runner
            .getUpgradesCountByState(SWUpgradeStateEnum.EXECUTING) > 0;
  }

  @Override
  public boolean isUpgradeRunningForNe(int neID) {
    return runner.isUpgradeRunningForNE(neID);
  }

  //used in polling
  @Override
  public void updateNeVersionsInDB(int neID) throws NoSuchMDObjectException, SNMPCommFailure {

    NetworkElement neImpl = neHdlr.getNetworkElement(neID);
    SNMPCtrl snmpCtrl = neImpl.getSNMPCtrl();

    NetworkElementVersionsUpdateProcess updater = getUpdaterForNe(snmpCtrl, neImpl);
    updater.updateNeVersions(dao, neImpl);
    unsupportedNetworkElementsHandler.handle(neImpl.neDBImpl());
  }

  private NetworkElementVersionsUpdateProcess getUpdaterForNe(@NotNull SNMPCtrl snmpCtrl, NetworkElement neImpl) {
    Optional<NetworkElementVersionsUpdateProcess> worker = getMatchingBean(prepareQualifier(neImpl.getNetworkElementTypeString()));
    return worker.orElseGet(() -> (dao, networkElement) -> {
      String currentVersion = snmpCtrl.getActiveFSP_SWVersion();
      String previousVersion = snmpCtrl.getInactiveFSP_SWVersion();
      dao.updateVersionsForNe(neImpl.getID(), previousVersion, currentVersion);
    });
  }

  private Optional<NetworkElementVersionsUpdateProcess> getMatchingBean(@NotNull final String qualifierName) {
    return versionUpdateProcesses.findService(qualifierName);
  }

  private String prepareQualifier(String qualifierName) {
    return qualifierName;
  }

  /**
   *
   * Change priority of devices
   *
   * @param request : List of priorities and device names with IPs.
   */
  @Path(MDRestPath.SWDL.IMPORT_PRIORITY)
  @Consumes(MediaType.APPLICATION_JSON)
  @Authorization(permissions = PermissionAction.UpdNsw)
  @POST
  public void updatePrioritiesForNE(final NEUpdateCsvDto request) {
    int neId = dao.updatePriority(request);
    sendSWUpgradeGuiEvent(neId);
  }

  @Path(MDRestPath.SWDL.IMPORT_PRIORITY  + "/{selection}")
  @Consumes(MediaType.APPLICATION_JSON)
  @Authorization(permissions = PermissionAction.UpdNsw)
  @POST
  public void updatePriorities(final List<NEUpdateCsvDto> request, @PathParam("selection") String selection) {
    List<Integer> neIds = dao.updatePriority(request, selection);
    neIds.forEach(this::sendSWUpgradeGuiEvent);
  }

  private void sendSWUpgradeGuiEvent(long neID)  {
    try {
      NetworkElement networkElement = factory.getNetworkElementById(neID);
      SWUpgradeAbstract swUpgradeAbstract = factory.getSWUpgrade(networkElement);
      swUpgradeAbstract.sendUpgradeStateChangeEvent();
    } catch (SWUpgradeException e) {
      log.error("SWUpgradeAbstract for neID: {} not found.", neID);
    }
  }

  /**
   * Bulk change comments for software image files.
   * Allows to type https://localhost:8443/advabase/swdl/files
   *
   * @param files : Modified packages list.
   */
  @PUT
  @Path(MDRestPath.SWDL.FILES)
  @MDTransactional()
  public void changeSwFileComments(List<SwImageFileDTO> files) {
    for (SwImageFileDTO file : files) {
      //so far all we care about is comment
      MDPersistenceHelper.getObjectById(NEMISWFileDBImpl.class, file.getId()).setComment(file.getComment());
    }
  }

  /**
   * Gets all software image files stored on ftp/sftp server.
   *
   * @return : collection of software image DTOs
   * <p>
   * Allows to type https://localhost:8443/advabase/swdl/file
   */
  @GET
  @Path(MDRestPath.SWDL.FILES)
  public Collection<SwImageFileDTO> getAvailableVersions() {

    final FileServerState serverState = getServerState();

    if (serverState.isConfigured) {
      if (!serverState.isAlive) {
        log.warn("SWDL server is registered as not responding. It'request state will be refreshed within 5 min");
        return new ArrayList<>();
      }
    } else {
      return new ArrayList<>();
    }

    Collection<SwImageFileDTO> availableVersions = getAvailableVersions(this.getSoftwareDownloadSecurityMode());
    ftpCache.setAvailableVersions(availableVersions);
    return availableVersions;
  }

  /**
   * Starts multiple software upgrades.
   *
   * @param swUpgradeDTO : upgrade requests collection
   * @throws MDOperationFailedException * Allows to type https://localhost:8443/advabase/swdl/upgrades
   */
  @POST
  @Path(MDRestPath.SWDL.UPGRADES)
  @Authorization(permissions = PermissionAction.UpdNsw)
  public void doUpgradesStart(SwUpgradeDTO swUpgradeDTO) throws MDOperationFailedException {
    if(swUpgradeDTO.getRestriction() != null && swUpgradeDTO.getNeIDs() == null) {
      swUpgradeDTO.setNeIDs(swUpgradePageHdlr.getNeIds(swUpgradeDTO));
      swUpgradeDTO.getNeIDs().removeAll(swUpgradeDTO.getUnsupportedNeIDs());
    }
    for (SwUpgradeRequest request : swUpgradeDTO.generateOldRequests()) {
      try {
        if (request.getTimeUpgradeStart() < System.currentTimeMillis() || !request.isScheduled()) {
          request.setTimeUpgradeStart(System.currentTimeMillis() + 1000);
        } else {
          request.setScheduled(true);
        }
        upgrader.addUpgrade(request.getNeID(), request);
      } catch (SWUpgradeException e) {
        throw new MDOperationFailedException(e);
      }
    }
  }

  @POST
  @Path(MDRestPath.SWDL.FMW.UPDATE)
  @Authorization(permissions = PermissionAction.UpdNsw)
  public void doFMWStart(FmwUpdateDTO fmwUpdateDTO) throws MDOperationFailedException {
    try {
      if(fmwUpdateDTO.getRestriction() != null && fmwUpdateDTO.getNeIDs() == null) {
        fmwUpdateDTO.setNeIDs(swUpgradePageHdlr.getNeIds(fmwUpdateDTO));
        fmwUpdateDTO.getNeIDs().removeAll(fmwUpdateDTO.getUnsupportedNeIDs());
      }
      if (fmwUpdateDTO.getTimeUpgradeStart() < System.currentTimeMillis() || !fmwUpdateDTO.isScheduled()) {
        fmwUpdateDTO.setTimeUpgradeStart(System.currentTimeMillis() + 1000);
      } else {
        fmwUpdateDTO.setScheduled(true);
      }
      upgrader.addFmwUpdate(fmwUpdateDTO);
    } catch (SWUpgradeException e) {
      throw new MDOperationFailedException(e);
    }
  }

  @POST
  @Path(MDRestPath.SWDL.CRYPTO.DOWNLOAD)
  @Authorization(permissions = PermissionAction.UpdNsw)
  public void doCryptoFMWStart(SwUpgradeDTO swUpgradeDTO) throws MDOperationFailedException {
    if(swUpgradeDTO.getRestriction() != null && swUpgradeDTO.getNeIDs() == null) {
      swUpgradeDTO.setNeIDs(swUpgradePageHdlr.getNeIds(swUpgradeDTO));
      swUpgradeDTO.getNeIDs().removeAll(swUpgradeDTO.getUnsupportedNeIDs());
    }
    for (SwUpgradeRequest request : swUpgradeDTO.generateOldRequests()) {
      try {
        if (request.getTimeUpgradeStart() < System.currentTimeMillis() || !request.isScheduled()) {
          request.setTimeUpgradeStart(System.currentTimeMillis() + 1000);
        } else {
          request.setScheduled(true);
        }
        upgrader.addCryptoFmwDownload(request.getNeID(), request);
      } catch (SWUpgradeException e) {
        throw new MDOperationFailedException(e);
      }
    }
  }

  /**
   * Stop multiple software upgrades.
   *
   * @param swUpgradeDTO : network elements ids
   * @throws MDOperationFailedException Allows to type https://localhost:8443/advabase/swdl/upgrades
   */
  @PUT
  @Path(MDRestPath.SWDL.UPGRADES)
  @Authorization(permissions = PermissionAction.UpdNsw)
  public void doUpgradesStop(SwUpgradeDTO swUpgradeDTO) throws MDOperationFailedException {
    if(swUpgradeDTO.getRestriction() != null && swUpgradeDTO.getNeIDs() == null) {
      swUpgradeDTO.setNeIDs(swUpgradePageHdlr.getNeIds(swUpgradeDTO));
      swUpgradeDTO.getNeIDs().removeAll(swUpgradeDTO.getUnsupportedNeIDs());
    }
    for (Integer neId : swUpgradeDTO.getNeIDs()) {
      try {
        upgrader.stopUpgrade(neId);
      } catch (SWUpgradeException e) {
        throw new MDOperationFailedException(e.getStringStackTrace());
      }
    }
  }

  /**
   * Validates correctness of software upgrade requests.
   *
   * @param swUpgradeDTO : collection of upgrade requests for validation
   * @return validation result collection
   * @throws MDOperationFailedException Allows to type https://localhost:8443/advabase/swdl/validate
   */
  @POST
  @Path(MDRestPath.SWDL.VALIDATE)
  @Authorization(permissions = PermissionAction.UpdNsw)
  public Collection<SwRequestValidationResult> validateUpgrades(SwUpgradeDTO swUpgradeDTO) throws MDOperationFailedException {
    Collection<SwRequestValidationResult> results = new HashSet<>();
    if(swUpgradeDTO.getRestriction() != null && swUpgradeDTO.getNeIDs() == null) {
      swUpgradeDTO.setNeIDs(swUpgradePageHdlr.getNeIds(swUpgradeDTO));
      swUpgradeDTO.getNeIDs().removeAll(swUpgradeDTO.getUnsupportedNeIDs());
    }
    try {
      for (SwUpgradeRequest request : swUpgradeDTO.generateOldRequests()) {
        results.add(validateUpgrade(request));
      }
    } catch (Exception e) {
      throw new MDOperationFailedException(e.getMessage(), e);
    }

    return results;
  }

  /**
   * Validates correctness of software upgrade requests.
   *
   * @param fmwUpdateDTO: collection of fmw upgrade requests for validation
   * @return validation result collection
   * @throws MDOperationFailedException Allows to type https://localhost:8443/advabase/swdl/validate
   */
  @POST
  @Path(MDRestPath.SWDL.VALIDATE_FMW)
  @Authorization(permissions = PermissionAction.UpdNsw)
  public Collection<SwRequestValidationResult> validateUpgrades(FmwUpdateDTO fmwUpdateDTO) throws MDOperationFailedException {
    Collection<SwRequestValidationResult> results = new HashSet<>();
    if(fmwUpdateDTO.getRestriction() != null && fmwUpdateDTO.getNeIDs() == null) {
      fmwUpdateDTO.setNeIDs(swUpgradePageHdlr.getNeIds(fmwUpdateDTO));
      fmwUpdateDTO.getNeIDs().removeAll(fmwUpdateDTO.getUnsupportedNeIDs());
    }
    try {
      for (int neId : fmwUpdateDTO.getNeIDs()) {
        results.add(validateFmwRequest(neId, fmwUpdateDTO.getUpgradeType()));
      }
    } catch (Exception e) {
      throw new MDOperationFailedException(e.getMessage(), e);
    }

    return results;
  }


  @POST
  @Path(MDRestPath.SWDL.VALIDATE_MULTISELECTION)
  @Authorization(permissions = PermissionAction.UpdNsw)
  public SwRequestValidationResult prevalidateUpgrades(SwUpgradeDTO swUpgradeDTO) throws MDOperationFailedException {
    try {
      return validateUpgradeRequests(swUpgradeDTO);
    } catch (Exception e) {
      throw new MDOperationFailedException(e.getMessage(), e);
    }
  }

  @POST
  @Path(MDRestPath.SWDL.VALIDATE_FMW_MULTISELECTION)
  @Authorization(permissions = PermissionAction.UpdNsw)
  public SwRequestValidationResult prevalidateFmwRequest(FmwUpdateDTO fmwUpdateDTO) throws MDOperationFailedException {
    try {
      if(fmwUpdateDTO.getRestriction() != null && fmwUpdateDTO.getNeIDs() == null) {
        fmwUpdateDTO.setNeIDs(swUpgradePageHdlr.getNeIds(fmwUpdateDTO));
        fmwUpdateDTO.getNeIDs().removeAll(fmwUpdateDTO.getUnsupportedNeIDs());
      }
      return fmwRequestValidator.validateFmwRequest(fmwUpdateDTO);
    } catch (Exception e) {
      throw new MDOperationFailedException(e.getMessage(), e);
    }
  }

  @POST
  @Path(MDRestPath.SWDL.VERSIONS_FOR_NE)
  @Authorization(permissions = PermissionAction.UpdNsw)
  public Collection<SwImageFileDTO> getImagesForNe(int neID) {
    return getImagesForNe(neID, true);
  }

  private Collection<SwImageFileDTO> getImagesForNe(int neID, boolean useCache) {
    log.debug("getAvailableVersionsForNE() started. id={}", neID);
    final Collection<SwImageFileDTO> swFileDOs = getAvailableVersions(useCache);
    final List<SwImageFileDTO> images = getAvailableVersionForNE(neID, swFileDOs);
    images.sort(Comparator.comparing(SwImageFileDTO::getFilename).reversed());
    if (log.isDebugEnabled())
      log.debug("getAvailableVersionsForNE() finished. " + images);
    return images;
  }

  @Path(MDRestPath.SWDL.SECURITY_MODE)
  @GET
  @MDPersistenceContext
  public SecurityMode getSoftwareDownloadSecurityMode() {
    return SecurityMode.values()[((MDPersistenceHelper.getObjectById(PreferencesDBImpl.class, neCommCtrl.getPreferencesObjID())).getSwSecurityMode())];
  }

  @Path(MDRestPath.SWDL.SECURITY_MODE)
  @POST
  @MDTransactional()
  public void setSoftwareDownloadSecurityMode(SecurityMode mode) {
    (MDPersistenceHelper.getObjectById(PreferencesDBImpl.class, neCommCtrl.getPreferencesObjID())).setSwSecurityMode(mode.ordinal());
  }

  @Path(MDRestPath.SWDL.UPGRADE_CONFIG)
  @POST
  public void setNeSwUpgradeConfig(NeSwUpgradeConfigDTO config) {
    // Synchronization has to cover business logic and the whole transaction.
    synchronized (neSwUpgradeConfigChangeLock) {
      numberOfParallelDownloads = config.getMaxParalellUploads();
      setNeSwUpgradeConfigTransact(config);
    }
  }

  @Path(MDRestPath.SWDL.UPGRADE_CONFIG)
  @GET
  public NeSwUpgradeConfigDTO getConfigWithMaskedPassword() {
    NeSwUpgradeConfigDTO config = getNeSwUpgradeConfig();
    FTPServerPasswordMasker ftpServerPasswordMasker = new FTPServerPasswordMasker();
    config.setSecureServerProperties(ftpServerPasswordMasker.maskPasswordInSettings(config.getSecureServerProperties()));
    config.setUnsecureServerProperties(ftpServerPasswordMasker.maskPasswordInSettings(config.getUnsecureServerProperties()));
    return config;
  }

  @MDPersistenceContext
  public NeSwUpgradeConfigDTO getNeSwUpgradeConfig() {
    Map<FTPServerFuncEnum, FTPServerSettings> ftpServerSettings = ftpPreferencesDAO.getFTPSettingsAsMap(new FTPServerFuncEnum[]{FTPServerFuncEnum.SW_UPGRADE, FTPServerFuncEnum.SSW_UPGRADE});
    PreferencesDBImpl prefDBImpl = MDPersistenceHelper.getObjectById(PreferencesDBImpl.class, neCommCtrl.getPreferencesObjID());

    NeSwUpgradeConfigDTO res = new NeSwUpgradeConfigDTO();
    if (ftpServerSettings != null) {
      res.setUnsecureServerProperties(
              ftpServerSettings.get(FTPServerFuncEnum.SW_UPGRADE) == null ?
                      new FTPServerSettings(FTPServerFuncEnum.SW_UPGRADE, "", "", "", new byte[]{}, ProtocolType.FTP, FTPConnectionType.PASSIVE) : ftpServerSettings.get(FTPServerFuncEnum.SW_UPGRADE));
      res.setSecureServerProperties(
              ftpServerSettings.get(FTPServerFuncEnum.SSW_UPGRADE) == null ?
                      new FTPServerSettings(FTPServerFuncEnum.SSW_UPGRADE, "", "", "", new byte[]{}, ProtocolType.SCP, FTPConnectionType.PASSIVE) : ftpServerSettings.get(FTPServerFuncEnum.SSW_UPGRADE));
    }
    res.setMaxParalellUploads(prefDBImpl.getSwNumberOfParallelDownloads());
    SecurityMode securityMode = SecurityMode.values()[prefDBImpl.getSwSecurityMode()];
    res.setSecurityType(securityMode);

    SWUpgradePropertiesDBImpl upgradePropertiesDB;
    try {
      upgradePropertiesDB = dao.getSWUpgradeProperties().orElse(new SWUpgradePropertiesDBImpl());
    } catch (SWUpgradeException e) {
      log.warn("Using default cn_swupgrade_properties.", e);
      upgradePropertiesDB = new SWUpgradePropertiesDBImpl();
    }
    res.setNumberOfRetries(upgradePropertiesDB.getMaxRetryCount());
    res.setMinIntervalBtwRetries(upgradePropertiesDB.getMinInterval());

    return res;
  }

  @Path(MDRestPath.SWDL.NO_OF_PARALLEL_DLS)
  @GET
  public int getSWNumberOfParallelDownloads() {
    return numberOfParallelDownloads;
  }

  private synchronized Collection<SwImageFileDTO> getAvailableVersions(boolean useCache){

    if (!useCache || ftpCache.isStale()) {
      Collection<SwImageFileDTO> availableVersions = getAvailableVersions();
      ftpCache.setAvailableVersions(availableVersions);
      return availableVersions;
    } else {
      return ftpCache.getAvailableVersions();
    }
  }

  @MDTransactional
  private void setNeSwUpgradeConfigTransact(NeSwUpgradeConfigDTO config) {
    FTPSettingsUtils ftpSettingsUtils = new FTPSettingsUtils(ftpPreferencesDAO);
    ftpSettingsUtils.updateFTPSettings(config.getUnsecureServerProperties(), config.getSecureServerProperties(), FTPServerFuncEnum.SW_UPGRADE, FTPServerFuncEnum.SSW_UPGRADE);
    PreferencesDBImpl prefDBImpl = MDPersistenceHelper.getObjectById(PreferencesDBImpl.class, neCommCtrl.getPreferencesObjID());
    prefDBImpl.setSwNumberOfParallelDownloads(config.getMaxParalellUploads());
    prefDBImpl.setSwSecurityMode(config.getSecurityType().ordinal());
    Optional<SWUpgradePropertiesDBImpl> swUpgradeProperties = Optional.empty();
    try {
      swUpgradeProperties = dao.getSWUpgradeProperties();
    } catch (SWUpgradeException e) {
      log.trace(e);
      log.info("DELETING setNeSwUpgradeConfig");
      dao.deleteSWUpgradeProperties();
    }
    if (swUpgradeProperties.isPresent()) {
      log.info("UPDATING setNeSwUpgradeConfig");
      SWUpgradePropertiesDBImpl upgradePropertiesDB = swUpgradeProperties.get();
      upgradePropertiesDB.setMaxRetryCount(config.getNumberOfRetries());
      upgradePropertiesDB.setMinInterval(config.getMinIntervalBtwRetries());
    } else {
      log.info("CREATING setNeSwUpgradeConfig");
      dao.createSWUpgradeProperties(config.getNumberOfRetries(), config.getMinIntervalBtwRetries());
    }
  }

  private SwRequestValidationResult validateUpgrade(SwUpgradeRequest upgradeRequest) {
    try {
      return factory.validateUpgradeRequest(upgradeRequest);
    } catch (SWUpgradeException e) {
      return new SwRequestValidationResult(upgradeRequest.getNeID(), e.getMessage(), SWUpgradeValidationResult.ERROR);
    }
  }

  private SwRequestValidationResult validateUpgradeRequests(SwUpgradeDTO swUpgradeDTO) throws MDOperationFailedException {
    int neType = -1;
    String currentSWVersion = null;
    int currentNeId = -1;

    boolean neTypeMatches = true;
    boolean swVerMatches = true;

    Collection<Integer> neIds = swUpgradePageHdlr.getNeIds(swUpgradeDTO);

    try {
      for (Integer neId : neIds) {
        final NetworkElement ne = factory.getNetworkElementById(neId);
        SWUpgradeNEDBImpl swUpgradeNEDB = ne.neDBImpl().getSwUpgradeNeInfo();
        if(currentSWVersion == null) {
          currentSWVersion = swUpgradeNEDB.getCurrentSWVersion();
        }
        if(neType != -1 &&
            neType != ne.getNetworkElementType() && !SWUpgradeNEFamilyUtils.isSameNeFamily(neType, ne.getNetworkElementType())) { // FNMD-31680
          neTypeMatches = false;
        } else if (!(currentSWVersion != null && currentSWVersion.equals(swUpgradeNEDB.getCurrentSWVersion()))){
          swVerMatches = false;
        } else {
          neType = ne.getNetworkElementType();
          currentSWVersion = swUpgradeNEDB.getCurrentSWVersion();
          currentNeId = neId;
        }
      }
    } catch (Exception e) {
      throw new MDOperationFailedException(e.getMessage(), e);
    }
    if (!neTypeMatches) {
      return new SwRequestValidationResult(currentNeId, SWUpgradeProperties.NE_TYPE_MISMATCH_ERROR_MSG, SWUpgradeValidationResult.SWUPGRADE_ERROR_NE_TYPE_MISMATCH);
    } else if (!swVerMatches) {
      return new SwRequestValidationResult(currentNeId, SWUpgradeProperties.SW_VER_MISMATCH_ERROR_MSG, SWUpgradeValidationResult.SWUPGRADE_WARNING_SW_MISMATCH);
    }
    return new SwRequestValidationResult(currentNeId, SWUpgradeProperties.VALIDATION_SUCCEEDED, SWUpgradeValidationResult.OK);
  }

  private SwRequestValidationResult validateFmwRequest(int neId, SwUpgradeType upgradeType) {
    try {
      final NetworkElement ne = factory.getNetworkElementById(neId);
      if(!NEUtils.isFSP3000_R7Device(ne.getNetworkElementType())) {
        log.error("[validateFmwRequest] {} : device type is not FSP3000R7", SWUpgradeProperties.OPERATION_NOT_SUPPORTED);
        return new SwRequestValidationResult(neId, ne.getName(), SWUpgradeProperties.OPERATION_NOT_SUPPORTED, SWUpgradeValidationResult.ERROR);
      } else {
        SWUpgradeNEDBImpl swUpgradeNEDB = ne.neDBImpl().getSwUpgradeNeInfo();
        if((upgradeType.equals(SwUpgradeType.FMW_TRANSFER) || upgradeType.equals(SwUpgradeType.FMW_ACTIVATE))
            && F7SoftwareVersionComparison.compare(swUpgradeNEDB.getCurrentSWVersion(), "13.1.1").equals(F7SoftwareVersionComparison.RESULT.SECOND_HIGHER)) {
          log.error("[validateFmwRequest] {} : software version lower than 13.1.1", SWUpgradeProperties.OPERATION_NOT_SUPPORTED);
          return new SwRequestValidationResult(neId, ne.getName(), SWUpgradeProperties.OPERATION_NOT_SUPPORTED, SWUpgradeValidationResult.ERROR);
        }
        if (!swUpgradeNEDB.getUpgradeState().is(SWUpgradeStateEnum.FINISHED, SWUpgradeStateEnum.READYFORACTIVATION, SWUpgradeStateEnum.IDLE)) {
          if (!(swUpgradeNEDB.getUpgradeState().is(SWUpgradeStateEnum.STOPPED, SWUpgradeStateEnum.FAILED)
                  && swUpgradeNEDB.getPreviousOperation().is(SWUpgradeTypeEnum.FMW_UPDATE, SWUpgradeTypeEnum.FMW_TRANSFER, SWUpgradeTypeEnum.FMW_ACTIVATE, SWUpgradeTypeEnum.NONE, SWUpgradeTypeEnum.CRYPTO_FMW_DOWNLOAD))) {
            log.error("[validateFmwRequest] {} : ", SWUpgradeProperties.OPERATION_NOT_SUPPORTED);
            return new SwRequestValidationResult(neId, ne.getName(), SWUpgradeProperties.OPERATION_NOT_SUPPORTED, SWUpgradeValidationResult.ERROR);
          }
        }
      }
      return new SwRequestValidationResult(neId, ne.getName(), SWUpgradeProperties.VALIDATION_SUCCEEDED, SWUpgradeValidationResult.OK);
    } catch (SWUpgradeException e) {
      log.error(e);
    }
    return new SwRequestValidationResult(neId, SWUpgradeProperties.OPERATION_NOT_SUPPORTED, SWUpgradeValidationResult.ERROR);
  }

  private FileServerState getServerState() {
    NeSwUpgradeConfigDTO upgradeConfig = this.getNeSwUpgradeConfig();
    SecurityMode securityMode = this.getSoftwareDownloadSecurityMode();
    FTPServerSettings secureServerProperties = upgradeConfig.getSecureServerProperties();
    FTPServerSettings unsecureServerProperties = upgradeConfig.getUnsecureServerProperties();

    FileServerState serverState = new FileServerState(true, true);
    if (securityMode == SecurityMode.MIXED) {
      serverState = getServerState(secureServerProperties);
      if (!serverState.isConfigured) {
        serverState = getServerState(unsecureServerProperties);
      }
    } else if (securityMode == SecurityMode.UNSECURE) {
      serverState = getServerState(unsecureServerProperties);
    } else if (securityMode == SecurityMode.SECURE) {
      serverState = getServerState(secureServerProperties);
    }

    return serverState;
  }

  private FileServerState getServerState(FTPServerSettings unsecureServerProperties) {
    boolean serverConfigured = isServerConfigured(unsecureServerProperties);
    boolean serverAlive = true;
    if (serverConfigured) {
      serverAlive = stateRepository.isAlive(unsecureServerProperties);
    }
    return new FileServerState(serverConfigured, serverAlive);
  }

  private static class FileServerState {
    private boolean isConfigured;
    private boolean isAlive;

    FileServerState(boolean serverConfigured, boolean serverAlive) {
      this.isConfigured = serverConfigured;
      this.isAlive = serverAlive;
    }

    public boolean isConfigured() {
      return isConfigured;
    }

    public void setIsConfigured(boolean isConfigured) {
      this.isConfigured = isConfigured;
    }

    public boolean isAlive() {
      return isAlive;
    }

    public void setIsAlive(boolean isAlive) {
      this.isAlive = isAlive;
    }
  }

  private Collection<SwImageFileDTO> getAvailableVersions(SecurityMode securityMode) {
    if (log.isInfoEnabled()) log.info("getAvailableVersions() started.");
    NeSwUpgradeConfigDTO cfg = this.getNeSwUpgradeConfig();
    Collection<SwImageFileDTO> ftpSwidlFiles = Lists.newArrayList();
    Collection<SwImageFileDTO> sftpSwidlFiles = Lists.newArrayList();

    if (securityMode == SecurityMode.MIXED) {
      ftpSwidlFiles = SWUpgradeFileHelper.getAvailable_SWVersions(
              cfg.getUnsecureServerProperties(),
              TransportProtocol.FTP,
              messageManager);
      sftpSwidlFiles = SWUpgradeFileHelper.getAvailable_SWVersions(
              cfg.getSecureServerProperties(),
              TransportProtocol.SFTP,
              messageManager);
    } else if (securityMode == SecurityMode.SECURE) {
      sftpSwidlFiles = SWUpgradeFileHelper.getAvailable_SWVersions(
              cfg.getSecureServerProperties(),
              TransportProtocol.SFTP,
              messageManager);
    } else if (securityMode == SecurityMode.UNSECURE) {
      ftpSwidlFiles = SWUpgradeFileHelper.getAvailable_SWVersions(
              cfg.getUnsecureServerProperties(),
              TransportProtocol.FTP,
              messageManager);
    }
    Collection<SwImageFileDTO> mergedFiles = Lists.newArrayList();
    mergedFiles.addAll(ftpSwidlFiles);
    mergedFiles.addAll(sftpSwidlFiles);
    if (log.isInfoEnabled()) log.info("getAvailableVersions() finished.");
    return mergedFiles;
  }

  private boolean isServerConfigured(FTPServerSettings serverProperties) {
    // check if login and node is not empty
    return StringUtils.isNotBlank(serverProperties.getLogin()) && StringUtils.isNotBlank(serverProperties.getNode());
  }

  private List<SwImageFileDTO> getAvailableVersionForNE(int neID, Collection<SwImageFileDTO> files) {
    List<SwImageFileDTO> dtos = new ArrayList<>();
    final SWServerPropertiesSelector selector = getSelector(neID);

    if (selector == null) {
      return dtos;
    }

    final ServerType serverType = selector.isSecure() ? ServerType.SFTP : ServerType.FTP;

    boolean isDownloadModeSupported = isDownloadModeSupported(neID);
    for (SwImageFileDTO file : files) {
      if (isIncludeFullFile(file, isDownloadModeSupported)
          && file.getSrvType() == serverType
          && SWTypeConditionHelper.isSWFileForNE(file.getFilename(), selector.getNetworkElement())) {
        dtos.add(file);
      }
    }

    return dtos;
  }

  private boolean isIncludeFullFile(SwImageFileDTO file, boolean isDownloadModeSupported) {
    return !isDownloadModeSupported || !file.getFilename().contains("_FULL");
  }

  private boolean isDownloadModeSupported(int neID) {
    try {
      String version = neHdlr.getNetworkElement(neID).getCachedCurrentSWVersion();
      return new F7FullOrRequiredDownloadModeConfigurator().isDownloadModeSupported(version);
    } catch (NoSuchNetworkElementException noNEException) {
      log.error("Failed to find NE with id:" + neID + " for SW update search.", noNEException);
    }
    return false;
  }

  private SWServerPropertiesSelector getSelector(int neID) {
    SWServerPropertiesSelector selector = null;
    try {
      selector = ServerPropertiesSelector.buildSWServerPropertiesSelector(getNeSwUpgradeConfig(),
              neHdlr.getNetworkElement(neID));
    } catch (RepositoryManagerException e) {
      log.warn("Exception occurred during building selector.. {}", e.getMessage());
    } catch (NoSuchMDObjectException e) {
      log.warn("Exception occurred during building selector, probably network element does not exist. {}", e.getMessage());
    }
    return selector;
  }

  public boolean checkUpgrades(String swUpgradeState) {
    boolean anyUpgradeRunning = false;
    switch (swUpgradeState) {
      case "running":
        anyUpgradeRunning = isAnyUpgradeRunning();
        break;
      case "pending":
        anyUpgradeRunning = isAnyUpgradePending();
        break;
      default:
        log.error("checkUpgrades: wrong parameter: {}", ()-> SecurityTools.replaceCRLFs(swUpgradeState));
    }
    return anyUpgradeRunning;
  }
}
