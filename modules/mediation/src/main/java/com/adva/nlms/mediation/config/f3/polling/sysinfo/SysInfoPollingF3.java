/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: twitting
 */

package com.adva.nlms.mediation.config.f3.polling.sysinfo;

import com.adva.nlms.common.event.TrapParameterID;
import com.adva.nlms.common.snmp.MIB;
import com.adva.nlms.common.snmp.MIBFSP150CM;
import com.adva.nlms.mediation.common.serviceProvisioning.NetworkElementSPProperties;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.NetworkElementDBImpl;
import com.adva.nlms.mediation.config.NetworkElementPersistenceHelper;
import com.adva.nlms.mediation.config.f3.neComm.discovery.PeerSystemInfoScan;
import com.adva.nlms.mediation.config.f3.neComm.discovery.SystemInfoScan;
import com.adva.nlms.mediation.config.polling.PollingNESystemProperties;
import com.adva.nlms.mediation.config.polling.sysinfo.SysInfoPollingDefault;
import com.adva.nlms.mediation.ne_comm.snmp.api.LowerUpperClass;
import com.adva.nlms.mediation.ne_comm.SNMPCtrl;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPAdapter;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPCommFailure;
import com.adva.nlms.mediation.ne_comm.snmpscan.scanplan.manager.ScanPlanResultSet;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class SysInfoPollingF3 extends SysInfoPollingDefault {

  private final static Logger LOG = LoggerFactory.getLogger(SysInfoPollingF3.class);

  public SysInfoPollingF3 (String sysInfoTrapIdString) {
    super(sysInfoTrapIdString);
  }

  public SysInfoPollingF3 () {
    super("fsp150NeAttributeValueChange");
  }

  @Override
  public void pollSysInfo(NetworkElement ne) {
    LOG.info("[ SysInfoPollingF3.{}.pollSysInfo()", ne);

    try {
      final PollingNESystemProperties pollingNESystemProperties = getPollingNESystemProperties(ne);
      checkSystemPropertiesAndGenerateSrvTrap(ne, pollingNESystemProperties);
    } catch (SNMPCommFailure snmpCommFailure){
      LOG.warn("* SysInfoPollingF3.pollSysInfo() ignored SNMPCommFailure: {} *", snmpCommFailure.getErrMessage());
    } finally {
      LOG.info("] SysInfoPollingF3.{}.pollSysInfo()", ne);
    }
  }

  protected PollingNESystemProperties getPollingNESystemProperties(NetworkElement ne) throws SNMPCommFailure{
    final SNMPCtrl snmpCtrl = ne.getSNMPCtrl();
    SNMPAdapter snmpAdapter = snmpCtrl.getSnmpAdapter();
    ScanPlanResultSet systemInfoResultSet;
    final String snmpSysName;
    final String snmpSysLocation;
    final String snmpSysContact;
    if(ne.isPeer()) {
      LowerUpperClass lu = LowerUpperClass.createLUForAllIndicesOfTable(ne.getNeIndex());
      systemInfoResultSet = new PeerSystemInfoScan(snmpAdapter).scan(lu);
      Map<String, Object> sysInfoList = systemInfoResultSet.getValues().iterator().next().getDataMap();
      snmpSysName = sysInfoList.get(MIBFSP150CM.Entity.NetworkElementTable.OID_NAME).toString();
      snmpSysLocation = sysInfoList.get(MIBFSP150CM.Entity.NetworkElementTable.OID_LOCATION).toString();
      snmpSysContact = sysInfoList.get(MIBFSP150CM.Entity.NetworkElementTable.OID_CONTACT).toString();
    } else {
      systemInfoResultSet = new SystemInfoScan(snmpAdapter).scan(null);
      Map<String, Object> sysInfoList = systemInfoResultSet.getValues().iterator().next().getDataMap();
      snmpSysName = sysInfoList.get(MIB.System.OID_NAME).toString();
      snmpSysLocation = sysInfoList.get(MIB.System.OID_LOCATION).toString();
      snmpSysContact = sysInfoList.get(MIB.System.OID_CONTACT).toString();
    }

    final NetworkElementSPProperties dbSysInfo = ne.getSysInfo();
    final String dbSysName = dbSysInfo.get(NetworkElementSPProperties.VS.SysName);
    final String dbSysContact = dbSysInfo.get(NetworkElementSPProperties.VS.SysContact);
    final String dbSysLocation = dbSysInfo.get(NetworkElementSPProperties.VS.SysLocation);

    final PollingNESystemProperties pollingNESystemProperties  = new PollingNESystemProperties();
    pollingNESystemProperties.setDbSysContact(dbSysContact);
    pollingNESystemProperties.setDbSysInfo(dbSysInfo);
    pollingNESystemProperties.setDbSysLocation(dbSysLocation);
    pollingNESystemProperties.setSnmpSysContact(snmpSysContact);
    pollingNESystemProperties.setSnmpSysLocation(snmpSysLocation);
    pollingNESystemProperties.setSnmpSysName(snmpSysName);
    pollingNESystemProperties.setDbSysName(dbSysName);

    return pollingNESystemProperties;
  }

  private void checkSystemPropertiesAndGenerateSrvTrap(NetworkElement ne,
                                                       final PollingNESystemProperties pollingNESystemProperties)
  {
    final String snmpSysName = pollingNESystemProperties.getSnmpSysName();
    final String snmpSysContact = pollingNESystemProperties.getSnmpSysContact();
    final String snmpSysLocation = pollingNESystemProperties.getSnmpSysLocation();
    final NetworkElementSPProperties dbSysInfo = pollingNESystemProperties.getDbSysInfo();
    final String dbSysContact = pollingNESystemProperties.getDbSysContact();
    final String dbSysLocation = pollingNESystemProperties.getDbSysLocation();
    boolean updateSysInfoInDB = false;

    resyncNEName(ne, snmpSysName, TrapParameterID.sysName);

    if (!snmpSysContact.equals(dbSysContact)) {
      addServerTrap(ne, TrapParameterID.sysContact, snmpSysContact);
      updateSysInfoInDB = true;
    }

    if (!snmpSysLocation.equals(dbSysLocation)){
      addServerTrap(ne, TrapParameterID.sysLocation, snmpSysLocation);
      updateSysInfoInDB = true;
    }

    if (updateSysInfoInDB){
      ne.getPersistenceHelper().updateNetworkElementDBImplEntity(new NetworkElementPersistenceHelper.NeEntityUpdate() {
        @Override
        public void update (NetworkElementDBImpl neDBImpl) {
          neDBImpl.setSysDescr(dbSysInfo.get(NetworkElementSPProperties.VS.SysDescr));
          neDBImpl.setSysContact(snmpSysContact);
          neDBImpl.setSysLocation(snmpSysLocation);
        }
      });
    }
  }
}
