/*
 *  Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 *  Owner: yuliiab
 */

package com.adva.nlms.mediation.cua;

import com.adva.nlms.common.cua.UnsupportedPrivilegeLevelException;
import com.adva.nlms.mediation.ne_comm.configuration.snmp.UserCredentials;

public interface CUAWorker {
    void createUser() throws UserCreationException, UnsupportedPrivilegeLevelException;
    UserCredentials createUserDTO(CreateUserPollingParameters params) throws UserCreationException, UnsupportedPrivilegeLevelException;
}
