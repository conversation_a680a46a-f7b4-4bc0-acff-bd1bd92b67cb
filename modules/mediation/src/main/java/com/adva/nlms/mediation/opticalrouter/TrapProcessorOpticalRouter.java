package com.adva.nlms.mediation.opticalrouter;

import com.adva.nlms.common.AlarmTrapType;
import com.adva.nlms.common.AlarmTypeHandler;
import com.adva.nlms.common.AlarmTypeHandlerImpl;
import com.adva.nlms.common.NetworkAlarmTypeProperty;
import com.adva.nlms.common.event.types.TrapShortName;
import com.adva.nlms.mediation.common.event.WebSocketNotification;
import com.adva.nlms.mediation.ec.event.EcWebSocketNotification;
import com.adva.nlms.mediation.ec.model.EcModel;
import com.adva.nlms.mediation.event.EventDTO;
import com.adva.nlms.mediation.event.EventLogger;
import com.adva.nlms.mediation.evtProc.TrapParameters;
import com.adva.nlms.mediation.evtProc.TrapProcessorAbstract;
import com.adva.nlms.mediation.evtProc.driver.AlarmMapParser;
import com.adva.nlms.opticalrouter.api.polling.out.OpticalRouterEvent;
import com.adva.nlms.opticalrouter.api.polling.out.OpticalRouterNotification;
import org.snmp4j.smi.VariableBinding;

import java.util.List;
import java.util.NoSuchElementException;
import java.util.Optional;

public class TrapProcessorOpticalRouter extends TrapProcessorAbstract {

  AlarmTypeHandler alarmTypeHandler = AlarmTypeHandlerImpl.getInstance();

  @Override
  public void updateEventProperties(TrapParameters trapParameters) {
    setEventDescriptionForEmptyEvents(trapParameters);
  }


  @Override
  protected boolean isNESpecificAlarm(EventDTO event, List<VariableBinding> vbl) {
    return Optional
      .ofNullable(event.getOpticalRouterNotification())
      .map(OpticalRouterNotification::event)
      .map(opticalRouterEvent -> opticalRouterEvent instanceof OpticalRouterEvent.EntityAlarmed)
      .orElse(false);
  }

  @Override
  public void setSpecificParametersForNotification(TrapParameters trapParameters) {
    setEventDescriptionForEmptyEvents(trapParameters);
  }


  @Override
  public boolean isAlarm(EventDTO event, List<VariableBinding> vbl) {
    return isNESpecificAlarm(event, vbl);
  }

  @Override
  public boolean setEventEnterpriseAndTrapId(final EventDTO event, final List<VariableBinding> vbl) {
//    if((event.getWebSocketNotification() == null) && (event.snmpTrapOid != null)) {
//      if(DYING_GASP_TRAP_OID.equals(event.snmpTrapOid)) {
//        AlarmTrapType trapType = getAlarmTrapType(event, "dying-gasp");
//        event.enterprise = EcModel.ecEnterprise;
//        event.setAidString("SYSTEM");
//        event.setTrapID(trapType.getAlarmTypeID());
//        event.severity = trapType.getEventSeverityType(NetworkAlarmTypeProperty.SEVERITY_ASSIGNMENT.WORKING);
//        return true;
//      }
//
//      log.info("unknown snmp trap received from device: {}, trapOid={}", event.sourceNE_IP, event.snmpTrapOid);
//      return false;
//    }

//    if (event.getWebSocketNotification() == null)
//      return false;

//    EcWebSocketNotification notification = event.getWebSocketNotification();
    if (event.getOpticalRouterNotification() == null) {
      return false;
    }
    OpticalRouterNotification notification = event.getOpticalRouterNotification();
    event.addToDebugLog(EventLogger.getDebugLogString(EventLogger.LoggingType.SEQUENCE_NUMBER, notification.get(EcWebSocketNotification.Key.traceId))
    );
    if (isNESpecificAlarm(event, vbl)) {
      AlarmTrapType trapType = null;
      try {
        trapType = getAlarmTrapType(event, notification);
        event.enterprise = EcModel.ecEnterprise;
        event.setTrapID(trapType.getAlarmTypeID());
        return true;
      } catch (Exception e) {
//        EventLogger.logWarnOnce(logSbi, EventLogger.createCacheKey(event.sourceNEType, notification.getDescription()), "EC Notification unknown alarm " + notification.getPdu(), event, null);
      }
    }
    event.enterprise = EcModel.ecEnterprise;
    event.setTrapID(notification.getEventType().equalsIgnoreCase(TrapShortName.TCA.toString()) ? ecTcaTrapId : ecCustomTrapId);
    return true;
  }


  private AlarmTrapType getAlarmTrapType(EventDTO event, EcWebSocketNotification notification) {
    String alarmName = notification.getDescription().replaceAll(" ", "-");
    try {
      return alarmTypeHandler.getTrapDescriptorByTrapName(event.sourceNEType, alarmName);
    } catch (Exception e) { /* ignore */ }
    String shortName = notification.getDnm();
    try {
      return alarmTypeHandler.getTrapDescriptorByTrapShortName(event.sourceNEType, shortName);
    } catch (Exception e) { // ignore and try with short name
    }
    try {
      return alarmTypeHandler.getTrapDescriptorByTrapShortName(event.sourceNEType, alarmName);
    } catch (Exception e) { // ignore and try again with alarmName short
    }
    throw new NoSuchElementException("AlarmTypeHdlr() Trap not found: NE-type=" + event.sourceNEType + ", Name/Acronym=" + alarmName + "/" + shortName);
  }

  private void setEventDescriptionForEmptyEvents(TrapParameters trapParameters) {
    trapParameters.getEvent().setTextIfEmpty(
      trapParameters.getEvent().getTrapParamID().name() + "=" +
        (trapParameters.getEvent().getNewValue() != -1
          ? trapParameters.getEvent().getNewValue()
          : trapParameters.getEvent().getStringNewValue())
    );
  }
}
