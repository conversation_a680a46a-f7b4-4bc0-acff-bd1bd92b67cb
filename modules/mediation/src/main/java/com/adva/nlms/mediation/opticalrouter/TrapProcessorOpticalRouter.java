package com.adva.nlms.mediation.opticalrouter;

import com.adva.nlms.mediation.common.event.WebSocketNotification;
import com.adva.nlms.mediation.ec.event.EcWebSocketNotification;
import com.adva.nlms.mediation.event.EventDTO;
import com.adva.nlms.mediation.evtProc.TrapParameters;
import com.adva.nlms.mediation.evtProc.TrapProcessorAbstract;
import com.adva.nlms.opticalrouter.api.polling.out.OpticalRouterEvent;
import org.snmp4j.smi.VariableBinding;

import java.util.List;
import java.util.Optional;

public class TrapProcessorOpticalRouter extends TrapProcessorAbstract {

  @Override
  public void updateEventProperties(TrapParameters trapParameters) {
    setEventDescriptionForEmptyEvents(trapParameters);
  }


  @Override
  protected boolean isNESpecificAlarm(EventDTO event, List<VariableBinding> vbl) {
    return Optional
      .of(event.getOpticalRouterNotification())
      .map(notification -> notification.event())
      .orElse(null);
  }

  @Override
  public void setSpecificParametersForNotification(TrapParameters trapParameters) {
    setEventDescriptionForEmptyEvents(trapParameters);
  }


  private void setEventDescriptionForEmptyEvents(TrapParameters trapParameters) {
    trapParameters.getEvent().setTextIfEmpty(
      trapParameters.getEvent().getTrapParamID().name() + "=" +
        (trapParameters.getEvent().getNewValue() != -1
          ? trapParameters.getEvent().getNewValue()
          : trapParameters.getEvent().getStringNewValue())
    );
  }
}
