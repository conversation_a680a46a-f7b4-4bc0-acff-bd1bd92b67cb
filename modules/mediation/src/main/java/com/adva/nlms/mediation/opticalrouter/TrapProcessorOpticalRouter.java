package com.adva.nlms.mediation.opticalrouter;

import com.adva.nlms.common.AlarmTrapType;
import com.adva.nlms.common.AlarmTypeHandler;
import com.adva.nlms.common.AlarmTypeHandlerImpl;
import com.adva.nlms.common.NetworkAlarmTypeProperty;
import com.adva.nlms.common.event.types.TrapShortName;
import com.adva.nlms.mediation.common.event.WebSocketNotification;
import com.adva.nlms.mediation.ec.event.EcWebSocketNotification;
import com.adva.nlms.mediation.ec.model.EcModel;
import com.adva.nlms.mediation.event.EventDTO;
import com.adva.nlms.mediation.event.EventLogger;
import com.adva.nlms.mediation.evtProc.TrapParameters;
import com.adva.nlms.mediation.evtProc.TrapProcessorAbstract;
import com.adva.nlms.mediation.evtProc.driver.AlarmMapParser;
import com.adva.nlms.opticalrouter.api.polling.out.OpticalRouterEvent;
import com.adva.nlms.opticalrouter.api.polling.out.OpticalRouterNotification;
import org.snmp4j.smi.VariableBinding;

import java.util.List;
import java.util.NoSuchElementException;
import java.util.Optional;

public class TrapProcessorOpticalRouter extends TrapProcessorAbstract {

  AlarmTypeHandler alarmTypeHandler = AlarmTypeHandlerImpl.getInstance();

  @Override
  public void updateEventProperties(TrapParameters trapParameters) {
    setEventDescriptionForEmptyEvents(trapParameters);
  }


  @Override
  protected boolean isNESpecificAlarm(EventDTO event, List<VariableBinding> vbl) {
    return Optional
      .ofNullable(event.getOpticalRouterNotification())
      .map(OpticalRouterNotification::event)
      .map(opticalRouterEvent -> opticalRouterEvent instanceof OpticalRouterEvent.EntityAlarmed)
      .orElse(false);
  }

  @Override
  public void setSpecificParametersForNotification(TrapParameters trapParameters) {
    setEventDescriptionForEmptyEvents(trapParameters);
  }


  @Override
  public boolean isAlarm(EventDTO event, List<VariableBinding> vbl) {
    return isNESpecificAlarm(event, vbl);
  }

  @Override
  public boolean setEventEnterpriseAndTrapId(final EventDTO event, final List<VariableBinding> vbl) {
//    if((event.getWebSocketNotification() == null) && (event.snmpTrapOid != null)) {
//      if(DYING_GASP_TRAP_OID.equals(event.snmpTrapOid)) {
//        AlarmTrapType trapType = getAlarmTrapType(event, "dying-gasp");
//        event.enterprise = EcModel.ecEnterprise;
//        event.setAidString("SYSTEM");
//        event.setTrapID(trapType.getAlarmTypeID());
//        event.severity = trapType.getEventSeverityType(NetworkAlarmTypeProperty.SEVERITY_ASSIGNMENT.WORKING);
//        return true;
//      }
//
//      log.info("unknown snmp trap received from device: {}, trapOid={}", event.sourceNE_IP, event.snmpTrapOid);
//      return false;
//    }

//    if (event.getWebSocketNotification() == null)
//      return false;

//    EcWebSocketNotification notification = event.getWebSocketNotification();
    if (event.getOpticalRouterNotification() == null) {
      return false;
    }
    OpticalRouterNotification notification = event.getOpticalRouterNotification();
    event.addToDebugLog(EventLogger.getDebugLogString(EventLogger.LoggingType.SEQUENCE_NUMBER, notification.get(EcWebSocketNotification.Key.traceId))
    );
    if (isNESpecificAlarm(event, vbl)) {
      AlarmTrapType trapType = null;
      try {
        trapType = getAlarmTrapType(event, notification);
        event.enterprise = EcModel.ecEnterprise;
        event.setTrapID(trapType.getAlarmTypeID());
        return true;
      } catch (Exception e) {
//        EventLogger.logWarnOnce(logSbi, EventLogger.createCacheKey(event.sourceNEType, notification.getDescription()), "EC Notification unknown alarm " + notification.getPdu(), event, null);
      }
    }
    event.enterprise = EcModel.ecEnterprise;
    event.setTrapID(notification.getEventType().equalsIgnoreCase(TrapShortName.TCA.toString()) ? ecTcaTrapId : ecCustomTrapId);
    return true;
  }


  /**
   * Retrieves the AlarmTrapType for the given event and notification.
   * Tries multiple lookup strategies in order of preference:
   * 1. By trap name (using normalized alarm description)
   * 2. By trap short name (using DNM from notification)
   * 3. By trap short name (using normalized alarm description as fallback)
   *
   * @param event the event DTO containing source NE type
   * @param notification the EC WebSocket notification containing alarm details
   * @return the matching AlarmTrapType
   * @throws NoSuchElementException if no matching trap type is found
   */
  private AlarmTrapType getAlarmTrapType(EventDTO event, OpticalRouterNotification opticalRouterNotification) {
    final int neType = event.sourceNEType;
    final String normalizedAlarmName = normalizeAlarmName(notification.get());
    final String shortName = notification.getDnm();

    // Try different lookup strategies in order of preference
    return tryAlarmTrapLookupStrategies(neType, normalizedAlarmName, shortName);
  }

  /**
   * Normalizes alarm name by replacing spaces with hyphens.
   * This is required for proper alarm type lookup.
   *
   * @param description the raw alarm description
   * @return normalized alarm name
   */
  private String normalizeAlarmName(String description) {
    return description != null ? description.replaceAll(" ", "-") : "";
  }

  /**
   * Attempts to find AlarmTrapType using multiple lookup strategies.
   *
   * @param neType the network element type
   * @param normalizedAlarmName the normalized alarm name
   * @param shortName the short name from notification
   * @return the matching AlarmTrapType
   * @throws NoSuchElementException if no matching trap type is found
   */
  private AlarmTrapType tryAlarmTrapLookupStrategies(int neType, String normalizedAlarmName, String shortName) {
    // Strategy 1: Lookup by trap name using normalized alarm description
    AlarmTrapType trapType = tryGetTrapDescriptorByName(neType, normalizedAlarmName);
    if (trapType != null) {
      return trapType;
    }

    // Strategy 2: Lookup by trap short name using DNM
    trapType = tryGetTrapDescriptorByShortName(neType, shortName);
    if (trapType != null) {
      return trapType;
    }

    // Strategy 3: Fallback - lookup by trap short name using normalized alarm name
    trapType = tryGetTrapDescriptorByShortName(neType, normalizedAlarmName);
    if (trapType != null) {
      return trapType;
    }

    // All strategies failed
    throw new NoSuchElementException(
        String.format("AlarmTypeHdlr() Trap not found: NE-type=%d, Name/Acronym=%s/%s",
                     neType, normalizedAlarmName, shortName));
  }

  /**
   * Safely attempts to get trap descriptor by trap name.
   *
   * @param neType the network element type
   * @param trapName the trap name to lookup
   * @return the AlarmTrapType if found, null otherwise
   */
  private AlarmTrapType tryGetTrapDescriptorByName(int neType, String trapName) {
    if (trapName == null || trapName.isEmpty()) {
      return null;
    }

    try {
      return alarmTypeHandler.getTrapDescriptorByTrapName(neType, trapName);
    } catch (Exception e) {
      // Log at debug level for troubleshooting
      // Note: This is expected behavior when trap is not found
      return null;
    }
  }

  /**
   * Safely attempts to get trap descriptor by trap short name.
   *
   * @param neType the network element type
   * @param shortName the short name to lookup
   * @return the AlarmTrapType if found, null otherwise
   */
  private AlarmTrapType tryGetTrapDescriptorByShortName(int neType, String shortName) {
    if (shortName == null || shortName.isEmpty()) {
      return null;
    }

    try {
      return alarmTypeHandler.getTrapDescriptorByTrapShortName(neType, shortName);
    } catch (Exception e) {
      // Log at debug level for troubleshooting
      // Note: This is expected behavior when trap is not found
      return null;
    }
  }

  private void setEventDescriptionForEmptyEvents(TrapParameters trapParameters) {
    trapParameters.getEvent().setTextIfEmpty(
      trapParameters.getEvent().getTrapParamID().name() + "=" +
        (trapParameters.getEvent().getNewValue() != -1
          ? trapParameters.getEvent().getNewValue()
          : trapParameters.getEvent().getStringNewValue())
    );
  }
}
