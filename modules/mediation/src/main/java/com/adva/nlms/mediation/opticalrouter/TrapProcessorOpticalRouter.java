package com.adva.nlms.mediation.opticalrouter;

import com.adva.nlms.common.AlarmTrapType;
import com.adva.nlms.common.AlarmTypeHandler;
import com.adva.nlms.common.AlarmTypeHandlerImpl;
import com.adva.nlms.mediation.ec.model.EcModel;
import com.adva.nlms.mediation.event.EventDTO;
import com.adva.nlms.mediation.event.EventLogger;
import com.adva.nlms.mediation.evtProc.TrapParameters;
import com.adva.nlms.mediation.evtProc.TrapProcessorAbstract;
import com.adva.nlms.opticalrouter.api.polling.out.OpticalRouterEvent;
import com.adva.nlms.opticalrouter.api.polling.out.OpticalRouterNotification;
import org.snmp4j.smi.VariableBinding;

import java.util.List;
import java.util.NoSuchElementException;
import java.util.Optional;

public class TrapProcessorOpticalRouter extends TrapProcessorAbstract {
  public final int ecCustomTrapId = 12000;
  private final AlarmTypeHandler alarmTypeHandler = AlarmTypeHandlerImpl.getInstance();

  @Override
  public void updateEventProperties(TrapParameters trapParameters) {
    setEventDescriptionForEmptyEvents(trapParameters);
  }


  @Override
  public boolean isAlarm(EventDTO event, List<VariableBinding> vbl) {
    return isNESpecificAlarm(event, vbl);
  }

  @Override
  protected boolean isNESpecificAlarm(EventDTO event, List<VariableBinding> vbl) {
    return Optional
      .ofNullable(event.getOpticalRouterNotification())
      .map(OpticalRouterNotification::event)
      .map(opticalRouterEvent -> opticalRouterEvent instanceof OpticalRouterEvent.EntityAlarmed)
      .orElse(true);
  }

  @Override
  public void setSpecificParametersForNotification(TrapParameters trapParameters) {
    setEventDescriptionForEmptyEvents(trapParameters);
  }


  @Override
  public boolean setEventEnterpriseAndTrapId(final EventDTO event, final List<VariableBinding> vbl) {
    if (event.getOpticalRouterNotification() == null) {
      return false;
    }
    OpticalRouterNotification notification = event.getOpticalRouterNotification();
    event.addToDebugLog(EventLogger.getDebugLogString(EventLogger.LoggingType.SEQUENCE_NUMBER, notification.neId().toString())
    );
    if (isNESpecificAlarm(event, vbl)) {
      AlarmTrapType trapType;
      try {
        trapType = getAlarmTrapType(event, notification);
        event.enterprise = EcModel.ecEnterprise; //???
        event.setTrapID(trapType.getAlarmTypeID());
        return true;
      } catch (Exception e) {
        EventLogger.logWarnOnce(logSbi, EventLogger.createCacheKey(event.sourceNEType, notification.entityLabel()), "OpticalRouter Notification unknown alarm " + notification.event(), event, vbl);
      }
    }
    event.enterprise = EcModel.ecEnterprise; ////???
    event.setTrapID(ecCustomTrapId);
    return true;
  }

  private AlarmTrapType getAlarmTrapType(EventDTO event, OpticalRouterNotification notification) {
    String alarmName = ((OpticalRouterEvent.EntityAlarmed) notification.event()).alarmType();
    try {
      return alarmTypeHandler.getTrapDescriptorByTrapName(event.sourceNEType, alarmName);
    } catch (Exception e) { /* ignore */ }
    try {
      return alarmTypeHandler.getTrapDescriptorByTrapShortName(event.sourceNEType, alarmName);
    } catch (Exception e) { // ignore and try again with alarmName short
    }
    throw new NoSuchElementException("AlarmTypeHdlr() Trap not found: NE-type=" + event.sourceNEType + ", Name/Acronym=" + alarmName + "/" + alarmName);
  }

  private void setEventDescriptionForEmptyEvents(TrapParameters trapParameters) {
    trapParameters.getEvent().setTextIfEmpty(
      trapParameters.getEvent().getTrapParamID().name() + "=" +
        (trapParameters.getEvent().getNewValue() != -1
          ? trapParameters.getEvent().getNewValue()
          : trapParameters.getEvent().getStringNewValue())
    );
  }
}
