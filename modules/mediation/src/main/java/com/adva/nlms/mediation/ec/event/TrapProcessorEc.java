/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: krystianl
 */

package com.adva.nlms.mediation.ec.event;

import com.adva.nlms.common.AlarmTrapType;
import com.adva.nlms.common.AlarmTypeHandler;
import com.adva.nlms.common.AlarmTypeHandlerImpl;
import com.adva.nlms.common.NO_TIMESTAMP;
import com.adva.nlms.common.NetworkAlarmTypeProperty;
import com.adva.nlms.common.config.EntityIndex;
import com.adva.nlms.common.event.Direction;
import com.adva.nlms.common.event.EventSeverity;
import com.adva.nlms.common.event.EventStatus;
import com.adva.nlms.common.event.EventType;
import com.adva.nlms.common.event.Location;
import com.adva.nlms.common.event.types.TrapShortName;
import com.adva.nlms.common.property.FNMPropertyConstants;
import com.adva.nlms.common.property.FNMPropertyFactory;
import com.adva.nlms.mediation.config.ManagedObjectDAO;
import com.adva.nlms.mediation.config.ManagedObjectDBImpl;
import com.adva.nlms.mediation.config.ec.EcAidStringUtil;
import com.adva.nlms.mediation.config.ec.entity.facility.ctp.ConnectionTerminationPointEcDBImpl;
import com.adva.nlms.mediation.config.f8.croma.ServiceEndpointEcDBImpl;
import com.adva.nlms.mediation.config.neprofile.masterprofile.f8.impl.NeProfileF8Constants;
import com.adva.nlms.mediation.ec.event.notification.AlarmsEventsNotificationsService;
import com.adva.nlms.mediation.ec.model.EcModel;
import com.adva.nlms.mediation.ec.neComm.pollings.SyncEcToNmsConverter;
import com.adva.nlms.mediation.event.EventDTO;
import com.adva.nlms.mediation.event.EventLogger;
import com.adva.nlms.mediation.evtProc.TrapParameters;
import com.adva.nlms.mediation.evtProc.TrapProcessorAbstract;
import com.adva.nlms.mediation.evtProc.ec.AOSCoreStateType;
import com.adva.nlms.mediation.evtProc.ec.AlarmProfileSeverityType;
import com.adva.nlms.mediation.evtProc.ec.Converter;
import com.adva.nlms.mediation.evtProc.ec.Transients;
import com.adva.nlms.mediation.synchronization.util.mobyref.F4MOFetcher;
import com.adva.nlms.mediation.util.EventTypeConverter;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.base.Strings;
import jakarta.xml.bind.JAXBContext;
import jakarta.xml.bind.JAXBElement;
import jakarta.xml.bind.JAXBException;
import jakarta.xml.bind.Unmarshaller;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.codec.binary.Hex;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.TextStringBuilder;
import org.snmp4j.smi.VariableBinding;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.xml.transform.Source;
import javax.xml.transform.stream.StreamSource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeFormatterBuilder;
import java.time.temporal.ChronoField;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.NoSuchElementException;
import java.util.Objects;
import java.util.Optional;
import java.util.StringJoiner;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.adva.nlms.mediation.ec.model.EcModel.MocPtpClock;
import static com.adva.nlms.mediation.ec.model.EcModel.MocPtpPort;
import static com.adva.nlms.mediation.ec.model.EcModel.MocSyncRef;
import static com.adva.nlms.mediation.ec.model.mo.ClassEvent.EVTINF_NODE;


@Component
public class TrapProcessorEc extends TrapProcessorAbstract {

    @Autowired
    private AlarmsEventsNotificationsService alarmsEventsNotificationsService;

    public final int ecCustomTrapId = 10000;
    public final int ecTcaTrapId = 11000;
    private static final int SETPOINT_SCALE_FACTOR = 10;
    private static final String jaxbPackage = "com.adva.nlms.mediation.evtProc.ec";
    private static final String transientsXmlName = "F8Transients.xml";
    private static final String eventMapF4XmlName = "NetworkEventMap_EC_F4.xml";
    private static final String eventMapF4AndF8XmlName = "NetworkEventMap_EC.xml";
    private static final DateTimeFormatter dateTimeFormatter = new DateTimeFormatterBuilder().appendPattern("yyyy-MM-dd'T'HH:mm:ss")
            .appendFraction(ChronoField.MICRO_OF_SECOND, 0, 4, true)
            .toFormatter(Locale.ENGLISH)
            .withZone(ZoneId.of("UTC"));

    private Transients transients;
    private Map<String, EventTypeEc> ecEventTypeMap = new HashMap<>();

    private static final String OPT_SET = "/optset";
    private static final String USRLBL = "usrlbl";
    private static final String EVENT_INFO = "evtinf";
    private static final String PLUG = "plg";
    private static final String NAME = "name";
    private static final String CTYP = "ctyp";
    private static final String ENTITY_CREATION_MESSAGE = "New Entity Created";
    private static final String INDEXED_TRANSIENT_REGEX = ".*/\\d+$";
    private static final String DYING_GASP_TRAP_OID = "1.3.6.1.4.1.2544.1.20.1.4.1.1";
    public static final String USER_LABEL = "User Label";

    public TrapProcessorEc() {
        try {
            JAXBContext jc = JAXBContext.newInstance(jaxbPackage);
            Unmarshaller unmarshaller = jc.createUnmarshaller();
            Source source = new StreamSource(Transients.class.getResourceAsStream(transientsXmlName));
            JAXBElement<Transients> result = unmarshaller.unmarshal(source, Transients.class);
            transients = result.getValue();
        } catch (JAXBException e) {
            log.error("Failed to load F8 transient trap definitions", e);
            transients = new Transients();
        }

        if (!FNMPropertyFactory.getPropertyAsBoolean(FNMPropertyConstants.INIT_F8_FROM_DB_ENABLED,
                FNMPropertyConstants.INIT_F8_FROM_DB_ENABLED_DEFAULT)) {
            ecEventTypeMap.putAll(EventLoaderEc.readEventMap(eventMapF4AndF8XmlName));
        }
    }

    //==============================================================================
    //=== TrapProcessor interface ==================================================
    //==============================================================================

    protected boolean isNESpecificAlarm(EventDTO event, List<VariableBinding> vbl) {
        if (event.getWebSocketNotification() == null)
            return true;

        EcWebSocketNotification notification = event.getWebSocketNotification();
        EcWebSocketNotification.AosCategory category = notification.getCategory();
        return category == EcWebSocketNotification.AosCategory.ALARM;
    }

    @Override
    public boolean setEventEnterpriseAndTrapId(final EventDTO event, final List<VariableBinding> vbl) {
        if((event.getWebSocketNotification() == null) && (event.snmpTrapOid != null)) {
            if(DYING_GASP_TRAP_OID.equals(event.snmpTrapOid)) {
                AlarmTrapType trapType = getAlarmTrapType(event, "dying-gasp");
                event.enterprise = EcModel.ecEnterprise;
                event.setAidString("SYSTEM");
                event.setTrapID(trapType.getAlarmTypeID());
                event.severity = trapType.getEventSeverityType(NetworkAlarmTypeProperty.SEVERITY_ASSIGNMENT.WORKING);
                return true;
            }

            log.info("unknown snmp trap received from device: {}, trapOid={}", event.sourceNE_IP, event.snmpTrapOid);
            return false;
        }

        if (event.getWebSocketNotification() == null)
            return false;

        EcWebSocketNotification notification = event.getWebSocketNotification();
        event.addToDebugLog(
                EventLogger.getDebugLogString(EventLogger.LoggingType.SEQUENCE_NUMBER, notification.get(EcWebSocketNotification.Key.traceId))
        );
        if (notification.getCategory() == EcWebSocketNotification.AosCategory.ALARM) {
            AlarmTrapType trapType = null;
            try {
                trapType = getAlarmTrapType(event, notification);
                event.enterprise = EcModel.ecEnterprise;
                event.setTrapID(trapType.getAlarmTypeID());
                return true;
            } catch (Exception e) {
                EventLogger.logWarnOnce(logSbi, EventLogger.createCacheKey(event.sourceNEType, notification.getDescription()),
                        "EC Notification unknown alarm " + notification.getPdu(), event, null);
            }
        }
        event.enterprise = EcModel.ecEnterprise;
        event.setTrapID(notification.getEventType().equalsIgnoreCase(TrapShortName.TCA.toString()) ? ecTcaTrapId : ecCustomTrapId);
        return true;
    }

    @Override
    public void updateEventProperties(TrapParameters trapParameters) {
        setEventDescriptionForEmptyEvents(trapParameters);
    }

    @Override
    public void setSpecificParametersForNotification(TrapParameters trapParameters) {
        setEventDescriptionForEmptyEvents(trapParameters);
    }

    private void setEventDescriptionForEmptyEvents(TrapParameters trapParameters) {
        trapParameters.getEvent().setTextIfEmpty(trapParameters.getEvent().getTrapParamID().name() + "=" +
                (trapParameters.getEvent().getNewValue() != -1 ? trapParameters.getEvent().getNewValue() : trapParameters.getEvent().getStringNewValue()));
    }

    //==============================================================================
    //=== AlarmProcessor interface =================================================
    //==============================================================================

    public void setAlarmParametersFromVbl(final TrapParameters trapParameters) {
        EventDTO eventDTO = trapParameters.getEvent();
        if (eventDTO.getWebSocketNotification() == null)
            return;

        // parse alarm parameters
        EcWebSocketNotification notification = eventDTO.getWebSocketNotification();
        try {
            EntityIndex entityIndex = new EntityIndex(EntityIndex.EC_CATEGORY_INDEX + notification.getHost());
            EventSeverity severity = notification.getSeverityType();
            long neTimestamp = getNeTimestamp(notification.getAlarmTimestamp());
            EventStatus.Impairment impairment = notification.getImpairmentType(EventStatus.Impairment.NONSERVICE_AFFECTING);
            Location location = notification.getLocationType(Location.NONE);
            Direction direction = notification.getDirectionType(Direction.NONE);

            if (!EcAidStringUtil.updateAidString(eventDTO, notification.getAid(), notification.getHost())) {
                eventDTO.setAidString(EcModel.aidConvertToNMS(notification.getAid(), notification.getHost()));
            }
            updateObjectIndex(eventDTO, entityIndex);
            eventDTO.neTimeStamp = neTimestamp;
            eventDTO.neLogIndex = notification.getEventId();

            if (severity != null) {
                eventDTO.severity = severity;
                eventDTO.impairment = (impairment == EventStatus.Impairment.SERVICE_AFFECTING);
                eventDTO.location = location.getMIBValue();
                eventDTO.direction = direction.getMIBValue();
                return;
            }
        } catch (Exception e) {
            log.error("TrapProcessorEC.setAlarmParametersFromVbl failed: " + e.getMessage(), e);
        }
        trapParameters.setEventType(EventType.TRANSIENT);
    }

    //==============================================================================
    //=== NotificationProcessor interface ==========================================
    //==============================================================================


    public void handleTransientTrap(final TrapParameters trapParameters) {
        // set object-index, AID, Short-Name (override), Description
        EventDTO eventDTO = trapParameters.getEvent();
        if (eventDTO.getWebSocketNotification() == null)
            return;

        trapParameters.setEventType(EventType.TRANSIENT);

        EcWebSocketNotification notification = eventDTO.getWebSocketNotification();
        // query events by eventType in map -> short name upper case
        EntityIndex eventIndex = new EntityIndex(EntityIndex.EC_CATEGORY_INDEX + notification.getHost());
        updateObjectIndex(eventDTO, eventIndex);
        if (!EcAidStringUtil.updateAidString(eventDTO, notification.getAid(), notification.getHost())) {
            eventDTO.setAidString(EcModel.aidConvertToNMS(notification.getAid(), notification.getHost()));
        }
        //set LogIndex to partially process it
        eventDTO.neLogIndex = notification.getEventId();

        try {
            EventTypeEc eventType = ecEventTypeMap.get(notification.getEventType().toUpperCase());
            if (eventType == null) {
                log.error("TrapProcessorEc: Can't find event definition for notification [" + notification.getEventType().toUpperCase() + "]");
                return;
            }

            // set Short-Name: AosEventType.shortName or Notification-Header (AOS-eventtype) or NetworkEventMap.xml (CONF)
            String shortName = eventType.getShortName();
            eventDTO.shortName = firstNonEmpty(shortName, notification.getEventType(), eventDTO.shortName);
            eventDTO.dbChgCategory = eventType.getDbChgCategory();

            // set Description (text): Notification ("descr"-field) or AosEventType.description or NetworkEventMap.xml
            setEventDescription(eventDTO, eventType, notification, shortName);


        } catch (Exception e) {
            log.error("TrapProcessorEc.handleTransientTrap failed: " + e.getMessage(), e);
        }
    }

    private void updateObjectIndex(EventDTO eventDTO, EntityIndex eventIndex) {
        EcEventObjectIndexFinder ecEventObjectIndexFinder = new EcEventObjectIndexFinder(eventIndex);
        eventDTO.objectIndex = ecEventObjectIndexFinder.getObjectIndexOrDefault(eventIndex);
        ecEventObjectIndexFinder.getEventSuffix().ifPresent(
                suff -> eventDTO.addToDebugLog(
                        EventLogger.getDebugLogString(EventLogger.LoggingType.EC_NOTIFICATION_URI_SUFFIX, suff))
        );
    }

    private void updateSecurityEvent(EcWebSocketNotification.AosCategory category, EcWebSocketNotification notification, EventDTO eventDTO) {
        if (category != null && category.equals(EcWebSocketNotification.AosCategory.SECURITY)) {
            JsonNode info = notification.getPduNode().path("ednm");
            if (info != null) {
                String currentEventText = eventDTO.getText() != null ? eventDTO.getText().toString() : "";
                currentEventText += currentEventText.length() > 0 ? " - " : "";
                eventDTO.setText(currentEventText + info.asText());
            }
            eventDTO.securityEvent = true;
        }
    }

    public void setTimestampAndLogIndex(final TrapParameters trapParameters) {
        setEventTimeStamp(trapParameters);
    }

    public void setEventTimeStamp(final TrapParameters trapParameters) {
        EventDTO eventDTO = trapParameters.getEvent();
        if (eventDTO.getWebSocketNotification() == null)
            return;

        eventDTO.neTimeStamp = getNeTimestamp(eventDTO.getWebSocketNotification().get(EcWebSocketNotification.Key.timestamp));
    }

    public void handleNoNewValueInNotification(final TrapParameters trapParameters) {
        // nothing to do (is always called)
    }

    //==============================================================================
    //=== Private methods ==========================================================
    //==============================================================================

    private AlarmTrapType getAlarmTrapType(EventDTO event, EcWebSocketNotification notification) {
        AlarmTypeHandler alarmTypeHandler = AlarmTypeHandlerImpl.getInstance();
        String alarmName = notification.getDescription().replaceAll(" ", "-");
        try {
            return alarmTypeHandler.getTrapDescriptorByTrapName(event.sourceNEType, alarmName);
        } catch (Exception e) { /* ignore */ }
        String shortName = notification.getDnm();
        try {
            return alarmTypeHandler.getTrapDescriptorByTrapShortName(event.sourceNEType, shortName);
        } catch (Exception e) { // ignore and try with short name
        }
        try {
            return alarmTypeHandler.getTrapDescriptorByTrapShortName(event.sourceNEType, alarmName);
        } catch (Exception e) { // ignore and try again with alarmName short
        }

        throw new NoSuchElementException("AlarmTypeHdlr() Trap not found: NE-type=" + event.sourceNEType + ", Name/Acronym=" + alarmName + "/" + shortName);
    }

    private AlarmTrapType getAlarmTrapType(EventDTO event, String alarmName) {
        try {
            AlarmTypeHandler alarmTypeHandler = AlarmTypeHandlerImpl.getInstance();
            return alarmTypeHandler.getTrapDescriptorByTrapName(event.sourceNEType, alarmName);
        } catch (Exception e) { /* ignore */ }

        throw new NoSuchElementException("AlarmTypeHdlr() Trap not found: NE-type=" + event.sourceNEType + ", alarmName=" + alarmName);
    }

    private void describeEventEntConfAndEntState(EventDTO eventDTO, EcWebSocketNotification notification, EventTypeEc eventType) {
        JsonNode evtInfoNode = notification.getPduNode().path(EVENT_INFO);
        JsonNode pchNode = evtInfoNode.findPath("pch");

        if (pchNode.isArray() && pchNode.isEmpty()){
          log.warn("Entity with index: {} contains empty pchNode", eventDTO.getObjectIndex().toStringIndex());
        }
        // ex: "evtinf": {"pch": [{"path": "/suppsigr/5", "value": "11096", "op": "add"},
      if (pchNode.isArray() && !pchNode.isEmpty()) {
            describeEventFromPchNode(pchNode, eventDTO);
        } else {
            describeEventFromEvtInfoNode(evtInfoNode, eventDTO, eventType);
        }
    }

    private void describeEventFromPchNode(JsonNode pchNode, EventDTO eventDTO) {
        for (JsonNode pathNode : pchNode) {
            String pathElement = pathNode.findPath("path").textValue();
            String name = retrieveName(pathElement, pathNode);
            JsonNode node = pathNode.findPath("value");
            if (node.asText().isEmpty()) {
                if(pathElement.equals("/usrlbl") && pathNode.findPath("op").asText().equals("replace")) {
                    addText(eventDTO, USER_LABEL, "Replaced to empty");
                }else{
                    // if no value then op ex. remove
                    node = pathNode.findPath("op");
                }
            }

            boolean isFound = false;
            boolean isSeverity = name.equals("Severity");

            for (String enumName : isSeverity ? getAlarmProfileSeverityTypeEnumNames() : getAOSCoreStateTypeEnumNames()) {
                if (enumName.equalsIgnoreCase(node.asText())) {
                    addText(eventDTO, name, node.asText(), isSeverity ? AlarmProfileSeverityType.class : AOSCoreStateType.class);
                    isFound = true;
                    break;
                }
            }

            if (!isFound) {
                handleNotFoundEventEnum(pathElement, pathNode, eventDTO, name, node);
            }
        }
    }

    private String retrieveName(String pathElement, JsonNode pathNode) {
        String name;
        if (pathElement.startsWith("/sm")) {
            name = getTransientSub(pathElement.substring(pathElement.indexOf("/", pathElement.indexOf("/") + 1)));
        } else if (pathElement.startsWith("/peplst")) {
            String opType = getTransientSub(pathNode.findPath("op").asText());
            String opTypeUpperFirstLetter = opType.substring(0,1).toUpperCase() + opType.substring(1);
            name = opTypeUpperFirstLetter+ " " + getTransientSub(pathElement.substring(0,7));
        } else if (isIndexedPath(pathElement)) {
            name = buildNameForIndexedTransient(pathElement);
        } else {
            name = getTransientSub(pathElement);
        }
        return name;
    }

    private String buildNameForIndexedTransient(String pathElement) {
        String[] pathElements = pathElement.split("/");
        Transients.Transient indexedTransient = getIndexedTransient(pathElement);
        if (indexedTransient != null) {
            return indexedTransient.getName() + " " + pathElements[pathElements.length - 1];
        }
        log.warn("Unsupported transient for pathElement: {}", pathElement);
        return pathElement;
    }

    private void handleNotFoundEventEnum(String pathElement, JsonNode pathNode, EventDTO eventDTO, String name, JsonNode node) {
        if (isSyncRelatedPath(pathElement)) {
            handleSyncEventDescriptions(pathElement, pathNode, eventDTO);
        } else {
            addText(eventDTO, name, node, pathElement);
        }
    }

    private void describeEventFromEvtInfoNode(JsonNode evtInfoNode, EventDTO eventDTO, EventTypeEc eventType) {
        StringBuilder description = handleSyncEventDescriptions(evtInfoNode, eventDTO.getNeId());
        boolean isDescriptionAdded = description.length() != 0;

        if (isDescriptionAdded) {
            addText(eventDTO, description.toString());
            log.debug("Fields from evtInfoNode: {}", evtInfoNode.toString());
        } else {
            eventDTO.setText(eventType.getDescription());
        }
    }

    private void handleSyncEventDescriptions(String pathElement, JsonNode pathNode, EventDTO eventDTO) {
        if (pathElement.startsWith("/syncid")) {
            ManagedObjectDBImpl mo = ManagedObjectDAO.getInstance().findByUuid(getTransientSub(pathNode.findPath("value").asText()), eventDTO.getNeId());
            addText(eventDTO, "Sync ID", mo != null ? mo.getShortDescription() : "None");
        } else if (pathElement.startsWith("/src") || pathElement.startsWith("/todsrc")) {
            ManagedObjectDBImpl mo = ManagedObjectDAO.getInstance().findByUuid(getTransientSub(pathNode.findPath("value").asText()), eventDTO.getNeId());
            addText(eventDTO, "Source", mo != null ? mo.getShortDescription() : " ");
        } else if (pathElement.startsWith("/qlmden") || pathElement.startsWith("/assumql") || pathElement.startsWith("/expectql") || pathElement.startsWith("/squelql") ||
                pathElement.startsWith("/selmode") || pathElement.startsWith("/wtrtm")) {
            StringBuilder description = handleSyncEventDescriptions(pathElement, pathNode.findPath("value").asText());
            boolean isDescriptionAdded = description.length() != 0;
            if (isDescriptionAdded) {
                addText(eventDTO, description.toString());
            }
        }
    }

    private boolean isSyncRelatedPath(String pathElement) {
        return pathElement.startsWith("/syncid") || pathElement.startsWith("/src") || pathElement.startsWith("/todsrc") || pathElement.startsWith("/qlmden") ||
                pathElement.startsWith("/assumql") || pathElement.startsWith("/expectql") || pathElement.startsWith("/squelql") ||
                pathElement.startsWith("/selmode") || pathElement.startsWith("/wtrtm");
    }

    private StringBuilder handleSyncEventDescriptions(JsonNode evtInfoNode, int neId) {
        StringBuilder description = new StringBuilder();

        //PTP Port
        modifyDescription(evtInfoNode, description, "portsta", "Port State",
                () -> SyncEcToNmsConverter.getNmsPTPPortStateFromEcValue(getTransientSub(evtInfoNode.findPath("portsta").asText())));
        modifyDescription(evtInfoNode, description, "bmcacode", "BMCA Decision Code",
                () -> SyncEcToNmsConverter.getNmsBmcaDecisionCodeFromEcValue(getTransientSub(evtInfoNode.findPath("bmcacode").asText())));
        // PTP Clock
        modifyDescription(evtInfoNode, description, "recvql", "Received QL",
                () -> SyncEcToNmsConverter.getNmsSyncQLFromEcValue(getTransientSub(evtInfoNode.findPath("recvql").asText())));
        modifyDescription(evtInfoNode, description, "clkclass", "Clock Class",
                getTransientSub(evtInfoNode.findPath("clkclass").asText()));
        modifyDescription(evtInfoNode, description, "phasecr", "Phase Recovery State",
                () -> SyncEcToNmsConverter.getNmsPhaseRecoveryStateFromEcValue(getTransientSub(evtInfoNode.findPath("phasecr").asText())));
        if (evtInfoNode.has("slavept")) {
            String slave = getTransientSub(evtInfoNode.findPath("slavept").asText());
            String portUuid = "";
            if (!slave.startsWith("/mit"))
                portUuid = "/mit";
            portUuid = portUuid + slave.replace("=", "/");
            ManagedObjectDBImpl mo = ManagedObjectDAO.getInstance().findByUuid(portUuid, neId);
            modifyDescription(evtInfoNode, description, "slavept", "Active Slave Port",
                    mo != null ? mo.getShortDescription() : " ");
        }
        modifyDescription(evtInfoNode, description, "freqtrac", "Frequency Traceability",
                () -> SyncEcToNmsConverter.getNmsBooleanTypeFromEcValue(getTransientSub(evtInfoNode.findPath("freqtrac").asText())));
        modifyDescription(evtInfoNode, description, "timetrac", "Time Traceability",
                () -> SyncEcToNmsConverter.getNmsBooleanTypeFromEcValue(getTransientSub(evtInfoNode.findPath("timetrac").asText())));
        modifyDescription(evtInfoNode, description, "ofsvar", "Offset Scaled Log Variance",
                getTransientSub(evtInfoNode.findPath("ofsvar").asText()));
        modifyDescription(evtInfoNode, description, "clkcr", "Clock Recovery State",
                () -> SyncEcToNmsConverter.getNmsClockRecoveryStateFromEcValue(getTransientSub(evtInfoNode.findPath("clkcr").asText())));
        modifyDescription(evtInfoNode, description, "accuracy", "Clock Accuracy",
                getTransientSub(evtInfoNode.findPath("accuracy").asText()));
        //Bits OUT
        modifyDescription(evtInfoNode, description, "transql", "Transmit QL",
                getTransientSub(evtInfoNode.findPath("transql").asText()));
        //Sync
        modifyDescription(evtInfoNode, description, "clkmode", "Clock Mode",
                () -> SyncEcToNmsConverter.getNmsSyncClockModeFromEcValue(getTransientSub(evtInfoNode.findPath("clkmode").asText())));
        if (evtInfoNode.has("selrel")) {
            String selectedReference = getTransientSub(evtInfoNode.findPath("selrel").asText());
            String selectedReferenceUuid = "";
            if (!selectedReference.startsWith("/mit"))
                selectedReferenceUuid = "/mit";
            selectedReferenceUuid = selectedReferenceUuid + selectedReference.replace("=", "/");
            ManagedObjectDBImpl mo = ManagedObjectDAO.getInstance().findByUuid(selectedReferenceUuid, neId);
            modifyDescription(evtInfoNode, description, "selrel", "Selected Reference",
                    mo != null ? mo.getShortDescription() : " ");
        }
        //SyncRef
        modifyDescription(evtInfoNode, description, "effecql", "Effective QL",
                () -> SyncEcToNmsConverter.getNmsSyncQLFromEcValue(getTransientSub(evtInfoNode.findPath("effecql").asText())));
        modifyDescription(evtInfoNode, description, "refstu", "Reference Status",
                () -> SyncEcToNmsConverter.getNmsReferenceStatusFromEcValue(getTransientSub(evtInfoNode.findPath("refstu").asText())));
        modifyDescription(evtInfoNode, description, "refsta", "Reference State",
                () -> SyncEcToNmsConverter.getNmsReferenceStateFromEcValue(getTransientSub(evtInfoNode.findPath("refsta").asText())));
        modifyDescription(evtInfoNode, description, "syncql", "Sync QL",
                () -> SyncEcToNmsConverter.getNmsSyncQLFromEcValue(getTransientSub(evtInfoNode.findPath("syncql").asText())));
        return description;
    }

    private <V> void modifyDescription(JsonNode evtInfoNode, StringBuilder description, String fieldName,
                                       String humanReadableFieldName, Supplier<V> getterMethod) {
        if (evtInfoNode.has(fieldName)) {
            if (description.length() != 0)
                description.append("; ");
            description.append(humanReadableFieldName);
            description.append("=");
            description.append(getterMethod.get());
        }
    }

    private <V> void modifyDescription(JsonNode evtInfoNode, StringBuilder description, String fieldName,
                                       String humanReadableFieldName, String value) {
        if (evtInfoNode.has(fieldName)) {
            if (description.length() != 0)
                description.append("; ");
            description.append(humanReadableFieldName);
            description.append("=");
            description.append(value);
        }
    }

    private <V> void modifyDescriptionFromPathElement(String pathElement, StringBuilder description, String fieldName,
                                                      String humanReadableFieldName, String value) {
        if (pathElement.contains(fieldName)) {
            if (description.length() != 0)
                description.append("; ");
            description.append(humanReadableFieldName);
            description.append("=");
            description.append(value);
        }
    }


    private StringBuilder handleSyncEventDescriptions(String pathElement, String value) {
        StringBuilder description = new StringBuilder();
        // Bits IN/OUT
        modifyDescriptionFromPathElement(pathElement, description, "qlmden", "QL Mode Enabled", value);
        modifyDescriptionFromPathElement(pathElement, description, "assumql", "Assumed QL", SyncEcToNmsConverter.getNmsSyncQLFromEcValue(value));
        modifyDescriptionFromPathElement(pathElement, description, "expectql", "Expected QL", SyncEcToNmsConverter.getNmsSyncQLFromEcValue(value));
        modifyDescriptionFromPathElement(pathElement, description, "squelql", "Squelch QL", SyncEcToNmsConverter.getNmsSyncQLFromEcValue(value));
        //Sync
        modifyDescriptionFromPathElement(pathElement, description, "selmode", "Selection Mode", SyncEcToNmsConverter.getNmsSelectionModeFromEcValue(value));
        modifyDescriptionFromPathElement(pathElement, description, "wtrtm", "WTRTM", value);

        return description;
    }

    private void describeEventStateAndOperState(EventDTO eventDTO, EcWebSocketNotification notification) {
        JsonNode evtInfoNode = notification.getPduNode().path(EVENT_INFO);
        JsonNode adminStateNode = evtInfoNode.findPath("admin");
        if (!adminStateNode.isMissingNode()) {
            addText(eventDTO, "Admin State", adminStateNode.asText(), AOSCoreStateType.class);
        }

        JsonNode isst = evtInfoNode.findPath("isst");
        if(!isst.isMissingNode()) {
            if (isst.isArray()) {
                StringJoiner sj = new StringJoiner(",", "[", "]");
                for (JsonNode objNode : isst) {
                    sj.add(translateSST(AOSCoreStateType.class, objNode.asText()));
                }
                String description = sj.toString().replaceAll("[\\[\\](){}]", "");
                eventDTO.addText("In Service Substate" + "=" + description);
            } else {
                addText(eventDTO, "In Service Substate", isst.asText(), AOSCoreStateType.class);
            }
        }

        JsonNode operationalStateNode = evtInfoNode.findPath("oper");
        if (!operationalStateNode.isMissingNode()) {
            addText(eventDTO, "Oper State", operationalStateNode.asText(), AOSCoreStateType.class);
        }

        JsonNode sst = evtInfoNode.findPath("sst");
        if (!sst.isMissingNode()) {
            if (sst.isArray()) {
                StringJoiner sj = new StringJoiner(",", "[", "]");
                for (JsonNode objNode : sst) {
                    sj.add(translateSST(AOSCoreStateType.class, objNode.asText()));
                }
                String description = sj.toString().replaceAll("[\\[\\](){}]", "");
                eventDTO.addText("Secondary States" + "=" + description);
            } else {
                addText(eventDTO, "Secondary States", sst.asText(), AOSCoreStateType.class);
            }
        }
    }

    private void describeEventEntAddForPlug(EventDTO eventDTO, EcWebSocketNotification notification, EventTypeEc eventType) {
        JsonNode evtInfoNode = notification.getPduNode().path(EVENT_INFO);
        String plgName = notification.getPduNode().findPath(PLUG).findPath(NAME).asText();
        eventDTO.setText(firstNonEmpty(plgName, eventType.getDescription(), ENTITY_CREATION_MESSAGE));
        if (evtInfoNode.hasNonNull(USRLBL)) {
            eventDTO.setEntityAlias(evtInfoNode.get(USRLBL).asText());
        }
    }

    private void describeEventEntAdd(EventDTO eventDTO, EcWebSocketNotification notification, EventTypeEc eventType) {
        JsonNode evtInfoNode = notification.getPduNode().path(EVENT_INFO);

        // handle plugs a little bit differently
        String eqCat = notification.getPduNode().findPath("eqcat").asText();
        if (eqCat.equals(PLUG)) {
            describeEventEntAddForPlug(eventDTO, notification, eventType);
        } else {
            String userName = evtInfoNode.findPath("un").textValue();
            String ctyp = evtInfoNode.findPath(CTYP).textValue();
            if (userName != null && ctyp.equals("/cim/mm/moc/sec,usr")) {
                addText(eventDTO, "New User Created", userName);
            } else if (evtInfoNode.get(CTYP) != null) {
                if (evtInfoNode.get(CTYP).asText().equals(MocPtpPort.getCimType())) {
                    StringBuilder description = handlePtpPortCreationEventDescription(evtInfoNode, eventDTO.getNeId());
                    addText(eventDTO, description.toString());
                } else if (evtInfoNode.get(CTYP).asText().equals(MocPtpClock.getCimType())) {
                    StringBuilder description = handlePtpClockCreationEventDescription(evtInfoNode, eventDTO.getNeId());
                    addText(eventDTO, description.toString());
                } else if (evtInfoNode.get(CTYP).asText().equals(MocSyncRef.getCimType())) {
                    StringBuilder description = handleSyncRefCreationEventDescription(evtInfoNode, eventDTO.getNeId());
                    addText(eventDTO, description.toString());
                } else {
                    eventDTO.setText(firstNonEmpty(eventType.getDescription(), ENTITY_CREATION_MESSAGE));
                }
            } else {
                eventDTO.setText(firstNonEmpty(eventType.getDescription(), ENTITY_CREATION_MESSAGE));
            }
        }
        if(notification.getCimType().equals(EcModel.MocEqEq.CARD.CCLSTI.getCimType())) {
            eventDTO.setText("Card Cluster " + eventDTO.getText());
        }
    }

    private StringBuilder handlePtpPortCreationEventDescription(JsonNode evtInfoNode, int neId) {
        StringBuilder description = new StringBuilder();

        modifyDescription(evtInfoNode, description, MocPtpPort.ENCAPSULATION.substring(1), "Encapsulation",
                () -> SyncEcToNmsConverter.getNmsPTPTransportTypeFromEcValue(getTransientSub(evtInfoNode.findPath(MocPtpPort.ENCAPSULATION.substring(1)).asText())));
        modifyDescription(evtInfoNode, description, MocPtpPort.FLOWPOINT.PTP_TRANSPORT_MODE.substring(1), "Transport Mode",
                () -> SyncEcToNmsConverter.getNmsPTPTransportModeFromEcValue(getTransientSub(evtInfoNode.findPath(MocPtpPort.FLOWPOINT.PTP_TRANSPORT_MODE.substring(1)).asText())));

        if (evtInfoNode.has(MocPtpPort.DYNAMIC.substring(1))) {
            JsonNode dynamicNode = evtInfoNode.get(MocPtpPort.DYNAMIC.substring(1));
            modifyDescription(dynamicNode, description, MocPtpPort.DYNAMICP.BMCA_CODE.substring(1), "BMCA Decision Code",
                    () -> SyncEcToNmsConverter.getNmsBmcaDecisionCodeFromEcValue(getTransientSub(evtInfoNode.findPath(MocPtpPort.DYNAMICP.BMCA_CODE.substring(1)).asText())));
            modifyDescription(dynamicNode, description, MocPtpPort.DYNAMICP.ANNOUNCE_TIMEOUT.substring(1), "Announce Receipt Timeout",
                    getTransientSub(evtInfoNode.findPath(MocPtpPort.DYNAMICP.ANNOUNCE_TIMEOUT.substring(1)).asText()));
            modifyDescription(dynamicNode, description, MocPtpPort.DYNAMICP.SYNC_TIMEOUT.substring(1), "Sync Receipt Timeout",
                    getTransientSub(evtInfoNode.findPath(MocPtpPort.DYNAMICP.SYNC_TIMEOUT.substring(1)).asText()));
            modifyDescription(dynamicNode, description, MocPtpPort.DYNAMICP.DELAY_RSP_TIMEOUT.substring(1), "Delay Response Receipt Timeout",
                    getTransientSub(evtInfoNode.findPath(MocPtpPort.DYNAMICP.DELAY_RSP_TIMEOUT.substring(1)).asText()));
            modifyDescription(dynamicNode, description, MocPtpPort.MASTERP.SYNC_MESSAGE_RATE.substring(1), "Sync message Rate",
                    () -> SyncEcToNmsConverter.getNmsSyncMessageRateFromEcValue(getTransientSub(evtInfoNode.findPath(MocPtpPort.MASTERP.SYNC_MESSAGE_RATE.substring(1)).asText())));
            modifyDescription(dynamicNode, description, MocPtpPort.DYNAMICP.ANNOUNCE_RATE.substring(1), "Announce Message Rate",
                    () -> SyncEcToNmsConverter.getNmsAnnounceMessageRateFromEcValue(getTransientSub(evtInfoNode.findPath(MocPtpPort.DYNAMICP.ANNOUNCE_RATE.substring(1)).asText())));
            modifyDescription(dynamicNode, description, MocPtpPort.DYNAMICP.DELAY_REQUEST_RATE.substring(1), "Delay Request message Rate",
                    () -> SyncEcToNmsConverter.getNmsSyncMessageRateFromEcValue(getTransientSub(evtInfoNode.findPath(MocPtpPort.DYNAMICP.DELAY_REQUEST_RATE.substring(1)).asText())));
            modifyDescription(dynamicNode, description, MocPtpPort.DYNAMICP.DELAY_RESPONSE_RATE.substring(1), "Delay Response message Rate",
                    () -> SyncEcToNmsConverter.getNmsSyncMessageRateFromEcValue(getTransientSub(evtInfoNode.findPath(MocPtpPort.DYNAMICP.DELAY_RESPONSE_RATE.substring(1)).asText())));
        }

        if (evtInfoNode.has(MocPtpPort.MASTER.substring(1))) {
            JsonNode masterNode = evtInfoNode.get(MocPtpPort.MASTER.substring(1));
            modifyDescription(masterNode, description, MocPtpPort.MASTERP.SYNC_MESSAGE_RATE.substring(1), "Sync message Rate",
                    () -> SyncEcToNmsConverter.getNmsSyncMessageRateFromEcValue(getTransientSub(evtInfoNode.findPath(MocPtpPort.MASTERP.SYNC_MESSAGE_RATE.substring(1)).asText())));
            modifyDescription(masterNode, description, MocPtpPort.MASTERP.ANNOUNCE_MESSAGE_RATE.substring(1), "Announce message Rate",
                    () -> SyncEcToNmsConverter.getNmsAnnounceMessageRateFromEcValue(getTransientSub(evtInfoNode.findPath(MocPtpPort.MASTERP.ANNOUNCE_MESSAGE_RATE.substring(1)).asText())));
            modifyDescription(masterNode, description, MocPtpPort.MASTERP.DELAY_RESPONSE_MESSAGE_RATE.substring(1), "Delay Response Message Rate",
                    () -> SyncEcToNmsConverter.getNmsSyncMessageRateFromEcValue(getTransientSub(evtInfoNode.findPath(MocPtpPort.MASTERP.DELAY_RESPONSE_MESSAGE_RATE.substring(1)).asText())));
        }

        if (evtInfoNode.has(MocPtpPort.TRAFFIC_PORT.substring(1))) {
            String trafficPort = getTransientSub(evtInfoNode.findPath(MocPtpPort.TRAFFIC_PORT.substring(1)).asText());
            String trafficPortUuid = "";
            if (!trafficPort.startsWith("/mit"))
                trafficPortUuid = "/mit";
            trafficPortUuid = trafficPortUuid + trafficPort.replace("=", "/");
            ManagedObjectDBImpl mo = ManagedObjectDAO.getInstance().findByUuid(trafficPortUuid, neId);
            modifyDescription(evtInfoNode, description, MocPtpPort.TRAFFIC_PORT.substring(1), "Traffic Port",
                    mo != null ? mo.getShortDescription() : " ");
        }

        modifyDescription(evtInfoNode, description, MocPtpPort.PTP_PROFILE.substring(1), "PTP Profile",
                () -> SyncEcToNmsConverter.getNmsPTPProfileFromEcValue(getTransientSub(evtInfoNode.findPath(MocPtpPort.PTP_PROFILE.substring(1)).asText())));
        modifyDescription(evtInfoNode, description, MocPtpPort.ENTITY_NAME.substring(1), "Entity Name",
                getTransientSub(evtInfoNode.findPath(MocPtpPort.ENTITY_NAME.substring(1)).asText()));
        modifyDescription(evtInfoNode, description, MocPtpPort.ROLE.substring(1), "Role",
                () -> SyncEcToNmsConverter.getNmsPTPPortRoleFromEcValue(getTransientSub(evtInfoNode.findPath(MocPtpPort.ROLE.substring(1)).asText())));
        modifyDescription(evtInfoNode, description, MocPtpPort.DESTINATION_MAC.substring(1), "Destination MAC",
                () -> SyncEcToNmsConverter.getNmsPTPClockDestMacFromEcValue(getTransientSub(evtInfoNode.findPath(MocPtpPort.DESTINATION_MAC.substring(1)).asText())));

        if (evtInfoNode.has(MocPtpPort.PORTID.getCimType().substring(1))) {
            JsonNode portIdentity = evtInfoNode.get(MocPtpPort.PORTID.getCimType().substring(1));
            String clockIdentity = convertToHEX(portIdentity.get(MocPtpPort.PORTID.CLOCK_IDENTITY).asText());
            String portNumber = Strings.padStart(Integer.toHexString(portIdentity.get(MocPtpPort.PORTID.PORT_NUMBER).asInt()), 4, '0');
            modifyDescription(evtInfoNode, description, MocPtpPort.PORTID.getCimType().substring(1), "Port Identity", clockIdentity.concat(portNumber));
        }

        modifyDescription(evtInfoNode, description, MocPtpPort.FLOWPOINT.BANDWIDTH.substring(1), "Bandwidth",
                getTransientSub(evtInfoNode.findPath(MocPtpPort.FLOWPOINT.BANDWIDTH.substring(1)).asText()));
        modifyDescription(evtInfoNode, description, MocPtpPort.FLOWPOINT.COS.substring(1), "CoS",
                getTransientSub(evtInfoNode.findPath(MocPtpPort.FLOWPOINT.COS.substring(1)).asText()));
        modifyDescription(evtInfoNode, description, MocPtpPort.USER_LABEL.substring(1), USER_LABEL,
                getTransientSub(evtInfoNode.findPath(MocPtpPort.USER_LABEL.substring(1)).asText()));

        if (evtInfoNode.has(MocPtpPort.SM.getPath().substring(1))) {
            JsonNode adminStateNode = evtInfoNode.findPath("admin");
            if (!adminStateNode.isMissingNode()) {
                modifyDescription(evtInfoNode.get(MocPtpPort.SM.getPath().substring(1)), description, "admin", "Admin State",
                        getText(adminStateNode.asText(), AOSCoreStateType.class));
            }

            JsonNode operationalStateNode = evtInfoNode.findPath("oper");
            if (!operationalStateNode.isMissingNode()) {
                modifyDescription(evtInfoNode.get(MocPtpPort.SM.getPath().substring(1)), description, "oper", "Oper State",
                        operationalStateNode.asText());
            }

            JsonNode sst = evtInfoNode.findPath("sst");
            if (!sst.isMissingNode()) {
                if (sst.isArray()) {
                    StringJoiner sj = new StringJoiner(",", "[", "]");
                    for (JsonNode objNode : sst) {
                        sj.add(translateSST(AOSCoreStateType.class, objNode.asText()));
                    }
                    String descriptionState = sj.toString().replaceAll("[\\[\\](){}]", "");
                    modifyDescription(evtInfoNode.get(MocPtpPort.SM.getPath().substring(1)), description, "sst", "Secondary States",
                            descriptionState);
                } else {
                    modifyDescription(evtInfoNode.get(MocPtpPort.SM.getPath().substring(1)), description, "sst", "Secondary States",
                            operationalStateNode.asText());
                }
            }
        }

        modifyDescription(evtInfoNode, description, MocPtpPort.LOCAL_PRIORITY.substring(1), "Local Priority",
                getTransientSub(evtInfoNode.findPath(MocPtpPort.LOCAL_PRIORITY.substring(1)).asText()));


        return description;
    }

    private StringBuilder handlePtpClockCreationEventDescription(JsonNode evtInfoNode, int neId) {
        StringBuilder description = new StringBuilder();

        modifyDescription(evtInfoNode, description, MocPtpClock.PTP_PROFILE.substring(1), "PTP Profile",
                () -> SyncEcToNmsConverter.getNmsPTPProfileFromEcValue(getTransientSub(evtInfoNode.findPath(MocPtpClock.PTP_PROFILE.substring(1)).asText())));
        modifyDescription(evtInfoNode, description, MocPtpClock.LOCAL_PRIORITY.substring(1), "Local Priority",
                getTransientSub(evtInfoNode.findPath(MocPtpClock.LOCAL_PRIORITY.substring(1)).asText()));
        modifyDescription(evtInfoNode, description, MocPtpClock.ENTITY_NAME.substring(1), "Entity Name",
                getTransientSub(evtInfoNode.findPath(MocPtpClock.ENTITY_NAME.substring(1)).asText()));
        modifyDescription(evtInfoNode, description, MocPtpClock.CLOCK_TYPE.substring(1), "Clock Type",
                () -> SyncEcToNmsConverter.getNmsPTPClockTypeFromEcValue(getTransientSub(evtInfoNode.findPath(MocPtpClock.CLOCK_TYPE.substring(1)).asText())));
        modifyDescription(evtInfoNode, description, MocPtpClock.DONAIM_NUMBER.substring(1), "Domain Number",
                getTransientSub(evtInfoNode.findPath(MocPtpClock.DONAIM_NUMBER.substring(1)).asText()));
        modifyDescription(evtInfoNode, description, MocPtpClock.CLOCK_IDENTITY.substring(1), "Clock Identity",
                convertToHEX(getTransientSub(evtInfoNode.findPath(MocPtpClock.CLOCK_IDENTITY.substring(1)).asText())));
        modifyDescription(evtInfoNode, description, MocPtpClock.USER_LABEL.substring(1), USER_LABEL,
                getTransientSub(evtInfoNode.findPath(MocPtpClock.USER_LABEL.substring(1)).asText()));

        if (evtInfoNode.has(MocPtpClock.SM.getPath().substring(1))) {
            JsonNode adminStateNode = evtInfoNode.findPath("admin");
            if (!adminStateNode.isMissingNode()) {
                modifyDescription(evtInfoNode.get(MocPtpClock.SM.getPath().substring(1)), description, "admin", "Admin State",
                        getText(adminStateNode.asText(), AOSCoreStateType.class));
            }

            JsonNode operationalStateNode = evtInfoNode.findPath("oper");
            if (!operationalStateNode.isMissingNode()) {
                modifyDescription(evtInfoNode.get(MocPtpClock.SM.getPath().substring(1)), description, "oper", "Oper State",
                        operationalStateNode.asText());
            }

            JsonNode sst = evtInfoNode.findPath("sst");
            if (!sst.isMissingNode()) {
                if (sst.isArray()) {
                    StringJoiner sj = new StringJoiner(",", "[", "]");
                    for (JsonNode objNode : sst) {
                        sj.add(translateSST(AOSCoreStateType.class, objNode.asText()));
                    }
                    String descriptionState = sj.toString().replaceAll("[\\[\\](){}]", "");
                    modifyDescription(evtInfoNode.get(MocPtpClock.SM.getPath().substring(1)), description, "sst", "Secondary States",
                            descriptionState);
                } else {
                    modifyDescription(evtInfoNode.get(MocPtpClock.SM.getPath().substring(1)), description, "sst", "Secondary States",
                            operationalStateNode.asText());
                }
            }
        }

        modifyDescription(evtInfoNode, description, MocPtpClock.PRIORITY_1.substring(1), "Priority1",
                getTransientSub(evtInfoNode.findPath(MocPtpClock.PRIORITY_1.substring(1)).asText()));

        if (evtInfoNode.has(MocPtpClock.SYNC_ID.substring(1))) {
            ManagedObjectDBImpl mo = ManagedObjectDAO.getInstance().findByUuid(getTransientSub(evtInfoNode.findPath(MocPtpClock.SYNC_ID.substring(1)).asText()), neId);
            modifyDescription(evtInfoNode, description, MocPtpClock.SYNC_ID.substring(1), "Sync ID", mo != null ? mo.getShortDescription() : "None");
        }

        modifyDescription(evtInfoNode, description, MocPtpClock.PRIORITY_2.substring(1), "Priority2",
                getTransientSub(evtInfoNode.findPath(MocPtpClock.PRIORITY_2.substring(1)).asText()));

        modifyDescription(evtInfoNode, description, MocPtpClock.MAX_STEPS_REMOVED.substring(1), "Max Steps Removed",
                getTransientSub(evtInfoNode.findPath(MocPtpClock.MAX_STEPS_REMOVED.substring(1)).asText()));

        modifyDescription(evtInfoNode, description, MocPtpClock.SYNC_REF_CANDIDATE.substring(1), "Sync Reference Candidate",
                () -> SyncEcToNmsConverter.getNmsBooleanTypeFromEcValue(getTransientSub(evtInfoNode.findPath(MocPtpClock.SYNC_REF_CANDIDATE.substring(1)).asText())));

        return description;
    }

    private StringBuilder handleSyncRefCreationEventDescription(JsonNode evtInfoNode, int neId) {
        StringBuilder description = new StringBuilder();

        modifyDescription(evtInfoNode, description, MocSyncRef.PRIORITY.substring(1), "Priority",
                getTransientSub(evtInfoNode.findPath(MocSyncRef.PRIORITY.substring(1)).asText()));
        modifyDescription(evtInfoNode, description, MocSyncRef.getDnm().substring(1), "Entity Name",
                getTransientSub(evtInfoNode.findPath(MocSyncRef.getDnm().substring(1)).asText()));
        modifyDescription(evtInfoNode, description, MocSyncRef.USER_LABEL.substring(1), USER_LABEL,
                getTransientSub(evtInfoNode.findPath(MocSyncRef.USER_LABEL.substring(1)).asText()));

        if (evtInfoNode.has(MocSyncRef.REFERENCE.substring(1))) {
            F4MOFetcher.EthernetPortEcFetcherF4 ethernetPortEcFetcherF4 = new F4MOFetcher.EthernetPortEcFetcherF4();
            ConnectionTerminationPointEcDBImpl ethPort =
                    (ConnectionTerminationPointEcDBImpl) ethernetPortEcFetcherF4.getMOByRef(neId,
                            getTransientSub(evtInfoNode.findPath(MocSyncRef.REFERENCE.substring(1)).asText()));

            modifyDescription(evtInfoNode, description, MocSyncRef.REFERENCE.substring(1), "Reference", ethPort != null ? ethPort.getShortDescription() : "None");
        }

        return description;
    }


    private void describeEventEntLgtn(EventDTO eventDTO, EcWebSocketNotification notification, EventTypeEc eventType) {
        JsonNode evtInfoNode = notification.getPduNode().path(EVENT_INFO);
        String username = evtInfoNode.findPath("intun").textValue();
        if (username != null) {
            addText(eventDTO, eventType.getDescription(), username);
        } else {
            eventDTO.setText(firstNonEmpty(eventType.getDescription(), "Logout"));
        }
    }

    private void describeEventEntDel(EventDTO eventDTO, EcWebSocketNotification notification, EventTypeEc eventType) {
        JsonNode usrInfoNode = notification.getPduNode().path("usri");
        String username = usrInfoNode.findPath("un").textValue();
        String entity = notification.getPduNode().path("ednm").textValue();
        if(entity != null) {
            addText(eventDTO, "Entity deleted ", entity);
        } else if (username != null) {
            addText(eventDTO, eventType.getDescription(), username);
        } else {
            eventDTO.setText(firstNonEmpty(eventType.getDescription(), "Entity Deletion"));
        }
        if(notification.getCimType().equals(EcModel.MocEqEq.CARD.CCLSTI.getCimType())) {
            eventDTO.setText("Card Cluster " + eventDTO.getText());
        }
    }

    private void describeEventTCA(EventDTO eventDTO, EcWebSocketNotification notification) {
        JsonNode evtInfoNode = notification.getPduNode().path(EVENT_INFO);
        JsonNode tcaData = evtInfoNode.findPath("tcadat");
        if (tcaData != null) {
            String binType = tcaData.findPath("bintv").asText();
            String conditionType = tcaData.findPath("condtyp").asText();
            String value = tcaData.findPath("monvl").asText();
            String threshold = tcaData.findPath("provth").asText();
            String description = conditionType + " threshold crossed (interval = " + binType + ", mon value = " + value + ", threshold value = " + threshold + ")";
            addText(eventDTO, description);
        } else addText(eventDTO, notification.getDescription());
    }

    private void describeEventMPLDCMPL(EventDTO eventDTO, EcWebSocketNotification notification) {
        JsonNode evtInfoNode = notification.getPduNode().path(EVTINF_NODE);
        JsonNode warnData = evtInfoNode.findPath(NeProfileF8Constants.WARN);
        StringBuilder eventDetails = new StringBuilder(notification.getDescription());
        if (warnData != null && !warnData.asText().isEmpty()) {
            eventDetails
              .append(" (")
              .append(warnData.asText())
              .append(")");
        }
        addText(eventDTO, eventDetails.toString());
    }

    private void setEventDescription(EventDTO eventDTO, EventTypeEc eventType, EcWebSocketNotification notification, String shortName) {
        EcWebSocketNotification.AosCategory category = notification.getCategory();

        if (shortName.equalsIgnoreCase(EcWebSocketNotification.EventTypeEcEnum.ENT_CONF.getName())
                || shortName.equalsIgnoreCase(EcWebSocketNotification.EventTypeEcEnum.ENT_STATE.getName())) {

            describeEventEntConfAndEntState(eventDTO, notification, eventType);
            updateSecurityEvent(category, notification, eventDTO);
        } else if (shortName.equalsIgnoreCase(EcWebSocketNotification.EventTypeEcEnum.STATE.getName())
                || shortName.equalsIgnoreCase(EcWebSocketNotification.EventTypeEcEnum.OPER_STATE.getName())) {

            describeEventStateAndOperState(eventDTO, notification);
        } else if (shortName.equalsIgnoreCase(EcWebSocketNotification.EventTypeEcEnum.ENT_ADD.getName())) {

            describeEventEntAdd(eventDTO, notification, eventType);
            updateSecurityEvent(category, notification, eventDTO);
        } else if (shortName.equalsIgnoreCase(EcWebSocketNotification.EventTypeEcEnum.ENT_LGTN.getName())) {

            describeEventEntLgtn(eventDTO, notification, eventType);
            updateSecurityEvent(category, notification, eventDTO);
        } else if (shortName.equalsIgnoreCase(EcWebSocketNotification.EventTypeEcEnum.ENT_DEL.getName())) {
            describeEventEntDel(eventDTO, notification, eventType);
            updateSecurityEvent(category, notification, eventDTO);
        } else if (shortName.equalsIgnoreCase(EcWebSocketNotification.EventTypeEcEnum.TCA.getName())) {
            describeEventTCA(eventDTO, notification);
            updateSecurityEvent(category, notification, eventDTO);
        } else if (shortName.equalsIgnoreCase(EcWebSocketNotification.EventTypeEcEnum.MPLDCMPL.getName())) {
            describeEventMPLDCMPL(eventDTO, notification);
            updateSecurityEvent(category, notification, eventDTO);
        } else if (shortName.equalsIgnoreCase(EcWebSocketNotification.EventTypeEcEnum.MPMDCHD.getName())) {
            addText(eventDTO, notification.getDescription(), getValueFromEvtInfForGivenMPMDCHDDescr(notification));
            updateSecurityEvent(category, notification, eventDTO);
        } else if (shortName.equalsIgnoreCase(EcWebSocketNotification.EventTypeEcEnum.DGENSTRT.getName())) {
            addText(eventDTO, eventType.getDescription());
            updateSecurityEvent(category, notification, eventDTO);
        }
        // continue to default handling
        else {
            eventDTO.setText(firstNonEmpty(notification.getDescription(), eventType.getDescription(), "Entity Configuration Change"));
            updateSecurityEvent(category, notification, eventDTO);
            if (category == EcWebSocketNotification.AosCategory.NA) {
                EventLogger.logWarnOnce(logSbi, EventLogger.createCacheKey(eventDTO.sourceNEType, category, eventType),
                        "EC-Notif unknown Category" + notification.getPdu(), eventDTO, null);
            }
            if (shortName.equalsIgnoreCase(EcWebSocketNotification.EventTypeEcEnum.NA.getName())) {
                EventLogger.logWarnOnce(logSbi, EventLogger.createCacheKey(eventDTO.sourceNEType, eventType),
                        "EC-Notif unknown Event-Type " + notification.getPdu(), eventDTO, null);
            }
        }
    }

    private String getValueFromEvtInfForGivenMPMDCHDDescr(EcWebSocketNotification notification) {
        Optional<String> foundValue = NeProfileF8Constants.MPMDCHD_VALUES.entrySet().stream()
          .filter(mpmdchdVal -> notification.getDescription().contains(mpmdchdVal.getKey()))
          .map(val -> notification.getEventInfo().get(val.getValue()).toString())
          .findFirst();

        return foundValue.orElse("");
    }

    public static Object getEnum(Class<? extends Enum<?>> type, String s) {
        try {
            for (Object constant : type.getEnumConstants()) {
                String name = (String) constant.getClass().getMethod(NAME).invoke(constant);
                if (name.equals(s)) {
                    return constant;
                }
            }
        } catch (Exception e) {

        }
        return null;
    }

    public static <T> T parseObject(Class<T> type, String s) {
        Object result = null;
        if (type == boolean.class || type == Boolean.class) {
            String uppercaseParameter = s.toUpperCase();
            if ("TRUE".equals(uppercaseParameter)) {
                result = true;
            } else if ("FALSE".equals(uppercaseParameter)) {
                result = false;
            } else {
                throw new IllegalArgumentException();
            }
        } else if (type == char.class || type == Character.class) {
            if (s.length() != 1) {
                throw new IllegalArgumentException();
            }
            result = s.charAt(0);
        } else if (type == byte.class || type == Byte.class) {
            try {
                result = Byte.valueOf(s);
            } catch (NumberFormatException e) {
                throw new IllegalArgumentException();
            }
        } else if (type == short.class || type == Short.class) {
            try {
                result = Short.valueOf(s);
            } catch (NumberFormatException e) {
                throw new IllegalArgumentException();
            }
        } else if (type == int.class || type == Integer.class) {
            try {
                result = Integer.valueOf(s);
            } catch (NumberFormatException e) {
                throw new IllegalArgumentException();
            }
        } else if (type == long.class || type == Long.class) {
            try {
                result = Long.valueOf(s);
            } catch (NumberFormatException e) {
                throw new IllegalArgumentException();
            }
        } else if (type == float.class || type == Float.class) {
            try {
                result = Float.valueOf(s);
            } catch (NumberFormatException e) {
                throw new IllegalArgumentException();
            }
        } else if (type == double.class || type == Double.class) {
            try {
                result = Double.valueOf(s);
            } catch (NumberFormatException e) {
                throw new IllegalArgumentException();
            }
        } else if (type == String.class) {
            result = EcModel.connectionStateMap.getOrDefault(s, s);
        } else if (type.isEnum()) {
            String enumValueName = s.toUpperCase().replace('-', '_');
            result = getEnum((Class<Enum<?>>) type, enumValueName);
        } else {
            throw new IllegalArgumentException();
        }
        return (T) result;
    }

    private String translateValue(Transients.Transient t, String text) {
        if (t != null) {
            // parse value
            try {
                Class c = Class.forName(t.getType());
                String text2 = parseObject(c, text).toString();
                if (text2 != null) {
                    text = text2;
                }
            } catch (Exception e) {
                // do not report this
            }
            // convert it if specified
            if (t.getConverter() != null) {
                try {
                    Class c = Class.forName(t.getConverter());
                    String text2 = (String) ((Converter) c.newInstance()).convert(text);
                    if (text2 != null) {
                        text = text2;
                    }
                } catch (Exception e) {
                    // do not report this
                }
            }
        }
        return text;
    }

    private void addText(EventDTO eventDTO, String name, String value) {
        if (!name.isEmpty() && !value.isEmpty()) {
            eventDTO.addText(name + "=" + value);
        }
    }

    private void addText(EventDTO eventDTO, String description) {
        if (!description.isEmpty()) {
            eventDTO.addText(description);
        }
    }

    private void addText(EventDTO eventDTO, String name, String value, Class<?> c) {
        // parse value
        try {
            String text2 = parseObject(c, value).toString();
            if (text2 != null) {
                value = text2;
            }
        } catch (Exception e) {
            // do not report this
        }
        if (!name.isEmpty())
            eventDTO.addText(name + "=" + value);
    }

    private String getText(String value, Class<?> c) {
        // parse value
        try {
            String text2 = parseObject(c, value).toString();
            if (text2 != null) {
                value = text2;
            }
        } catch (Exception e) {
            // do not report this
        }

        return value;
    }

    private String translateSST(Class<?> c, String value) {
        try {
            String text2 = parseObject(c, value).toString();
            if (text2 != null) {
                value = text2;
            }
        } catch (Exception e) {
            // do not report this
        }
        return value;
    }


    public static TextStringBuilder scaleSetpoint(TextStringBuilder setpoint) {
        try {
            float floatSetpoint = Float.parseFloat(setpoint.toString());
            float dividedSetpoint = floatSetpoint / SETPOINT_SCALE_FACTOR;
            return new TextStringBuilder(Float.toString(dividedSetpoint));
        } catch (NumberFormatException e) {
            return new TextStringBuilder("Invalid Setpoint value: " + setpoint);
        }
    }

  private void addText(EventDTO eventDTO, String name, JsonNode node, String pathElement) {
    TextStringBuilder sb = new TextStringBuilder();
    boolean isProgressBar = pathElement.equalsIgnoreCase("/pri/cur") || pathElement.equalsIgnoreCase("/pri/rem");
    if (node.isArray()) {
      for (JsonNode childNode : node) {
        sb.appendSeparator(",").append(childNode.asText());
      }
    } else {
        if (isIndexedPath(pathElement)) {
            sb.append(translateValue(getIndexedTransient(pathElement), node.asText()));
        } else {
            sb.append(translateValue(getTransient(pathElement), node.asText()));
        }
    }
    if (!name.isEmpty() && !sb.isEmpty()){
      // Defect raised on device team to send the unit of value in the update notification so that it can be added to event Description to make it more meaningful.
      // Device Defect : NGB-160566 - [ajob] AJob REST notification is missing a unit information
      //if(isProgressBar){
      //  eventDTO.addText(name + "=" + sb +"%");
      //}else{
        if (pathElement.equals(OPT_SET)) {
            sb = scaleSetpoint(sb);
        }
        eventDTO.addText(name + "=" + sb);
      //}
    }
  }
  private void handlePeplst(EventDTO eventDTO, String name, JsonNode node, String pathElement,  TextStringBuilder sb){

      if(node.asText().equals("remove")){
        String shortDesc = ManagedObjectDAO.getInstance().getMoViaEntityIndex(ServiceEndpointEcDBImpl.class, eventDTO.getNeId(), eventDTO.getObjectIndex()).getDisplayName();
        sb.append(name + " from: " + shortDesc);
        eventDTO.addText(sb.toString());
      }else{
        ManagedObjectDBImpl mo = ManagedObjectDAO.getInstance().findByUuid(node.textValue(), eventDTO.getNeId());
        sb.append(mo.getShortDescription());
        eventDTO.addText(name + "=" + sb);
      }
  }

    private String firstNonEmpty(String... values) {
        for (String value : values) {
            if (StringUtils.isNotEmpty(value))
                return value;
        }
        return null;
    }

    private List<String> getAOSCoreStateTypeEnumNames() {
        return Stream.of(AOSCoreStateType.values())
                .map(Enum::name)
                .collect(Collectors.toList());
    }

    private List<String> getAlarmProfileSeverityTypeEnumNames() {
        return Stream.of(AlarmProfileSeverityType.values())
                .map(Enum::name)
                .collect(Collectors.toList());
    }

    public static long getNeTimestamp(String timestamp) {
        try {
            if (timestamp.endsWith("Z") && timestamp.length() <= 25) {
                LocalDateTime dateTime = (LocalDateTime) dateTimeFormatter.parseBest(timestamp.substring(0, timestamp.length() - 1), LocalDateTime::from, LocalDate::from);
                return dateTime.toInstant(ZoneOffset.UTC).toEpochMilli();
            } else if (isLocalTimestamp(timestamp)) {
                return OffsetDateTime.parse(timestamp).toInstant().toEpochMilli();
            } else {
                log.error("unsupported time format for timestamp: " + timestamp);
                return NO_TIMESTAMP.value;
            }
        } catch (Exception e) {
            log.error("TrapProcessorF8.getNeTimestamp failed for " + timestamp + ", error: " + e.getMessage(), e);
            return NO_TIMESTAMP.value;
        }
    }

    private static String convertToHEX(String sc) {
        byte[] decoded = Base64.decodeBase64(sc);
        String hexString = Hex.encodeHexString(decoded);
        return hexString;
    }

    private static boolean isLocalTimestamp(String timestamp) {
        //example: 2022-04-13T11:52:35+02:00
        return timestamp.length() >= 25 && (timestamp.contains("+") || (timestamp.indexOf(":") < timestamp.lastIndexOf("-")));
    }

    public Transients getTransients() {
        return transients;
    }

    public List<Transients.Transient> getIndexedTransients() {
        return transients.getTransient().stream()
          .filter(Transients.Transient::getIndexed)
          .toList();
    }

    public Map<String, EventTypeEc> getCopyEcEventTypeMap() {
        return Objects.nonNull(ecEventTypeMap)
          ? Map.copyOf(ecEventTypeMap)
          : Collections.emptyMap();
    }

    public String getTransientSub(String jsonPath) {
        for (Transients.Transient t : getTransients().getTransient()) {
            if (jsonPath.startsWith(t.getJsonPath())) {
                if (jsonPath.length() >= t.getJsonPath().length() + 1) {
                    String varValue = jsonPath.substring(t.getJsonPath().length() + 1);
                    if (varValue.contains("/")) {
                        // continue parsing
                        int endIndex = varValue.indexOf("/");
                        String varSubValue = varValue.substring(endIndex);
                        String varStrValue = varValue.substring(0, endIndex);
                        return getTransientSubRec(varSubValue, t.getName() + " " + varStrValue);
                    }
                    return t.getName() + " " + varValue;
                } else {
                    return t.getName();
                }
            }
        }
        return jsonPath;
    }

    private Transients.Transient getTransient(String jsonPath) {
        for (Transients.Transient t : getTransients().getTransient()) {
            if (jsonPath.startsWith(t.getJsonPath())) {
                return t;
            }
        }
        return null;
    }
    private Transients.Transient getIndexedTransient(String jsonPath) {
        for (Transients.Transient t : getIndexedTransients()) {
            if (isPathMatchingIndexedTransient(jsonPath, t)) {
                return t;
            }
        }
        return null;
    }
    private boolean isIndexedPath(String jsonPath) {
        return jsonPath.matches(INDEXED_TRANSIENT_REGEX);
    }

    private boolean isPathMatchingIndexedTransient(String jsonPath, Transients.Transient t) {
        return jsonPath.matches(t.getJsonPath().replace("{}", "\\d+"));
    }

    public String getTransientSubRec(String jsonPath, String acu) {
        for (Transients.Transient t : getTransients().getTransient()) {
            if (jsonPath.startsWith(t.getJsonPath())) {
                if (jsonPath.length() >= t.getJsonPath().length() + 1) {
                    String varValue = jsonPath.substring(t.getJsonPath().length() + 1);
                    if (varValue.contains("/")) {
                        // continue parsing
                        int endIndex = varValue.indexOf("/");
                        String varSubValue = varValue.substring(endIndex);
                        String varStrValue = varValue.substring(0, endIndex);
                        return getTransientSubRec(varSubValue, acu + " " + t.getName() + " " + varStrValue);
                    }
                    return acu + " " + t.getName() + " " + varValue;
                } else {
                    return acu + " " + t.getName();
                }
            }
        }
        return acu;
    }

    public void retrieveEventTypesFromProvider() {
        final List<EventTypeEc> retrievedEventTypes = alarmsEventsNotificationsService.getEventsAsEventTypeEcList();
        Map<String, EventTypeEc> ecEventTypeMapPrepared = EventTypeConverter.getConvertedEvents(retrievedEventTypes);
        ecEventTypeMap.putAll(ecEventTypeMapPrepared);
        mergeF4EventsFromXml();
    }

    private void mergeF4EventsFromXml() {
        Map<String, EventTypeEc> f4Events = EventLoaderEc.readEventMap(eventMapF4XmlName);
        f4Events.forEach((key, value) -> {
            if (key.equals("n/a")) {
                return;
            }

            EventTypeEc eventTypeEc = f4Events.get(key);
            if (ecEventTypeMap.containsKey(key)) {
                eventTypeEc = ecEventTypeMap.get(key);
                eventTypeEc.getNeType().addAll(value.getNeType());
            }
            ecEventTypeMap.put(key, eventTypeEc);
        });
    }

    public void updateEventTypesFromNotification(List<EventTypeEc> retrievedEventTypes) {
        synchronized (this) {
            ecEventTypeMap.putAll(EventTypeConverter.getConvertedEvents(retrievedEventTypes));
        }
    }

    // 1. find the definition for path
    // 2. extract human friendly name
    // 3. extract from where the value is taken
    // 4. extract type of value
    // 5. if this is an enum find appropriate enum via reflection
    // 6. translate value to enum and get string for value from enum
    //     -> each enum needs to implement isMatchingValue(String), returns true if enum value matches string value, used to find matching enum

    // paths kept in xml
    // enums are java enums

}
