/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: kwitkowski
 */

package com.adva.nlms.mediation.config;

import com.adva.device_inventory.license_manager.api.in.DeviceLicenseManager;
import com.adva.nlms.common.NEUtils;
import com.adva.nlms.common.benchmark.Benchmark;
import com.adva.nlms.common.config.EntityIndex;
import com.adva.nlms.common.config.netypes.NEType;
import com.adva.nlms.common.config.netypes.NeTypeIds;
import com.adva.nlms.common.config.netypes.NeTypeString;
import com.adva.nlms.common.messages.ConfigChangeType;
import com.adva.nlms.common.messages.UpdateAction;
import com.adva.nlms.common.messages.UpdateState;
import com.adva.nlms.common.messages.UpdateStatus;
import com.adva.nlms.common.property.FNMPropertyConstants;
import com.adva.nlms.common.property.FNMPropertyFactory;
import com.adva.nlms.common.snmp.MDOperationFailedException;
import com.adva.nlms.mediation.bean.provider.api.BeanProvider;
import com.adva.nlms.mediation.common.AuthorizationException;
import com.adva.nlms.mediation.common.UntrustedCertificateException;
import com.adva.nlms.mediation.common.persistence.MDPersistenceContext;
import com.adva.nlms.mediation.common.transactions.InvalidPollingException;
import com.adva.nlms.mediation.common.transactions.NetTransactionManager;
import com.adva.nlms.mediation.config.dbconsistency.DBConsistencyCheckFactory;
import com.adva.nlms.mediation.config.entity.slot.SlotDAO;
import com.adva.nlms.mediation.config.fsp_r7.utils.MessageUtil;
import com.adva.nlms.mediation.config.neconfig.CommunicationProtocolsConfiguration;
import com.adva.nlms.mediation.config.polling.InventoryPollingParameters;
import com.adva.nlms.mediation.config.polling.StatusPollingParameters;
import com.adva.nlms.mediation.config.polling.managedobject.MODescCommandParameters;
import com.adva.nlms.mediation.config.resync.NetworkElementResyncManager;
import com.adva.nlms.mediation.config.resync.NetworkElementResyncManagerImpl;
import com.adva.nlms.mediation.config.sr.MDSRContext;
import com.adva.nlms.mediation.config.sr.SRLogger;
import com.adva.nlms.mediation.config.sr.SRLoggerCarrier;
import com.adva.nlms.mediation.config.sr.SRWorker;
import com.adva.nlms.mediation.event.message.MessageManager;
import com.adva.nlms.mediation.ne_comm.NetworkElementDiscovery;
import com.adva.nlms.mediation.ne_comm.SNMPCtrl;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPCommDownException;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPCommFailure;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPTimeOutException;
import com.adva.nlms.mediation.polling.PollingType;
import com.adva.nlms.mediation.polling.api.PollingFramework;
import com.adva.nlms.mediation.security.api.SecurityCtrl;
import com.adva.nlms.mediation.server.MDMessageSender;
import com.adva.nlms.mediation.server.StatusNotificationBroker;
import com.adva.nlms.mediation.server.spring.IDependencyFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.snmp4j.smi.OID;

import jakarta.annotation.Nullable;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

public class SynchronizationProcess implements SRLoggerCarrier {

  private static final Logger log = LoggerFactory.getLogger(SynchronizationProcess.class);

  private static final Logger SNMP_COMM_LOGGER = LoggerFactory.getLogger("com.adva.nlms.mediation.SNMPCommLogger");

  protected final DeviceLicenseHelper deviceLicenseHelper = new DeviceLicenseHelper();
  // ------------------------------------------------------------------------------------------------------------------

  public interface CheckBeforeAndProcessAfterActionContainer {
    /**
     * Method will be run just before whole inventory polling stuff
     */
    void check();

    /**
     * Method will be run after postProcessResync process
     */
    void process();
  }

  // ------------------------------------------------------------------------------------------------------------------
  // Dependencies
  // ------------------------------------------------------------------------------------------------------------------

  protected NetworkElementImpl networkElement;

  protected SynchronizationContainer synchronizationContainer;

  protected StatusNotificationBroker statusNotificationBroker;

  private SRWorker srWorker;

  protected ConfigCtrl configCtrl;

  private CommunicationProtocolsConfiguration communicationProtocolsConfiguration;

  private IDependencyFactory dependencyFactory;

  private SecurityCtrl securityCtrl;

  private EntityDAO entityDAO;

  private MDMessageSender mdMessageSender;

  // ------------------------------------------------------------------------------------------------------------------

  protected MessageUtil messageUtil;

  protected SynchronizationProcessState state = new SynchronizationProcessState();

  protected DeviceLicenseManager licenseManager;

  protected static final boolean LICENSE_CHECK_ENABLED =
      FNMPropertyFactory.getPropertyAsBoolean(FNMPropertyConstants.LICENSE_CHECK_ENABLED, FNMPropertyConstants.LICENSE_CHECK_ENABLED_DEFAULT);

  protected String identifierString;

  @Nullable
  protected NEType neType; // may be NULL for custom devices

  // ------------------------------------------------------------------------------------------------------------------

  public SynchronizationProcess(NetworkElementImpl networkElement, SynchronizationContainer synchronizationContainer,
                                StatusNotificationBroker statusNotificationBroker, SRWorker srWorker,
                                ConfigCtrl configCtrl, CommunicationProtocolsConfiguration communicationProtocolsConfiguration,
                                DBConsistencyCheckFactory dbConsistencyCheckFactory, IDependencyFactory dependencyFactory,
                                SecurityCtrl securityCtrl, EntityDAO entityDAO) {
    this.networkElement = networkElement;
    this.synchronizationContainer = synchronizationContainer;
    this.statusNotificationBroker = statusNotificationBroker;
    this.srWorker = srWorker;
    this.configCtrl = configCtrl;
    this.communicationProtocolsConfiguration = communicationProtocolsConfiguration;
    this.dependencyFactory = dependencyFactory;
    this.securityCtrl = securityCtrl;
    this.entityDAO = entityDAO;
    messageUtil = new MessageUtil(networkElement);
    this.mdMessageSender = BeanProvider.get().getBean(MDMessageSender.class);
    if (LICENSE_CHECK_ENABLED) { licenseManager = BeanProvider.get().getBean(DeviceLicenseManager.class); }
    identifierString = networkElement.getIpAddressNeNameIdentifier();
    int networkElementType = networkElement.getNetworkElementType();

    // take into account that there are magic ne type ids which should not be converted here at all
    // they mean custom devices, use NULL for them, it will not be used anyway since equipment licenses
    // are only supported for specific Adva devices that override initializeEquipmentCountOnDevice
    neType = networkElementType < NeTypeIds.CUSTOM_DEVICES_THRESHHOLD ? NEType.valueOf(networkElementType) : null;
  }

  // ------------------------------------------------------------------------------------------------------------------

  @MDSRContext
  public void run_Scan(NetworkElementResyncController.ResyncType resyncType, InventoryPollingParameters invParams) throws MDOperationFailedException, SNMPCommFailure, InvalidPollingException {
    try {
      networkElement.resyncLock();
      state.setResyncType(resyncType);
      state.setInvParams(invParams);
      setState(SynchronizationProcessStateType.Initialization);
      messageUtil.addMessageForDetailedInventory(MessageManager.Info, "Discovery has started");
      state.setT0(System.currentTimeMillis());
      if (log.isInfoEnabled()) {
        log.info("********** Synchronized part for IP " + networkElement.getIPAddress() + " started **********");
      }
      try {
        state.setCheckBeforeAndProcessAfterActionContainer(onBeforeResyncDiscoverPostProcessAction());
        if (state.getCheckBeforeAndProcessAfterActionContainer() != null) {
          state.getCheckBeforeAndProcessAfterActionContainer().check();
        }
        //1) Scan main NE
        state.setRm(createNetworkElementResyncManager());
        state.setEntityTypeMap(new HashMap<>(invParams.getEntityTypeMap()));
        setState(SynchronizationProcessStateType.RemoteScanning);
        state.setNeDiscovery(discoverMainNE(state.getEntityTypeMap(), state.getRm()));
        state.getNeDiscovery().initInvParams(state.getInvParams());
      } catch (Exception e) {
        onException(e);
        if (log.isInfoEnabled()) {
          log.info("********** Synchronized part for IP " + networkElement.getIPAddress() + " ended **********");
        }
        onEndResyncDiscoverPostProcessAction();
      }
    } finally {
      networkElement.resyncUnlock();
    }
  }

  // ------------------------------------------------------------------------------------------------------------------

  @MDSRContext
  public void run_Db() throws MDOperationFailedException, SNMPCommFailure, InvalidPollingException {
    try {
      networkElement.resyncLock();
      try {
        setState(SynchronizationProcessStateType.WritingToDatabase);
        doBeforeResync();
        resyncPostProcessResyncUpdate(state.getNeDiscovery(), state.getResyncType(), state.getEntityTypeMap(), state.getRm());
        doAfterResync();
        setState(SynchronizationProcessStateType.Finalization);
        if (state.getCheckBeforeAndProcessAfterActionContainer() != null) {
          state.getCheckBeforeAndProcessAfterActionContainer().process();
        }
        handleFirstTimeResync(state.getResyncType());
        messageUtil.addMessageForDetailedInventory(MessageManager.Ok, "Discovery has ended and it took " + ((float) (System.currentTimeMillis() - state.getT0()) / (float) 1000) + " sec");
      } catch (Exception e) {
        onException(e);
      } finally {
        if (log.isInfoEnabled()) {
          log.info("********** Synchronized part for IP " + networkElement.getIPAddress() + " ended **********");
        }
        onEndResyncDiscoverPostProcessAction();
      }
    } finally {
      networkElement.resyncUnlock();
    }
    onSuccessfullEndResyncDiscoverPostProcessAction(state.getResyncType(), state.getInvParams());
    setState(SynchronizationProcessStateType.Done);
  }

  // ------------------------------------------------------------------------------------------------------------------

  protected void doBeforeResync() {

  }

  // ------------------------------------------------------------------------------------------------------------------

  protected void doAfterResync() {

  }

  // ------------------------------------------------------------------------------------------------------------------

  public void run(NetworkElementResyncController.ResyncType resyncType, InventoryPollingParameters invParams)
    throws MDOperationFailedException, SNMPCommFailure, InvalidPollingException {
    run_Scan(resyncType, invParams);
    checkLicensesAndRunDb(resyncType);
  }

  protected void checkLicensesAndRunDb(NetworkElementResyncController.ResyncType resyncType) throws InvalidPollingException, MDOperationFailedException, SNMPCommFailure {
    if (resyncType == NetworkElementResyncController.ResyncType.FIRST && LICENSE_CHECK_ENABLED) {
      if (acquireLicensesForNE()) {
        try {
          run_Db();
        } catch (Exception ex) {
          if (getState().getStateType() == SynchronizationProcessStateType.Failed) {
            log.error("Returning license since discovery failed due to exception: {0}", ex);
            releaseLicenseForNE();
          }
        }
      }
    } else {
      run_Db();
    }
  }

  private NEType handleNEType(NetworkElementImpl networkElement) {

    if (networkElement.getNetworkElementType() != NEType.F3_EFM.getTypeId()) {
      return NEType.valueOf(this.networkElement.getNetworkElementType());
    }

    return networkElement.getNetworkElementTypeString().equals(NeTypeString.FSP_150CC_GE102PRO_EFMH)?NEType.GE102PRO_EFMH:NEType.F3_EFM;
  }

  private boolean acquireLicensesForNE() {
    return licenseManager.acquireLicensesForDevice(handleNEType(networkElement),
              this.networkElement.getIpAddressNeNameIdentifier());
  }

  private void releaseLicenseForNE() {
    licenseManager.returnLicensesForDevice(handleNEType(networkElement),
            this.networkElement.getIpAddressNeNameIdentifier());
  }

  protected void acquireLicensesForEquipmentWhenPollingFailed(Map<String, Integer> differenceMap, Map<String, Integer> equipmentCountOnDb, NEType neType, String identifierString) {
    List<EntityDBImpl> entitiesOnDBList = EntityDAO.getAllEntitiesForNetworkElement(networkElement.getID());
    Map<String, Integer> equipmentCountOnDbAfterFail = deviceLicenseHelper.getEquipmentCount(entitiesOnDBList,neType);
    Map<String, Integer> equipmentCountDifference = calculateDifference(equipmentCountOnDb, equipmentCountOnDbAfterFail);
    Map<String, Integer> equipmentToAcquireLicenceMap = calculateDifference(differenceMap, equipmentCountDifference);
    acquireLicensesForNewEquipmentWithDeficit(equipmentToAcquireLicenceMap,neType,identifierString);
  }

  protected void calculateAndReturnLicencesForEquipment(Map<String, Integer> equipmentCountOnDevice, NEType neType, String identifierString) {
    List<EntityDBImpl> entitiesOnDBList = EntityDAO.getAllEntitiesForNetworkElement(networkElement.getID());
    Map<String, Integer> equipmentCountOnDb = deviceLicenseHelper.getEquipmentCount(entitiesOnDBList,neType);
    Map<String, Integer> equipmentCountDifference = calculateDifference(equipmentCountOnDevice, equipmentCountOnDb);

    returnLicensesForEquipment(equipmentCountDifference,neType,identifierString);
  }

  protected boolean returnLicensesForEquipment(Map<String, Integer> equipment, NEType neType, String identifierString) {
    if(equipment.isEmpty()) {
      return true;
    } else
      return licenseManager.returnLicensesForEquipment(equipment,neType,identifierString);
  }

  protected boolean acquireLicensesForNewEquipmentWithDeficit(Map<String,Integer> equipment, NEType neType, String identifierString) {
    if(equipment.isEmpty()) {
      return true;
    } else
      return licenseManager.acquireLicensesForNewEquipmentWithDeficit(equipment,neType,identifierString);
  }

  protected boolean acquireLicensesForNewEquipmentWithoutDeficit(Map<String,Integer> equipment, NEType neType, String identifierString) {
    if(equipment.isEmpty()) {
      return true;
    } else
      return licenseManager.acquireLicensesForNewEquipmentWithoutDeficit(equipment,neType,identifierString);
  }

  protected Map<String, Integer> calculateDifference(Map<String, Integer> equipmentA, Map<String, Integer> equipmentB) {
    Map<String,Integer> differenceMap = new HashMap<>();
    int value;
    for (Map.Entry<String, Integer> entry : equipmentA.entrySet()) {
      value = entry.getValue() - (equipmentB.get(entry.getKey()) == null ? 0 : equipmentB.get(entry.getKey()));
      if(value > 0) {
        differenceMap.put(entry.getKey(), value);
      }
    }
    return differenceMap;
  }

  protected void acquireAndReturnLicenses(Map<String, Integer> equipmentCountOnDevice, Map<String, Integer> equipmentCountOnDb, Map<String, Integer> equipmentCountDifference, String identifierString, NEType neType) {
    if (getState().getStateType() == SynchronizationProcessStateType.Failed) {
      if(getState().getResyncType() == NetworkElementResyncController.ResyncType.FIRST) {
        //something went wrong during discovering NE, check difference between equipment on NE and equipment already persisted to db
        calculateAndReturnLicencesForEquipment(equipmentCountOnDevice, neType, identifierString);
      } else {
        //something went wrong during polling when NE is already discovered, check if there are entities which are not deleted for returned licences and acquire                      licences back for them. Then return licences for equipment not persisted to db.
        acquireLicensesForEquipmentWhenPollingFailed(equipmentCountDifference, equipmentCountOnDb, neType, identifierString);
        calculateAndReturnLicencesForEquipment(equipmentCountOnDevice, neType, identifierString);
      }
    }
  }

  // ------------------------------------------------------------------------------------------------------------------

  public NetworkElementImpl getNetworkElement() {
    return networkElement;
  }

  // ------------------------------------------------------------------------------------------------------------------

  public SynchronizationContainer getSynchronizationContainer() {
    return synchronizationContainer;
  }

  // ------------------------------------------------------------------------------------------------------------------

  public SRWorker getSrWorker() {
    return srWorker;
  }

  // ------------------------------------------------------------------------------------------------------------------

  public IDependencyFactory getDependencyFactory() {
    return dependencyFactory;
  }

  // ------------------------------------------------------------------------------------------------------------------

  public ConfigCtrl getConfigCtrl() {
    return configCtrl;
  }

  // ------------------------------------------------------------------------------------------------------------------

  public SecurityCtrl getSecurityCtrl() {
    return securityCtrl;
  }

  // ------------------------------------------------------------------------------------------------------------------

  protected boolean licensesReactOnEquipmentChanges() {
    return true;
  }

  // ------------------------------------------------------------------------------------------------------------------

  protected void licensesReactOnException(Throwable t) {

  }

  // ------------------------------------------------------------------------------------------------------------------

  @MDSRContext
  protected void onException(Throwable t) throws SNMPCommFailure, MDOperationFailedException, InvalidPollingException {
    setState(SynchronizationProcessStateType.Failed);
    if (t instanceof LicenseAcquiringException) {
      return;
    }
    if(LICENSE_CHECK_ENABLED) {
      licensesReactOnException(t);
    }
    if (t instanceof SNMPTimeOutException e) {
      log.error("SNMPTimeOutException exception occurred: {}", networkElement.getIPAddress(), e);
      SNMP_COMM_LOGGER.warn("SNMPTimeOutException occurred: ", e);
      messageUtil.addMessageForInventoryTab(MessageManager.Error, "Inventory polling has failed - SNMP timeout Exception occurred.");
      interruptedDiscoverPostProcessAction(e.getErrMessage());
      throw e;
    } else if (t instanceof SNMPCommDownException e) {
      log.error("SNMPCommDownException exception occurred: {}", networkElement.getIPAddress(), e);
      SNMP_COMM_LOGGER.warn("SNMPCommDownException occurred: ", e);
      messageUtil.addMessageForInventoryTab(MessageManager.Error, "Inventory polling has failed - SNMP Communication down.");
      interruptedDiscoverPostProcessAction(e.getErrMessage());
      throw e;
    } else if (t instanceof SNMPCommFailure e) {
      log.error("SNMPCommFailure exception occurred: {}", networkElement.getIPAddress(), e);
      messageUtil.addMessageForInventoryTab(MessageManager.Error, "Inventory polling has failed - SNMP Communication failure.");
      interruptedDiscoverPostProcessAction(e.getErrMessage());
      throw e;
    } else if (t instanceof MDOperationFailedException e) {
      log.error("MDOperationFailedException occurred {}", networkElement.getIPAddress(), e);
      interruptedDiscoverPostProcessAction(e.getMessage());
      throw e;
    } else if (t instanceof RuntimeException e) {
      log.error("RuntimeException occurred {}", networkElement.getIPAddress(), e);
      interruptedDiscoverPostProcessAction(e.toString());
      throw e;
    } else if (t instanceof InvalidPollingException e) {
      log.error("InvalidPollingException occurred {}", networkElement.getIPAddress(), e);
      interruptedDiscoverPostProcessAction(e.toString());
      throw e;
    } else if (t instanceof UntrustedCertificateException e) {
      log.error("UntrustedCertificateException occurred {}", networkElement.getIPAddress(), e);
      messageUtil.addMessageForInventoryTab(MessageManager.Error, "Inventory polling has failed - Untrusted certificate.");
      interruptedDiscoverPostProcessAction(e.toString());
      throw new MDOperationFailedException(e);
    } else if (t instanceof AuthorizationException e) {
      log.error("AuthorizationException occurred {}", networkElement.getIPAddress(), e);
      messageUtil.addMessageForInventoryTab(MessageManager.Error, "Inventory polling has failed - Authorization failed.");
      interruptedDiscoverPostProcessAction(e.toString());
      throw new MDOperationFailedException(e);
    } else if (t instanceof Exception e) {
      // XXX needed to catch this?? (HGR)
      log.error("NetworkElement.resync() ignored Exception:", e);
      interruptedDiscoverPostProcessAction(e.toString());
    }
  }

  // ------------------------------------------------------------------------------------------------------------------

  protected void setState(SynchronizationProcessStateType stateType) {
    state.setStateType(stateType);
  }

  // ------------------------------------------------------------------------------------------------------------------

  /**
   * Here you have define some steps which should be done before inventory polling action
   */
  protected CheckBeforeAndProcessAfterActionContainer onBeforeResyncDiscoverPostProcessAction() {
    srWorker.setDiscoveryState(DiscoveryState.IN_PROGRESS);
    return null;
  }

  // ------------------------------------------------------------------------------------------------------------------

  protected NetworkElementResyncManager createNetworkElementResyncManager() {
    return new NetworkElementResyncManagerImpl(getNetworkElement());
  }

  // ------------------------------------------------------------------------------------------------------------------

  @Benchmark
  @MDPersistenceContext
  private NetworkElementDiscovery discoverMainNE(final Map<Integer, List<OID>> entityTypeMap,
                                                 final NetworkElementResyncManager rm)
      throws SNMPCommFailure, MDOperationFailedException {

    messageUtil.addMessageForDetailedInventory(MessageManager.Info, "Reading data from the device has started");
    long t1 = System.currentTimeMillis();
    final NetworkElementDiscovery neDiscovery;
    final SNMPCtrl snmpCtrl;

    if (networkElement.hasSNMPCtrl()) {
      snmpCtrl = networkElement.getSNMPCtrl();
    } else if (networkElement.isPeer() && networkElement.getPeerMgr().getPeer() != null) {
      snmpCtrl = networkElement.getPeerMgr().getPeer().getSNMPCtrl();
    } else {
      snmpCtrl = null;
    }

    if (networkElement.isDiscovered()) {
      neDiscovery = configCtrl.getNECommCtrl().subsequentDiscovery(networkElement, entityTypeMap, snmpCtrl, rm);
      communicationProtocolsConfiguration.refresh(networkElement);
    } else {
      neDiscovery = configCtrl.getNECommCtrl().firstDiscovery(networkElement, snmpCtrl, rm);
    }
    long t2 = System.currentTimeMillis();
    messageUtil.addMessageForDetailedInventory(MessageManager.Info, "Reading data from the device has ended and it took " + ((float) (t2 - t1) / (float) 1000) + " sec");
    return neDiscovery;
  }

  // ------------------------------------------------------------------------------------------------------------------

  protected boolean resyncPostProcessResyncUpdate(NetworkElementDiscovery neDiscovery, NetworkElementResyncController.ResyncType resyncType,
                                             Map<Integer, List<OID>> entityTypeMap, NetworkElementResyncManager rm)
          throws MDOperationFailedException, InvalidPollingException, LicenseAcquiringException {
    messageUtil.addMessageForDetailedInventory(MessageManager.Info, "Resync network element has started");
    if(LICENSE_CHECK_ENABLED && !licensesReactOnEquipmentChanges()) {
      throw new LicenseAcquiringException();
    }
    neDiscovery.resyncNetworkElement();
    messageUtil.addMessageForDetailedInventory(MessageManager.Info, "Resync network element has ended");
    NetTransactionManager.isPollingValidNotification(networkElement);
    try {
      messageUtil.addMessageForDetailedInventory(MessageManager.Info, "Post process resync network element has started");
      postProcessResyncNE(resyncType, entityTypeMap, rm);
      messageUtil.addMessageForDetailedInventory(MessageManager.Info, "Post process resync network element has ended");
    } catch (MDOperationFailedException e) {
      messageUtil.addMessageForDetailedInventory(MessageManager.Warning, "Post process resync network element has failed");
      log.error("*  NetworkElementImpl.resyncDiscoverPostProcess() ignored" + e.getMessage() + " *");
      log.debug("* " + e.getMessage() + " *", e);
    }
    return true;
  }

  // ------------------------------------------------------------------------------------------------------------------

  protected void handleFirstTimeResync(final NetworkElementResyncController.ResyncType resyncType) {
    if (resyncType == NetworkElementResyncController.ResyncType.FIRST) {
      // register as trapsink!
      networkElement.checkSNMPTrapsinkRegistration();

      //register as bulktrap
      networkElement.enableBulkTrap();

      // notify client(s)!
      mdMessageSender.push(new UpdateStatus(UpdateAction.UPDATE_ACTION_RESYNC.getAsInt(), UpdateState.UPDATE_STATE_SUCCEEDED.getValue(), networkElement.getID()));
      networkElement.pushTopologyChange(ConfigChangeType.CHANGED);

      handleFirstTimeInvokeStatusPollingWithoutStatusFields();
      handleFirstTimeInvokeConfigurationPolling();
    }
  }

  protected void handleFirstTimeInvokeConfigurationPolling() {
    // admin state is not set for legacy devices during the inventory polling, adding it EntityDBImpl can cause many defcts
    // running configuration polling for legacy devices is the simples solution
  }

  protected void handleFirstTimeInvokeStatusPollingWithoutStatusFields() {
    boolean is2k3k = false;

    // update status!
    PollingFramework.inline(networkElement, PollingType.STATUS, new StatusPollingParameters(is2k3k));
  }

  // ------------------------------------------------------------------------------------------------------------------

  /**
   * Method which will do action when there was an exception during inventory polling.
   */
  protected void interruptedDiscoverPostProcessAction(String message) {
    srWorker.logSRExceptionAndSetDiscoveryState(message, networkElement.isDiscovered());
    messageUtil.addMessageForDetailedInventory(MessageManager.Error, "Inventory polling has failed");
  }

  // ------------------------------------------------------------------------------------------------------------------

  protected void onEndResyncDiscoverPostProcessAction() {
    clearDataMapsForNE();
    srWorker.setDiscoveryState(DiscoveryState.COMPLETED_SUCCESS);
    srWorker.setFinalDiscoveryState(srWorker.getDiscoveryState());
    networkElement.getPersistenceHelper().validateNEIdentifier();
  }

  // ------------------------------------------------------------------------------------------------------------------

  /**
   * Method which will do action at the end of inventory polling.
   *
   * @param resyncType
   * @param invParams
   */
  protected void onSuccessfullEndResyncDiscoverPostProcessAction(NetworkElementResyncController.ResyncType resyncType, InventoryPollingParameters invParams) {
    PollingFramework.commission(networkElement, PollingType.NOTIFY_INCOMPLETE_EVENTS);
      executeMOStringPolling(resyncType, invParams);
    if(NEUtils.isF3Device(networkElement.getNetworkElementType()) ||
            NEUtils.isF4Device(networkElement.getNetworkElementType())
    ){
      PollingFramework.commission(networkElement, PollingType.ETH_BANDWIDTH_FOR_NE);
    }
    if(resyncType == NetworkElementResyncController.ResyncType.FIRST){
      PollingFramework.commission(networkElement, PollingType.PTP_FLOW_POINT_ALIAS);
    }
  }

  // ------------------------------------------------------------------------------------------------------------------

    protected void postProcessResyncNE(final NetworkElementResyncController.ResyncType resyncType,
                                   final Map<Integer, List<OID>> entityTypeMap,
                                   final NetworkElementResyncManager rm)
      throws MDOperationFailedException, InvalidPollingException {

    boolean changed = postProcessResync(resyncType, entityTypeMap, rm);
    // ConfigData changed event.
    if (changed && resyncType != NetworkElementResyncController.ResyncType.FIRST) {
      networkElement.pushTopologyChange(ConfigChangeType.CHANGED);
    }
    if (!networkElement.isDiscovered()) {
      networkElement.setDiscovered();
      networkElement.activate();
    }
  }

  // ------------------------------------------------------------------------------------------------------------------

  protected void clearDataMapsForNE() {
    synchronizationContainer.clear();
  }

  // ------------------------------------------------------------------------------------------------------------------

  /**
   * Executes the polling to update descriptive strings on managed objects.
   * If hierarchical inventory polling is taking place we limit the domain of managed objects that get passed through to the SYNC MO polling.
   *
   * @param resyncType
   * @param invParams  inventory polling parameters
   */
  @MDPersistenceContext
  protected void executeMOStringPolling(NetworkElementResyncController.ResyncType resyncType, final InventoryPollingParameters invParams) {
    if (resyncType == NetworkElementResyncController.ResyncType.FIRST) {
      // if this is first discovery then we are sure that mo strings will be synchronized via commit listener.
      return;
    }
    PollingFramework.inline(networkElement, PollingType.SYNCH_MO_STRINGS, new MODescCommandParameters(networkElement.getID()));
  }

  // ------------------------------------------------------------------------------------------------------------------

  /**
   * Post processing of changed entities after resync.
   * 1) Removed entities post processing: remove the no longer existing entities
   * and the connected connections or set installation state, send update
   * events/server traps.
   * 2) New entities post processing: send update events/server traps.
   * 3) Reinserted entities post processing: set installation state, send update
   * events/server traps
   * 4) Event generation: Report changes by generating local events.
   *
   * @param resyncType    The type of resynchronization.
   * @param entityTypeMap Map of specific entity types ready to invoke with specific indices. Support only for inventory polling(hierarchical).
   * @return True if something has changed (update necessary).
   * @throws MDOperationFailedException The operation failed.
   */
  protected boolean postProcessResync(final NetworkElementResyncController.ResyncType resyncType,
                                      final Map<Integer, List<OID>> entityTypeMap,
                                      final NetworkElementResyncManager rm)
      throws MDOperationFailedException, InvalidPollingException {
    boolean changed = false;  // the return value

    try {
      networkElement.resyncLock();
      if (log.isInfoEnabled())
        log.info("NetworkElement." + this + ".postProcessResync(resyncType = " + resyncType + ")");


      //                  === (1) removed entities ===
      //
      // Remove the no longer existing entities and the connected connections (including
      // the references from Subnet and Line). Removed entities are all
      // entries in the previous entity list (not removed from the list during
      // resync).
      if (resyncType != NetworkElementResyncController.ResyncType.FIRST) {
        PostProcessResyncExecutor postProcessResyncRemovedEntitiesWorker = createPostProcessResyncRemovedEntitiesWorker(entityTypeMap);
        postProcessResyncRemovedEntitiesWorker.execute();
      }

      //                  === (2) new entities ===
      // Send update events/server traps if polling mode. All changes were done
      // before.
      PostProcessResyncExecutor postProcessResyncNewEntitiesWorker = createPostProcessResyncNewEntitiesWorker(resyncType);

      postProcessResyncNewEntitiesWorker.execute();

      //                  === (3) reinserted entities ===
      // Set installation state, send update events/server traps
      // All other changes were done before.
      if (resyncType != NetworkElementResyncController.ResyncType.FIRST) {
        createPostProcessResyncReinsertedEntitiesWorker(resyncType, entityTypeMap).execute();
      }
    } finally {
      networkElement.resyncUnlock();
    }

    return changed;
  }

  // ------------------------------------------------------------------------------------------------------------------

  /**
   * It create worker for post process resync of reinsertedentities.
   *
   * @param resyncType    The type of resync.
   * @param entityTypeMap Map of specific entity types ready to invoke with specific indices.
   *                      Support only for inventory polling(hierarchical).
   * @return worker
   */
  protected PostProcessResyncExecutor createPostProcessResyncReinsertedEntitiesWorker(final NetworkElementResyncController.ResyncType resyncType,
                                                                                      final Map<Integer, List<OID>> entityTypeMap) {
    return new PostProcessResyncReinsertedEntitiesWorker(networkElement, new GetEntitiesForHierarchyMapStrategy.GetOnlySpecificClasses(networkElement.getID(), entityTypeMap), createServerTrapFactory());
  }

  // ------------------------------------------------------------------------------------------------------------------

  protected PostProcessResyncExecutor createPostProcessResyncRemovedEntitiesWorker(final Map<Integer, List<OID>> entityTypeMap) {
    return dependencyFactory.autowireBean(new PostProcessResyncRemovedEntitiesWorker(
        configCtrl.getEvtMoNotifHdlr(),
        networkElement,
        new SlotDAO(),
        new GetEntitiesForHierarchyMapStrategy.GetOnlySpecificClasses(networkElement.getID(), entityTypeMap),
        createServerTrapFactory()));
  }

  // ------------------------------------------------------------------------------------------------------------------

  protected PostProcessResyncExecutor createPostProcessResyncNewEntitiesWorker(final NetworkElementResyncController.ResyncType resyncType) {

    return new PostProcessResyncNewEntitiesWorker(networkElement, resyncType,
        entityDAO, createServerTrapFactory());
  }

  // ------------------------------------------------------------------------------------------------------------------

  /**
   * Creates ServerTrapFactory.
   * This method has to be overridden if you want send create/delete serverTrap during inventory polling.
   *
   * @return ServerTrapFactory
   */
  protected ServerTrapFactory createServerTrapFactory() {
    return ServerTrapFactory.DO_NOT_SEND_TRAPS;
  }

  // ------------------------------------------------------------------------------------------------------------------

  public CommunicationProtocolsConfiguration getCommunicationProtocolsConfiguration() {
    return communicationProtocolsConfiguration;
  }

  // ------------------------------------------------------------------------------------------------------------------

  public EntityDAO getEntityDAO() {
    return entityDAO;
  }

  // ------------------------------------------------------------------------------------------------------------------

  @Override
  public SRLogger getSRLogger () {
    return networkElement.getSRLogger();
  }

  // ------------------------------------------------------------------------------------------------------------------

  public SynchronizationProcessState getState() {
    return state;
  }

  // ------------------------------------------------------------------------------------------------------------------

  protected List<EquipmentDBImpl> getEquipmentOnDB(Map<Integer, List<OID>> entityTypeMap) {
    if (!entityTypeMap.isEmpty()) {
      List<EquipmentDBImpl> entitiesOnDBList = new ArrayList<>();
      Set<OID> entitiesIndexesSet = entityTypeMap.values().stream().flatMap(Collection::stream).collect(Collectors.toSet());
      for (OID entityIndexOID : entitiesIndexesSet) {
        EntityDBImpl entityDB = EntityDAO.getByEntityIndex(networkElement.getID(), new EntityIndex(entityIndexOID.toDottedString()));
        if (entityDB instanceof EquipmentDBImpl) {
          entitiesOnDBList.add((EquipmentDBImpl) entityDB);
        }
      }
      return entitiesOnDBList;
    }
    return EntityDAO.getAllEquipment(networkElement.getID());
  }

  // ------------------------------------------------------------------------------------------------------------------

  protected void initializeEquipmentCountOnDb(Map<String, Integer> equipmentCountOnDb, Map<Integer, List<OID>> entityTypeMap) {
    List<EquipmentDBImpl> entitiesOnDBList = getEquipmentOnDB(entityTypeMap);
    equipmentCountOnDb.putAll(deviceLicenseHelper.getEquipmentCount((List) entitiesOnDBList, neType));
  }

  public boolean updateEquipmentLicenses(Map<String, Integer> equipmentCountOnDevice, Map<String, Integer> equipmentCountOnDb,
                                         Map<String, Integer> equipmentCountDifference) {
    Map<Integer, List<OID>> entityTypeMap = state.getEntityTypeMap();
    initializeEquipmentCountOnDevice(equipmentCountOnDevice);
    // Get licences for initial discovery
    if (getState().getResyncType() == NetworkElementResyncController.ResyncType.FIRST && LICENSE_CHECK_ENABLED) {
      if(!acquireLicensesForNewEquipmentWithoutDeficit(equipmentCountOnDevice,neType,identifierString)) {
        //licences not acquired, end
        return false;
      } // Get licences when NE already discovered
    } else if (getState().getResyncType() == NetworkElementResyncController.ResyncType.SUBSEQUENT && LICENSE_CHECK_ENABLED) {
      initializeEquipmentCountOnDb(equipmentCountOnDb, entityTypeMap);

      // Return licences
      Map<String, Integer> removedEquipmentCount = calculateDifference(equipmentCountOnDb, equipmentCountOnDevice);
      if (!removedEquipmentCount.isEmpty()) {
        returnLicensesForEquipment(removedEquipmentCount, neType, identifierString);
      }

      // Acquire licences
      Map<String, Integer> addedEquipmentCount = calculateDifference(equipmentCountOnDevice, equipmentCountOnDb);
      equipmentCountDifference.putAll(addedEquipmentCount);
      if(!addedEquipmentCount.isEmpty()) {
        if (!acquireLicensesForNewEquipmentWithDeficit(equipmentCountDifference,neType,identifierString)) {
          //something went wrong during getting licences, end
          return false;
        }
      }
    }
    return true;
  }

  protected void initializeEquipmentCountOnDevice(Map<String, Integer> equipmentCountOnDevice) {
    throw new UnsupportedOperationException("Licence check is not supported for device type: " + (neType != null ? neType.getName() : "Custom (" + networkElement.getNetworkElementType() + ")"));
  }

}
