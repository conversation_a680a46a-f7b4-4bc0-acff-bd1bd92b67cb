/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: jliu2
 */
package com.adva.nlms.mediation.sm.track.discovery;

import com.adva.common.model.template.Group;
import com.adva.common.model.template.Keyword;
import com.adva.common.model.template.Parameter;
import com.adva.common.model.template.ParameterGroup;
import com.adva.common.util.collect.Lists;
import com.adva.nlms.common.TopologyNodeType;
import com.adva.nlms.common.config.EntityIndex;
import com.adva.nlms.common.config.netypes.NEType;
import com.adva.nlms.common.config.netypes.NeTypeIds;
import com.adva.nlms.common.messages.ConfigChangeType;
import com.adva.nlms.common.messages.ProgressStatusType;
import com.adva.nlms.common.mltopologymodel.enums.MLNewSecondaryLifecycleState;
import com.adva.nlms.common.paging.PagingRestriction;
import com.adva.apps.sm.Definition;
import com.adva.apps.sm.ServiceLayer;
import com.adva.nlms.common.sm.ServiceNodeProperties;
import com.adva.nlms.common.sm.ServiceOperationalState;
import com.adva.nlms.common.sm.ServiceTemplateHelper;
import com.adva.nlms.common.sm.ServiceView;
import com.adva.nlms.common.sm.TraversalResult;
import com.adva.nlms.common.sm.explorepath.ExploreServicesState;
import com.adva.nlms.common.sm.explorepath.ExploreState;
import com.adva.nlms.common.sm.explorepath.ReExploreServiceFilter;
import com.adva.nlms.common.snmp.MDOperationFailedException;
import com.adva.nlms.common.snmp.MIB;
import com.adva.nlms.common.snmp.MIBFSP_R7Enums;
import com.adva.nlms.common.snmp.NoSuchMDObjectException;
import com.adva.nlms.mediation.bean.provider.api.BeanProvider;
import com.adva.nlms.mediation.common.persistence.MDPersistenceContext;
import com.adva.nlms.mediation.common.persistence.MDPersistenceHelper;
import com.adva.nlms.mediation.common.persistence.MDPersistenceManager;
import com.adva.nlms.mediation.common.persistence.MDTransactional;
import com.adva.nlms.mediation.config.EntityDAO;
import com.adva.nlms.mediation.config.EntityDBImpl;
import com.adva.nlms.mediation.config.ManagedObjectDAO;
import com.adva.nlms.mediation.config.ManagedObjectDBImpl;
import com.adva.nlms.mediation.config.ModuleDBImpl;
import com.adva.nlms.mediation.config.NetworkElementDAO;
import com.adva.nlms.mediation.config.NetworkElementDBImpl;
import com.adva.nlms.mediation.config.NoSuchNetworkElementException;
import com.adva.nlms.mediation.config.PortDBImpl;
import com.adva.nlms.mediation.config.UnmanagedObjectDBImpl;
import com.adva.nlms.mediation.config.fsp1500.CPEUnitDBImpl;
import com.adva.nlms.mediation.config.fsp1500.NetworkElementFSP1500DBImpl;
import com.adva.nlms.mediation.config.fsp_r7.NetworkElementFSP_R7DBImpl;
import com.adva.nlms.mediation.config.fsp_r7.entity.eci.AbstractExternalChannelDBImpl;
import com.adva.nlms.mediation.config.fsp_r7.entity.equipment.module.ModuleFSP_R7CMDBImpl;
import com.adva.nlms.mediation.config.fsp_r7.entity.equipment.module.ModuleFSP_R7DBImpl;
import com.adva.nlms.mediation.config.fsp_r7.entity.facility.physical.PortFSP_R7ClientDBImpl;
import com.adva.nlms.mediation.config.fsp_r7.entity.facility.physical.PortFSP_R7DBImpl;
import com.adva.nlms.mediation.config.fsp_r7.entity.facility.physical.PortFSP_R7NetworkDBImpl;
import com.adva.nlms.mediation.config.fsp_r7.entity.facility.physical.PortFSP_R7TTClientDBImpl;
import com.adva.nlms.mediation.config.fsp_r7.entity.fibermap.IntraNeConnectionDAO;
import com.adva.nlms.mediation.config.fsp_r7.entity.fibermap.IntraNeConnectionDBImpl;
import com.adva.nlms.mediation.config.fsp_r7.entity.fibermap.TerminationPointFSP_R7DBImpl;
import com.adva.nlms.mediation.config.fsp_r7.entity.fibermap.TerminationPointsDao;
import com.adva.nlms.mediation.config.fsp_r7.topology.discovery.LinkMismatchAlarmsHelper;
import com.adva.nlms.mediation.config.fsp_r7.utils.F7Utils;
import com.adva.nlms.mediation.config.unmanaged.dwdm.model.UnmanagedPortDBImpl;
import com.adva.nlms.mediation.config.unmanaged.dwdm.model.UnmanagedTerminationPointDBImpl;
import com.adva.nlms.mediation.event.message.MessageManager;
import com.adva.nlms.mediation.mltopologymodel.api.MLServiceManagementFacade;
import com.adva.nlms.mediation.mltopologymodel.core.concurrent.api.MLTaskExecutorService;
import com.adva.nlms.mediation.mltopologymodel.model.dao.MLTopologyElementDAO;
import com.adva.nlms.mediation.mltopologymodel.model.db.MLConnectionDBImpl;
import com.adva.nlms.mediation.mltopologymodel.model.db.MLConnectionPointDBImpl;
import com.adva.nlms.mediation.mltopologymodel.model.db.MLServiceDBImpl;
import com.adva.nlms.mediation.mltopologymodel.model.db.MLTopologyElementDBImpl;
import com.adva.nlms.mediation.mltopologymodel.model.db.MLTrailDBImpl;
import com.adva.nlms.mediation.mltopologymodel.model.interfaces.MLConnection;
import com.adva.nlms.mediation.mltopologymodel.model.interfaces.MLTopologyElement;
import com.adva.nlms.mediation.mltopologymodel.model.type.MLMoReferenceType;
import com.adva.nlms.mediation.mltopologymodel.model.type.MLNetworkLayer;
import com.adva.nlms.mediation.mltopologymodel.model.type.MLTEPropertyKey;
import com.adva.nlms.mediation.mltopologymodel.model.type.MLTopologyServiceMoReference;
import com.adva.nlms.mediation.mltopologymodel.mofacade.MLTopologyResyncFacade;
import com.adva.nlms.mediation.mltopologymodel.momediation.proc.MLTopologyMoFacade;
import com.adva.nlms.mediation.mltopologymodel.resources.MLServiceHelper;
import com.adva.nlms.mediation.mltopologymodel.service.intent.implementation.db.ServiceIntent;
import com.adva.nlms.mediation.mltopologymodel.service.intent.implementation.db.ServiceIntentDBImpl;
import com.adva.nlms.mediation.mltopologymodel.servicetopologychecker.MLTopologyCheckerUtils;
import com.adva.nlms.mediation.mltopologymodel.sync.entity.task.ResyncModuleTask;
import com.adva.nlms.mediation.security.session.SessionHdlr;
import com.adva.nlms.mediation.server.MDMessageSender;
import com.adva.nlms.mediation.sm.ClassicServicePageHdlrImpl;
import com.adva.nlms.mediation.sm.ConnectionHelper;
import com.adva.nlms.mediation.sm.SMServiceHelper;
import com.adva.nlms.mediation.sm.SMTrackExplorePathServiceDefinitionCreator;
import com.adva.nlms.mediation.sm.SMTrackServiceManager;
import com.adva.nlms.mediation.sm.ServiceManagerBuilder;
import com.adva.nlms.mediation.sm.ServiceManagerCtrlImpl;
import com.adva.nlms.mediation.sm.ServiceManagerFacade;
import com.adva.nlms.mediation.sm.ServiceManagerHelper;
import com.adva.nlms.mediation.sm.ServiceMessageManager;
import com.adva.nlms.mediation.sm.dao.ConnInfoDAO;
import com.adva.nlms.mediation.sm.dao.ConnectionDAO;
import com.adva.nlms.mediation.sm.exception.SMException;
import com.adva.nlms.mediation.sm.exception.SMProvisioningException;
import com.adva.nlms.mediation.sm.exception.SMValidationCondition;
import com.adva.nlms.mediation.sm.exception.SMValidationException;
import com.adva.nlms.mediation.sm.helper.EchOcsHelper;
import com.adva.nlms.mediation.sm.model.AbstractConnectionDBImpl;
import com.adva.nlms.mediation.sm.model.AbstractProtOChConnectionDBImpl;
import com.adva.nlms.mediation.sm.model.ConnectionDBImpl;
import com.adva.nlms.mediation.sm.model.ContractDBImpl;
import com.adva.nlms.mediation.sm.model.CustomerDBImpl;
import com.adva.nlms.mediation.sm.model.CustomerHandlerImpl;
import com.adva.nlms.mediation.sm.model.CustomerServiceGroupDBImpl;
import com.adva.nlms.mediation.sm.model.OChConnectionDBImpl;
import com.adva.nlms.mediation.sm.model.ODSConnectionDBImpl;
import com.adva.nlms.mediation.sm.model.SubChConnectionDBImpl;
import com.adva.nlms.mediation.sm.notification.Notifier;
import com.adva.nlms.mediation.sm.operationalStatus.OperationalStatusHandler;
import com.adva.nlms.mediation.sm.operationalStatus.OperationalStatusHandlerImpl;
import com.adva.nlms.mediation.sm.prov.OpticalDataService;
import com.adva.nlms.mediation.sm.prov.TransportService;
import com.adva.nlms.mediation.sm.servicewizardsupport.ServiceWizardLoadFacade;
import com.adva.nlms.mediation.sm.topology.ServiceTopologyHelper;
import com.adva.nlms.mediation.sm.track.discovery.exceptions.TrackServiceDiscoveryException;
import com.adva.nlms.mediation.sm.track.discovery.utils.ProtMechanismUtils;
import com.adva.nlms.mediation.sm.utils.ParameterUtils;
import com.adva.nlms.mediation.sm.validation.interfaces.TraversalStopRule;
import com.adva.nlms.mediation.sm.validation.interfaces.TraversalStopRules;
import com.adva.nlms.mediation.topology.LineDBImpl;
import com.adva.nlms.mediation.topology.LineDao;
import com.adva.nlms.mediation.topology.TopologyNodeDBImpl;
import com.adva.nlms.mediation.topology.VirtualOtnPortDBImpl;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringEscapeUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import jakarta.validation.constraints.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import jakarta.inject.Singleton;
import jakarta.persistence.EntityManager;
import jakarta.persistence.EntityNotFoundException;
import jakarta.persistence.Query;
import java.io.BufferedOutputStream;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.TreeMap;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * The central class for handling explore path related functionalities.
 */
@Component
@Singleton
public class SMExploreServicesHandler {
  private static final Logger log = LogManager.getLogger(SMExploreServicesHandler.class);
  private static final Logger LOGGER_FOR_PARAMETER_GROUP = LogManager.getLogger("LOGGER_FOR_PARAMETER_GROUP");
  @Autowired
  private ServiceManagerCtrlImpl serviceManagerCtrl;
  @Autowired
  private SMServiceHelper smServiceHelper = null;
  @Autowired
  @Qualifier("smTrackExportPathServiceManager")
  private SMTrackExplorePathServiceDefinitionCreator smTrackExplorePathServiceDefinitionCreator;
  @Autowired
  private SMTrackServiceDiscoveryManager smTrackServiceDiscoveryManager;
  @Autowired
  private CustomerHandlerImpl customerHandler;
  @Autowired
  private LineDao lineDAO;
  @Autowired
  private NetworkElementDAO networkElementDAO;
  @Autowired
  private ConnectionHelper connectionHelper;
  @Autowired
  private SMTrackExplorePathConnectionBuilder smExploreConnectionBuilder;
  @Autowired
  @Qualifier ("smTrackService")
  private SMTrackServiceManager smTrackServiceManager;
  @Autowired
  private TerminationPointsDao terminationPointsDao;
  @Autowired
  private MLTaskExecutorService mlTaskExecutorService;
  @Autowired
  private SessionHdlr sessionHdlr;
  @Autowired
  private MDMessageSender mdMessageSender;

  @Autowired
  private ConnInfoDAO connInfoDAO;

  @Autowired
  private ConnectionDAO connectionDAO;

  @Autowired
  private Notifier notifier;

  @Autowired
  private ManagedObjectDAO  managedObjectDAO;

  @Autowired
  private MessageManager messageManager;

  @Autowired
  private LinkMismatchAlarmsHelper linkMismatchAlarmsHelper;

  @Autowired
  private OperationalStatusHandler operationalStatusHandler;

  @Autowired
  private ServiceManagerFacade serviceManagerFacade;
  @Autowired
  private ProtMechanismUtils protMechanismUtils;
  @Autowired
  private MLTopologyElementDAO mlTopologyElementDAO;

  @Autowired
  private MLServiceHelper mlServiceHelper;

  @Autowired @Qualifier("classicServicePageHdlr")
  private ClassicServicePageHdlrImpl servicePageHdlr;
  @Autowired
  private ServiceMessageManager serviceMessageManager;

  @Autowired
  @Qualifier("ServiceWizardLoadHdlrImpl")
  private ServiceWizardLoadFacade serviceWizardLoadHdlr;

  // create an instance whenever we need it, avoid use it directly, call getSMExplorePathFromMLTrailBuilder() instead
  private SMExplorePathFromMLTrailBuilder smExplorePathFromMLTrailBuilder;

  private static final String reportName= "var" + File.separator + "explorePathServices" + File.separator +"explorePath_report.csv";
  private static final int REEXPLORE_TIMEOUT_IN_MSECS = 60000;

  public void exploreServicesForNEs(Set<Integer> neIDs, boolean allNEs) {
    deleteUnManagedServices(neIDs, allNEs);
    cleanupReportFile();
    try (FileOutputStream report = new FileOutputStream(reportName);
         BufferedOutputStream reportStream = new BufferedOutputStream(report);
         OutputStreamWriter outputStreamWriter=new OutputStreamWriter(reportStream,"UTF-8");
         BufferedWriter reportWriter = new BufferedWriter(outputStreamWriter)
    ) {
      writeReportHeader(reportWriter);
      for (int neID : neIDs)
        getExploreServices(neID,reportWriter);
      log.info("EXPLORE PATH on all NEs is completed.");
    } catch (IOException e) {
      log.error("Report failure", e);
    }


  }

  private boolean cleanupReportFile() {
    File file=new File(reportName);
    if (file.exists()){
      return file.delete();
    }else
    {
        return file.getParentFile().mkdirs();
    }
  }

  private void writeReportHeader(BufferedWriter reportStream) throws IOException {
    String header="Start Node Name,Start Node NEID,Start Port AID,Error,Relative path to the report file\n";
    reportStream.write(header);
    reportStream.flush();
  }

  @MDPersistenceContext
  public void deleteUnManagedServices(Set<Integer> neIDs, boolean allNEs) {
    EntityManager em = MDPersistenceManager.current();
    Query query;
    String queryStr = "SELECT e.id FROM " + SubChConnectionDBImpl.class.getSimpleName()
      + " e WHERE e.isManaged = false and e.setupMode = " + Definition.ServiceCreationMode.TRACK_MODE.getId();
    if (!allNEs && neIDs.size() > 0) {
      queryStr += " and (e.startNEID IN :hashSet or e.peerNEID IN :hashSet)";
      query = em.createQuery(queryStr);
      query.setParameter("hashSet", neIDs);
    } else
      query = em.createQuery(queryStr);

    List<Integer> subConnIDs = (List<Integer>) (query.getResultList());
    log.info("Found " + subConnIDs.size() + " unmanaged service ");
    for (Integer id : subConnIDs) {
      log.info("Deleting unmanaged service: " + id);
      try {
        serviceManagerCtrl.deleteService(id, false);
      } catch (MDOperationFailedException e) {
        log.error("Failed to delete unmanaged service: " + id, e);
      }
    }
  }

  /**
   * Trigger explore path for services on a particular NE or on all NEs in the database; when neId is -1, it will
   * explore service paths for all NEs.
   *
   * @param neId network element id
   */
  public void clearUnmanagedServicesForNEs(int neId) {
    Set<Integer> neIDs = new HashSet<>();
    if (neId == -1)
      neIDs = getAllExploreNEIDs();
    else
      neIDs.add(neId);

    deleteUnManagedServices(neIDs, neId == -1);
  }

  @MDPersistenceContext
  private Set<Integer> getAllExploreNEIDs() {
    // explore for all NEs (FSP_R7 & FSP_1500)
    Set<Integer> neIDs = new HashSet<>();
    Set<NetworkElementFSP_R7DBImpl> neDBList = MDPersistenceHelper.queryByParam(NetworkElementFSP_R7DBImpl.class);
    neIDs.addAll(neDBList.stream().map(TopologyNodeDBImpl::getId).collect(Collectors.toList()));
    Set<NetworkElementFSP1500DBImpl> ne1500DBList = MDPersistenceHelper.queryByParam(NetworkElementFSP1500DBImpl.class);
    neIDs.addAll(ne1500DBList.stream().map(TopologyNodeDBImpl::getId).collect(Collectors.toList()));
    return neIDs;
  }

  /**
   * Trigger explore path for services on a particular NE or on all NEs in the database; when neId is -1, it will
   * explore service pathes for all NEs.
   *
   * @param neId network element id
   */
  public void exploreServicesForNEs(int neId) {
    Set<Integer> neIDs = new HashSet<>();
    if (neId == -1)
      neIDs = getAllExploreNEIDs();
    else
      neIDs.add(neId);

    exploreServicesForNEs(neIDs, neId == -1);
  }


  /**
   * Explore services for a network element;
   *
   * @param neId network element id
   * @param reportWriter Generic report input stream
   */
  @MDPersistenceContext
  private void getExploreServices(int neId, BufferedWriter reportWriter) {
    NetworkElementDBImpl ne = networkElementDAO.getNEDBImplIns(neId);
    NEType productType = getNeType(neId);
    if (!(productType == NEType.FSP_3000R7 || (productType != null && productType.getTypeId() == NeTypeIds.NETWORK_ELEMENT_TYPE_FSP1500))) {
      log.info("Explore path on network element (" + neId + ") does not supported - product type: " + productType);
      return;
    }

    long start = System.currentTimeMillis();
    String neName = ne.getName();
    log.info("++++++ START Explore Service on ne: " + neName + "(" + neId + ")++++++");
    Comparator<PortDBImpl> portCompare = (instance1, instance2) -> EntityIndex.compare(instance1.getEntityIndex(), instance2.getEntityIndex());
    TreeMap<PortDBImpl, Integer> portsToExplore = new TreeMap<>(portCompare);
    TreeMap<PortDBImpl, String> portsFailed = new TreeMap<>(portCompare);
    // collect the ports
    if (productType.getTypeId() == NeTypeIds.NETWORK_ELEMENT_TYPE_FSP1500)
      getFSP1500PortsToExplore(neId, neName, portsToExplore);
    else
      getF7PortsToExplore(neId, neName, portsToExplore);

    // explore services
    getExploreServicesOnClientPorts(neId, neName, productType, portsToExplore, portsFailed);

    // report
    reportResult(neId, neName, productType, portsToExplore, portsFailed, start, reportWriter);
  }

  private void getFSP1500PortsToExplore(int neId, String neName, TreeMap<PortDBImpl, Integer> portsToExplore) {
    final Set<CPEUnitDBImpl> modules = EntityDAO.get(CPEUnitDBImpl.class, neId);
    for (CPEUnitDBImpl module : modules) {
      // local ports
      List<PortDBImpl> ports = module.getPorts(MIB.If.TYPE_GPI_TTP);
      // go through client ports;
      for (PortDBImpl port : ports) {
        if (port.getAdminState() == MIB.If.ADMIN_STATUS_UP) {
          TransportService inService = connInfoDAO.getEndPointConnection(port.getContainingModule(), port);
          if (inService == null)
            portsToExplore.put(port, 0);
          else
            log.info("++++++ Explore Service on port: " + neName + "(" + neId + ") " + getPortName(port) +
              " Service exists: " + inService.getName() + "++++++");
        }
      }
    }
  }

  private String getPortName(PortDBImpl port) {
    if (port.getAidString() == null || port.getAidString().isEmpty())
      return port.getFullDesc(); // FSP1500 port does not have aid
    else
      return port.getAidString();
  }

  private String getAdminState(PortDBImpl port, NEType neType) {
    if (neType == NEType.FSP_3000R7) {
      if (port.getAdminState() == MIBFSP_R7Enums.EntityState.ADMIN_STATE_IS)
        return "ADMIN_STATE_IS";
      else if (port.getAdminState() == MIBFSP_R7Enums.EntityState.ADMIN_STATE_AINS)
        return "ADMIN_STATE_AINS";
    } else if (neType.getTypeId() == NeTypeIds.NETWORK_ELEMENT_TYPE_FSP1500) {
      if (port.getAdminState() == MIB.If.ADMIN_STATUS_UP)
        return "ADMIN_STATUS_UP";
    }
    return "" + port.getAdminState();
  }

  private void getF7PortsToExplore(int neId, String neName, TreeMap<PortDBImpl, Integer> portsToExplore) {
    final Set<PortFSP_R7DBImpl> ports = EntityDAO.get(PortFSP_R7DBImpl.class, neId);
    for (PortDBImpl port : ports) {
      if (isValidClientPortToExplore(port)) {
        TransportService inService = connInfoDAO.getEndPointConnection(port.getContainingModule(), port);
        if (inService == null)
          portsToExplore.put(port, 0);
        else
          log.info("++++++ Explore Service on port: " + neName + "(" + neId + ") " + getPortName(port) +
            " Service exists: " + inService.getName() + "++++++");
      }
    }
  }

  @MDPersistenceContext
  public boolean isValidClientPortToExplore(PortDBImpl port){
      if((port.getAdminState() == MIBFSP_R7Enums.EntityState.ADMIN_STATE_IS ||
              port.getAdminState() == MIBFSP_R7Enums.EntityState.ADMIN_STATE_AINS) &&
              (port instanceof PortFSP_R7ClientDBImpl ||
                      port instanceof PortFSP_R7TTClientDBImpl)){
          ModuleDBImpl module = port.getContainingModule();
          if(module != null && module.isChannelModule() && module instanceof ModuleFSP_R7DBImpl){
              if(module instanceof ModuleFSP_R7CMDBImpl && ((ModuleFSP_R7CMDBImpl)module).isRegenerator())
                  return false;
              else if(((ModuleFSP_R7DBImpl)module).getDeploy() == MIBFSP_R7Enums.DeployProvModule.DEPLOY_PASS_THROUGH ||
                      ((ModuleFSP_R7DBImpl)module).getDeploy() == MIBFSP_R7Enums.DeployProvModule.DEPLOY_BACK_TO_BACK)
                  return false;
              else
              {
                  TerminationPointFSP_R7DBImpl ptp = terminationPointsDao.getPtpByIndexOfRelatedEntityIns(port.getNeID(), port.getEntityIndex());
                  if(ptp != null) {
                      // if it has any intra connection on client ptp, then we don't do explore path
                    List<IntraNeConnectionDBImpl> conns = IntraNeConnectionDAO.getIntraNeConnsByPtpAid(ptp.getAidString(), ptp.getNeID());
                    if(conns.size() > 0) {
                      // but if intra connection to Y-cable, this could be CCCP, we should still explore
                      for(IntraNeConnectionDBImpl intraNeConnectionDB:conns) {
                        TerminationPointFSP_R7DBImpl ptp1 = null;
                        if (ptp.getAidString().equals(intraNeConnectionDB.getEntityAEndpointAidString())) {
                          ptp1 = intraNeConnectionDB.getEntityEndpointB();
                        } else {
                          ptp1 = intraNeConnectionDB.getEntityEndpointA();
                        }
                        if(ptp1 == null || !ptp1.getAidString().contains("-Y-"))
                            return false;
                      }
                    }
                      // no any port to port link either
                      LineDBImpl line = lineDAO.getLinesByLinePort(port.getNeID(), port.getEntityIndex());
                      LineDBImpl line2 = lineDAO.getLinesByLinePort(ptp.getNeID(), ptp.getEntityIndex());
                      return line == null && line2 == null;
                  }
              }
              return true;
          }
      }
      return false;
  }

  private void getExploreServicesOnClientPorts(int neId, String neName, NEType neType,
                                               TreeMap<PortDBImpl, Integer> portsToExplore, TreeMap<PortDBImpl, String> portsFailed) {
    // go through client ports;
    for (PortDBImpl port : portsToExplore.keySet()) {
      log.info("++++++ Starting Explore Service on port: " + neName + "(" + neId + ") " + getPortName(port) + "++++++");
      // there might be a protected service already explored on this port, no need to do it again
      TransportService inService = connInfoDAO.getEndPointConnection(port.getContainingModule(), port);
      if (inService != null) {
        portsToExplore.put(port, inService.getId());
        continue;
      }
      try {
        CustomerDBImpl customer = customerHandler.getCreateUnassignedCustomer();
        String customerName = customer.getName();
        int customerId = customer.getId();
        String serviceName = ServiceManagerBuilder.getName(ServiceLayer.ODS);
        int serviceid = getExploreService(serviceName, neId, neType, port.getEntityIndex(), customerName, customerId, port, portsFailed);
        portsToExplore.put(port, serviceid);
      } catch (Exception e) {
        log.error("Explore service failed for port - " + neId + " " + getPortName(port) + " - " + e.getMessage());
      }
    }
  }

  private void reportResult(int neId, String neName, NEType neType, TreeMap<PortDBImpl, Integer> portsToExplore, TreeMap<PortDBImpl, String> portsFailed, long start, BufferedWriter reportWriter) {
    // report results
    String fileName="var" + File.separator + "explorePathServices" + File.separator +"explorePath_" + neName + "_" + neId + ".html";
    StringBuilder htmlMsg = new StringBuilder("");
    int success = 0;
    for (PortDBImpl port : portsToExplore.keySet()) {
      if (portsToExplore.get(port) != 0) {
        log.info("++++++ Explore Service on NE: " + neName + "(" + neId + ") " + getPortName(port) + " found services: " + portsToExplore.get(port) + "++++++");
        success++;
      } else {
        String msg = "Failed Explore Service on NE: " + neName + "(" + neId + ") "
          + getPortName(port) + " " + getAdminState(port, neType)
          + (neType == NEType.FSP_3000R7 ? (" assign type: " + port.getContainingModule().getAssignedTypeString()) : "");
        htmlMsg.append("<html><h1>").append(msg).append("</h1><html>").append(portsFailed.get(port));
        writeToGenericExplorePathFailureFile(reportWriter,neName,neId,getPortName(port), portsFailed.get(port),fileName);
      }
    }
    // save the failure log into a html file
    String failfilename = null;
    if (htmlMsg.length() > 0)
      failfilename = writeToExplorePathFailureFile(fileName, htmlMsg.toString());

    StringBuilder failedPorts = new StringBuilder("");
    for (PortDBImpl port : portsFailed.keySet()) {
      failedPorts.append(getPortName(port)).append("\n");
    }
    log.info("++++++ END Explore Service on ne: " + neName + "(" + neId + ") took " + (System.currentTimeMillis() - start) + "ms, success: " + success +
      " failed: " + portsFailed.size() +
      ((portsFailed.size() == 0) ? "" : (" failed client ports are: " + failedPorts.toString() + "Please check file for details: " + failfilename)));
  }

  private void writeToGenericExplorePathFailureFile(BufferedWriter reportWriter, String neName, int neId, String terminationPoint, String message, String fileName) {
    String line=neName+","+neId+","+terminationPoint+","+decodeErrorMessageFromHTMLToCSV(message)+","+fileName+"\n";
    try {
      reportWriter.write(line);
      reportWriter.flush();
    } catch (IOException e) {
      log.error("Failed to write"+ line+"to report file");
    }
  }

  private String decodeErrorMessageFromHTMLToCSV(String message){
    if (message!=null) {
      int startIndex;
      int endIndex;
      if ((startIndex=message.indexOf("<h2>")) > 0 && (endIndex=message.indexOf("</h2>"))>startIndex+4){
        return StringEscapeUtils.escapeCsv(message.substring(startIndex+4,endIndex));
      }else {
        return message;
      }
    }else {
      return "";
    }
  }

  /**
   *
   */
  private boolean isConnectionExist(int connId){
    try{
      AbstractConnectionDBImpl newODS = connectionDAO.getConnectionById(connId);
      return newODS != null;
    }
    catch (EntityNotFoundException e){
      return false;
    }
  }

  @MDPersistenceContext
  public ServiceDiscoveryParameters discoverServiceParameters(int serviceId){
    ParameterGroup serviceGroup = null;
    try {
      AbstractConnectionDBImpl connection = connectionDAO.getConnectionById(serviceId);
//      String unsupportReason = validateReExploreSupported(connection, true);
      boolean validate = true;
      if(connection != null) {
        if (connection.isSubChConnection()) // explore ODS
          serviceGroup = getServiceDefinitionForExplorePath((SubChConnectionDBImpl) connection, true);
        else {
          int neId = connection.getStartNEID();
          NEType productType = getNeType(neId);
          if (productType == NEType.FSP_3000R7) {
            if (connection.isOChConnection()) {
              serviceGroup = ServiceManagerHelper.getExplorePathServiceDefinitionForOCS(connection.getLabel(), neId, productType, ((OChConnectionDBImpl) connection).getStartPort().getEntityIndex(), "", 0, connection.isManaged());
              handleExternalChannel((OChConnectionDBImpl)connection, serviceGroup);
            }
            else if (connection instanceof ConnectionDBImpl) {
              validate = false;
              serviceGroup = ServiceManagerHelper.getExplorePathServiceDefinitionForOCS(connection.getLabel(), neId, productType, ((ConnectionDBImpl) connection).getStartEntity().getEntityIndex(), "", 0, connection.isManaged());
            }
            updateConnectivityName(connection, serviceGroup);
          }
        }
      } else
        log.debug("Failed to locate classic connection - " + serviceId);

      if (serviceGroup != null) {
        try {
          return smTrackServiceDiscoveryManager.explorePath(serviceGroup, validate, false);
        }catch (TrackServiceDiscoveryException e){
          log.debug("Failed to traverse the service - " + e.getMessage());
        }
      }
    } catch (Exception e){
      log.debug("Exception - " + e.getMessage(), e);
    }

    return null;
  }

  @MDPersistenceContext
  public List<ExploreServicesState> reExploreService(int connectionID) {
    List<ExploreServicesState> result = null;
    int sessionId = sessionHdlr.getSessionID();
    if (connectionID > 0) {
      try {
        MLTrailDBImpl mlTrailDB = MLTopologyElementDAO.getInstance().getMLTrail(connectionID);
        AbstractConnectionDBImpl connectionDB = getExplorableClassicService(mlTrailDB);
        if(connectionDB == null)
          return result;
        result = reExploreServicesWithDependencies(Lists.asList(connectionDB.getId()), false, false, sessionId, 20, 80, 100);
        /*
        Optional<AbstractConnectionDBImpl> connectionDB = mlTrailDB.getRelatedMOObjects().stream()
              .filter(ref -> ref.getRefType() == MLMoReferenceType.ServiceMOReference)
              .map(ref -> MDPersistenceHelper.find(AbstractConnectionDBImpl.class, ((MLTopologyServiceMoReference) ref).getConnID()))
              .findFirst();
        result = reExploreServicesWithDependencies(Lists.asList(connectionDB.get().getId()), false, false, sessionId, 20, 80, 100);
         */
        mdMessageSender.setProgressState(sessionId, "Clear mismatch on lines...", 81,100, ProgressStatusType.REQUEST_RUNNING);
        clearMismatchesOnLinesForReExploredServices(result);
        updateExploreResults(result, sessionId, 85, 98, 100);
      } catch (MDOperationFailedException | RuntimeException e) {
        log.error("reExploreMlService: {}", e.toString());
      }
    }
    return result;
  }

  public List<ExploreServicesState> updateExploreResults(List<ExploreServicesState> result, int sessionId,
          /*boolean resyncBeforeCheck,*/ int startProgress, int endProgress, int totalProgress) {
    // trigger resynchronization
    boolean showProgressReport = sessionId != 0 && startProgress != 0 && endProgress>startProgress && totalProgress>=endProgress;
    // new classic layers rebuilt should trigger ResyncServiceTask for the classic layers, resync here would only add more unnecessary tasks
    // which could make ML discovery even busier, we just need to wait longer time in order for ML model completely finished
    // to cover other cases - use a flag to sync before check ML model, or sync only if needed after check ML model
//    if(resyncBeforeCheck) {
//      result.stream().forEach(o -> {
//        if (o.getState() == ExploreState.Success && o.getExploredCfcId() == 0 && o.getSelectedItemId() > 0) {
//          mlServiceHelper.checkMLForClassicService(o.getSelectedItemId());
//        }
//      });
//    }
    int step = (endProgress-startProgress)/5;
    int previousProgress = startProgress;
    long previousRefreshTime = 0L;

    long currentTime = System.currentTimeMillis();
    int count = 5;// wait 5 min
    long endTime = currentTime + count * REEXPLORE_TIMEOUT_IN_MSECS;
    boolean refreshProgress = true;
    while ((currentTime = System.currentTimeMillis()) < endTime) {
      if(showProgressReport) {
        // every 1 minute, refresh progress status
        if(!refreshProgress && currentTime-previousRefreshTime >= REEXPLORE_TIMEOUT_IN_MSECS) {
          refreshProgress = true;
          previousProgress = previousProgress + step;
        }
        if(refreshProgress) {
          refreshProgress = false;
          previousRefreshTime = currentTime;
          mdMessageSender.setProgressState(sessionId, "Waiting ML service sync up.... timeout in "+ count-- +" minutes", previousProgress, totalProgress, ProgressStatusType.REQUEST_RUNNING);
        }
      }
      Optional<ExploreServicesState>  mlNotDiscovered = result.stream().filter(o -> (o.getState() == ExploreState.Success && o.getExploredCfcId() == 0)).findAny();
      if (!mlNotDiscovered.isPresent()) // ml information is still missing
        break;

      result.stream().forEach(state -> {
        // sleep
        if (state.getExploredCfcId() == 0) {
          MLTrailDBImpl mlTrailDB = MLTopologyElementDAO.getInstance().getMLTrail(state.getSelectedItemId());
          if (mlTrailDB != null)
            state.setExploredCfcId(mlTrailDB.getId());
        }
      });
      try {
        Thread.sleep(500);
      } catch (Exception e) {
        Thread.currentThread().interrupt();
      }
    }

    ArrayList<Integer> connIDsNotInML = new ArrayList<>();
    result.stream().forEach(o -> {
      if (o.getState() == ExploreState.Success && !(o.getExploredCfcId() > 0)) {
        // ML service is not discovered and hence update state to failure
        MLTrailDBImpl mlTrailDB = MLTopologyElementDAO.getInstance().getMLTrail(o.getSelectedItemId());
        if (mlTrailDB != null)
          o.setExploredCfcId(mlTrailDB.getId());
        else { // could be still in progress
          connIDsNotInML.add(o.getSelectedItemId());
          String reason = "Service exploration did not complete in the expected timeframe";
          o.setExplorable(ExploreState.Failed);
          if (StringUtils.isEmpty(o.getFailureReason()))
            o.setFailureReason(reason);
          if (StringUtils.isEmpty(o.getErrorMessage()))
            o.setErrorMessage(reason);
        }
      }
    });
    // after waiting period, if there are still some ML model missing, trigger resync task, just in case something was missed out, but wait on the result
    if(!connIDsNotInML.isEmpty() /*&& !resyncBeforeCheck*/) {
      log.debug("[REEXPLORE]....... updateExploreResults {} classic service not in ML...  ", connIDsNotInML.size());
      if(showProgressReport)
        mdMessageSender.setProgressState(sessionId, "ML service resync for " + connIDsNotInML.size() + " services ...", endProgress,totalProgress, ProgressStatusType.REQUEST_RUNNING);
      BeanProvider.get().getBean(MLServiceHelper.class).resync(connIDsNotInML);
    }
    return result;
  }

  public AbstractConnectionDBImpl getExplorableClassicService(MLTrailDBImpl mlTrailDB){
    if(mlTrailDB == null)
      return null;
    List<AbstractConnectionDBImpl> connectionDBs = mlTrailDB.getRelatedMOObjects().stream()
            .filter(ref -> ref.getRefType() == MLMoReferenceType.ServiceMOReference ||ref.getRefType() == MLMoReferenceType.ServiceOCSMOReference)
            .map(ref -> connectionDAO.getConnectionById(((MLTopologyServiceMoReference) ref).getConnID()))
            .filter(Objects::nonNull).collect(Collectors.toList());
    for(AbstractConnectionDBImpl conn:connectionDBs){
      if(conn.isOChConnection() || conn.isSubChConnection())
        return conn;
    }
    return connectionDBs.isEmpty()?null:connectionDBs.get(0);
  }

  public List<ExploreServicesState> reExploreMlService(int connectionID) {
    List<ExploreServicesState> result = null;
    int sessionId = sessionHdlr.getSessionID();
    long start0 = System.currentTimeMillis();
    if(log.isDebugEnabled())
      log.debug("[REEXPLORE]....... reExploreMlService {} started .... ", connectionID);
    if (connectionID > 0) {
      mdMessageSender.setProgressState(sessionId, "Preparing for service re-exploration...", 2,100, ProgressStatusType.REQUEST_RUNNING);
      MLTrailDBImpl mlTrailDB = mlTopologyElementDAO.getMLTrail(connectionID);
      AbstractConnectionDBImpl connectionDB = null;
      if (mlTrailDB != null) {
        connectionDB = getExplorableClassicService(mlTrailDB);

        boolean toSplit = isStitched(mlTrailDB); // handle splitting
        boolean toStitch = mlTrailDB.getNewSecondaryLifecycleStateSet().contains(MLNewSecondaryLifecycleState.DS_MISMATCH); // handle stitching
        List<MLConnection> damagedLinks = retrieveDamagedLinks(mlTrailDB);
        boolean isLineEndPointModified = !damagedLinks.isEmpty(); // handle line end point changes
        if (connectionDB != null) {
          if (toSplit && (!isLineEndPointModified || isSplitLinkDamaged(damagedLinks))) { // splitting
            result = Lists.asList(splitMlService(connectionDB.getId(), sessionId));
          } else if (toStitch && !isLineEndPointModified) { // stitching
            try {
              result = joinAndExploreServices(connectionDB.getId(), sessionId);
              for (ExploreServicesState state : result) {
                if (state.getState() == ExploreState.Success) {
                  MLTrailDBImpl trailDB = MLTopologyElementDAO.getInstance().getMLTrail(state.getSelectedItemId());
                  if (trailDB != null)
                    mlServiceHelper.setMlServiceName(trailDB.getId(), state.getCfcName());
                }
              }
            } catch (MDOperationFailedException e) {
              log.error("Error in creating stitched service" + mlTrailDB.getId());
            }
          } else {
            // reexplore
            result = reExploreService(connectionDB.getId());
          }
        }
        mdMessageSender.setProgressState(sessionId, "Service re-exploration completed...", 100,100, ProgressStatusType.REQUEST_SUCCEEDED);
      }
    }
    if(log.isDebugEnabled())
      log.debug("[REEXPLORE]....... reExploreMlService {} total took .... {} ms", connectionID,System.currentTimeMillis()-start0);
    return result;
  }

  public List<ExploreServicesState> joinAndExploreServices(int serviceId, int sessionId) throws MDOperationFailedException{
    List<ExploreServicesState> exploreServicesState = joinAndExploreServices(serviceId, sessionId, 20, 80, 100);
    return updateExploreResults(exploreServicesState, sessionId, 85,98,100);
  }

  private boolean isStitched(MLTrailDBImpl trailDB) {
    List<MLConnection> mismatchConns = new ArrayList<>();
    List<MLTopologyElement> mlTopologyElementDBList = mlTopologyElementDAO.getAllLowerLayerConnections(trailDB.getId());
    mlTopologyElementDBList.add(trailDB);

    mlTopologyElementDBList.stream().forEach(trail -> {
      if (trail instanceof MLTrailDBImpl && trail.getNewSecondaryLifecycleStateSet().isDamaged()) {
        mismatchConns.addAll(((MLTrailDBImpl) trail).getWorkingForwardPath().connectionSequence().stream().filter(Objects::nonNull).collect(Collectors.toList()));
      }
    });
    return mismatchConns.stream().filter(conn -> conn.getNewSecondaryLifecycleStateSet().contains(MLNewSecondaryLifecycleState.DS_MISMATCH)).findAny().isPresent();

  }

  private List<MLConnection> retrieveDamagedLinks(MLTrailDBImpl trailDB) {
    Set<MLConnectionDBImpl> mismatchConns = new HashSet<>();
    List<MLServiceDBImpl> lowerTrails = mlTopologyElementDAO.getOrderedLowerLayerPtpServices(trailDB);
    List<MLServiceDBImpl> lowerLineLayers = lowerTrails.stream().filter(s->s.getLayer() == MLNetworkLayer.OPS || s.getLayer() == MLNetworkLayer.OTS ).collect(Collectors.toList());

    for(MLServiceDBImpl layer:lowerLineLayers){
      mismatchConns.addAll(layer.getAllConnectionsUnderService().stream().filter(c->c.getLayer()==MLNetworkLayer.FIBER && c.getNewSecondaryLifecycleStateSet().isDamaged()).collect(Collectors.toSet()));
    }

    return mismatchConns.stream().filter(conn -> (conn.getLayer() == MLNetworkLayer.FIBER && conn.getNewSecondaryLifecycleStateSet().isDamaged())).collect(Collectors.toList());
  }

  /**
   * return true if all connection are fiber connection and mo deleted;
   */
  private boolean isSplitLinkDamaged(List<MLConnection> connections){
    for(MLConnection conn:connections){
      if(conn.getLayer() != MLNetworkLayer.FIBER || !conn.getNewSecondaryLifecycleStateSet().isMoDamaged())
        return false;
    }
    return !connections.isEmpty();
  }

  @MDPersistenceContext
  public List<ExploreServicesState> runReExploreServicesWithDependenciesForList(int sessionId, List<Integer> serviceIds,
                                                                                 boolean validateOnly, boolean exploreOnlyNeeded)
          throws MDOperationFailedException{
    Map<Integer, Integer> toReexploreList = new HashMap<>();
    List<Integer> toReexploreStitchedList = new ArrayList<>();
    List<Integer> toReexploreSplitList = new ArrayList<>();
    mdMessageSender.setProgressState(sessionId, "Preparing for service re-exploration...", 2, 100, ProgressStatusType.REQUEST_RUNNING);
    for (Integer id : serviceIds) {
      MLTrailDBImpl mlTrailDB = mlTopologyElementDAO.getTrailByID(id);
      if (mlTrailDB != null) {
        AbstractConnectionDBImpl connectionDB = getExplorableClassicService(mlTrailDB);
        if (connectionDB != null) {
          if (mlTrailDB.getNewSecondaryLifecycleStateSet().contains(MLNewSecondaryLifecycleState.DS_MISMATCH)) {  // handle stitching
            toReexploreStitchedList.add(connectionDB.getId());
          } else if (isStitched(mlTrailDB)) {  // handle splitting
            toReexploreSplitList.add(connectionDB.getId());
          } else {
            toReexploreList.put(connectionDB.getId(), mlTrailDB.getId());
          }
        } else {
          // classic service
          toReexploreList.put(id, 0);
        }
      }
      else
        toReexploreList.put(id, 0);
    }

    List<ExploreServicesState> result = new ArrayList<>();
    if (toReexploreList.size() > 0) {
      result = reExploreServicesWithDependencies(toReexploreList.keySet().stream().collect(Collectors.toList()), validateOnly,
              exploreOnlyNeeded, sessionId, 20,75,100);
      // for each successfully explored service make sure that mismatch alarms on lines are cleared, since they are
      // no longer valid
      clearMismatchesOnLinesForReExploredServices(result);
      updateExploreResults(result, sessionId, 80,95,100);
    }

    for (Integer id : toReexploreStitchedList) {
      result.addAll(joinAndExploreMlServices(id));
    }

    for (Integer id : toReexploreSplitList) {
      result.add(splitMlService(id));
    }
    mdMessageSender.setProgressState(sessionId, "Service re-exploration completed...", 100,100, ProgressStatusType.REQUEST_SUCCEEDED);
    return result;
  }


  @MDPersistenceContext
  private List<ExploreServicesState> joinAndExploreMlServices(int connectionID) {
    int sessionId = sessionHdlr.getSessionID();
    List<ExploreServicesState> exploredResult = new ArrayList<>();
    try {
      MLTrailDBImpl mlTrailDB = mlTopologyElementDAO.getTrailByID(connectionID);
      AbstractConnectionDBImpl connectionDB = null;

      if (mlTrailDB != null) {
        int classicServiceId = mlServiceHelper.getClassicFromMlService(mlTrailDB.getId());
        connectionDB = connectionDAO.getConnectionById(classicServiceId);
      }
      int serviceIdExplore = (connectionDB != null) ? connectionDB.getId() : connectionID;
      exploredResult = joinAndExploreServices(serviceIdExplore, sessionId);
      for (ExploreServicesState state : exploredResult) {
        if (state.getState() == ExploreState.Success) {
          MLTrailDBImpl trailDB = MLTopologyElementDAO.getInstance().getMLTrail(state.getSelectedItemId());
          if (trailDB != null)
            mlServiceHelper.setMlServiceName(trailDB.getId(), state.getCfcName());
        }
      }
    } catch (MDOperationFailedException e) {}
    return exploredResult;
  }

  @MDPersistenceContext
  private ExploreServicesState splitMlService(int connectionID) {
    int sessionId = sessionHdlr.getSessionID();
    ExploreServicesState result = new ExploreServicesState();
    MLTrailDBImpl mlTrailDB = mlTopologyElementDAO.getMLTrail(connectionID);
    if (mlTrailDB != null) {
      AbstractConnectionDBImpl connectionDB = getExplorableClassicService(mlTrailDB);
      if(connectionDB == null)
        return result;
      result = splitMlService(connectionDB.getId(), sessionId);

    }
    return result;
  }


  public void performStitchODUCrossoverUpdate942() {
    // Now for certain criteria we need to have one ODU layer cross over
    long startTime = System.currentTimeMillis();
    int totalNumer = 0;
    try {
      List<Integer> odsToReExplore = getODSWithExploredOTU1Services();
      totalNumer = odsToReExplore.size();
      if (totalNumer > 0) {
        // temp disable operational status handler processing - could cause deadlock during re-explore many services at the same time
        ((OperationalStatusHandlerImpl) operationalStatusHandler).pauseNotificationListener();
        reExploreDataServices(odsToReExplore);
        // resume operational status handler processing
        ((OperationalStatusHandlerImpl) operationalStatusHandler).resumeNotificationListener();
      }
    } finally {
      if (totalNumer > 0)
        log.info("StitchODU1Crossover Update for 9.4.2 has completed for " + totalNumer + " ODS services.... took " + ((System.currentTimeMillis() - startTime) / 1000) + " seconds");
    }

  }

  @MDPersistenceContext
  private List<Integer> getODSWithExploredOTU1Services() {
    List<Integer> odsToReExplore = new ArrayList<>();

    final EntityManager em = MDPersistenceManager.current();
    // upgrade only for these explored unprotected ods with service type OTU1 and stitched cases
    jakarta.persistence.Query query = em.createQuery("SELECT DISTINCT e FROM SubChConnectionDBImpl e"
            + " WHERE e.serviceType = 13 AND e.isExplored = true AND e.serviceProtectionType = 0");
    @SuppressWarnings("unchecked")
    List<SubChConnectionDBImpl> connList = (List<SubChConnectionDBImpl>) query.getResultList();
    connList.stream().filter(ods -> potentialReExploreOTU1ODS(ods) && ods.getStartPort() instanceof PortFSP_R7DBImpl).forEach(ods -> {
      log.info("Add re-explore ODS: " + ods.getName() + " id: " + ods.getId());
      odsToReExplore.add(ods.getId());
    });
    log.info("Re-explore ODS: " + odsToReExplore.size());

    return odsToReExplore;
  }
  private boolean potentialReExploreOTU1ODS(SubChConnectionDBImpl ods) {
    List<AbstractConnectionDBImpl> layersUnderODS = ods.getWorkingConnectionsInOrder();
    if (layersUnderODS.size() <= 1)
      return false;
    boolean possibleStitch = false;
    // has all ODU1 (stitch within the same node), or has at least OCS on Client ports with OTU1 facility type
    for (AbstractConnectionDBImpl connection : layersUnderODS) {
      if (connection instanceof OChConnectionDBImpl) {
        // OCS must be both client ports with service type OTU1
        PortDBImpl startPort = ((OChConnectionDBImpl) connection).getStartPort();
        PortDBImpl peerPort = ((OChConnectionDBImpl) connection).getStartPort();
        if (connection.getServiceType() != Definition.ServiceType.OTU1.getId() ||
                !(startPort instanceof PortFSP_R7DBImpl) ||
                !(peerPort instanceof PortFSP_R7DBImpl) ||
                !((PortFSP_R7DBImpl) startPort).isClientPort() ||
                !((PortFSP_R7DBImpl) peerPort).isClientPort()) {
          return false;
        }
        possibleStitch = true;
      } else if (connection instanceof ConnectionDBImpl) {
        // Must ODU1
        if (connection.getServiceLayer() != ServiceLayer.ODU1)
          return false;
        possibleStitch = true;
      } else
        return false;
    }
    // multiple underneath layers are all ODU1 and OTU1 OCS, at least one OCS with both client ports
    log.debug("ODS: " + ods.getName() + " id: " + ods.getId() + " Possible stitch service: " + possibleStitch);
    return possibleStitch;
  }

  /**
   * Re-explore a list of data services - is used for fixing the ODU1 layer issue for existing data services;
   *
   * @param odsIDsToReExplore ods ids to re-explore
   */
  private void reExploreDataServices(List<Integer> odsIDsToReExplore) {
    for (int odsId : odsIDsToReExplore) {
      // no need to update OCS, only validate OCS endPoints match with each other
      try {
        reExploreService(odsId, false, true);
      } catch (MDOperationFailedException e) {
        log.error("Failed re-explore ods - " + odsId + "......" + e.getMessage());
      }
    }
  }

  @MDPersistenceContext
  public List<ExploreServicesState> runReExploreServicesWithDependenciesForPaging(int sessionId, TopologyNodeType nodeType,
                                                                                   PagingRestriction restriction,
                                                                                   boolean validateOnly,
                                                                                   boolean exploreOnlyNeeded) throws MDOperationFailedException {
    mdMessageSender.setProgressState(sessionId, "Preparing for service re-exploration...", 2,100, ProgressStatusType.REQUEST_RUNNING);
    List<ExploreServicesState> result = reExploreServicesWithDependencies(getAllExplorableServices(nodeType,restriction), validateOnly,
            exploreOnlyNeeded,sessionId, 20,80,100);
    // for each successfully explored service make sure that mismatch alarms on lines are cleared, since they are
    // no longer valid
    clearMismatchesOnLinesForReExploredServices(result);
    updateExploreResults(result,sessionId, 85,98,100);
    mdMessageSender.setProgressState(sessionId, "Service re-exploration completed...", 100,100, ProgressStatusType.REQUEST_SUCCEEDED);
    return result;
  }


  private List<Integer> getAllExplorableServices(TopologyNodeType nodeType, PagingRestriction restriction) {
    if (log.isInfoEnabled()) log.info("getAllExplorableServices()");
    List<Integer> nodeIDs = restriction.getIds();
    int[] nodeIDsArray = ArrayUtils.toPrimitive(nodeIDs.toArray(new Integer[nodeIDs.size()]));
    //Need to get all data not only first page
    restriction.setPageSize(-1);
    Collection<ServiceView> serviceViews= servicePageHdlr.getPagingData(nodeType, nodeIDsArray, restriction);
    //todo filter as frontend...
    filterOutNonExplorableItems(serviceViews);
    return serviceViews.stream().map(ServiceView::getId).collect(Collectors.toList());
  }


  private void  filterOutNonExplorableItems(Collection<ServiceView> serviceNodes) {
    List<ServiceView> excludedServiceNodes = new ArrayList<>();
    ReExploreServiceFilter serviceFilter = new ReExploreServiceFilter();
    excludedServiceNodes.addAll(serviceNodes.stream().filter(serviceFilter::filter).collect(Collectors.toList()));
    serviceNodes.removeAll(excludedServiceNodes);
    excludedServiceNodes.stream().filter(excludedServiceNode -> excludedServiceNode.getServiceName() != null).forEach(excludedServiceNode ->
            serviceMessageManager.addMessage(MessageManager.Info, excludedServiceNode.getServiceName(), "Re-explore skipped "
                    + excludedServiceNode.getServiceName()
                    + serviceFilter.getMessageFragment(excludedServiceNode)));
  }


  public TraversalResult getExplorePathForService(ParameterGroup pg) throws MDOperationFailedException {
    try {
      StringBuilder builder = new StringBuilder("Explore Path:");
      logParameterGroup(pg, builder);
      LOGGER_FOR_PARAMETER_GROUP.info(builder);
      ParameterGroup sourceNode = pg.getParameterGroup(Group.SOURCE_NODE);
      ParameterGroup sourceWorkingCard = sourceNode.getParameterGroup(Group.WORKING_CARD);
      int neID = sourceNode.getId();
      if (neID == 0 || sourceWorkingCard == null)   // could be single ended service - destination node
        throw new MDOperationFailedException("Invalid info");

      // NOTE: do not remove this flag - for development debugging purpose
      boolean testingBuildConnectionLayers = false;
      ParameterGroup sd;
      if (testingBuildConnectionLayers)
        sd = buildExplorePathServiceDefinitionWithTestings(pg);
      else
        sd = buildExplorePathServiceDefinition(pg,false);

      return new TraversalResult(sd,null);
    } catch (NullPointerException e) {
      log.error("SERVICE_DEFINITION_INVALID - NullPointerException", e);
      throw new MDOperationFailedException(" Service Definition construction caught an exception please see sm.log for details... ", e);
    }catch (TrackServiceDiscoveryException t){
      log.error("TrackServiceDiscoveryException -", t);
      return new TraversalResult(pg,t.getMessage());
    } catch (Exception e) {
      log.error("Exception -", e);
      throw new MDOperationFailedException(e.getMessage(), e);
    }
  }

  private void logParameterGroup(ParameterGroup pg, StringBuilder builder) {
    builder.append("\n").append(pg).append(Arrays.toString(pg.getParameters()));
    for (ParameterGroup group : pg.getParameterGroups()) {
      logParameterGroup(group, builder);
    }
  }

  @MDPersistenceContext
  public ServiceNodeProperties getNodePropertiesForExplorablePorts(int neID) throws NoSuchNetworkElementException {
    try {
      Comparator<EntityIndex> portCompare = (instance1, instance2) -> EntityIndex.compare(instance1, instance2);
      TreeMap<EntityIndex, Integer> portsToExplore = new TreeMap<>(portCompare);
      getExplorablePorts(neID, portsToExplore);

      return serviceWizardLoadHdlr.getExplorableNodeProperties(neID, portsToExplore);
    } catch (jakarta.persistence.EntityNotFoundException onf) {
      log.error(onf.getMessage());
      throw new NoSuchNetworkElementException("No such network element ID " + neID,onf);
    }
  }

  /**
   *
   * @param serviceId Service ID
   * @param updateOCS Flag to update OCS
   * @param updateLayers Flag to update layers
   * @throws MDOperationFailedException
   */
  @MDPersistenceContext
  public int reExploreService(int serviceId, boolean updateOCS, boolean updateLayers)throws MDOperationFailedException{
    AbstractConnectionDBImpl connection = null;
    try {
      connection = connectionDAO.getConnectionById(serviceId);

      String serviceName = connection.getName();
      log.info("Starting re-explore " + serviceName + " ID: " + serviceId);
      String unsupportReason = validateReExploreSupported(connection, false);
      if(unsupportReason  == null){
        if(connection.isSubChConnection()){
          // re-explore ODS
          ParameterGroup serviceGroup = getExplorePathServiceDefinition((SubChConnectionDBImpl) connection, true);
          if(serviceGroup != null){
            ConnectionLayerData odsLayerData = buildServiceLayers(serviceGroup);
            if(connectionHelper.isODSOCSMatched((SubChConnectionDBImpl) connection, odsLayerData)){
              String ODSDetails = odsLayerData.getLayerDetails(0);
              int newODSId = -1;
              try{
                List<Integer> updatedOCSIDs = new ArrayList<>();
                newODSId = smTrackServiceManager.updateODS((SubChConnectionDBImpl) connection, serviceGroup, odsLayerData, updateOCS, updateLayers, updatedOCSIDs);
                // extended layer update under OCS - only works once OCS built.
                smTrackServiceManager.updateOCSExtLayers(updatedOCSIDs);
                log.info("Completed re-explore " + serviceName + " old ID: " + serviceId + " new ID: " + newODSId);
                return newODSId;
              }
              catch(Exception e){
                // Chance is small, but could it be old one is deleted and new one is failed to create? Let's report it and log some basic info
                int odsId = newODSId != -1? newODSId:serviceId;
                if(!isConnectionExist(odsId)){
                  log.info("Re-explore " + serviceName + " failed due to unexpected reason - " + e.getMessage() +
                          ". The original ODS could be lost, here is the explored service details, please manually recover if needed. DETAILS---\n" + ODSDetails);
                  throw new MDOperationFailedException("Re-explore " + serviceName + " failed due to unexpected reason, the original ODS could be lost, check for sm.log for explored service details, and manually recover it if needed. ");
                }
              }
            }
            else {
              setExplorationAndNotifyGui(connection.getId(),Definition.ExplorationStatus.FAILED);
              throw new MDOperationFailedException("Re-explore on ODS failed on " + connection.getLabel() +
                      " - Conflicting termination points detected");
            }
          }
          else
            throw new MDOperationFailedException("Re-explore on ODS failed on " + connection.getLabel() +
                    " - failed to generate service definition " );
        }
        else if(connection.isOChConnection()) { // only support re-explore on ODS in 9.4.2
          // reexplore on OCS
          ParameterGroup serviceGroup = getExplorePathServiceDefinitionForOCS((OChConnectionDBImpl) connection);
          if(serviceGroup != null){
            ConnectionLayerData ocsLayerData = buildServiceLayers(serviceGroup);
            if(connectionHelper.isConnectionMatched(connection, ocsLayerData, null)){
              String OCSDetails = ocsLayerData.getLayerDetails(0);
              int newOCSId = -1;
              try{
                List<Integer> updatedOCSIDs = new ArrayList<>();
                newOCSId = smTrackServiceManager.updateOCS((OChConnectionDBImpl) connection,ocsLayerData, updatedOCSIDs);
                // extended layer update under OCS - only works once OCS built.
                smTrackServiceManager.updateOCSExtLayers(updatedOCSIDs);
                log.info("Completed re-explore " + serviceName + " old ID: " + serviceId + " new ID: " + newOCSId);
                return newOCSId;
              }
              catch(Exception e){
                // Chance is small, but could it be old one is deleted and new one is failed to create? Let's report it and log some basic info
                int ocsId = newOCSId != -1? newOCSId:serviceId;
                if(!isConnectionExist(ocsId)){
                  log.info("Re-explore " + serviceName + " failed due to unexpected reason - " + e.getMessage() +
                          ". The original OCS could be lost, here is the explored service details, please manually recover if needed. DETAILS---\n" + OCSDetails);
                  throw new MDOperationFailedException("Re-explore " + serviceName + " failed due to unexpected reason, the original ODC could be lost, check for sm.log for explored service details, and manually recover it if needed. ");
                }
              }
            }
            else {
              setExplorationAndNotifyGui(connection.getId(),Definition.ExplorationStatus.FAILED);
              throw new MDOperationFailedException("Re-explore on OCS failed on " + connection.getLabel() +
                      " - Conflicting termination points detected ");
            }
          }
          else
            throw new MDOperationFailedException("Re-explore on OCS failed on " + connection.getLabel() +
                    " - failed to generate service definition " );
        }
      }
      else {
        throw new MDOperationFailedException("Re-explore on ODS not supported for " + connection.getLabel() +
              " - " + unsupportReason);
      }
    }
    catch (EntityNotFoundException e){
      throw new MDOperationFailedException("Re-explore on service failed - can't find the service with id " + serviceId);
    }
    catch (MDOperationFailedException e){
      setExplorationAndNotifyGui(connection.getId(), Definition.ExplorationStatus.FAILED);
      throw e;
    }
    catch (SMException e){
      setExplorationAndNotifyGui(connection.getId(), Definition.ExplorationStatus.FAILED);
      throw new MDOperationFailedException("Re-explore on connection failed on " + connection.getLabel() +
              " at Layer " + (connection.getServiceLayer() == null?"Unknown" : connection.getServiceLayer().name()) +
              " due to " + e.getMessage(), e);
    }
    return -1;
  }


  @MDPersistenceContext
  private void setExplorationStatus(int connId ,Definition.ExplorationStatus explorationStatus) {
    AbstractConnectionDBImpl connection = connectionDAO.getConnectionById(connId);
    if(connection!=null) {
      setExplorationStatus(connection, explorationStatus, explorationStatus != Definition.ExplorationStatus.IN_PROGRESS);
    }
  }


  @MDTransactional
  private void setExplorationStatus(AbstractConnectionDBImpl connection,Definition.ExplorationStatus explorationStatus, boolean allLayers){
    connection = MDPersistenceHelper.refind(connection,connection.getId());
    if(connection.isProvisionMode())
      return;
    if (ServiceLayer.ODS.equals(connection.getServiceLayer())||ServiceLayer.OCS.equals(connection.getServiceLayer())){
      connection.setExplorationStatus(explorationStatus.getId());
    }
    if(!allLayers)
      return;
    for(AbstractConnectionDBImpl conn:connection.getWorkingConnectionsInOrder()){
      setExplorationStatus(conn, explorationStatus, allLayers);
    }

    for(AbstractConnectionDBImpl conn:connection.getProtectionConnectionsInOrder()){
      setExplorationStatus(conn,explorationStatus, allLayers);
    }

  }

  private void setExplorationAndNotifyGui(int connectionId,Definition.ExplorationStatus explorationStatus){
      setExplorationStatus(connectionId, explorationStatus);
      notifier.notifyTopologyChangeDirectly(connectionId,ConfigChangeType.CHANGED,false);
  }
  private boolean isValidNePair(NEType productType1, NEType productType2){
    if(productType1 == null || productType2 == null)
      return false;
    else if (productType1 == NEType.FSP_3000R7 && productType1 == productType2)
      return true;
    else if (productType1 == NEType.FSP_3000R7 &&
            (productType2 == NEType.UNMANAGED||productType2 == NEType.UNMANAGED_NETWORK))
      return true;
    else if (productType2 == NEType.FSP_3000R7 &&
            (productType1 == NEType.UNMANAGED||productType1 == NEType.UNMANAGED_NETWORK))
      return true;
    else
      return false;
  }

  private String checkValidEndpointPair(ManagedObjectDBImpl aEnd, ManagedObjectDBImpl zEnd){
    if(aEnd == null || zEnd == null)
      return "Missing A or Z endpoint from service";
    else if(aEnd instanceof PortFSP_R7DBImpl && zEnd instanceof PortFSP_R7DBImpl)
      return null;
    else if((aEnd instanceof PortFSP_R7DBImpl && isUnoEndPoint(zEnd)) ||
            (zEnd instanceof PortFSP_R7DBImpl && isUnoEndPoint(aEnd)))
      return null;
    else
      return "Not support service between " +
              ((aEnd instanceof PortFSP_R7DBImpl)?((PortFSP_R7DBImpl) aEnd).getAidString():aEnd.getShortDescription()) + " and " +
              ((zEnd instanceof PortFSP_R7DBImpl)?((PortFSP_R7DBImpl) zEnd).getAidString():zEnd.getShortDescription());
  }

  private boolean isUnoEndPoint(ManagedObjectDBImpl managedObjectDB){
    return managedObjectDB instanceof UnmanagedPortDBImpl ||
            managedObjectDB instanceof UnmanagedTerminationPointDBImpl ||
            managedObjectDB instanceof VirtualOtnPortDBImpl;
  }

  private String validateReExploreSupported(AbstractConnectionDBImpl connection, boolean ignoreProvision){
    if(!ignoreProvision && connection.isProvisionMode())
      return "Provisioning ODS";
    NEType productType = getNeType(connection.getStartNEID());
    NEType productType2 = getNeType(connection.getPeerNEID());
    if (!isValidNePair(productType, productType2))
      return "Not support service between " + productType + " and " + productType2;
    if(connection.isSubChConnection()){
      SubChConnectionDBImpl ods = (SubChConnectionDBImpl)connection;
      String error = checkValidEndpointPair(ods.getStartPortOrMO(), ods.getPeerPortOrMO());
      if(error != null)
        return error;
    }
    else if(connection.isOChConnection() && !(connection instanceof AbstractProtOChConnectionDBImpl)){
      OChConnectionDBImpl ocs = (OChConnectionDBImpl)connection;
      String error = checkValidEndpointPair(ocs.getStartPortOrMO(), ocs.getPeerPortOrMO());
      if(error != null)
        return error;
      else if(ocs.hasECHs())
        return "Re-explore does not support for ECH OCS.";
    }
    else
      return "Re-explore only supported for track or explored ODS or unprotected OCS, not supported on " + connection.getLabel() +
              " with Layer " + (connection.getServiceLayer() == null ? "Unknown" : connection.getServiceLayer().name());
    return null;
  }



  private String validateReExploreForLineRemovalSupported(AbstractConnectionDBImpl connection){
    if(connection.isProvisionMode())
      return "Provisioned ODS";
    NEType productType = getNeType(connection.getStartNEID());
    NEType productType2 = getNeType(connection.getPeerNEID());
    if ((productType != NEType.FSP_3000R7 && productType != NEType.UNMANAGED) || ( productType2 != NEType.FSP_3000R7 && productType2 != NEType.UNMANAGED))
      return "Contains non FSP R7 termination node";
    if(connection.isSubChConnection()){
      SubChConnectionDBImpl ods = (SubChConnectionDBImpl)connection;
      if((!(ods.getStartPortOrMO() instanceof PortFSP_R7DBImpl) && !(ods.getStartPortOrMO() instanceof UnmanagedObjectDBImpl)) ||
              ( !(ods.getPeerPortOrMO() instanceof PortFSP_R7DBImpl) &&!(ods.getPeerPortOrMO() instanceof UnmanagedObjectDBImpl)))
        return "Contains non FSP R7 termination port";
    }
    else if(connection.isOChConnection() && !(connection instanceof AbstractProtOChConnectionDBImpl)){
      OChConnectionDBImpl ocs = (OChConnectionDBImpl)connection;
      if(!(ocs.getStartPort() instanceof PortFSP_R7DBImpl) ||
              !(ocs.getPeerPort() instanceof PortFSP_R7DBImpl))
        return "Contains non FSP R7 termination port";
      else if(ocs.hasECHs())
        return "Re-explore does not support for ECH OCS.";
    }
    else
      return "Re-explore only supported for track or explored ODS or unprotected OCS, not supported on " + connection.getLabel() +
              " with Layer " + (connection.getServiceLayer() == null ? "Unknown" : connection.getServiceLayer().name());
    return null;
  }

  private NEType getNeType(int neId){
    NetworkElementDBImpl ne = networkElementDAO.getNEDBImplIns(neId);
    return ne == null ? null : NEType.getNETypeFor(ne.getNetworkElementType());
  }
  /**
   * Initiate explore path and build the service definition, with some internal development testings
   * This is not for production use.
   * @param pg Initial Parameter Group
   * @return Service Definition
   * @throws MDOperationFailedException
   * @throws SMException
   */
  public ParameterGroup buildExplorePathServiceDefinitionWithTestings(ParameterGroup pg)throws MDOperationFailedException, SMException{

    ServiceDiscoveryParameters serviceDiscoveryParameters=smTrackServiceDiscoveryManager.explorePath(pg, false);
    // 1. test if the client port is a valid client port to initiate explore path
//          ManagedObjectDBImpl startClientPtp = serviceDiscoveryParameters.getStartWorkClientTP();
//          if(startClientPtp instanceof TerminationPointFSP_R7DBImpl){
//            PortDBImpl port1 = PortFSP_R7DAO.getPortByEntityIndex(((TerminationPointFSP_R7DBImpl) startClientPtp).getEntityIndexOfRelatedEntity(), ((TerminationPointFSP_R7DBImpl) startClientPtp).getNeID());
//            if(port1 != null){
//              boolean validPort = smExploreServicesHandler.isValidClientPortToExplore(port1);
//              int i = 0;
//            }
//          }
    // 2. just try to see how it build from fiber map path
    try{
    if(!ServiceManagerHelper.isECHOCSLayer(pg))  // explore ech ocs not supported in smExploreConnectionBuilder
      smExploreConnectionBuilder.buildExplorePathServiceLayers(serviceDiscoveryParameters);
    }catch (Exception e){
      log.error(e.toString(), e);
    }
    // 3. test to see how it build from service definition from GUI
    //         smExploreConnectionBuilder.buildExplorePathServiceLayers(sd);

    return smTrackExplorePathServiceDefinitionCreator.getExplorePathServiceDefinition (serviceDiscoveryParameters, pg);

  }

  /**
   * Initiate explore path and build the service definition
   * @param serviceGroup Initial Service Definition
   * @param doRetries Enable to do a polling on NE and retry
   * @return Service Definition
   * @throws MDOperationFailedException
   * @throws SMException
   */
  public ParameterGroup buildExplorePathServiceDefinition(ParameterGroup serviceGroup, boolean doRetries)
          throws MDOperationFailedException, SMException{

    ServiceDiscoveryParameters serviceDiscoveryParameters = smTrackServiceDiscoveryManager.explorePath(serviceGroup,doRetries);
    serviceGroup = smTrackExplorePathServiceDefinitionCreator.getExplorePathServiceDefinition(serviceDiscoveryParameters, serviceGroup);
    return serviceGroup;
  }

  private ParameterGroup buildExplorePathServiceDefinition(ParameterGroup serviceGroup, ManagedObjectDBImpl aEnd, ManagedObjectDBImpl zEnd)
          throws MDOperationFailedException, SMException{
    ServiceDiscoveryParameters serviceDiscoveryParameters = smTrackServiceDiscoveryManager.explorePath(serviceGroup, getStopRule(aEnd, zEnd, ServiceManagerHelper.isOCSLayer(serviceGroup)));
    serviceGroup = smTrackExplorePathServiceDefinitionCreator.getExplorePathServiceDefinition(serviceDiscoveryParameters, serviceGroup);
    return serviceGroup;
  }

  @NotNull
  private TraversalStopRule getStopRule(ManagedObjectDBImpl aEnd, ManagedObjectDBImpl zEnd, boolean isExploreOCS) {
    ManagedObjectDBImpl zEndForExplore = zEnd;
    if(zEnd instanceof PortFSP_R7DBImpl){
      zEndForExplore = terminationPointsDao.getPtpByIndexOfRelatedEntityIns(zEnd.getNeID(),zEnd.getEntityIndex());
      if(zEndForExplore ==null){
        zEndForExplore = zEnd;
      }
    }
    if (isSpurLinkEndPoints(aEnd,zEnd)){
      return TraversalStopRules.stopAfter(zEndForExplore,true);
    } else if(isExploreNFCOnModuleGroupPort(aEnd, zEnd, isExploreOCS)) {
      return null; // explore ocs on quadflex GTP need to use default OCS explore stop rule - as traverse on N1, not GTP
    }
    return TraversalStopRules.stopAfter(zEndForExplore);
  }
  private boolean isExploreNFCOnModuleGroupPort(ManagedObjectDBImpl aEnd, ManagedObjectDBImpl zEnd, boolean isExploreOCS){
    return isExploreOCS && isModuleNetworkGroupPort(aEnd) && isModuleNetworkGroupPort(zEnd);
  }

  private boolean isModuleNetworkGroupPort(ManagedObjectDBImpl endPoint){
    TerminationPointFSP_R7DBImpl ptp = null;
    if(endPoint instanceof TerminationPointFSP_R7DBImpl)
      ptp = (TerminationPointFSP_R7DBImpl)endPoint;
    else if(endPoint instanceof PortDBImpl)
      ptp = TerminationPointsDao.getPtpForRelatedEntity(endPoint.getEntityIndex(), endPoint.getNeID());
    ModuleDBImpl moduleDB;
    return ptp != null && ptp.isSuperchannelGroupTerminationPoint() &&
            (moduleDB = ptp.getContainingModule()) != null &&
            F7Utils.getInstance().isModuleWitOTLs(moduleDB);
  }

  private boolean isSpurLinkEndPoints(ManagedObjectDBImpl aEnd, ManagedObjectDBImpl zEnd) {
    return  (aEnd instanceof PortFSP_R7DBImpl && ((PortFSP_R7DBImpl) aEnd).isNetworkPort() && zEnd instanceof PortFSP_R7DBImpl && ((PortFSP_R7DBImpl) zEnd).isClientPort()) ||
            (aEnd instanceof PortFSP_R7DBImpl && ((PortFSP_R7DBImpl) aEnd).isClientPort() && zEnd instanceof PortFSP_R7DBImpl && ((PortFSP_R7DBImpl) zEnd).isNetworkPort());
  }

  private List<ParameterGroup> buildExplorePathSplitServiceDefinition(ParameterGroup serviceGroupLeft, SubChConnectionDBImpl ods) throws MDOperationFailedException, SMException {
    ParameterGroup serviceGroupRight = null;
    List<ParameterGroup> serviceGroups = new ArrayList<>();

    ManagedObjectDBImpl aEnd = ods.getStartPortOrMO();
    ManagedObjectDBImpl zEnd = ods.getPeerPortOrMO();

    if (aEnd == null || zEnd == null)
      return serviceGroups;

      ServiceDiscoveryParameters serviceDiscoveryParameterLeft = smTrackServiceDiscoveryManager.explorePath(serviceGroupLeft, null, true, true);
      ServiceDiscoveryParameters serviceDiscoveryParameterRight = null;
      if (getNeType(ods.getPeerNEID()) == NEType.FSP_3000R7) {
        serviceGroupRight = getExplorePathServiceDefinition(ods, false);
        serviceDiscoveryParameterRight = smTrackServiceDiscoveryManager.explorePath(serviceGroupRight,  null, true, true);
      }
      String oldConnectivityName = ods.getServiceIntent() != null?ods.getServiceIntent().getName():ods.getName();
      if (serviceDiscoveryParameterLeft != null) {
        ParameterGroup pg = getServiceDefinition(serviceDiscoveryParameterLeft, serviceGroupLeft, oldConnectivityName + "_A");
        serviceGroups.add(pg);
      }
      if (serviceGroupRight != null && serviceDiscoveryParameterRight != null) {
        ParameterGroup pg = getServiceDefinition(serviceDiscoveryParameterRight, serviceGroupRight, oldConnectivityName + "_B");
        serviceGroups.add(pg);
      }

    return serviceGroups;
  }

  /**
   * Initiate explore path and build the service definition
   *
   * @param serviceGroupLeft Initial Service Definition
   * @return Service Definition
   * @throws MDOperationFailedException
   * @throws SMException
   */
  private List<ParameterGroup> buildExplorePathSplitServiceDefinition(ParameterGroup serviceGroupLeft, LineDBImpl line, SubChConnectionDBImpl ods)
          throws MDOperationFailedException, SMException {

    List<ParameterGroup> serviceGroups = new ArrayList<>();
    EntityIndex aEntityIndex = line.getAEndpoint().getEntityIndex();
    EntityIndex zEntityIndex = line.getZEndpoint().getEntityIndex();
    if (aEntityIndex == null || zEntityIndex == null) {
      return serviceGroups;
    }
    ManagedObjectDBImpl aEndTp = managedObjectDAO.get(line.getAEndNEId(), aEntityIndex);
    ManagedObjectDBImpl zEndTp = managedObjectDAO.get(line.getZEndNEId(), zEntityIndex);
    if (aEndTp == null || zEndTp == null) {
      return serviceGroups;
    }

    ParameterGroup serviceGroupRight = null;
    ServiceDiscoveryParameters serviceDiscoveryParameterLeft = smTrackServiceDiscoveryManager.explorePath(serviceGroupLeft, TraversalStopRules.stopAfter(aEndTp, zEndTp), true, true);
    ServiceDiscoveryParameters serviceDiscoveryParameterRight = null;
    if (serviceDiscoveryParameterLeft != null) {
      ManagedObjectDBImpl clientTP = serviceDiscoveryParameterLeft.getPeerWorkClientTP();
      if (clientTP.getId() == aEndTp.getId()) {
        if (zEndTp instanceof TerminationPointFSP_R7DBImpl) {
          serviceGroupRight = getServiceDefinitionForExplorePath(ods, zEndTp);
          serviceDiscoveryParameterRight = smTrackServiceDiscoveryManager.explorePath(serviceGroupRight, true, false);
        } else {
          int neId = ods.getPeerNEID();
          NEType productType = getNeType(neId);
          if (productType == NEType.FSP_3000R7) {
            serviceGroupRight = getExplorePathServiceDefinition(ods, false);
            serviceDiscoveryParameterRight = smTrackServiceDiscoveryManager.explorePath(serviceGroupRight,  TraversalStopRules.stopAfter(aEndTp, zEndTp), true, true);
          }
        }
      } else if (clientTP.getId() == zEndTp.getId()) {
        if (aEndTp instanceof TerminationPointFSP_R7DBImpl) {
          serviceGroupRight = getServiceDefinitionForExplorePath(ods, aEndTp);
          serviceDiscoveryParameterRight = smTrackServiceDiscoveryManager.explorePath(serviceGroupRight, true, false);
        } else {
          int neId = ods.getPeerNEID();
          NEType productType = getNeType(neId);
          if (productType == NEType.FSP_3000R7) {
            serviceGroupRight = getExplorePathServiceDefinition(ods, false);
            serviceDiscoveryParameterRight = smTrackServiceDiscoveryManager.explorePath(serviceGroupRight,  TraversalStopRules.stopAfter(aEndTp, zEndTp), true, true);
          }
        }
      }
      String oldConnectivityName = ods.getServiceIntent() != null?ods.getServiceIntent().getName():ods.getName();
      ParameterGroup pg = getServiceDefinition(serviceDiscoveryParameterLeft, serviceGroupLeft, oldConnectivityName + "_A");
      serviceGroups.add(pg);
      if (serviceDiscoveryParameterRight != null && serviceGroupRight != null) {
        ParameterGroup pgRight = getServiceDefinition(serviceDiscoveryParameterRight, serviceGroupRight, oldConnectivityName + "_B");
        serviceGroups.add(pgRight);
      }
    }
    return serviceGroups;
  }

  private ParameterGroup getServiceDefinition(ServiceDiscoveryParameters serviceDiscoveryParameter, ParameterGroup serviceGroup, String connectivityName)
          throws NoSuchNetworkElementException, SMException {
    ParameterGroup pg = smTrackExplorePathServiceDefinitionCreator.getExplorePathServiceDefinition(serviceDiscoveryParameter, serviceGroup);
      updateConnectivityName(pg, connectivityName);
    return pg;
  }

  /**
   * Update connectivity name to be specific name, use default name for service name
   * 2025-07-23 moved from SMServiceHelper
   * @param sd
   * @param connectivityName
   */
  private void updateConnectivityName(ParameterGroup sd, String connectivityName){
    Parameter connNameParam = sd.getParameter(Keyword.SERVICE_CONNECTIVITY_NAME);
    if (connNameParam == null)
      return;
    Parameter nameParam = sd.getParameter(Keyword.SERVICE_NAME);
    if (nameParam == null)
      return;
    ParameterUtils.setSafeParameterValue(sd, Keyword.SERVICE_CONNECTIVITY_NAME, connectivityName);
    String defaultServiceName = smServiceHelper.getDefaultServiceName(sd, false, nameParam.getValue()); //""); FNMD-85605
    ParameterUtils.setSafeParameterValue(sd, Keyword.SERVICE_NAME, defaultServiceName);
  }

  public ConnectionLayerData buildServiceLayers(ParameterGroup serviceGroup) throws SMException {
    return smExploreConnectionBuilder.buildServiceLayers(serviceGroup);
  }

    /**
     * Build service definition based on an existing ods
     * @param ods Data Service
     * @return Service Definition
     * @throws MDOperationFailedException
     * @throws SMException
     */
    public ParameterGroup getExplorePathServiceDefinition(SubChConnectionDBImpl ods, boolean exploreOnStart) throws MDOperationFailedException, SMException {
      ParameterGroup serviceGroup = getServiceDefinitionForExplorePath(ods, exploreOnStart);
      if(serviceGroup != null)
        try {
          return buildExplorePathServiceDefinition(serviceGroup, false);
        }catch (RuntimeException re){
          log.error("Unexpected error during traversal",re);
          throw new MDOperationFailedException(re);
        }

      return null;
    }

  public List<ParameterGroup> getExplorePathSplitServiceDefinition(SubChConnectionDBImpl ods, LineDBImpl line) throws MDOperationFailedException, SMException {
    ParameterGroup serviceGroup = getServiceDefinitionForExplorePath(ods, true);
    if(serviceGroup != null && line != null) // handle line removal
      return buildExplorePathSplitServiceDefinition(serviceGroup,line,ods);
    if (serviceGroup != null && line == null) // handle splitting scenario
      return buildExplorePathSplitServiceDefinition(serviceGroup, ods);
    return null;
  }


  public ParameterGroup getServiceDefinitionForExplorePath(SubChConnectionDBImpl ods, ManagedObjectDBImpl endpoint) throws MDOperationFailedException, SMException {
    int neId= endpoint.getNeID();
    NEType productType = getNeType(neId);
    if (productType == NEType.FSP_3000R7) {
      ParameterGroup serviceGroup=null;
      if((ods instanceof ODSConnectionDBImpl) && endpoint instanceof TerminationPointFSP_R7DBImpl) { // CCCP - use channel module port
        serviceGroup = ServiceManagerHelper.getExplorePathServiceDefinition(ods.getLabel(), neId, productType, ((TerminationPointFSP_R7DBImpl)endpoint).getContainingModule().getEntityIndex(), ods.getCustomer().getName(), ods.getCustomer().getId(), ods.isManaged());
      } else {
        ManagedObjectDBImpl port=null;
        if (endpoint instanceof TerminationPointFSP_R7DBImpl) {
          port = protMechanismUtils.getPort(neId, (TerminationPointFSP_R7DBImpl) endpoint);
        }else {
          port= endpoint;
        }
        if (port!=null) {
          serviceGroup = ServiceManagerHelper.getExplorePathServiceDefinition(ods.getLabel(), neId, productType, port.getEntityIndex(), ods.getCustomer().getName(), ods.getCustomer().getId(), ods.isManaged());
        }
      }
      if (serviceGroup == null){
        log.error("Missing port or module for service " + ods.getLabel());
        throw new MDOperationFailedException("Missing port or module for service " +ods.getLabel());
      }
      ParameterUtils.setSafeParameterValue(serviceGroup, Keyword.REMARK,ods.getRemarksFromServiceIntent());
      ParameterUtils.setSafeParameterValue(serviceGroup, Keyword.SERVICE_ALTERNATE_NAME,ods.getAlternateNameFromServiceIntent());

      CustomerServiceGroupDBImpl serviceGroupDBImpl = getServiceGroup(ods);
      if (serviceGroupDBImpl != null)
      {
        Parameter serviceFolder = serviceGroup.getParameter(Keyword.SERVICE_FOLDER);
        if (serviceFolder != null)
        {
          serviceFolder.setId(serviceGroupDBImpl.getId());
          serviceFolder.setValue(serviceGroupDBImpl.getName());
        }
      }
      updateConnectivityName(ods, serviceGroup);
      return serviceGroup;
    }
    return null;
  }

  private void updateConnectivityName(AbstractConnectionDBImpl connectionDB, ParameterGroup serviceGroup) {
    ServiceIntent serviceIntent = connectionDB.getServiceIntent();
    if (serviceIntent == null && connectionDB instanceof OChConnectionDBImpl ochConnectionDB) {
      // ocs check its uppper layer for service intent
      ConnectionDBImpl upper = BeanProvider.get().getBean(SMServiceHelper.class).getMatchUpperLayerOfOCS(ochConnectionDB);
      if (upper != null) {
        serviceIntent = upper.getServiceIntent();
      }
    }
    if (serviceIntent != null) {
      String connectivityName = serviceIntent.getName();
      if (connectivityName != null && !connectivityName.isEmpty())
        ParameterUtils.setSafeParameterValue(serviceGroup, Keyword.SERVICE_CONNECTIVITY_NAME, connectivityName);
    }
  }
  public ParameterGroup getServiceDefinitionForExplorePath(SubChConnectionDBImpl ods, boolean exploreOnStart) throws MDOperationFailedException, SMException {
    int neId= exploreOnStart?ods.getStartNEID():ods.getPeerNEID();
    NEType productType = getNeType(neId);
    if (productType == NEType.FSP_3000R7) {
      ParameterGroup serviceGroup;
      if(ods instanceof ODSConnectionDBImpl) // CCCP - use channel module port
        serviceGroup = ServiceManagerHelper.getExplorePathServiceDefinition(ods.getLabel(), neId, productType,
                exploreOnStart?((ODSConnectionDBImpl)ods).getStartChannelModulePort().getEntityIndex():((ODSConnectionDBImpl)ods).getPeerChannelModulePort().getEntityIndex(),
                ods.getCustomer().getName(), ods.getCustomer().getId(), ods.isManaged());
      else
        serviceGroup = ServiceManagerHelper.getExplorePathServiceDefinition(ods.getLabel(), neId, productType,
                exploreOnStart?ods.getStartPort().getEntityIndex():ods.getPeerPort().getEntityIndex(),
                ods.getCustomer().getName(), ods.getCustomer().getId(), ods.isManaged());
      if (serviceGroup == null){
        log.error("Missing port or module for service " + ods.getLabel());
        throw new MDOperationFailedException("Missing port or module for service " +ods.getLabel());
      }
      ParameterUtils.setSafeParameterValue(serviceGroup, Keyword.REMARK,ods.getRemarksFromServiceIntent());
      ParameterUtils.setSafeParameterValue(serviceGroup, Keyword.SERVICE_ALTERNATE_NAME,ods.getAlternateNameFromServiceIntent());

      CustomerServiceGroupDBImpl serviceGroupDBImpl = getServiceGroup(ods);
      if (serviceGroupDBImpl != null)
      {
        Parameter serviceFolder = serviceGroup.getParameter(Keyword.SERVICE_FOLDER);
        if (serviceFolder != null)
        {
          serviceFolder.setId(serviceGroupDBImpl.getId());
          serviceFolder.setValue(serviceGroupDBImpl.getName());
        }
      }
      updateConnectivityName(ods, serviceGroup);
      return serviceGroup;
    }
    return null;
  }

  private CustomerServiceGroupDBImpl getServiceGroup(SubChConnectionDBImpl ods){
      CustomerServiceGroupDBImpl serviceGroup = ods.getServiceGroup();
      if(serviceGroup == null && ods.getParent() instanceof ServiceIntentDBImpl){
        serviceGroup = ((ServiceIntentDBImpl) ods.getParent()).getParentGroup();
      }
      return serviceGroup;
  }
  /**
   * Build service definition based on an existing ocs
   * @param ocs OCS
   * @return Service Definition
   * @throws MDOperationFailedException from buildExplorePathServiceDefinition
   * @throws SMException from buildExplorePathServiceDefinition
   */
  private ParameterGroup getExplorePathServiceDefinitionForOCS(OChConnectionDBImpl ocs) throws MDOperationFailedException, SMException {
    int neId= ocs.getStartNEID();
    NEType productType = getNeType(neId);
    if (productType == NEType.FSP_3000R7) {
      ParameterGroup serviceGroup = ServiceManagerHelper.getExplorePathServiceDefinitionForOCS(ocs.getLabel(), neId, productType, ocs.getStartPort().getEntityIndex(), "", 0, ocs.isManaged());
      ParameterUtils.setSafeParameterValue(serviceGroup, Keyword.REMARK, ocs.getRemarksFromServiceIntent());
      ParameterUtils.setSafeParameterValue(serviceGroup, Keyword.SERVICE_ALTERNATE_NAME, ocs.getAlternateNameFromServiceIntent());
      updateConnectivityName(ocs, serviceGroup);
      handleExternalChannel(ocs, serviceGroup);

      if (ocs.getServiceGroup() != null) {
        CustomerServiceGroupDBImpl serviceGroupDBImpl = ocs.getServiceGroup();
        Parameter serviceFolder = serviceGroup.getParameter(Keyword.SERVICE_FOLDER);
        if (serviceFolder != null) {
          serviceFolder.setId(serviceGroupDBImpl.getId());
          serviceFolder.setValue(serviceGroupDBImpl.getName());
        }
      }
      return buildExplorePathServiceDefinition(serviceGroup, false);
    }
    return null;
  }

  private void handleExternalChannel(OChConnectionDBImpl ocs, ParameterGroup parameterGroup) {
    if(!EchOcsHelper.isECHOpticalChannelService(ocs))
      return;
    String startEchAid = ocs.getStartECHAID();
    if (startEchAid != null) {
      ParameterUtils.setSafeParameterValue(parameterGroup, Keyword.EXTERNAL_WDM_CHANNEL, String.valueOf(true));
      ParameterGroup node = parameterGroup.getParameterGroup(Group.SOURCE_NODE);
      ParameterGroup workingCard = node.getParameterGroup(Group.WORKING_CARD);
      ParameterGroup echGroup = ServiceTemplateHelper.findExternalChannelParameterGroup(workingCard);
      ParameterUtils.setSafeParameterValue(echGroup, Keyword.AID, startEchAid);
    }
  }

  /**
   * explore path, create the service definition, and then create the service
   * @param serviceName Service Name
   * @param neID Network Element ID
   * @param neType Network Element Type
   * @param clientPortEntityIndex Client Port Entity Index
   * @param customerName Customer Name
   * @param customerId Customer Id
   * @return Service Id
   * @throws MDOperationFailedException
   * @throws SMException
   */
  public int createExploreService(String serviceName, int neID, NEType neType, EntityIndex clientPortEntityIndex, String customerName, int customerId)
          throws MDOperationFailedException, SMException{

    ParameterGroup serviceGroup = ServiceManagerHelper.getExplorePathServiceDefinition(serviceName, neID, neType, clientPortEntityIndex, customerName, customerId, false);
    serviceGroup = buildExplorePathServiceDefinition(serviceGroup, false);
    return serviceManagerCtrl.setServiceParameters(serviceGroup, false);

  }


  /**
   * explore path, create the service definition, and then create the service
   * @param serviceName Service Name
   * @param neID Network Element ID
   * @param neType Network Element Type
   * @param clientPortEntityIndex Client Port Entity Index
   * @throws MDOperationFailedException
   * @throws SMException
   */
  public int createExploreService(String serviceName, String connectivityName, int neID, NEType neType, EntityIndex clientPortEntityIndex, boolean muteMessages) throws MDOperationFailedException, SMException{
    CustomerDBImpl customer = customerHandler.getCreateUnassignedCustomer();
    String customerName = customer.getName();
    int customerId = customer.getId();
    ParameterGroup serviceGroup = ServiceManagerHelper.getExplorePathServiceDefinition(serviceName, neID, neType, clientPortEntityIndex, customerName, customerId, false);
    serviceGroup = buildExplorePathServiceDefinition(serviceGroup, true);
    ParameterUtils.setSafeParameterValue(serviceGroup, Keyword.SERVICE_CONNECTIVITY_NAME, connectivityName);
    return serviceManagerCtrl.setServiceParameters(serviceGroup, false, false, muteMessages, false);
  }

  /**
   * Create the explored service in classic model based on ML Trail object.
   * This method would be:
   *    - generate a start point of ParameterGroup for explore
   *    - build the service definition ParameterGroup object from ML Trail object
   *    - Create explored service in classic by the general creation (serviceManagerCtrl.setServiceParameters)
   * @param mlTrailDB
   * @param serviceName
   * @param connectivityName
   * @param neID
   * @param neType
   * @param aEnd
   * @param muteMessages
   * @return
   * @throws MDOperationFailedException
   * @throws SMException
   */
  public int createExploreServiceFromMLTrail(MLTrailDBImpl mlTrailDB, String serviceName, String connectivityName, int neID, NEType neType,
                                             ManagedObjectDBImpl aEnd, boolean muteMessages) throws MDOperationFailedException, SMException{
    // to build service group with ml trail ID, bypass the fiber map traverse,
    CustomerDBImpl customer = customerHandler.getCreateUnassignedCustomer();
    String customerName = customer.getName();
    int customerId = customer.getId();
    ParameterGroup serviceGroup = ServiceManagerHelper.getExplorePathServiceDefinition(serviceName, neID, neType, aEnd.getEntityIndex(), customerName, customerId, false);

    // build service definition based on ML trail, if non-UNO related, A-end would be the same as aEnd
    // For UNO, explore from non-UNO side, could be opposite direction of ML Trail
    ManagedObjectDBImpl startPort = mlServiceHelper.getPortDBImpl(mlTrailDB.getAend());
    //if startPort is null, and the trailDb is an ODU trail, check for parent of the start point as data services may be terminating on VCHs in ML
    if (startPort==null && MLNetworkLayer.getAllOduLayers().contains(mlTrailDB.getLayer()) &&
        mlTrailDB.getAend().getType().isOTNVch() && mlTrailDB.getAend().parent() instanceof MLConnectionPointDBImpl cpDB) {
      startPort = mlServiceHelper.getPortDBImpl(cpDB);
    }
    // check if the start port is the same as the start port of the ML trail, at least from the same NE
    boolean sameDirection = startPort == aEnd || (startPort != null && startPort.getNeID() == neID);
    serviceGroup = buildExplorePathServiceDefinitionFromMLTrail(serviceGroup, mlTrailDB, sameDirection);

    ParameterUtils.setSafeParameterValue(serviceGroup, Keyword.SERVICE_CONNECTIVITY_NAME, connectivityName);
    // create service from service definition
    return serviceManagerCtrl.setServiceParameters(serviceGroup, false, false, muteMessages, false);
  }

  /**
   * Explore and populate service defintion based on MLTrail object. The returned ParameterGroup object should contain the explored service data,
   * which could be ready for service creation.
   * @param serviceGroup
   * @param mlTrailDB
   * @param sameDirection
   * @return
   * @throws MDOperationFailedException
   * @throws SMException
   */
  public ParameterGroup buildExplorePathServiceDefinitionFromMLTrail(ParameterGroup serviceGroup, MLTrailDBImpl mlTrailDB, boolean sameDirection)
          throws MDOperationFailedException, SMException{
    // start explore based on ML trail and build service definition with validation
    ServiceDiscoveryParameters serviceDiscoveryParameters = smTrackServiceDiscoveryManager.explorePathFromMLTrail(serviceGroup, mlTrailDB, true, sameDirection);
    serviceGroup = smTrackExplorePathServiceDefinitionCreator.getExplorePathServiceDefinition(serviceDiscoveryParameters, serviceGroup);
    return serviceGroup;
  }
  private SMExplorePathFromMLTrailBuilder getSMExplorePathFromMLTrailBuilder(){
    if(smExplorePathFromMLTrailBuilder == null)
      smExplorePathFromMLTrailBuilder = new SMExplorePathFromMLTrailBuilder();
    return smExplorePathFromMLTrailBuilder;
  }

  /**
   * Build the classic model connection layer with all hierarchy layers based on ML trail.
   * @param mlTrailId
   * @return
   * @throws SMException
   */
  @MDPersistenceContext
  public ConnectionLayerData buildServiceLayersFromMLTrail(ParameterGroup sd, int mlTrailId) throws SMException {
    MLTrailDBImpl mlTrailDB = MLTopologyElementDAO.getInstance().getTrailByID(mlTrailId);
    if(mlTrailDB != null){
      return getSMExplorePathFromMLTrailBuilder().buildServiceLayers(sd, mlTrailDB);
    }
    throw new SMValidationException(SMValidationCondition.ENTITY_NOT_FOUND_IN_DB, "Error building explore service due to missing ML trail with id: "+ mlTrailId);
  }


  public int createExploreNFCService(String serviceName, String connectivityName, int neID,NEType neType, ManagedObjectDBImpl aEnd, ManagedObjectDBImpl zEnd) throws SMException, MDOperationFailedException {
    CustomerDBImpl customer = customerHandler.getCreateUnassignedCustomer();
    String customerName = customer.getName();
    int customerId = customer.getId();

    ManagedObjectDBImpl aEndForExplore = aEnd;
    ManagedObjectDBImpl zEndForExplore = zEnd;
    if(isSpurLinkEndPoints(aEnd,zEnd)){
      Pair<ManagedObjectDBImpl, ManagedObjectDBImpl> endPointsPair = flipEndPointsForSpurLinkCase(aEnd, zEnd);
      aEndForExplore = endPointsPair.getLeft();
      zEndForExplore = endPointsPair.getRight();
      neID = aEndForExplore.getNeID();
    }

    ParameterGroup serviceGroup = ServiceManagerHelper.
            getExplorePathServiceDefinitionForOCS(serviceName, neID, neType, aEndForExplore.getEntityIndex(), customerName, customerId, false);
    serviceGroup = buildExplorePathServiceDefinition(serviceGroup,aEndForExplore,zEndForExplore);
    ParameterUtils.setSafeParameterValue(serviceGroup, Keyword.SERVICE_CONNECTIVITY_NAME, connectivityName);
    return serviceManagerCtrl.setServiceParameters(serviceGroup, false, false, true);
  }

  Pair<ManagedObjectDBImpl,ManagedObjectDBImpl> flipEndPointsForSpurLinkCase(ManagedObjectDBImpl aEnd, ManagedObjectDBImpl zEnd){
    return  (aEnd instanceof PortFSP_R7DBImpl && ((PortFSP_R7DBImpl) aEnd).isClientPort()) &&
            (zEnd instanceof PortFSP_R7DBImpl && ((PortFSP_R7DBImpl) zEnd).isNetworkPort()) ? Pair.of(zEnd, aEnd) : Pair.of(aEnd, zEnd);
  }

  public int createExploreECHService(String serviceName, String connectivityName, int neID, NEType neType, AbstractExternalChannelDBImpl echDB, boolean muteMessages)
          throws MDOperationFailedException, SMException{
    ModuleDBImpl moduleDB = echDB.getContainingModule();
    EntityDBImpl portDB = null;
    for (EntityDBImpl child : moduleDB.getChildrens()) {
      if (child instanceof PortFSP_R7NetworkDBImpl) {
        portDB = child;
        break;
      }
    }

    if(portDB != null) {
      CustomerDBImpl customer = customerHandler.getCreateUnassignedCustomer();
      String customerName = customer.getName();
      int customerId = customer.getId();
      ParameterGroup serviceGroup = ServiceManagerHelper.
              getExplorePathServiceDefinitionForOCS(serviceName, neID, neType, portDB.getEntityIndex(), customerName, customerId, false);

      ParameterUtils.setSafeParameterValue(serviceGroup, Keyword.EXTERNAL_WDM_CHANNEL, Boolean.TRUE.toString());
      if(serviceGroup != null) {
        ParameterGroup node = serviceGroup.getParameterGroup(Group.SOURCE_NODE);
        ParameterGroup card = node.getParameterGroup(Group.WORKING_CARD);
        ParameterGroup params = card.getParameterGroup(Group.CARD_PARAMETERS);
        ParameterGroup extChan = params.getParameterGroup(Group.EXTERNAL_CHANNEL);
        ParameterUtils.setSafeParameterValue(extChan, Keyword.AID, echDB.getAidString());
        ParameterUtils.setSafeParameterValue(extChan, Keyword.ENTITY_INDEX, echDB.getEntityIndex().toString());
        extChan.setEntityIndex(echDB.getEntityIndex().toString());
        serviceGroup = buildExplorePathServiceDefinition(serviceGroup, true);
        ParameterUtils.setSafeParameterValue(serviceGroup, Keyword.SERVICE_CONNECTIVITY_NAME, connectivityName);
        return serviceManagerCtrl.setServiceParameters(serviceGroup, false, false, muteMessages);
      } else {
        throw new MDOperationFailedException("Cannot create ParameterGroup for ECH explore of port: " + portDB);
      }
    } else {
      throw new MDOperationFailedException("ModuleDBImpl has no valid children:" + moduleDB);
    }
  }
  /**
   * Explore the service based on the client port, return the ID of the explored service;
   *
   * @param serviceName Service Name
   * @param neID Network Element Id
   * @param clientPortEntityIndex Client Port Entity Index
   * @param customerName Customer Name
   * @param customerId Customer Id
   * @param port Port
   * @param portsFailed Failed Ports
   * @return Service Id
   */
  private int getExploreService(String serviceName, int neID, NEType neType, EntityIndex clientPortEntityIndex, String customerName, int customerId, PortDBImpl port, TreeMap<PortDBImpl, String> portsFailed) {
    try {
      int createdServiceId = createExploreService(serviceName, neID, neType, clientPortEntityIndex, customerName, customerId);
      log.info("Explore service success - " + createdServiceId);
      return createdServiceId;
    } catch (MDOperationFailedException e) {
      log.error("Explore service failed - " + e.getMessage());
      portsFailed.put(port, massageWithHtmlString(e.getMessage()));
    } catch (SMProvisioningException e) {
      log.error("Explore service failed - " + e.errMessage);
      portsFailed.put(port, massageWithHtmlString(e.errMessage));
    } catch (SMException e) {
      log.error("Explore service failed - " + e.getMessage());
      portsFailed.put(port, massageWithHtmlString(e.getMessage()));
    }
    return 0;
  }

  private String massageWithHtmlString(String message) {
    if (message.startsWith("<html>"))
      return message;
    return "<html><h2>" + message + "</h2></html>";
  }

  /**
   * Write the failure message into a html file; Return the file name with absolute path;
   *
   *
   * @param fileName File Name
   * @param messages Messages
   * @return result
   */
  private String writeToExplorePathFailureFile(String fileName,String messages) {

    File file = new File(fileName);
//                "explorePath_" + neName + "_" + neId + "_" + System.currentTimeMillis() +".html");
    FileOutputStream os = null;
    try {
      if (!file.getParentFile().exists())
        file.getParentFile().mkdirs();
      if (!file.exists())
        file.createNewFile();
      os = new FileOutputStream(file);
      os.write(messages.getBytes("UTF-8"));
    } catch (Exception e) {
      // problem to writing, so log it then
      log.error("Problem to write into a file " + file.getAbsolutePath() + " with contect: " + messages);
    } finally {
      try {
        if (os != null) os.close();
      } catch (Exception ignored) {
      } //ignore exception
    }
    return file.getAbsolutePath();
  }

  /**
   *
   * @param serviceId
   * @return
   * @throws MDOperationFailedException
   */
  @MDPersistenceContext
  public List<ExploreServicesState> joinAndExploreServices(int serviceId, int sessionId, int startProgress, int endProgress, int totalprogress)throws MDOperationFailedException {
    boolean showProgressReport = startProgress != 0 && endProgress > startProgress && totalprogress >= endProgress;
    if(showProgressReport)
      mdMessageSender.setProgressState(sessionId, "Validating and building parameter group...", startProgress,totalprogress, ProgressStatusType.REQUEST_RUNNING);
    // 1. validate and build ParameterGroup
    Map<SubChConnectionDBImpl, List<OChConnectionDBImpl>> odsOCSmap = new HashMap<>();
    StringBuilder detailedFailureReason = new StringBuilder();
    long start0 = System.currentTimeMillis();

    Set<OChConnectionDBImpl> ocss = new HashSet<>();
    AbstractConnectionDBImpl connectionDB = connectionDAO.getConnectionById(serviceId);
    collectDependencies(connectionDB, ocss, odsOCSmap);
    Set<SubChConnectionDBImpl> odsToRexplore = odsOCSmap.keySet().stream().collect(Collectors.toSet());
    List<ExploreServicesState> result = new ArrayList<>();
    int progressStages=odsToRexplore.size()* 2 + 2;

    for (SubChConnectionDBImpl ods : odsToRexplore) {
      List<SubChConnectionDBImpl> odssToJoin = new ArrayList<>();
      Map<AbstractConnectionDBImpl, ParameterGroup> serviceReExploreLayerDataMap = new HashMap<>();
      String validationFailure = validateJoinAndExploreServicesWithDependencies(ods.getId(), odssToJoin, odsOCSmap, serviceReExploreLayerDataMap, sessionId, detailedFailureReason);
      ExploreServicesState state = generateExploreServicesState(connectionDAO.getConnectionById(ods.getId()), StringUtils.EMPTY, odsOCSmap.get(connectionDAO.getConnectionById(ods.getId())), detailedFailureReason);
      result.add(state);
      if(validationFailure != null) {
        setFailure(state, validationFailure);
        mdMessageSender.setProgressState(sessionId, "Service force re-explore validation failed..." + validationFailure, progressStages++,progressStages, ProgressStatusType.REQUEST_FAILED);
        return result;
      }

      MLServiceManagementFacade mlServiceManagementFacade = BeanProvider.get().getBean(MLServiceManagementFacade.class);
      for (SubChConnectionDBImpl subChConnectionDB : odssToJoin) {
        MLTrailDBImpl mlTrailDB = MLTopologyElementDAO.getInstance().getMLTrail(subChConnectionDB.getId());
        if (mlTrailDB != null) {
          mlServiceManagementFacade.cleanupService(mlTrailDB.getId());
        }
      }

      if(odssToJoin.isEmpty()) {
        // not found multiple ODS(s) to join, suggest to use "Re-explore" action
        String message = "Service force re-explore can't locate multiple ODS to join, please try Re-Explore action...";
        setFailure(state, message);
        mdMessageSender.setProgressState(sessionId, message, 6,6, ProgressStatusType.REQUEST_FAILED);
        return result;
      }

      //set services state to in progress
      for(AbstractConnectionDBImpl connDB :serviceReExploreLayerDataMap.keySet()){
        setExplorationAndNotifyGui(connDB.getId(), Definition.ExplorationStatus.IN_PROGRESS);
      }

      int startProgress1 = !showProgressReport?0:(startProgress + (endProgress-startProgress)/4);
      int endProgress1 =  !showProgressReport?0:(endProgress - (endProgress-startProgress)/4);

      // 2. delete ODS as if bottom up fashion (delete ODS and related ODUx layer), leave OCS and all layer untouched
      // rebuild ConnectionLayerData, it should pick up the existing OCS/ODUx layers
      int newServiceId = joinAndExploreServicesDirectly(odssToJoin, odssToJoin.get(0), serviceReExploreLayerDataMap.get(odssToJoin.get(0)), sessionId, startProgress1, endProgress1, totalprogress);
      if (newServiceId != -1) {
        state.setExplorable(ExploreState.Success);
        state.setSelectedItemId(newServiceId);
      } else {
        state.setExplorable(ExploreState.Failed);
      }
    }
    if(showProgressReport)
      mdMessageSender.setProgressState(sessionId, "Re-exploration with dependency completed...", endProgress, totalprogress, endProgress==totalprogress?ProgressStatusType.REQUEST_SUCCEEDED:ProgressStatusType.REQUEST_RUNNING);
    if(log.isDebugEnabled())
      log.debug("[REEXPLORE]....... joinAndExploreServices completed .... took {} ms", System.currentTimeMillis()-start0);
    return result;
  }

  private int joinAndExploreServicesDirectly( List<SubChConnectionDBImpl> odss,
                                               SubChConnectionDBImpl toJoinStartODS,
                                               ParameterGroup serviceGroup, int sessionID, int startProgress, int endProgress, int totalprogress){
    long start0 = System.currentTimeMillis();
    int progressStages=odss.size()*2+2;
    int progressStage=odss.size()+1;
    boolean newProgressReport = startProgress != 0 && endProgress > startProgress && totalprogress >= endProgress;
    if(log.isDebugEnabled())
      log.debug("[REEXPLORE]....... joinAndExploreServicesDirectly started .... ");

    boolean managed = toJoinStartODS.isManaged();
    int adminState = toJoinStartODS.getIntAdminState();
    String serviceName = toJoinStartODS.getName();
    Parameter folderParam = null;
    CustomerServiceGroupDBImpl folder = null;
    if (toJoinStartODS.getServiceGroup() instanceof ContractDBImpl) {
      ContractDBImpl contract = (ContractDBImpl) toJoinStartODS.getServiceGroup();
      folderParam = serviceGroup.getParameter(Keyword.SERVICE_FOLDER);
      if (folderParam != null && folderParam.getId() == contract.getId()) {
        folder = contract.getParentGroup();
      } else {
        folderParam = null; // nothing to do
      }
    }

    // delete All related ODS services in bottom-up fashion, leave OCS untouched
    mdMessageSender.setProgressState(sessionID, "Cleanup for service re-exploration...", startProgress,totalprogress, ProgressStatusType.REQUEST_RUNNING);
    //re-explore service (ODS or OCS) directly, this should build the service and link the pre-existing OCS and related layers
    int newServiceId = -1;
    Set<ManagedObjectDBImpl> endpointsDBS = new HashSet<>();
    for(SubChConnectionDBImpl ods:odss){
      ManagedObjectDBImpl[] startEndpoints = ServiceTopologyHelper.getEndpointEntities(ods, null);
      endpointsDBS.addAll(Arrays.stream(startEndpoints).filter(Objects::nonNull).collect(Collectors.toSet()));
      try{
        smTrackServiceManager.deleteService(ods.getId(), true, false, true, false);
      }catch(NoSuchMDObjectException e){
        log.error(e.toString(), e);
      }catch (Exception ex) {
        log.error("Unknown exception while deleting service " + ods.getLabel(), ex);
        mdMessageSender.setProgressState(sessionID, "Service force re-exploration failed...", progressStages,progressStages, ProgressStatusType.REQUEST_FAILED);
        return newServiceId;
      }
    }

    int progressSpace = (endProgress-startProgress)/6;
    if(newProgressReport) {
      int newStartProgress = startProgress + progressSpace;
      mdMessageSender.setProgressState(sessionID, "Rebuilding services for re-exploration...", newStartProgress, totalprogress, ProgressStatusType.REQUEST_RUNNING);
    }

    if (folderParam != null) {
      ContractDBImpl contract = MDPersistenceHelper.find(ContractDBImpl.class, folderParam.getId());
      if (contract == null) { // was deleted with the service
        ParameterUtils.setSafeParameterValue(serviceGroup, Keyword.SERVICE_NAME, folderParam.getValue());
        if (folder != null) {
          folderParam.setValue(folder.getName());
          folderParam.setId(folder.getId());
        } else {
          folderParam.setValue(null);
          folderParam.setId(0);
        }
      }
    }

    // a little wait for all alarms cleaned up
    try {
      Thread.sleep(300);
    }catch (InterruptedException e){
      log.debug("Ignore - Interrupted - " + e.getMessage());
    }

      try{
        mdMessageSender.setProgressState(sessionID, "Performing service force re-exploration...", progressStage++,progressStages, ProgressStatusType.REQUEST_RUNNING);
        newServiceId = joinBuildExploreService(serviceGroup, serviceName, managed, adminState);
      }catch (TrackServiceDiscoveryException explorationException){
        log.error("Failed - " + explorationException.getTextMessage(true), explorationException);
      }catch (MDOperationFailedException|SMException e){
        log.error("Failed - " + e.getMessage(), e);
      }
    if(newServiceId != -1)
      smServiceHelper.addServiceReExploreEvent(newServiceId);
    // resync all the endpoints of old ODS to make sure the joining line/intra-connect beinng resync-ed properly
    BeanProvider.get().getBean(MLServiceHelper.class).resyncRelatedMLModelByEntities(endpointsDBS);
    if(newProgressReport)
      mdMessageSender.setProgressState(sessionID, "Service re-exploration rebuild classic services completed...", endProgress,totalprogress, ProgressStatusType.REQUEST_RUNNING);
    if(log.isDebugEnabled())
      log.debug("[REEXPLORE]....... joinAndExploreServicesDirectly completed .... took {} ms", System.currentTimeMillis()-start0);
    return newServiceId;
  }

//  @MDTransactional
  private int joinBuildExploreService(ParameterGroup serviceGroup,
                                    String serviceName, boolean managed, int adminState) throws MDOperationFailedException, SMException {

    int connectionId = serviceManagerCtrl.setServiceParameters(serviceGroup, true, true, false);
    return updateJoinedService(connectionId, serviceName, managed, adminState);
  }
  @MDTransactional
  private int updateJoinedService(int connectionId, String serviceName, boolean managed, int adminState){
    // recover underneath layer with the previous name
    AbstractConnectionDBImpl newService = connectionDAO.getConnectionById(connectionId);
    if(newService == null)
      return connectionId;

    // recover the old names if endpoints matched
    newService.setName(serviceName);
    newService.setManaged(managed);
    newService.setAdminState(adminState);

    setExplorationStatus(newService.getId(), Definition.ExplorationStatus.SUCCESS);
    return connectionId;
  }

  @MDPersistenceContext
  private String validateJoinAndExploreServicesWithDependencies(int serviceId,
                                                        List<SubChConnectionDBImpl> odssToStitch,
                                                        Map<SubChConnectionDBImpl, List<OChConnectionDBImpl>> odsOCSmap,
                                                        Map<AbstractConnectionDBImpl, ParameterGroup> serviceReExploreLayerDataMap,
                                                        int sessionID, StringBuilder detailedFalureReason) {
    AbstractConnectionDBImpl connectionDB = connectionDAO.getConnectionById(serviceId);

    if (!connectionDB.isSubChConnection() || connectionDB.isProvisionMode() || connectionDB.isProtected())
      return "Forced - Re-explore does not support protected/provision data service, or non-data service.";

    Set<OChConnectionDBImpl> ocss = new HashSet<>();
    collectDependencies(connectionDB, ocss, odsOCSmap);

    int progressStages=6;
    int progressStage=0;
    // Explore it first
    mdMessageSender.setProgressState(sessionID, "Checking service exploration...", progressStage++,progressStages, ProgressStatusType.REQUEST_RUNNING);
    Map<AbstractConnectionDBImpl, ConnectionLayerData> serviceExploreLayerDataMap = new HashMap<>();
    SubChConnectionDBImpl ods = (SubChConnectionDBImpl)connectionDB;
    String failedReason = getExploreLayerData(ods, serviceReExploreLayerDataMap, serviceExploreLayerDataMap, false, detailedFalureReason);
    if(failedReason != null)
      return failedReason;

    ConnectionLayerData newConnLayerData = serviceExploreLayerDataMap.get(ods);
    if(!newConnLayerData.getChildren(false).isEmpty())
      return "Forced - Re-explore does not support protected data service.";

    mdMessageSender.setProgressState(sessionID, "Validating service exploration...", progressStage++,progressStages, ProgressStatusType.REQUEST_RUNNING);
    // get all OCS related layerData in order
    List<ConnectionLayerData> ocsLayerDataInOrder = new ArrayList<>();
    connectionHelper.collectOCSLayerData(newConnLayerData, ocsLayerDataInOrder);

    // get layerData-OCS matching, and if pre-existing ocs used by what ODSs
    Map<ConnectionLayerData, OChConnectionDBImpl> layerDataOCSMap = new HashMap<>();
    Map<OChConnectionDBImpl, List<OpticalDataService>> ocsUsedByODSsMap = new HashMap<>();
    boolean valid = collectLayerDataDependencies( ocsLayerDataInOrder, layerDataOCSMap, ocsUsedByODSsMap);
    if(!valid)
      return "The explored OCS layer does not match existing OCS.";

    // collect the possible ocs list for each to-be-stitched ODS, also new stitching OCS data list
    List<ConnectionLayerData> stitchOCSDataList = new ArrayList<>();
    List<List<ConnectionLayerData>> OcsBlockList = new ArrayList<>();
    List<ConnectionLayerData> adaptStitchLayerData = new ArrayList<>();
    collectOCSLayerBlocksAndStitchLayerData(ocsLayerDataInOrder, layerDataOCSMap,
            ocsUsedByODSsMap, serviceReExploreLayerDataMap.get(connectionDB),
            stitchOCSDataList, adaptStitchLayerData, OcsBlockList);

    // find out all the ods list to be stitched
    mdMessageSender.setProgressState(sessionID, "Locating services for force re-exploration...", progressStage++,progressStages, ProgressStatusType.REQUEST_RUNNING);
    failedReason = locateExistingODSListForJoin(ods, newConnLayerData, odssToStitch,
            stitchOCSDataList, adaptStitchLayerData, OcsBlockList,
            layerDataOCSMap, odsOCSmap, ocsUsedByODSsMap);

    return failedReason;

  }

  private String collectOCSLayerBlocksAndStitchLayerData(List<ConnectionLayerData> ocsLayerDataInOrder,
                     Map<ConnectionLayerData, OChConnectionDBImpl> layerDataOCSMap,
                     Map<OChConnectionDBImpl, List<OpticalDataService>> ocsUsedByODSsMap,
                     ParameterGroup serviceParameterGroup,
                     List<ConnectionLayerData> stitchOCSDataList, List<ConnectionLayerData> adaptStitchLayerData, List<List<ConnectionLayerData>> OcsBlockList){

    List<ConnectionLayerData> list = null;
    ConnectionLayerData first = null, last = null;
    ConnectionLayerData previousOCSData = null;
    for(ConnectionLayerData ocsData:ocsLayerDataInOrder){
      if(first == null)
        first = ocsData;
      last = ocsData;
      OChConnectionDBImpl ocs = layerDataOCSMap.get(ocsData);
      if(ocs == null) {
        stitchOCSDataList.add(ocsData);
        if(smServiceHelper.isAdaptUnmanagedPort(ocsData.startEntity) || smServiceHelper.isAdaptUnmanagedPort(ocsData.endEntity)){
          adaptStitchLayerData.add(ocsData);
        }
        if(list != null && !list.isEmpty())
          OcsBlockList.add(list);
        list = null;
      }
      else if(previousOCSData != null && isStitchedInSameNode(ocs, layerDataOCSMap.get(previousOCSData), ocsUsedByODSsMap)){
        // current ocs could be stitched with previous ocs within the same node
        ConnectionLayerData stitchOCS = generateTempStitchOcsData(ocs, layerDataOCSMap.get(previousOCSData), serviceParameterGroup);
        if(stitchOCS != null)
           stitchOCSDataList.add(stitchOCS);
        if(list != null && !list.isEmpty())
          OcsBlockList.add(list);
        list = new ArrayList<>();
        list.add(ocsData);
      }
      else {
        if(list == null)
          list = new ArrayList<>();
        list.add(ocsData);
      }
      previousOCSData = ocsData;
    }
    if(list != null && !list.isEmpty() && !OcsBlockList.contains(list))
      OcsBlockList.add(list);

    if(stitchOCSDataList.contains(first) || stitchOCSDataList.contains(last) ||
            (stitchOCSDataList.size() - adaptStitchLayerData.size()/2 + 1 != OcsBlockList.size())){
      return "Couldn't locate all the stitching data.";
    }
    return null;
  }

  private boolean isStitchedInSameNode(OChConnectionDBImpl ocs,
                                      OChConnectionDBImpl previousOCS,
                                      Map<OChConnectionDBImpl, List<OpticalDataService>> ocsUsedByODSsMap){
    ManagedObjectDBImpl[] endPointsInSameNode = previousOCS == null?null:getEndPointsOnSameNE(previousOCS, ocs);
    if(endPointsInSameNode != null){
      List<OpticalDataService> previousODSList = ocsUsedByODSsMap.get(previousOCS);
      List<OpticalDataService> currentODSList = ocsUsedByODSsMap.get(ocs);
      if(previousODSList != null && currentODSList != null &&
             !previousODSList.isEmpty() && !currentODSList.isEmpty()){
              //previousODSList.size() == currentODSList.size()){
        for(OpticalDataService obj1:previousODSList){
          for(OpticalDataService obj2:currentODSList) {
            if (obj1 == obj2)
              return false;
            else if (obj1 != null && obj2 != null && obj1.getId() == obj2.getId())
              return false;
          }
        }
        return true;
      }
    }
    return false;
  }
  private ManagedObjectDBImpl[] getEndPointsOnSameNE(OChConnectionDBImpl ocs1,OChConnectionDBImpl ocs2){
    if(ocs1 == null || ocs2 == null)
      return null;
    if(ocs1.getStartNEID() == ocs2.getStartNEID())
      return new ManagedObjectDBImpl[]{ocs1.getStartPortOrMO(), ocs2.getStartPortOrMO()};
    else if(ocs1.getStartNEID() == ocs2.getPeerNEID())
      return new ManagedObjectDBImpl[]{ocs1.getStartPortOrMO(), ocs2.getPeerPortOrMO()};
    else if(ocs1.getPeerNEID() == ocs2.getStartNEID())
      return new ManagedObjectDBImpl[]{ocs1.getPeerPortOrMO(), ocs2.getStartPortOrMO()};
    else if(ocs1.getPeerNEID() == ocs2.getPeerNEID())
      return new ManagedObjectDBImpl[]{ocs1.getPeerPortOrMO(), ocs2.getPeerPortOrMO()};
    return null;
  }
  private ManagedObjectDBImpl[] getClientPortForEndPoints(List<List<ManagedObjectDBImpl>> list, ManagedObjectDBImpl firstEntity,ManagedObjectDBImpl secondEntity){
    if(!(firstEntity instanceof EntityDBImpl) || !(secondEntity instanceof EntityDBImpl))
      return null;
    // entity list could have ptp or port, check as long as one object matches
    TerminationPointFSP_R7DBImpl ptp1 = null, ptp2 = null;
    EntityDBImpl port1 = null, port2 = null;
    if(firstEntity instanceof TerminationPointFSP_R7DBImpl){
      ptp1 = (TerminationPointFSP_R7DBImpl)firstEntity;
      port1 = EntityDAO.getByEntityIndex(ptp1.getNeID(), ptp1.getEntityIndexOfRelatedEntity());
    }
    else if(firstEntity instanceof PortDBImpl){
      port1 = (EntityDBImpl)firstEntity;
      ptp1 = TerminationPointsDao.getPtpForRelatedEntity(firstEntity.getEntityIndex(), firstEntity.getNeID());
    }
    if(secondEntity instanceof TerminationPointFSP_R7DBImpl){
      ptp2 = (TerminationPointFSP_R7DBImpl)secondEntity;
      port2 = EntityDAO.getByEntityIndex(ptp2.getNeID(), ptp2.getEntityIndexOfRelatedEntity());
    }
    else if(secondEntity instanceof PortDBImpl){
      port2 = (EntityDBImpl)secondEntity;
      ptp2 = TerminationPointsDao.getPtpForRelatedEntity(secondEntity.getEntityIndex(), secondEntity.getNeID());
    }

    ManagedObjectDBImpl firstCPort = null, secondCPort = null;
    for(List<ManagedObjectDBImpl> entityList:list){
      int index1 = -1, index2 = -1;
      if(((ptp1 != null && (index1 = entityList.indexOf(ptp1)) != -1) ||
              (port1 != null && (index1 = entityList.indexOf(port1)) != -1)) &&
              ((ptp2 != null && (index2 = entityList.indexOf(ptp2)) != -1) ||
                      (port2 != null && (index2 = entityList.indexOf(port2)) != -1))){
        ModuleDBImpl m = ptp1 != null? ptp1.getContainingModule():
                (port1 != null?port1.getContainingModule():null);
        for(int i = index1 + 1; i < entityList.size();i++){
          ManagedObjectDBImpl obj = entityList.get(i);
          if(obj instanceof EntityDBImpl && ((EntityDBImpl) obj).getContainingModule() == m) {
            if (obj instanceof PortFSP_R7DBImpl && ((PortFSP_R7DBImpl) obj).isClientPort()) {
              firstCPort = obj;
              break;
            } else if (obj instanceof TerminationPointFSP_R7DBImpl ) {
              EntityDBImpl port = EntityDAO.getByEntityIndex(obj.getNeID(), ((TerminationPointFSP_R7DBImpl) obj).getEntityIndexOfRelatedEntity());
              if(port instanceof PortFSP_R7DBImpl && ((PortFSP_R7DBImpl) port).isClientPort()){
                firstCPort = port;
                break;
              }
            }
          }
        }
        m =  ptp2 != null? ptp2.getContainingModule():
                (port2 != null?port2.getContainingModule():null);
        for(int i = index2 - 1; i > 0; i--){
          ManagedObjectDBImpl obj = entityList.get(i);
          if(obj instanceof EntityDBImpl && ((EntityDBImpl) obj).getContainingModule() == m) {
            if (obj instanceof PortFSP_R7DBImpl && ((PortFSP_R7DBImpl) obj).isClientPort()) {
              secondCPort = obj;
              break;
            } else if (obj instanceof TerminationPointFSP_R7DBImpl ) {
              EntityDBImpl port = EntityDAO.getByEntityIndex(obj.getNeID(), ((TerminationPointFSP_R7DBImpl) obj).getEntityIndexOfRelatedEntity());
              if(port instanceof PortFSP_R7DBImpl && ((PortFSP_R7DBImpl) port).isClientPort()){
                secondCPort = port;
                break;
              }
            }
          }
        }
        if(firstCPort != null && secondCPort != null)
          return new ManagedObjectDBImpl[]{firstCPort, secondCPort};
      }
    }
    return null;
  }
  private ConnectionLayerData generateTempStitchOcsData(OChConnectionDBImpl ocs,
                                                       OChConnectionDBImpl previousOCS,
                                                       ParameterGroup serviceParameterGroup){

    ManagedObjectDBImpl[] endPointsInSameNode = previousOCS == null?null:getEndPointsOnSameNE(previousOCS, ocs);
    if(endPointsInSameNode != null) {
      // extract entity list
      List<List<ManagedObjectDBImpl>> list = smTrackServiceManager.getEntities(serviceParameterGroup);
      ManagedObjectDBImpl[] endPointsCPorts = getClientPortForEndPoints(list, endPointsInSameNode[0], endPointsInSameNode[1]);
      if(endPointsCPorts != null)
        return new ConnectionLayerData(endPointsCPorts[0], endPointsCPorts[1], null, null, ServiceLayer.OCS, Definition.ServiceType.valueOf(previousOCS.getServiceType()));
    }
    return null;
  }

  private String locateExistingODSListForJoin(SubChConnectionDBImpl ods, ConnectionLayerData newConnLayerData,
                                  List<SubChConnectionDBImpl> odssToStitch,
                                  List<ConnectionLayerData> stitchOCSDataList,
                                  List<ConnectionLayerData> adaptStitchLayerData,
                                  List<List<ConnectionLayerData>> OcsBlockList,
                                  Map<ConnectionLayerData, OChConnectionDBImpl> layerDataOCSMap,
                                  Map<SubChConnectionDBImpl, List<OChConnectionDBImpl>> odsOCSmap,
                                  Map<OChConnectionDBImpl, List<OpticalDataService>> ocsUsedByODSsMap)
  {
    List<OChConnectionDBImpl> ocssForODS = odsOCSmap.get(ods);
    for(int i = 0; i <OcsBlockList.size(); i++){
      List<ConnectionLayerData> ocsLayerDataList = OcsBlockList.get(i);
      if(i == 0) { // first block must the explored service on
        if (!matched(ocssForODS, ocsLayerDataList))
          return "Explored OCS does not match with existing OCS.";
        else
          odssToStitch.add(ods);
      }
      else {
        List<OChConnectionDBImpl> ocssList = new ArrayList<>();
        for(ConnectionLayerData data:ocsLayerDataList ) {
          OChConnectionDBImpl ocs = layerDataOCSMap.get(data);
          if(ocs.isProvisionMode())
            return "Forced - Re-explore does not support on provisioning service.";
          ocssList.add(ocs);
        }
        SubChConnectionDBImpl matchODS = null;
        ConnectionLayerData beforeStitch = stitchOCSDataList.size() >= i?stitchOCSDataList.get(i-1):null;
        if(beforeStitch != null){
          ConnectionLayerData afterStitch = i == OcsBlockList.size()-1?newConnLayerData:stitchOCSDataList.get(i);
          ManagedObjectDBImpl beforeStitchStart = beforeStitch.getStartEntity();
          ManagedObjectDBImpl beforeStitchEnd = beforeStitch.getEndEntity();
          ManagedObjectDBImpl afterStitchStart = afterStitch.getStartEntity();
          ManagedObjectDBImpl afterStitchEnd = afterStitch.getEndEntity();
          if(adaptStitchLayerData.contains(beforeStitch)){
            if(smServiceHelper.isAdaptUnmanagedPort(beforeStitchStart))
              beforeStitchStart = getOtherEndNonAdaptObject(adaptStitchLayerData, beforeStitchStart);
            else if(smServiceHelper.isAdaptUnmanagedPort(beforeStitchEnd))
              beforeStitchEnd = getOtherEndNonAdaptObject(adaptStitchLayerData, beforeStitchEnd);
          }
          if(adaptStitchLayerData.contains(afterStitch)){
            if(smServiceHelper.isAdaptUnmanagedPort(afterStitchStart))
              afterStitchStart = getOtherEndNonAdaptObject(adaptStitchLayerData, afterStitchStart);
            else if(smServiceHelper.isAdaptUnmanagedPort(afterStitchEnd))
              afterStitchEnd = getOtherEndNonAdaptObject(adaptStitchLayerData, afterStitchEnd);
          }
          matchODS = findODS(ocssList, ocsUsedByODSsMap, odssToStitch.get(odssToStitch.size()-1),
                  beforeStitchStart, beforeStitchEnd, afterStitchStart, afterStitchEnd);
        }
        if(matchODS == null) // wrong
          return "Failed to locate the existing ODS for joining.";
        else {
          odsOCSmap.put(matchODS, ocssList);
          odssToStitch.add(matchODS);
        }
      }
    }
    return null;
  }

  ManagedObjectDBImpl getOtherEndNonAdaptObject( List<ConnectionLayerData> adaptStitchLayerDataList, ManagedObjectDBImpl adaptEnd){

    int adapteNeId = adaptEnd.getNeID();
    for(ConnectionLayerData data:adaptStitchLayerDataList){
      ManagedObjectDBImpl start = data.getStartEntity();
      ManagedObjectDBImpl end = data.getEndEntity();
      if(start.getNeID() == adapteNeId && start != adaptEnd)
        return end;
      else if(end.getNeID() == adapteNeId && end != adaptEnd)
        return start;
    }
    return null;
  }
  SubChConnectionDBImpl findODS(List<OChConnectionDBImpl> ocssList,
                                Map<OChConnectionDBImpl, List<OpticalDataService>> ocsUsedByODSsMap,
                                SubChConnectionDBImpl previousODS,
                                 ManagedObjectDBImpl beforeStitchStart,
                                 ManagedObjectDBImpl beforeStitchEnd,
                                 ManagedObjectDBImpl afterStitchStart,
                                 ManagedObjectDBImpl afterStitchEnd){
    List<OpticalDataService> odss = getODSs(ocssList, ocsUsedByODSsMap);
    if(odss.isEmpty())
      return null;

    // verify to get a matched ODS with front and after stitch layer data
    PortDBImpl startPort = previousODS.getStartPort();
    PortDBImpl endPort = previousODS.getPeerPort();
    ManagedObjectDBImpl newODSEnd1 = null;
    ManagedObjectDBImpl startEnd = beforeStitchStart;
    ManagedObjectDBImpl endEnd = beforeStitchEnd;
    if(startEnd == startPort || startEnd == endPort)
      newODSEnd1 = endEnd;
    else if(endEnd == startPort || endEnd == endPort)
      newODSEnd1 = startEnd;

    // one end to locate the ODS, and get the other end;
    SubChConnectionDBImpl matchODS = null;
    ManagedObjectDBImpl newODSEnd2 = null;
    for(OpticalDataService ods:odss){
      startPort = ods.getStartPort();
      endPort = ods.getPeerPort();
      if(newODSEnd1 == startPort)
        newODSEnd2 = endPort;
      else if(newODSEnd1 == endPort)
        newODSEnd2 = startPort;

      if(newODSEnd2 != null){
        matchODS = (SubChConnectionDBImpl)ods;
        break;
      }
    }
    // verify if the other end match with one end of afterStitchData
    if(newODSEnd2 == afterStitchStart || newODSEnd2 == afterStitchEnd)
      return matchODS;

    return null;
  }

  private List<OpticalDataService> getODSs(List<OChConnectionDBImpl> ocss, Map<OChConnectionDBImpl, List<OpticalDataService>> ocsUsedByODSsMap){
    List<OpticalDataService> commonOdss = new ArrayList<>();
    Collection<OpticalDataService> odssForOcs = ocsUsedByODSsMap.get(ocss.get(0));
    commonOdss.addAll(odssForOcs);
    // verify the rest of OCS
    for(int i = 1; i < ocss.size(); i++){
      OChConnectionDBImpl ocs = ocss.get(i);
      Collection<OpticalDataService> odssForOcs1 = ocsUsedByODSsMap.get(ocs);
      commonOdss.retainAll(odssForOcs1);
      if(commonOdss.isEmpty())
        break;
    }
    return commonOdss;
  }
  /**
   * return true if all endpoints are matched;
   * @param ocss
   * @param ocsLayerDataList
   * @return
   */
  private boolean matched(List<OChConnectionDBImpl> ocss, List<ConnectionLayerData> ocsLayerDataList){
    if(ocss.size() != ocsLayerDataList.size())
      return false;
    for(int i = 0; i < ocss.size(); i++){
      if(!connectionHelper.isConnectionMatched(ocss.get(i), ocsLayerDataList.get(i), null))
        return false;
    }
    return true;
  }

  private boolean collectLayerDataDependencies(List<ConnectionLayerData> ocsLayerDataInOrder,
                                            Map<ConnectionLayerData, OChConnectionDBImpl> layerDataOCSMap,
                                            Map<OChConnectionDBImpl, List<OpticalDataService>> ocsUsedByODSsMap){
    for(ConnectionLayerData connectionLayer: ocsLayerDataInOrder){
      // find ocs DBImpl
      int connectionId1 = smServiceHelper.getConnectionID(connectionLayer.getStartEntity(), connectionLayer.getEndEntity(), ServiceLayer.OCS);
      int connectionId2 = smServiceHelper.getConnectionID(connectionLayer.getEndEntity(), connectionLayer.getStartEntity(), ServiceLayer.OCS);

      if (connectionId1 != 0 && connectionId2 != 0) {
        if (connectionId1 == connectionId2) {
          OChConnectionDBImpl ocs = (OChConnectionDBImpl) connectionDAO.getConnectionById(connectionId1);
          layerDataOCSMap.put(connectionLayer, ocs);

          Collection<OpticalDataService> odssForOcs = connectionDAO.getContainedSubChConnections(connectionId1);
          if(odssForOcs.isEmpty())
            ocsUsedByODSsMap.put(ocs, new ArrayList<>());
          else
            ocsUsedByODSsMap.put(ocs, new ArrayList<>(odssForOcs));

        } else  // OCS endpoints on different existing ocs
          return false;
      }
//      else // not find matching ocs
//        return false;
    }
    return true;
  }

  public ExploreServicesState splitMlService(int serviceId, int sessionId) {
    AbstractConnectionDBImpl odsConnectionDB = connectionDAO.getConnectionById(serviceId) ;
    String odsName = odsConnectionDB.getName();
    Map<SubChConnectionDBImpl, List<OChConnectionDBImpl>> odsOCSmap = new HashMap<>();
    Set<OChConnectionDBImpl> ocss = new HashSet<>();
    StringBuilder detailedFailureReason = new StringBuilder();
    int progressStages=6;
    int progressStage=0;

    collectDependencies(odsConnectionDB, ocss, odsOCSmap);
    ExploreServicesState state = generateExploreServicesState(connectionDAO.getConnectionById(serviceId), StringUtils.EMPTY, odsOCSmap.get(connectionDAO.getConnectionById(serviceId)), detailedFailureReason);
    mdMessageSender.setProgressState(sessionId, "Checking service exploration...", progressStage++,progressStages, ProgressStatusType.REQUEST_RUNNING);

    try {
      String validationReason = validateReExploreForLineRemovalSupported(odsConnectionDB);
      if (validationReason != null) {
        setFailure(state, validationReason);
        mdMessageSender.setProgressState(sessionId, "Service force re-explore validation failed..." + validationReason, 6,6, ProgressStatusType.REQUEST_FAILED);
        return state;
      }

      MLServiceManagementFacade mlServiceManagementFacade = BeanProvider.get().getBean(MLServiceManagementFacade.class);
      MLTrailDBImpl trailDB = MLTopologyElementDAO.getInstance().getMLTrail(serviceId);
      if (trailDB != null) {
        mlServiceManagementFacade.cleanupService(trailDB.getId());
          try {
            Thread.sleep(2000);
          } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
          }
      }

      List<ParameterGroup> serviceGroups = getExplorePathSplitServiceDefinition((SubChConnectionDBImpl) odsConnectionDB, null);
      if (!CollectionUtils.isEmpty(serviceGroups)) {
        mdMessageSender.setProgressState(sessionId, "Cleanup for service re-exploration...", progressStage++,progressStages, ProgressStatusType.REQUEST_RUNNING);

        try {
          smTrackServiceManager.deleteService(serviceId, true, true, false, false);
        } catch(NoSuchMDObjectException e) {
          log.error(e.toString(), e);
        } catch (Exception ex) {
          log.error("Unknown exception while deleting service " + odsConnectionDB.getLabel(), ex);
          String message = "Service force re-exploration failed...";
          setFailure(state, message);
          mdMessageSender.setProgressState(sessionId, message, progressStages,progressStages, ProgressStatusType.REQUEST_FAILED);
          return state;
        }

        mdMessageSender.setProgressState(sessionId, "Performing service force re-exploration...", progressStage++,progressStages, ProgressStatusType.REQUEST_RUNNING);

        String[] suffixes = new String[]{"A", "B"};
        int index = 0;
        List<String> odsNames = new ArrayList<>();
        boolean serviceExplored = true;

        for (ParameterGroup serviceGroup : serviceGroups) {
          index = index + 1;
          int newServiceId = serviceManagerCtrl.setServiceParameters(serviceGroup, true, true, false);
          if (!(newServiceId > 0))
            serviceExplored = false;
          state.setSelectedItemId(newServiceId);
        }

        if (serviceExplored) {
          mdMessageSender.setProgressState(sessionId, "Service force re-exploration completed...", progressStages, progressStages, ProgressStatusType.REQUEST_SUCCEEDED);
          state.setExplorable(ExploreState.Success);
        }

      } else {
        mdMessageSender.setProgressState(sessionId, "Service force re-exploration failed...", progressStages,progressStages, ProgressStatusType.REQUEST_FAILED);
        state.setExplorable(ExploreState.Failed);
      }
    } catch (Exception e) {}
    return state;
  }

  private ExploreServicesState setFailure(ExploreServicesState exploreServicesState, String message){
    exploreServicesState.setExplorable(ExploreState.Failed);
    exploreServicesState.setFailureReason(message);
    return exploreServicesState;
  }
    /**
     *
     * @param serviceIds Service ids
     * @param validateOnly true/false
     * @param sessionID  User session id
     * @return Exploration results
     * @throws MDOperationFailedException
     */
  public List<ExploreServicesState> reExploreServicesWithDependencies(List<Integer> serviceIds, boolean validateOnly, boolean exploreOnlyNeeded, int sessionID)throws MDOperationFailedException {
      return reExploreServicesWithDependencies(serviceIds, validateOnly, exploreOnlyNeeded, sessionID, 0,0,0);
    }
  public boolean hasODSorOCSMismatch(AbstractConnectionDBImpl connectionDB) {
    if (!connectionDB.isSubChConnection() && !connectionDB.isOChConnection())
      return false;
    boolean hasMismatch = isMismatchState(connectionDB.getExplorationStatus());
    if (connectionDB.isSubChConnection()) {
      if (!hasMismatch) {
        List<OChConnectionDBImpl> ocss = connectionDB.getAllContainingOChConnectionsDBs();
        for (OChConnectionDBImpl ocs : ocss) {
          // FNMD-105143 - the problem is to treat it mismatch when its ocs not in matched but ocs has other ods in mismatch
          // Fix: ODS not mismatch, then we should treat ODS to be mismatch only if any of its OCS is in mismatch
          if(isMismatchState(ocs.getExplorationStatus())) {
            hasMismatch = true;
            break;
          }
        }
      }
    }
    return hasMismatch;
  }

  private boolean isMismatchState(int explorationStatus){
    return explorationStatus != Definition.ExplorationStatus.NA.getId() &&
            explorationStatus != Definition.ExplorationStatus.INITIAL.getId() &&
            explorationStatus != Definition.ExplorationStatus.SUCCESS.getId();
  }

  private boolean canReExplore(MLTrailDBImpl trailDB){
    if(trailDB.getNewSecondaryLifecycleStateSet().isDamaged())
      return true;
    // ML model is OK, check the associated classic layer
    if(trailDB.getPropertyOrNull(MLTEPropertyKey.SETUPMODE) == Definition.ServiceCreationMode.TRACK_MODE) {
        AbstractConnectionDBImpl connectionDB = getExplorableClassicService(trailDB);
        boolean pureTrack = connectionDB != null && connectionDB.isTrackMode() && !connectionDB.isExplored();
        // pure track or if classic explore service has mismatch
        return pureTrack ||hasODSorOCSMismatch(connectionDB);
    }
    return false;
  }
  public List<ExploreServicesState> reExploreServicesWithDependencies(List<Integer> serviceIds, boolean validateOnly, boolean exploreOnlyNeeded, int sessionID,
                                                                      int startProgress, int endProgress, int totalprogress)throws MDOperationFailedException {
    boolean showProgressReport = startProgress != 0 && endProgress > startProgress && totalprogress >= endProgress;
    if(showProgressReport)
      mdMessageSender.setProgressState(sessionID, "Collecting Service Dependencies...", startProgress,totalprogress, ProgressStatusType.REQUEST_RUNNING);
    // get all dependencies and reexplorable state
    long start0 = System.currentTimeMillis();
    Map<ExploreServicesState, ParameterGroup> exploreStateDataMap = new HashMap<>();
    Map<ExploreServicesState, ParameterGroup> successExploreStateDataMap = new HashMap<>();
    List<String> inputServiceNames=new ArrayList<>();
    Map<ExploreServicesState, Map<ServiceLayer, List<TerminationPointsLayerData>>> serviceTerminationPointsMap = new HashMap<>();
    MLTopologyElementDAO mlTopologyElementDAO = BeanProvider.get().getBean(MLTopologyElementDAO.class);

    int firstId = validateReExploreServicesWithDependencies(serviceIds, exploreStateDataMap, serviceTerminationPointsMap, validateOnly, exploreOnlyNeeded, inputServiceNames, sessionID);
    // if just validation, return;
    int progressStages=exploreStateDataMap.size()*(validateOnly?1:2)+2;
    if(validateOnly) {
      mdMessageSender.setProgressState(sessionID, "Service re-exploration validation completed...", progressStages++,progressStages, ProgressStatusType.REQUEST_SUCCEEDED);
      return generateListWithFirstOf(firstId, exploreStateDataMap.keySet());
    }

    // if any one has a failed state, return now without explore
    Set<Integer> odsIDsToClearMismatch = new HashSet<>();
    Set<ExploreServicesState> odsIDsToResyncModules = new HashSet<>();
    for(ExploreServicesState state:exploreStateDataMap.keySet()){
      if(state.getState() == ExploreState.Failed) {
        if (exploreStateDataMap.size() == 1) {
          mdMessageSender.setProgressState(sessionID, "Service re-exploration completed with failures...", progressStages++, progressStages, ProgressStatusType.REQUEST_SUCCEEDED);
          return generateListWithFirstOf(firstId, exploreStateDataMap.keySet());
        }
      } else {
        MLTrailDBImpl mlDb = mlTopologyElementDAO.getMLTrail(state.getOdsId());
        if(mlDb != null) {
          if (canReExplore(mlDb)) { // if trail is damaged, pure track, or damaged classic, then trigger re-explore
            odsIDsToClearMismatch.add(state.getOdsId());
          } else {
            state.setExplorable(ExploreState.No_Mismatch);
            state.setFailureReason("Connection is OK, hence not re-explored.");
          }
        } else { // service doesn't have ML trail, since this is an explored service, resync all related modules to bring it up
          odsIDsToResyncModules.add(state);
        }
      }
    }

    // explore now
    // clear ML model mismatch if existing ML trail exists
    if(!odsIDsToClearMismatch.isEmpty()) {
      MLServiceManagementFacade mlServiceManagementFacade = BeanProvider.get().getBean(MLServiceManagementFacade.class);
      Set<Integer> neMoIDs = new HashSet<>();
      for (Integer odsId : odsIDsToClearMismatch) {
        MLTrailDBImpl trailDB = MLTopologyElementDAO.getInstance().getMLTrail(odsId);
        if (trailDB != null) {
          if(trailDB.getAEndConnectionPoint()!= null)
            neMoIDs.add(mlTopologyElementDAO.getNeIDFromRef(trailDB.getAEndConnectionPoint()));
          if(trailDB.getZEndConnectionPoint()!= null)
            neMoIDs.add(mlTopologyElementDAO.getNeIDFromRef(trailDB.getZEndConnectionPoint()));
          mlServiceManagementFacade.cleanupService(trailDB.getId());
          int count = 0; // at most wait 2 sec for each service
          while(count++ <=10){
            try {
              Thread.sleep(200);
            } catch (InterruptedException e) {
              Thread.currentThread().interrupt();
            }
            if(MLTopologyElementDAO.getInstance().getTrailByID(odsId) == null)
              break;
          }
        }
      }
      log.info("Affected {} NEs to trigger fix db inconsistency", neMoIDs.size());
      BeanProvider.get().getBean(MLTopologyCheckerUtils.class).triggerFixDBInconsistency(neMoIDs, 2000L); // wait 2 sec between
      log.info("Finished fix db inconsistency on {} NEs", neMoIDs.size());
    }

    //set services state to in progress
    for(ExploreServicesState state:exploreStateDataMap.keySet()){
      if(state.getState() != ExploreState.Failed && state.getState() != ExploreState.No_Mismatch) { // only good ones to change state
      int serviceId = state.retrieveExplorableServiceId();
      setExplorationAndNotifyGui(serviceId, Definition.ExplorationStatus.IN_PROGRESS);
      }
    }

    Map<Integer, String> exploreResult = new HashMap<>();
    Map<Integer, String> detailedFailuerResult = new HashMap<>();
    successExploreStateDataMap = exploreStateDataMap.entrySet().stream().filter(e -> e.getKey().getState() != ExploreState.Failed && e.getKey().getState() != ExploreState.No_Mismatch)
                                                                        .collect(Collectors.toMap(e -> e.getKey(), e -> e.getValue()));
    exploreStateDataMap.entrySet().removeIf(e -> e.getKey().getState() != ExploreState.Failed && e.getKey().getState() != ExploreState.No_Mismatch) ; // remove the success entries

    int startProgress1 = !showProgressReport?0:(startProgress + (endProgress-startProgress)/4);
    int endProgress1 =  !showProgressReport?0:(endProgress - (endProgress-startProgress)/4);
    reExploreServicesDirectly(successExploreStateDataMap, serviceTerminationPointsMap, exploreResult, inputServiceNames, sessionID,
            detailedFailuerResult, startProgress1, endProgress1, totalprogress);
    exploreStateDataMap.putAll(successExploreStateDataMap); // merge both the success and failure states

    if(showProgressReport && !successExploreStateDataMap.isEmpty())
      mdMessageSender.setProgressState(sessionID, "Re-exploration with resync-ing modules...", endProgress1, totalprogress, ProgressStatusType.REQUEST_RUNNING);
    // update state from Explorable to failed/success
    Map<Integer, ParameterGroup> sdToResynModules = new HashMap<>();
    for(ExploreServicesState state:exploreStateDataMap.keySet()){
      if (state.getState() != ExploreState.Failed && state.getState() != ExploreState.No_Mismatch) {
        int serviceId = state.retrieveExplorableServiceId();
        if(serviceId != 0){
          String result = exploreResult.get(serviceId);
          state.setExplorable(result == null ? ExploreState.Success : ExploreState.Failed);
          state.setFailureReason(result);
          state.setErrorMessage(detailedFailuerResult.get(serviceId));

          if(odsIDsToResyncModules.contains(state) && result == null && state.getSelectedItemId() > 0) {
            ParameterGroup sd;
            if ((sd = exploreStateDataMap.get(state)) != null)
              sdToResynModules.put(state.getSelectedItemId(), sd);
          }
        }
      }
    }
    resyncModulesOnServices(sdToResynModules);
    if(showProgressReport && !successExploreStateDataMap.isEmpty())
       mdMessageSender.setProgressState(sessionID, "Re-exploration with dependency completed...", endProgress, totalprogress, endProgress==totalprogress?ProgressStatusType.REQUEST_SUCCEEDED:ProgressStatusType.REQUEST_RUNNING);
    if(log.isDebugEnabled())
      log.debug("[REEXPLORE]....... reExploreServicesWithDependencies completed .... took {} ms", System.currentTimeMillis()-start0);
    return generateListWithFirstOf(firstId, exploreStateDataMap.keySet());
  }

  @MDPersistenceContext
  private void resyncModulesOnServices(Map<Integer, ParameterGroup> sdToResynModules){
    if(sdToResynModules.isEmpty())
      return;
    Set<ModuleDBImpl> moduleToRefresh = new HashSet<>();
    for(Integer serviceId:sdToResynModules.keySet()){
      AbstractConnectionDBImpl connectionDB = null;
      try{
        ParameterGroup sd = sdToResynModules.get(serviceId);
        if(sd != null) {
          List<List<ManagedObjectDBImpl>> grouplist = smTrackServiceManager.getEntities(sd);
          grouplist.forEach(list -> {
            list.forEach(obj -> {
              if (obj instanceof EntityDBImpl) {
                ModuleDBImpl moduleDB;
                if ((moduleDB = ((EntityDBImpl) obj).getContainingModule()) != null)
                  moduleToRefresh.add(moduleDB);
              }
            });
          });
        }
        connectionDB = MDPersistenceHelper.getObjectById(AbstractConnectionDBImpl.class, serviceId);
        if(connectionDB != null){
          ModuleDBImpl module;
          if((module = connectionDB.getStartModule())!= null)
            moduleToRefresh.add(module);
          if((module = connectionDB.getPeerModule())!= null)
            moduleToRefresh.add(module);
        }
      }catch (EntityNotFoundException e){
        log.debug("EntityNotFoundException");
      }
    }
    moduleToRefresh.forEach(moduleDB -> {
       Future future =  mlTaskExecutorService.submitFutureTask(new ResyncModuleTask(moduleDB.getNeID(), moduleDB.getId(),
                             MLTopologyElementDAO.getInstance(), MLTopologyMoFacade.getInstance(), MLTopologyResyncFacade.getInstance()));
       if (future != null)
         BeanProvider.get().getBean(MLServiceHelper.class).waitForFuture(future, true, 5000);
    });

  }

  @MDTransactional
  public void clearMismatchesOnLinesForReExploredServices(List<ExploreServicesState> result) {
    for (ExploreServicesState exploreServicesState : result) {
      if (exploreServicesState.getState() == ExploreState.Success) {
        int ocsId = exploreServicesState.getSelectedItemId();
        AbstractConnectionDBImpl connection = connectionDAO.getConnectionById(ocsId);
        if (connection != null) {
          List<LineDBImpl> lines = connection.getRoute();
          if (lines != null) {
            for (LineDBImpl line : lines) {
              linkMismatchAlarmsHelper.clearMismatch(line);
            }
          }
          // handle prot lines
          List<LineDBImpl> protLines = null;
          if(connection.isOChConnection() && connection instanceof AbstractProtOChConnectionDBImpl)
            protLines = ((AbstractProtOChConnectionDBImpl) connection).getProtRoute();
          else if(connection.isSubChConnection())
            protLines = ((SubChConnectionDBImpl)connection).getProtRoute();
          if (protLines != null) {
            for (LineDBImpl line : lines) {
              linkMismatchAlarmsHelper.clearMismatch(line);
            }
          }
        }
      }
    }
  }

  /**
   * Generate the list to match the first item with the service id;
   * @param firstServiceId First Service Id
   * @param servicesStates Service States
   * @return Exploration results
   */
  private ArrayList<ExploreServicesState> generateListWithFirstOf(int firstServiceId, Set<ExploreServicesState> servicesStates){
    ArrayList<ExploreServicesState> list = new ArrayList<>(servicesStates);
    ExploreServicesState first = null;
    for(ExploreServicesState state:list){
      if(firstServiceId == state.retrieveExplorableServiceId()){
        first = state;
        break;
      }
    }
    if(first != null){
      list.remove(first);
      list.add(0, first);
    }
    return list;
  }

  private String getServiceName(ExploreServicesState state){
    if(state == null)
      return "";
    String odsName = state.getOdsName();
    String ocsName = state.getOcsNames();
    String cfcName = state.getCfcName();
    return (odsName!= null && !odsName.isEmpty())?odsName:(cfcName != null && !cfcName.isEmpty()?cfcName: (ocsName != null?ocsName:""));
  }
  private void reExploreServicesDirectly(Map<ExploreServicesState, ParameterGroup> exploreStateDataMap,
                                         Map<ExploreServicesState, Map<ServiceLayer, List<TerminationPointsLayerData>>> serviceTerminationPointsMap,
                                         Map<Integer, String> exploreResult, List<String> inputServiceNames,int sessionID, Map<Integer, String> detailedFailureResult,
                                         int startProgress, int endProgress, int totalprogress){
    long start0 = System.currentTimeMillis();

    boolean showProgressReport = startProgress != 0 && endProgress > startProgress && totalprogress >= endProgress;
    if(log.isDebugEnabled())
      log.debug("[REEXPLORE]....... reExploreServicesDirectly started .... ");
    //Keep track of Operational State for ODSs during re-explore action
    Map<ExploreServicesState, OperStateStatus> odsOperationalStateStatus = new HashMap<>();
    // delete All related services
    if(showProgressReport)
      mdMessageSender.setProgressState(sessionID, "Deleting old services for re-exploration...", startProgress,totalprogress, ProgressStatusType.REQUEST_RUNNING);
    for(ExploreServicesState state:exploreStateDataMap.keySet()){
      int serviceId = state.retrieveExplorableServiceId();
      //Check if we are deleting an ODS. In this case we need to keep track of the Operational State and copy the values after service is recreated.
      boolean isOds = state.getOdsId()==serviceId;
      if (isOds) {
        Optional<OperStateStatus> odsOperState = getOperStateStatusForOds(serviceId);
        if (odsOperState.isPresent()) {
          odsOperationalStateStatus.put(state, odsOperState.get());
        }
      }
      try{
        smTrackServiceManager.deleteService(serviceId, true, true, false, true);
      }catch(NoSuchMDObjectException | SMValidationException e){
        log.error(e.toString(), e);
      }
    }
    int previousProgress = 0;
    int progressSpace = (endProgress-startProgress)/6;
    float eachServiceBuildStep = 0;
    if(showProgressReport) {
      int newStartProgress = startProgress + progressSpace;
      mdMessageSender.setProgressState(sessionID, "Rebuilding services for re-exploration...", newStartProgress, totalprogress, ProgressStatusType.REQUEST_RUNNING);
      previousProgress = newStartProgress;
      eachServiceBuildStep = (endProgress-startProgress - 2*progressSpace)/(exploreStateDataMap.size() *1.0f);
    }
    //re-explore service (ODS or OCS) one by one directly, report the result
    Set<Integer> updatedOcsIDs = new HashSet<>();
    Set<Integer> notifiedConnectionIDs = new HashSet<>();
    for(ExploreServicesState state:exploreStateDataMap.keySet()){
      int serviceId = state.retrieveExplorableServiceId();
      ParameterGroup serviceGroup = exploreStateDataMap.get(state);
      Map<ServiceLayer, List<TerminationPointsLayerData>> existingTPLayersMap = serviceTerminationPointsMap.get(state);

      try{
        if(showProgressReport){
          previousProgress = (int)(previousProgress + eachServiceBuildStep);
          mdMessageSender.setProgressState(sessionID, "Rebuild service ..." + getServiceName(state), previousProgress, totalprogress, ProgressStatusType.REQUEST_RUNNING);
        }
        int selectionId=rebuildExploreService(serviceGroup, updatedOcsIDs, notifiedConnectionIDs, existingTPLayersMap,inputServiceNames, Optional.ofNullable(odsOperationalStateStatus.get(state)));
        state.setSelectedItemId(selectionId);
      }catch (TrackServiceDiscoveryException explorationException){
        exploreResult.put(serviceId, "Failed - " + explorationException.getTextMessage(true));
        detailedFailureResult.put(serviceId, explorationException.getTextMessage(false));
      } catch (Exception e){
        exploreResult.put(serviceId, "Failed - " + e.getMessage());
        detailedFailureResult.put(serviceId, e.getMessage());
        log.error(e.toString(), e);
      }
    }
    notifiedConnectionIDs.forEach(c->smServiceHelper.addServiceReExploreEvent(c));
    if(showProgressReport)
      mdMessageSender.setProgressState(sessionID, "Service re-exploration - updating ext layers...", endProgress-1, totalprogress, ProgressStatusType.REQUEST_RUNNING);
    // update all ext layers
    // extended layer update under OCS - only works once OCS built.
    smTrackServiceManager.updateOCSExtLayers(new ArrayList<>(updatedOcsIDs));
    if(showProgressReport)
      mdMessageSender.setProgressState(sessionID, "Service re-exploration rebuild classic services completed...", endProgress,totalprogress, ProgressStatusType.REQUEST_RUNNING);
    if(log.isDebugEnabled())
      log.debug("[REEXPLORE].......reExploreServicesDirectly (rebuild classic services) completed... took {} ms", exploreStateDataMap.size(), System.currentTimeMillis()-start0);

  }

  private int rebuildExploreService(ParameterGroup serviceGroup, Set<Integer> updatedOcsIDs, Set<Integer> notifiedConnectionIds,
                                    Map<ServiceLayer, List<TerminationPointsLayerData>> existingTPLayersMap, List<String> inputServiceNames,
                                    Optional<OperStateStatus> odsOperStateStatus) throws MDOperationFailedException, SMException{

      int connectionId = serviceManagerCtrl.setServiceParameters(serviceGroup, true);
      // recover underneath layer with the previous name
      AbstractConnectionDBImpl newService = connectionDAO.getConnectionById(connectionId);
      if (odsOperStateStatus.isPresent()) {
        restoreOldOperState(odsOperStateStatus, newService);
        ((OperationalStatusHandlerImpl)operationalStatusHandler).getOperationalStatusNotifier().notifyServiceStateCounter_Async(newService, newService.getOperationalStatus().getServiceOperationalState(), null);
      }

      if(newService.isOChConnection()) {
        updatedOcsIDs.add(newService.getId());
      }
      else if(newService.isSubChConnection() || newService instanceof ConnectionDBImpl){
        List<OChConnectionDBImpl> ocss = newService.getAllContainingOChConnectionsDBs();
        for(OChConnectionDBImpl ocs:ocss) {
          updatedOcsIDs.add(ocs.getId());
        }
      }
      // recover the old names if endpoints matched
    updateConnectionWithTPLayerData(newService, existingTPLayersMap,notifiedConnectionIds,inputServiceNames);
    if (notifiedConnectionIds.isEmpty()){
      collectNotifiedObjects(notifiedConnectionIds,newService);
    }
    setExplorationStatus(newService.getId(), Definition.ExplorationStatus.SUCCESS);
    return connectionId;
  }
  @MDTransactional
  public void updateConnectionWithTPLayerData(AbstractConnectionDBImpl connection, Map<ServiceLayer, List<TerminationPointsLayerData>> tpLayersMap, Set<Integer> notifiedConnectionIds, List<String> inputServiceNames) {
    connection = MDPersistenceHelper.refind(connection,connection.getId());
    smTrackServiceManager.updateConnectionWithTPLayerData(connection, tpLayersMap,notifiedConnectionIds,inputServiceNames);
  }
  @MDTransactional
  private void restoreOldOperState(Optional<OperStateStatus> odsOperStateStatus, AbstractConnectionDBImpl newService) {
    newService = MDPersistenceHelper.refind(newService,newService.getId());
    newService.getOperationalStatus().setOperationalStatus(odsOperStateStatus.get().operationalStatus);
    newService.getOperationalStatus().setAcknowledged(odsOperStateStatus.get().acknowledged);
    newService.getOperationalStatus().setAckdBy(odsOperStateStatus.get().ackdBy);
    newService.getOperationalStatus().setAckdAt(odsOperStateStatus.get().ackdAt);
    newService.getOperationalStatus().setFaulted_timestamp(odsOperStateStatus.get().faulted_timestamp);
    newService.getOperationalStatus().setDegraded_timestamp(odsOperStateStatus.get().degraded_timestamp);
  }

  private void collectNotifiedObjects(Set<Integer> notifiedConnectionIds, AbstractConnectionDBImpl newService){
    if(newService.isOChConnection()) {
      notifiedConnectionIds.add(newService.getId());
    }else if(newService.isSubChConnection() || newService instanceof ConnectionDBImpl){
      if (newService.isSubChConnection()){
        notifiedConnectionIds.add(newService.getId());
      }else  if (newService instanceof ConnectionDBImpl) {
        List<OChConnectionDBImpl> ocss = newService.getAllContainingOChConnectionsDBs();
        for (OChConnectionDBImpl ocs : ocss) {
          notifiedConnectionIds.add(ocs.getId());
        }
      }
    }
  }

  /**
   * Collect all the dependencies related to service id list;
   * @param serviceIds service ids
   * @param exploreStateLayerDataMap explore state layer data map
   * @param serviceTerminationPointsMap service termination points map
   * @param validateOnly true/false
   * @param inputServiceNames
   */
  @MDPersistenceContext
  private int validateReExploreServicesWithDependencies(List<Integer> serviceIds,
                                                        Map<ExploreServicesState, ParameterGroup> exploreStateLayerDataMap,
                                                        Map<ExploreServicesState, Map<ServiceLayer, List<TerminationPointsLayerData>>> serviceTerminationPointsMap,
                                                        boolean validateOnly,
                                                        boolean exploreOnlyNeeded, List<String> inputServiceNames,int sessionID) throws MDOperationFailedException{
    int progressStages;
    int progressStage=0;
    Set<OChConnectionDBImpl> ocss = new HashSet<>();
    Map<SubChConnectionDBImpl, List<OChConnectionDBImpl>> odsOCSmap = new HashMap<>();
    List<SubChConnectionDBImpl> odssRequestOn = new ArrayList<>();
    List<OChConnectionDBImpl> ocssRequestOn = new ArrayList<>();
    AbstractConnectionDBImpl first = null;
    for(Integer id:serviceIds){
      AbstractConnectionDBImpl connectionDB = connectionDAO.getConnectionById(id);
      inputServiceNames.add(connectionDB.getName());
      if(connectionDB.isProvisionMode())
        continue;
      if(exploreOnlyNeeded && connectionDB.getExplorationStatus() == Definition.ExplorationStatus.SUCCESS.getId())
        continue;
      if(connectionDB.isSubChConnection())
        odssRequestOn.add((SubChConnectionDBImpl)connectionDB);
      else if(connectionDB.isOChConnection())
        ocssRequestOn.add((OChConnectionDBImpl)connectionDB);
      else
        continue;
      if(first == null)
        first = connectionDB;
      collectDependencies(connectionDB, ocss, odsOCSmap);
    }
    //get all directly impacted OCSes from the requested ODSes only
    HashSet<OChConnectionDBImpl> directImpactedOCSs = new HashSet<>();
    for(SubChConnectionDBImpl ods:odsOCSmap.keySet())
    {
      List<OChConnectionDBImpl> ocssForODS = odsOCSmap.get(ods);
      if(odssRequestOn.contains(ods)) {
        directImpactedOCSs.addAll(ocssForODS);
      }else if (!Collections.disjoint(ocssForODS, ocssRequestOn)) {
        directImpactedOCSs.addAll(ocssForODS);
      }
    }

    // re-explore only ODSs affected by the impacted OCSs, also any orphan OCSs by the request
    List<SubChConnectionDBImpl> odssToReExplore = new ArrayList<>();
    ArrayList<OChConnectionDBImpl> ocssToReExplore = new ArrayList<>(ocssRequestOn);
    for(SubChConnectionDBImpl ods:odsOCSmap.keySet())
    {
      List<OChConnectionDBImpl> ocssForODS = odsOCSmap.get(ods);
      for(OChConnectionDBImpl ocs:ocssForODS){
        if(directImpactedOCSs.contains(ocs)){
          odssToReExplore.add(ods);
          ocssToReExplore.removeAll(ocssForODS);
          break;
        }
      }
    }

    if(odssToReExplore.isEmpty() && ocssToReExplore.isEmpty()){
      throw new MDOperationFailedException("All service(s) are in good explored state. No re-explore action needed.");
    }
    ocssToReExplore.removeIf(ocs->ocs instanceof AbstractProtOChConnectionDBImpl);
    progressStages=(ocssToReExplore.size()+odssToReExplore.size())*(validateOnly?1:2)+2;
    mdMessageSender.setProgressState(sessionID, "Collecting Service Dependencies...", progressStage++,progressStages, ProgressStatusType.REQUEST_RUNNING);
    Map<AbstractConnectionDBImpl, ParameterGroup> serviceReExploreLayerDataMap = new HashMap<>();
    for(SubChConnectionDBImpl ods:odssToReExplore){
      mdMessageSender.setProgressState(sessionID, "Validating exploration process for service "+ods.getLabel(), progressStage++,progressStages, ProgressStatusType.REQUEST_RUNNING);
      StringBuilder detailedFailureMessage = new StringBuilder();
      String failedReason = getExploreLayerData(ods, serviceReExploreLayerDataMap, new HashMap<>(), true, detailedFailureMessage);
      // collect each layer's termination points and name
      Map<ServiceLayer, List<TerminationPointsLayerData>> existingTPLayersMap = new HashMap<>();
      smTrackServiceManager.collectTerminationLayerData(ods, existingTPLayersMap);

      ExploreServicesState state = generateExploreServicesState(ods, failedReason, odsOCSmap.get(ods), detailedFailureMessage);
      exploreStateLayerDataMap.put(state, serviceReExploreLayerDataMap.get(ods));
      serviceTerminationPointsMap.put(state, existingTPLayersMap);
    }

    // re-explore these orphan OCSes
    for(OChConnectionDBImpl ocs:ocssToReExplore){
      // collect each layer's termination points and name
      mdMessageSender.setProgressState(sessionID, "Validating exploration process for service "+ocs.getLabel(), progressStage++,progressStages, ProgressStatusType.REQUEST_RUNNING);
      Map<ServiceLayer, List<TerminationPointsLayerData>> existingTPLayersMap = new HashMap<>();
      smTrackServiceManager.collectTerminationLayerData(ocs, existingTPLayersMap);
      StringBuilder detailedFailureMessage = new StringBuilder();
      String failedReason = getExploreLayerData(ocs, serviceReExploreLayerDataMap, new HashMap<>(), true, detailedFailureMessage);
      ExploreServicesState state = generateExploreServicesState(ocs, failedReason, null, detailedFailureMessage);
      exploreStateLayerDataMap.put(state, serviceReExploreLayerDataMap.get(ocs));
      serviceTerminationPointsMap.put(state, existingTPLayersMap);
    }
    return first==null?-1:first.getId();
  }

  private ExploreServicesState generateExploreServicesState(AbstractConnectionDBImpl connection, String failedReason, List<OChConnectionDBImpl> ocss, StringBuilder detailedMessage) {
    ExploreServicesState state = new ExploreServicesState();
    state.setParentId(connection.getParent().getId());

    if(connection.isSubChConnection()){
      SubChConnectionDBImpl ods = (SubChConnectionDBImpl)connection;
      state.setOdsId(ods.getId());
      state.setOdsName(ods.getName());
      MLTrailDBImpl mlTrailDB = MLTopologyElementDAO.getInstance().getMLTrail(ods.getId());
      if (mlTrailDB != null) {
        state.setCfcId(mlTrailDB.getId());
        state.setCfcName(mlTrailDB.getLabel());
      }
      Map<Integer, String> ocsIdNames = new HashMap<>();
      Map<Integer, String> nfcIdNames = new HashMap<>();
      for(OChConnectionDBImpl ocs:ocss) {
        ocsIdNames.put(ocs.getId(), ocs.getName());
        if (!ocs.isProtected()) {
          MLTopologyElementDBImpl mlTopologyElement = serviceManagerFacade.getRelatedMLTopologyElement(ocs);
          if (mlTopologyElement != null) {
            nfcIdNames.put(mlTopologyElement.getId(), mlTopologyElement.getLabel());
          }
        } else {
          List<MLConnection> networkConns = MLTopologyElementDAO.getInstance().getUnderNetworkConnections(mlTrailDB);
          if (networkConns.size() > 0) {
            List<MLConnection> mlConnections = MLTopologyElementDAO.getInstance().collectTrailsUnderNC(networkConns.get(0));
            String label = mlConnections.stream().map(conn -> conn.getLabel()).collect(Collectors.joining(", "));
            nfcIdNames.put(networkConns.get(0).getId(), label);
          }
        }
      }
      state.setOcsIdNames(ocsIdNames);
      state.setNfcIdNames(nfcIdNames);
    }
    else if(connection.isOChConnection()){
      OChConnectionDBImpl ocs = (OChConnectionDBImpl)connection;
      state.setOdsId(0);
      state.setOdsName("");
      state.setCfcId(0);
      state.setCfcName("");
      Map<Integer, String> ocsIdNames = new HashMap<>();
      Map<Integer, String> nfcIdNames = new HashMap<>();
      ocsIdNames.put(ocs.getId(), ocs.getName());
      state.setOcsIdNames(ocsIdNames);
      MLTopologyElementDBImpl mlTopologyElement = serviceManagerFacade.getRelatedMLTopologyElement(ocs);
      if (mlTopologyElement != null) {
        nfcIdNames.put(mlTopologyElement.getId(), mlTopologyElement.getLabel());
        state.setNfcIdNames(nfcIdNames);
      }
    }
    else
        return state;

    state.setFailureReason(failedReason);
    state.setErrorMessage(StringUtils.isNotEmpty(detailedMessage) ? detailedMessage.toString() : StringUtils.EMPTY);
    state.setExplorable(failedReason == null ? ExploreState.Explorable : ExploreState.Failed);
    return state;
  }

  private void collectDependencies(AbstractConnectionDBImpl connectionDB,
                                   Set<OChConnectionDBImpl> ocss,
                                   Map<SubChConnectionDBImpl, List<OChConnectionDBImpl>> odsOCSmap){
    if(connectionDB.isSubChConnection()){
      if(!odsOCSmap.containsKey(connectionDB)){
        List tempUnorderedOcss = connectionDB.getAllContainingOChConnectionsDBs();
        List<AbstractConnectionDBImpl> orderedOcss = ServiceTopologyHelper.createOrderedOcs(tempUnorderedOcss, connectionDB.getStartNEID(), new ArrayList<>(), 0);
        List<OChConnectionDBImpl> ods_ocss = new ArrayList<>();
        for(AbstractConnectionDBImpl conn:orderedOcss)
          ods_ocss.add((OChConnectionDBImpl)conn);
        odsOCSmap.put((SubChConnectionDBImpl)connectionDB, ods_ocss);

        for(OChConnectionDBImpl ocs:ods_ocss){
          collectDependencies(ocs, ocss, odsOCSmap);
        }
      }

    }
    else if(connectionDB instanceof OChConnectionDBImpl){
      if(ocss.contains(connectionDB))
        return;
      ocss.add((OChConnectionDBImpl)connectionDB);

      Collection<OpticalDataService> odssForOcs = connectionDAO.getContainedSubChConnections(connectionDB.getId());
      for(OpticalDataService ods:odssForOcs){
        collectDependencies((AbstractConnectionDBImpl)ods, ocss, odsOCSmap);
      }
    }
  }

  private String getExploreLayerData(AbstractConnectionDBImpl connection, Map<AbstractConnectionDBImpl, ParameterGroup>  serviceExploredGroupMap,
                                     Map<AbstractConnectionDBImpl, ConnectionLayerData>  serviceExploreLayerDataMap, boolean mustMatchEnds, StringBuilder detailedFailureReason) {
    // re-explore ODS
    String unsupportReason = validateReExploreSupported(connection, false);
    if(unsupportReason != null)
      return unsupportReason;

    String label = (connection.getServiceLayer() == null?"Unknown Layer" : connection.getLabel());
    StringBuilder message = new StringBuilder();

    try{
      ParameterGroup serviceGroup = null;
      if(connection.isSubChConnection())
        serviceGroup = getExplorePathServiceDefinition((SubChConnectionDBImpl)connection, true);
      else if(connection.isOChConnection())
        serviceGroup = getExplorePathServiceDefinitionForOCS((OChConnectionDBImpl) connection);

      if(serviceGroup != null){
        ConnectionLayerData connectionLayerData = buildServiceLayers(serviceGroup);
        if(!mustMatchEnds){
          if(connectionHelper.isConnectionMatched(connection, connectionLayerData, message)){
            // join action - suppose the endpoint is different
            String problem = "Same termination points detected for " + label;
            if(connection.isSubChConnection()) { // Forced - Re-explore only for ODS
              // try to explore from the end node port
              serviceGroup = getExplorePathServiceDefinition((SubChConnectionDBImpl) connection, false);
              if(serviceGroup != null) {
                connectionLayerData = buildServiceLayers(serviceGroup);
                if(!connectionHelper.isConnectionMatched(connection, connectionLayerData, message)){
                  // found the stitched service, and clear problem
                  connection = resetServiceName(connection); // endpoints have changed; allow name to be overwritten by new ML name (FNMD-55610)
                  serviceExploredGroupMap.put(connection, serviceGroup);
                  serviceExploreLayerDataMap.put(connection, connectionLayerData);
                  problem = null;
                }
              }
            }
            if(problem != null)
              return problem;
          } else {
            connection = resetServiceName(connection); // endpoints have changed; allow name to be overwritten by new ML name (FNMD-55610)
            serviceExploredGroupMap.put(connection, serviceGroup);
            serviceExploreLayerDataMap.put(connection, connectionLayerData);
          }
        } else if(mustMatchEnds && connectionHelper.isConnectionMatched(connection, connectionLayerData, message)){
          serviceExploredGroupMap.put(connection, serviceGroup);
        }
        else {
          setExplorationAndNotifyGui(connection.getId(), Definition.ExplorationStatus.FAILED);
          String errMessage = "Re-explore failed on " + label + " - Conflicting termination points " + (StringUtils.isNotEmpty(message) ? message : "") + " detected ";
          detailedFailureReason.append(errMessage);
          return errMessage;
        }
      }
      else {
        setExplorationAndNotifyGui(connection.getId(), Definition.ExplorationStatus.FAILED);
        return "Re-explore failed on " + label +
                " - failed to generate service definition ";
      }
    }catch (TrackServiceDiscoveryException explorationException){
      setExplorationAndNotifyGui(connection.getId(), Definition.ExplorationStatus.FAILED);
      detailedFailureReason.append(explorationException.getTextMessage(false));
      return "Re-explore failed on " + label +
              " with reason: " + explorationException.getTextMessage(true);
    }catch (SMException|MDOperationFailedException e){
      setExplorationAndNotifyGui(connection.getId(), Definition.ExplorationStatus.FAILED);
      detailedFailureReason.append(e.getMessage());
      return "Re-explore failed on " + label +
              " due to " + e.getMessage();
    }
    return null;
  }

  @MDTransactional
  private AbstractConnectionDBImpl resetServiceName(AbstractConnectionDBImpl connectionDB) {
    AbstractConnectionDBImpl connectionDB1 = MDPersistenceHelper.refind(connectionDB, connectionDB.getId());
    if (connectionDB1 != null) {
      connectionDB = connectionDB1;
    }
    connectionDB.setName(ServiceManagerBuilder.getName(connectionDB.getServiceLayer()));
    return connectionDB;
  }

  @MDPersistenceContext
  public void getExplorablePorts(int neId, TreeMap<EntityIndex, Integer> portsToExplore) {
    NEType productType = getNeType(neId);
    if (!(productType == NEType.FSP_3000R7 )) {
      log.info("Explore path on network element (" + neId + ") does not supported - product type: " + productType);
      return;
    }
    // collect the ports
    if (productType.getTypeId() == NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_R7)
      getAllF7PortsToExplore(neId, portsToExplore);
  }
  private void getAllF7PortsToExplore(int neId, TreeMap<EntityIndex, Integer> portsToExplore) {
    final Set<PortFSP_R7DBImpl> ports = EntityDAO.get(PortFSP_R7DBImpl.class, neId);
    for (PortDBImpl port : ports) {
    /* for explore path, need to check if client port is valid for explore
      if (isValidClientPortToExplore(port)) {
          portsToExplore.put(port, 0);
      }
      */
    // power level hack - add all client ports and network ports
     if(port instanceof PortFSP_R7DBImpl && (((PortFSP_R7DBImpl) port).isNetworkPort() ||
              ((PortFSP_R7DBImpl) port).isClientPort())){
        portsToExplore.put(port.getEntityIndex(), 0);
      }
    }
  }

  @MDPersistenceContext
  public ServiceView[] validateLineRemoval(int lineID) {
    return handleLineRemoval(lineID,false);
  }

  private ManagedObjectDBImpl getStartPort(OChConnectionDBImpl oChConnection){
    if (oChConnection.getStartPort()!=null){
      return oChConnection.getStartPort();
    }else {
      ManagedObjectDBImpl port=managedObjectDAO.get(oChConnection.getStartNEID(),oChConnection.getStartPTPAID());
      if (port instanceof UnmanagedPortDBImpl){
        return port;
      }
    }
    return null;
  }

  private ManagedObjectDBImpl getPeerPort(OChConnectionDBImpl oChConnectionDB){
    if (oChConnectionDB.getPeerPort()!=null){
      return oChConnectionDB.getPeerPort();
    }else {
      ManagedObjectDBImpl port=managedObjectDAO.get(oChConnectionDB.getPeerNEID(),oChConnectionDB.getPeerPTPAID());
      if (port instanceof UnmanagedPortDBImpl){
        return port;
      }
    }
    return null;
  }

  private boolean isPortToPortLink(LineDBImpl line, OChConnectionDBImpl oChConnection){
    if (oChConnection.getStartPort()!=null && oChConnection.getPeerPort()!=null){
      return smServiceHelper.isPortToPortLink(line, oChConnection.getStartPort(), oChConnection.getPeerPort());
    }else{
        return smServiceHelper.isPortToPortLinkSupportUnmanaged(line, getStartPort(oChConnection), getPeerPort(oChConnection));
    }
  }

  public ServiceView[] handleLineRemoval(int lineID, boolean execute) {
    List<ServiceView> serviceViews=new ArrayList<>();
    LineDBImpl line=null;
    String odsName=null;
    try {
      line = lineDAO.getLineByLineId(lineID);
      if (line != null) {
        if (!line.isProtected()) {
          if (line.hasConnections()
                  && line.getActiveConnections().size() == 1
                  && line.getActiveConnections().iterator().next() instanceof OChConnectionDBImpl) {
            OChConnectionDBImpl oChConnection = (OChConnectionDBImpl) line.getActiveConnections().iterator().next();
            if (oChConnection.isExplored()) {
              if (isPortToPortLink(line,oChConnection)) {
                Collection<OpticalDataService> opticalDataServices = new ConnectionDAO().getContainedSubChConnections(oChConnection.getId());
                if (opticalDataServices.size() == 1) {
                  SubChConnectionDBImpl ods = (SubChConnectionDBImpl) opticalDataServices.iterator().next();
                  odsName=ods.getName();
                  if (!ods.isProtected()) {
                    Set<Integer> uls=connectionDAO.getUpperLayerConnectionIDs(ods.getId(),false);
                    uls.remove(ods.getId());
                      if (CollectionUtils.isEmpty(uls)) {
                        if (ods.getWorkingConnectionsInOrder().contains(oChConnection) || isUno(line,oChConnection)) {
                          String unsupportReason = validateReExploreForLineRemovalSupported(ods);
                          if (unsupportReason == null) {
                            List<ParameterGroup> serviceGroups;
                            serviceGroups = getExplorePathSplitServiceDefinition(ods, line);
                            if (!CollectionUtils.isEmpty(serviceGroups)) {
                              ServiceView serviceView=new ServiceView();
                              serviceView.setServiceName(oChConnection.getLabel());
                              serviceViews.add(serviceView);
                              serviceView=new ServiceView();
                              serviceView.setServiceName(ods.getLabel());
                              serviceViews.add(serviceView);
                              if (execute) {
                                smTrackServiceManager.deleteService(ods.getId(), true, false, true, false);
                                smTrackServiceManager.deleteService(oChConnection.getId(), true, true);
                                String[] suffixes =new String[] { "A", "B"};
                                int index=0;
                                List<String> odsNames=new ArrayList<>();
                                for (ParameterGroup serviceGroup : serviceGroups) {
                                  index = index+1;
                                  serviceManagerCtrl.setServiceParameters(serviceGroup, true, true, false);
                                }
                                messageManager.addMessage(MessageManager.NO_LOGFILE, ServiceManagerCtrlImpl.TAB_NAME_SERVICES,
                                        line, MessageManager.Info,
                                        "Service "+odsName +" has been split into services "+ StringUtils.join(odsNames," and ")+" due to deletion of line "+line.getName()+".",
                                        odsName);

                                return serviceViews.toArray(new ServiceView[]{});
                              }else {
                                return serviceViews.toArray(new ServiceView[]{});
                              }
                            } else {
                              log.debug("Line under deletion with id {}. Re-explore failed on {} - failed to generate service definition ", lineID, ods.getLabel());
                              if(execute) {
                                messageManager.addMessage(MessageManager.NO_LOGFILE, "Delete",
                                        line, MessageManager.Warning,
                                        "An error occurred while trying to modify service " + ods.getLabel() + " related to line " + line.getName() +". Re-explore failed. " ,
                                        line.getName());
                              }
                            }
                          } else {
                            log.debug("Line under deletion with id {} " + unsupportReason, lineID);
                            if(execute){
                              messageManager.addMessage(MessageManager.NO_LOGFILE, "Delete",
                                      line, MessageManager.Warning,
                                      "Unable to  modify service " + ods.getLabel() + " related to line " + line.getName() +". " + unsupportReason ,
                                      line.getName());
                            }
                          }
                        } else {
                          log.debug("Line under deletion with id {} contains multilayer ODS with ODU. Port to port links cannot support ODU on top at this point. Services modification was skipped.", lineID);

                        }
                      }else {
                        log.debug("Line under deletion with id {} contains ODS which has parents. Services modification was skipped.", lineID);
                        if(execute){
                          messageManager.addMessage(MessageManager.NO_LOGFILE, "Delete",
                                  line, MessageManager.Warning,
                                  "Line " + line.getName() + " contains an ODS that is associated with other ODS. Services modification was skipped.",
                                  line.getName());
                        }
                      }
                  } else {
                    log.debug("Line under deletion with id {} contains protected ODS. Services modification was skipped.", lineID);
                    if(execute){
                      messageManager.addMessage(MessageManager.NO_LOGFILE, "Delete",
                              line, MessageManager.Warning,
                              "Line " + line.getName() + " contains protected ODS. Services modification was skipped.",
                              line.getName());
                    }
                  }
                } else if (opticalDataServices.size() == 0){
                  if(execute) {
                    smTrackServiceManager.deleteService(oChConnection.getId(), true, true);
                  }
                  ServiceView serviceView=new ServiceView();
                  serviceView.setServiceName(oChConnection.getLabel());
                  serviceViews.add(serviceView);
                  return serviceViews.toArray(new ServiceView[]{});
                }
                else {
                  log.debug("Line under deletion with id {} contains more than one ODS. Services modification was skipped.", lineID);
                  if(execute){
                    messageManager.addMessage(MessageManager.NO_LOGFILE, "Delete",
                            line, MessageManager.Warning,
                            "Line " + line.getName() + " contains more than one ODS. Services modification was skipped.",
                            line.getName());
                  }
                }
              } else {
                log.debug("Line under deletion with id {} is not port to port link. Services modification was skipped.", lineID);
                if(execute){
                  messageManager.addMessage(MessageManager.NO_LOGFILE, "Delete",
                          line, MessageManager.Warning,
                          "Line " + line.getName() + " is not port to port link. Services modification was skipped.",
                          line.getName());
                }
              }
            } else {
              log.debug("Line under deletion with id {} contains non explored service {}. Services modification was skipped.", lineID, oChConnection.getId());
              if(execute){
                messageManager.addMessage(MessageManager.NO_LOGFILE, "Delete",
                        line, MessageManager.Warning,
                        "Line " + line.getName() + " contains non explored service. Services modification was skipped.",
                        line.getName());
              }
            }
          } else {
            if(execute){
              if (line.hasConnections() && line.getActiveConnections().size()>1 && line.getActiveConnections().iterator().next() instanceof OChConnectionDBImpl) {
                log.debug("Line with id {} has more than 1 active och connection. Services modification was skipped.", lineID);
                messageManager.addMessage(MessageManager.NO_LOGFILE, "Delete",
                        line, MessageManager.Warning,
                        "Line " + line.getName() + " contains more than one Optical Channel Services. Services modification was skipped.",
                        line.getName());
              }
            }
          }
        } else {
          log.debug("Line with id {} is protected. Services modification was skipped.", lineID);
          if(execute){
            messageManager.addMessage(MessageManager.NO_LOGFILE, "Delete",
                    line, MessageManager.Warning,
                    "Line " + line.getName() + " is protected. Services modification was skipped.",
                    line.getName());
          }
        }
      } else {
        log.error("Line with id {} was not found. Services modification was skipped.", lineID);
      }
    }catch (TrackServiceDiscoveryException ts){

      String message = "An error occurred while trying to modify service ";
      if(odsName!=null){
        message+=odsName + " ";
      }
      if (line!=null) {
        message += "related to line " + line.getName() + ". ";
      }
      message+=ts.getTextMessage(true) +".";
      if(execute) {
        messageManager.addMessage(MessageManager.NO_LOGFILE, "Delete",
                line, MessageManager.Warning,
                message,
                line.getName());
      }
      log.error(message,ts);
    }
    catch (Exception ex) {
      log.error("Unexpected error occurred while trying to locate and modify services related to removed line with id " + lineID, ex);
      String message = "Unexpected error occurred while trying to locate and modify service ";
      if(execute) {
        if (odsName != null) {
          message += odsName + " ";
        }
        if (line != null) {
          message += "related to line " + line.getName() + ". ";
        } else {
          message += ". ";
        }
        message += ex.getMessage() + ". Please check log files for more details.";
        messageManager.addMessage(MessageManager.NO_LOGFILE, "Delete",
                line, MessageManager.Warning,
                message,
                line != null ? line.getName() : String.valueOf(lineID));
      }
    }
    return null;
  }

  private boolean isUno(LineDBImpl line, OChConnectionDBImpl connection) {
    NEType productType = getNeType(connection.getStartNEID());
    NEType productType2 = getNeType(connection.getPeerNEID());
    if (NEType.UNMANAGED.equals(productType) || NEType.UNMANAGED.equals(productType2)){
      if (line.getAEndpoint()!=null && line.getZEndpoint()!=null) {
        EntityIndex aEntityIndex = line.getAEndpoint().getEntityIndex();
        EntityIndex zEntityIndex = line.getZEndpoint().getEntityIndex();
        if (aEntityIndex != null && zEntityIndex != null) {
          ManagedObjectDBImpl aEndTp = managedObjectDAO.get(line.getAEndNEId(), aEntityIndex);
          ManagedObjectDBImpl zEndTp = managedObjectDAO.get(line.getZEndNEId(), zEntityIndex);
          if (aEndTp!=null && zEndTp!=null){
            return !(aEndTp instanceof TerminationPointFSP_R7DBImpl) || !(zEndTp instanceof TerminationPointFSP_R7DBImpl);
          }
        }
      }
    }
    return false;
  }

  @MDPersistenceContext
  private Optional<OperStateStatus> getOperStateStatusForOds(int connID) {
    AbstractConnectionDBImpl conn = MDPersistenceHelper.find(AbstractConnectionDBImpl.class, connID);
    if (conn != null && conn.getOperationalStatus() != null &&
        (conn.getOperationalStatus().getOperationalStatus() == ServiceOperationalState.DEGRADED.getId()
            || conn.getOperationalStatus().getOperationalStatus() == ServiceOperationalState.FAULTED.getId())) {
      return Optional.of(new OperStateStatus(
          conn.getOperationalStatus().getOperationalStatus(),
          conn.getOperationalStatus().getFaulted_timestamp(),
          conn.getOperationalStatus().getDegraded_timestamp(),
          conn.getOperationalStatus().isAcknowledged(),
          conn.getOperationalStatus().getAckdBy(),
          conn.getOperationalStatus().getAckdAt()));
    }
    return Optional.empty();
  }

  class OperStateStatus {
    int operationalStatus;
    long faulted_timestamp;
    long degraded_timestamp;
    boolean acknowledged;
    String ackdBy;
    long ackdAt;

    public OperStateStatus(int operationalStatus, long faulted_timestamp, long degraded_timestamp, boolean acknowledged, String ackdBy, long ackdAt) {
      this.operationalStatus = operationalStatus;
      this.faulted_timestamp = faulted_timestamp;
      this.degraded_timestamp = degraded_timestamp;
      this.acknowledged = acknowledged;
      this.ackdBy = ackdBy;
      this.ackdAt = ackdAt;
    }


  }
}
