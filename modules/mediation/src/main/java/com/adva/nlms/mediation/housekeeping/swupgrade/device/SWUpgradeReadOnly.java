/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: ask<PERSON><PERSON><PERSON>
 */
package com.adva.nlms.mediation.housekeeping.swupgrade.device;

import com.adva.nlms.mediation.housekeeping.swupgrade.SWUpgradeAbstract;
import com.adva.nlms.mediation.housekeeping.swupgrade.device.helper.ISWUpgrade150CMHelper;
import com.adva.nlms.mediation.housekeeping.swupgrade.exception.SWUpgradeException;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPCommFailure;

public class SWUpgradeReadOnly extends SWUpgradeAbstract<ISWUpgrade150CMHelper> {


  @Override
  public void doSetUp() throws SWUpgradeException, SNMPCommFailure {
    //do nothing
  }

  @Override
  public void doDownload() throws SWUpgradeException, SNMPCommFailure {
    //do nothing
  }

  @Override
  public void doInstall() throws SWUpgradeException, SNMPCommFailure {
    //do nothing
  }

  @Override
  public void doActivate() throws SWUpgradeException, SNMPCommFailure {
    //do nothing
  }

  @Override
  public void doResync() throws SWUpgradeException, SNMPCommFailure {
    //do nothing
  }

  @Override
  public void doReboot() throws SWUpgradeException, SNMPCommFailure {
    //do nothing
  }

  @Override
  public void doPostRebootResync() throws SWUpgradeException, SNMPCommFailure {
    //do nothing
  }

  @Override
  public void doFinish() throws SWUpgradeException, SNMPCommFailure {
    //do nothing
  }

  @Override
  public void doRevert() throws SWUpgradeException, SNMPCommFailure {
    //do nothing
  }

  @Override
  public void doRevertResync() throws SWUpgradeException, SNMPCommFailure {
    //do nothing
  }
}
