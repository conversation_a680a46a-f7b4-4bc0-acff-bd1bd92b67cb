/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: tomaszm
 */

package com.adva.nlms.mediation.config.fsp150cm;

import com.adva.nlms.common.snmp.MDOperationFailedException;
import com.adva.nlms.mediation.common.persistence.MDPersistenceContext;
import com.adva.nlms.mediation.common.transactions.InvalidPollingException;
import com.adva.nlms.mediation.config.f3.NetworkElementPollingWorkerF3BeanContainer;
import com.adva.nlms.mediation.config.f3.entity.flow.FlowF3SpecificPolling;
import com.adva.nlms.mediation.config.f3.entity.flow.FlowF3WithRelativesPolling;
import com.adva.nlms.mediation.config.f3.entity.module.cpmr.ModuleCPMRF3_EFMPolling;
import com.adva.nlms.mediation.config.f3.entity.pg.ProtectionGroupF3DAO;
import com.adva.nlms.mediation.config.f3.entity.pg.ProtectionGroupF3DBImpl;
import com.adva.nlms.mediation.config.f3.entity.pg.ProtectionGroupF3PollingCommandWithMtosiAdditions;
import com.adva.nlms.mediation.config.f3.entity.policer.qospolicer.QOSFlowPolicerF3SpecificPolling;
import com.adva.nlms.mediation.config.f3.entity.port.acc.PortF3AccLogicalSpecificPolling;
import com.adva.nlms.mediation.config.f3.entity.port.acc.PortF3AccSpecificPolling;
import com.adva.nlms.mediation.config.f3.entity.port.net.PortF3NetLogicalSpecificPolling;
import com.adva.nlms.mediation.config.f3.entity.port.net.PortF3NetSpecificPolling;
import com.adva.nlms.mediation.config.f3.neComm.ProtectionGroupScanAndResyncGroupDefault;
import com.adva.nlms.mediation.config.f3.polling.DiscoverEthernetPortF3PollingParameters;
import com.adva.nlms.mediation.config.f3.polling.DiscoverFlowF3PollingParameters;
import com.adva.nlms.mediation.config.f3.polling.DiscoverFlowF3WithRelativesPollingParameters;
import com.adva.nlms.mediation.config.f3_efm.entity.module.fan.FanF3_EFMPolling;
import com.adva.nlms.mediation.config.f3_efm.entity.module.psu.PowerSupplyF3_EFMPolling;
import com.adva.nlms.mediation.config.fsp150cm.polling.DiscoverProtectionGroupF3PollingParameters;
import com.adva.nlms.mediation.config.fsp150cm.polling.VolatilePollingParameters;
import com.adva.nlms.mediation.config.fsp150cp_mx.polling.UpdateUserLabelPollingParameters;
import com.adva.nlms.mediation.config.polling.PollingCommand;
import com.adva.nlms.mediation.config.polling.PollingCommandParameters;
import com.adva.nlms.mediation.config.polling.qos.QOSPollingCommandF3;
import com.adva.nlms.mediation.config.sr.service.fdfr.FDFRServiceDiscoveryPolling;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPCommFailure;
import com.adva.nlms.mediation.polling.PollingType;

import java.util.Map;

public final class NetworkElementPollingWorkerFSP150CM_CP_EFMImpl extends AbstractNetworkElementPollingWorkerFSP150CMImpl<NetworkElementFSP150CM_CP_EFM> {

  /**
   * The polling types that are configurable on the network element level.
   */
  private static final long[] CONFIGURABLE_POLLING_TYPES = new long[]{
          PollingType.CONTINUOUS_DISCOVERY.getType(),
          PollingType.INVENTORY_REPORT_GENERATION.getType(),
          PollingType.ALARM_SEVERITY_COUNTER.getType(),
          PollingType.PERFORMANCE_LONG_TERM.getType(),
          PollingType.PERFORMANCE_SHORT_TERM.getType()
  };

  /**
   * The polling types that are performed by this instance.
   */
  private static final long[] PERFORMED_POLLING_TYPES = new long[]{
          PollingType.INVENTORY.getType(),
          PollingType.CONFIGURATION.getType(),
          PollingType.SYSINFO.getType(),
          PollingType.CONTINUOUS_DISCOVERY.getType(),
          PollingType.STATUS.getType(),
          PollingType.INIT_SNMP_CTRL.getType(),
          PollingType.DISCOVER_F3_ACC_PORT.getType(),
          PollingType.DISCOVER_F3_NET_PORT.getType(),
          PollingType.DISCOVER_FLOW.getType(),
          PollingType.DISCOVER_POLICER.getType(),
          PollingType.DISCOVER_FLOW_SHAPER.getType(),
          PollingType.DISCOVER_LAG.getType(),
          PollingType.DISCOVER_CPMR_MODULE.getType(),
          PollingType.VOLATILE.getType(),
          PollingType.SYNCH_MO_STRINGS.getType(),
          PollingType.UPDATE_SYSNAME.getType(),
          PollingType.UPDATE_PG_USER_LABEL.getType(),
          PollingType.NOTIFY_INCOMPLETE_EVENTS.getType(),
          PollingType.DISCOVER_FDFR.getType(),
          PollingType.DISCOVER_F3_FAN.getType(),
          PollingType.DISCOVER_F3_PSU.getType(),
          PollingType.SYNCHRONIZE_FTP.getType(),
          PollingType.ALARM_SEVERITY_COUNTER.getType(),
          PollingType.FLOW_WITH_RELATIVES.getType(),
          PollingType.SYNCHRONOUS_EVENT_PROCESSING.getType()
  };


  public NetworkElementPollingWorkerFSP150CM_CP_EFMImpl(NetworkElementFSP150CM_CP_EFM ne,
                                                        NetworkElementPollingWorkerF3BeanContainer networkElementPollingWorkerF3BeanContainer) {
    super(ne, networkElementPollingWorkerF3BeanContainer);
  }

  @Override
  protected void initializeNEPollings(Map<Long, PollingCommand> pollingCommands) {
    pollingCommands.put(PollingType.SYNCHRONOUS_EVENT_PROCESSING.getType(), moSynchronousEventProcessor);

    pollingCommands.put(PollingType.DISCOVER_FDFR.getType(), new FDFRServiceDiscoveryPolling());

    pollingCommands.put(PollingType.DISCOVER_CPMR_MODULE.getType(), new ModuleCPMRF3_EFMPolling(getNe()));

    pollingCommands.put(PollingType.VOLATILE.getType(), new PollingCommand() {
      @Override
      @MDPersistenceContext
      public void runPolling(PollingCommandParameters params) throws MDOperationFailedException, InvalidPollingException, SNMPCommFailure {
        getNe().doPollingVolatile((VolatilePollingParameters) params.getPollingParameters());
      }
    });

    pollingCommands.put(PollingType.DISCOVER_FLOW.getType(), new PollingCommand() {
      @Override
      @MDPersistenceContext
      public void runPolling(PollingCommandParameters params) throws MDOperationFailedException, InvalidPollingException, SNMPCommFailure {
        new FlowF3SpecificPolling(getNe().getDbObjectFactory(), params.getNe()).pollingAction((DiscoverFlowF3PollingParameters) params.getPollingParameters());
      }
    });

    pollingCommands.put(PollingType.DISCOVER_F3_ACC_PORT.getType(), new PollingCommand() {
      @Override
      @MDPersistenceContext
      public void runPolling(PollingCommandParameters params)
              throws MDOperationFailedException, InvalidPollingException, SNMPCommFailure {
        if(((DiscoverEthernetPortF3PollingParameters)params.getPollingParameters()).isLogicalPortsPolling()) {
          new PortF3AccLogicalSpecificPolling(getNe().getDbObjectFactory(), getNe()).runPolling(params);
        } else {
          new PortF3AccSpecificPolling(getNe().getDbObjectFactory(), getNe()).runPolling(params);
        }
      }
    });

    pollingCommands.put(PollingType.DISCOVER_POLICER.getType(), new PollingCommand() {
      @Override
      @MDPersistenceContext
      public void runPolling(PollingCommandParameters params) throws MDOperationFailedException, InvalidPollingException, SNMPCommFailure {
        new QOSFlowPolicerF3SpecificPolling(getNe().getDbObjectFactory(), getNe());
      }
    });

    pollingCommands.put(PollingType.DISCOVER_F3_NET_PORT.getType(), new PollingCommand() {
      @Override
      @MDPersistenceContext
      public void runPolling(PollingCommandParameters params)
              throws MDOperationFailedException, InvalidPollingException, SNMPCommFailure {

        if(((DiscoverEthernetPortF3PollingParameters)params.getPollingParameters()).isLogicalPortsPolling()) {
          new PortF3NetLogicalSpecificPolling(getNe().getDbObjectFactory(), getNe()).runPolling(params);
        } else {
          new PortF3NetSpecificPolling(getNe().getDbObjectFactory(), getNe()).runPolling(params);
        }
      }
    });


    pollingCommands.put(PollingType.DISCOVER_LAG.getType(), new PollingCommand() {
      @Override
      public void runPolling(PollingCommandParameters params) throws MDOperationFailedException, InvalidPollingException, SNMPCommFailure {
        new ProtectionGroupF3PollingCommandWithMtosiAdditions(getNe(), ProtectionGroupScanAndResyncGroupDefault.class).runPolling(params);
        createFTP(params);
      }

      @MDPersistenceContext
      private void createFTP(PollingCommandParameters params) {
        if(getNe().getNeConfigHdl().isMtosiNamingSupported()){
          DiscoverProtectionGroupF3PollingParameters polParams = (DiscoverProtectionGroupF3PollingParameters) params.getPollingParameters();
          ProtectionGroupF3DBImpl pgDBImpl = new ProtectionGroupF3DAO().get(params.getNe().getID(), polParams.getObjectIndexes());
          if(pgDBImpl != null){
            getNe().getPollingManager().preparePolling().forType(PollingType.UPDATE_PG_USER_LABEL.getType())
                    .withParameters(
                            new UpdateUserLabelPollingParameters(getNe(), pgDBImpl.getUserLabel(), null, pgDBImpl.getId())
                    ).execute();
          }
        }
      }
    });

    pollingCommands.put(PollingType.DISCOVER_F3_FAN.getType(), new FanF3_EFMPolling(getNe()));

    pollingCommands.put(PollingType.DISCOVER_F3_PSU.getType(), new PowerSupplyF3_EFMPolling(getNe()));

    pollingCommands.put(PollingType.UPDATE_PG_USER_LABEL.getType(), new PollingCommand() {
      @Override
      @MDPersistenceContext
      public void runPolling(PollingCommandParameters params) throws MDOperationFailedException, InvalidPollingException, SNMPCommFailure {
        getNe().discoverFTPService((UpdateUserLabelPollingParameters) params.getPollingParameters());
      }
    });


    pollingCommands.put(PollingType.DISCOVER_FLOW_SHAPER.getType(), new QOSPollingCommandF3((NetworkElementFSP150CMImpl) getNe()));

    //remove unneeded pollings to satisfy NetworkElementPollingValidator.validatePollingsImplementationFSP150CPMR test.
    pollingCommands.remove(PollingType.KEEP_ALIVE.getType());
    pollingCommands.remove(PollingType.NE_COLD_START_TIME_CHECK.getType());

    pollingCommands.put(PollingType.SYNCHRONIZE_FTP.getType(), new FTPPollingCommand());
    pollingCommands.put(PollingType.FLOW_WITH_RELATIVES.getType(), new PollingCommand() {
      @Override
      @MDPersistenceContext
      public void runPolling(PollingCommandParameters params) throws SNMPCommFailure, MDOperationFailedException {
        DiscoverFlowF3WithRelativesPollingParameters pollParams = (DiscoverFlowF3WithRelativesPollingParameters) params.getPollingParameters();
        new FlowF3WithRelativesPolling(getNe().getDbObjectFactory(), params.getNe()).pollingAction(pollParams);
      }
    });
  }

  @Override
  public long[] getConfigurablePollingTypes() {
    return CONFIGURABLE_POLLING_TYPES;
  }

  @Override
  protected long[] getDeclaredPerformedPollingTypes() {
    return PERFORMED_POLLING_TYPES;
  }
}
