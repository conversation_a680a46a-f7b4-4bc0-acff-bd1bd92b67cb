/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: arongas
 */

package com.adva.nlms.mediation.config.fsp_r7.polling.configuration;

import com.adva.nlms.mediation.config.fsp_r7.NetworkElementFSP_R7;
import com.adva.nlms.mediation.ne_comm.f7.necomm.SNMPCtrlImplFSP_R7CNX;
import com.adva.nlms.mediation.polling.api.PollingParameters;

/**
 * Created by IntelliJ IDEA.
 * User: PawelP
 * Date: 12.05.11
 * Time: 13:40
 * To change this template use File | Settings | File Templates.
 */
public class ServiceSynchronizationParameters extends PollingParameters {

  private NetworkElementFSP_R7 networkElement;
  private SNMPCtrlImplFSP_R7CNX snmpCtrlImplFSP_r7CNX;

  public SNMPCtrlImplFSP_R7CNX getSnmpCtrlImplFSP_r7CNX() {
    return snmpCtrlImplFSP_r7CNX;
  }

  public void setSnmpCtrlImplFSP_r7CNX(SNMPCtrlImplFSP_R7CNX snmpCtrlImplFSP_r7CNX) {
    this.snmpCtrlImplFSP_r7CNX = snmpCtrlImplFSP_r7CNX;
  }

  public NetworkElementFSP_R7 getNetworkElement() {
    return networkElement;
  }

  public void setNetworkElement(NetworkElementFSP_R7 networkElement) {
    this.networkElement = networkElement;
  }

  @Override
  public boolean isApplicableForType(long type) {
    return true;
  }
}
