/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: tomaszw
 */

package com.adva.nlms.mediation.config.osa5331.neComm;

import com.adva.common.util.net.InvalidIPAddressException;
import com.adva.nlms.common.annotation.UsedByReflection;
import com.adva.nlms.common.config.EntityIndex;
import com.adva.nlms.common.config.netypes.NeTypeIds;
import com.adva.nlms.common.event.EventDetectionType;
import com.adva.nlms.common.event.EventSeverity;
import com.adva.nlms.common.event.TrapParameterID;
import com.adva.nlms.common.event.types.CategoryType;
import com.adva.nlms.common.snmp.MIBOSA5331;
import com.adva.nlms.common.snmp.SNMPWriteAccessStatus;
import com.adva.nlms.common.util.IP;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.util.VarbindUtils;
import com.adva.nlms.mediation.event.EventDTO;
import com.adva.nlms.mediation.evtProc.api.TrapParamConverter;
import com.adva.nlms.mediation.housekeeping.nebackup.core.RestoreWorker;
import com.adva.nlms.mediation.ne_comm.AbstractSNMPCtrlImpl;
import com.adva.nlms.mediation.config.f3.neComm.SNMPCtrlImplF3Trapsink;
import com.adva.nlms.mediation.ne_comm.SNMPCtrlTrapsink;
import com.adva.nlms.mediation.ne_comm.StatusPollingSNMPCtrl;
import com.adva.nlms.mediation.ne_comm.TrapsinkStatus;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPAdapter;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPCommFailure;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.snmp4j.smi.VariableBinding;
import org.snmp4j.util.TableEvent;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * NMS container initialize this class by com.adva.nlms.mediation.ne_comm.SnmpCtrlImplFactory
 */
@UsedByReflection
public class SNMPCtrlImplOSA5331 extends AbstractSNMPCtrlImpl implements StatusPollingSNMPCtrl, SNMPCtrlTrapsink {

  private static final Logger LOG = LoggerFactory.getLogger(SNMPCtrlImplOSA5331.class);

  private SNMPCtrlTrapsink trapsinkSNMPCtrl;
  private final TrapParamConverter trapParamConverter;

  protected SNMPCtrlImplOSA5331 (NetworkElement networkElement, SNMPAdapter snmpAdapter) throws SNMPCommFailure {
    super(networkElement, snmpAdapter);
    try {
      this.trapsinkSNMPCtrl = new SNMPCtrlImplF3Trapsink(this, new IP(networkElement.getIPAddress()).isIPv6Addr());
    } catch (InvalidIPAddressException e) {
      LOG.error(String.valueOf(e));
    }
    this.trapParamConverter = getNetworkElement().getConfigCtrl().getEvtProcCtrl().getTrapParamConverter(
        NeTypeIds.NETWORK_ELEMENT_TYPE_OSA5331);
  }

  protected SNMPCtrlImplOSA5331 (String ipAddr, SNMPAdapter snmpAdapter) throws SNMPCommFailure {
    super(ipAddr, snmpAdapter);
    try {
      this.trapsinkSNMPCtrl = new SNMPCtrlImplF3Trapsink(this, new IP(ipAddr).isIPv6Addr());
    } catch (InvalidIPAddressException e) {
      LOG.error(String.valueOf(e));
    }
    this.trapParamConverter = getNetworkElement().getConfigCtrl().getEvtProcCtrl().getTrapParamConverter(
        NeTypeIds.NETWORK_ELEMENT_TYPE_OSA5331);
  }

  @Override
  public String getActiveFSP_SWVersion() throws SNMPCommFailure {
    return getStringValue(MIBOSA5331.Shelf.Mgmt.InventoryScalarTable.OID_SOFTWARE_VERSION);
  }

  @Override
  public String getInactiveFSP_SWVersion() throws SNMPCommFailure {
    //OSA5331 doesn't provide info about inactive software version
    return "";
  }

  @Override
  public RestoreWorker<?> getRestoreWorker () {
    return null;  //To change body of implemented methods use File | Settings | File Templates.
  }

  @Override
  public List<EventDTO> getCurrentAlarms (int neIndex) throws SNMPCommFailure {
    EventDTO alarm;
    final List<EventDTO> currentAlarmsMap = new ArrayList<EventDTO>();
    List<TableEvent> table  = new ArrayList<TableEvent>();

    table.addAll(getSnmpAdapter().getTable(MIBOSA5331.Shelf.Alarms.AlarmTable.NOTIFICATION_ID,
                          MIBOSA5331.Shelf.Alarms.AlarmTable.EVENT_TYPE,
                          MIBOSA5331.Shelf.Alarms.AlarmTable.SEVERITY,
                          MIBOSA5331.Shelf.Alarms.AlarmTable.STATE,
                          MIBOSA5331.Shelf.Alarms.AlarmTable.STATUS));

    for (final TableEvent tableEvent : table) {
      alarm = getServerTrap(tableEvent);

      if (alarm != null)
        currentAlarmsMap.add(alarm);
    }
    return currentAlarmsMap;
  }

  private EventDTO getServerTrap(final TableEvent tableEvent) {
    EventDTO serverTrap = new EventDTO(0 /* trapID */,
                                                               0 /* alarmType */,
                                                               EventSeverity.UNKNOWN,
                                                               System.currentTimeMillis(),
                                                               getIPAddress(),
                                                               getNetworkElement().getID(),
                                                               EntityIndex.ZERO,
                                                               TrapParameterID.UNKNOWN,
                                                               -1 /* value */,
                                                               "" /* string value */,
                                                               EventDetectionType.CONF_STATUS_UPDATE, null);
    serverTrap.category = CategoryType.OSA5331_EVENT;
    serverTrap.enterprise = MIBOSA5331.Trap.OID_ENTERPRISE;

    VariableBinding[] columns = tableEvent.getColumns();

    int i = 0;
    int alarmType = VarbindUtils.getIntValueFromVarbind(columns[i++]);
    int isAlarm = VarbindUtils.getIntValueFromVarbind(columns[i++], MIBOSA5331.AlarmEventType.EVENT);
    int severity = VarbindUtils.getIntValueFromVarbind(columns[i++], MIBOSA5331.AlarmSeverity.SEVERITY_CLEAR);
    int state = VarbindUtils.getIntValueFromVarbind(columns[i++], MIBOSA5331.AlarmState.DISABLED);
    int status = VarbindUtils.getIntValueFromVarbind(columns[i++], MIBOSA5331.AlarmStatus.OFF);

    if (isAlarm == MIBOSA5331.AlarmEventType.ALARM &&
        severity != MIBOSA5331.AlarmSeverity.SEVERITY_CLEAR &&
        state == MIBOSA5331.AlarmState.ENABLED &&
        status == MIBOSA5331.AlarmStatus.ON) {

      serverTrap.setTrapID(alarmType);
      serverTrap.alarmType = alarmType;
      serverTrap.severity = trapParamConverter.getEventSeverity(severity);
      serverTrap.setAidString(trapParamConverter.getAidFromNotificationId(alarmType));
      return serverTrap;
    }

    return null;
  }



  @Override
  public TrapsinkStatus getTrapsinkStatus() throws SNMPCommFailure {
    return trapsinkSNMPCtrl.getTrapsinkStatus();
  }

  @Override
  public void unregisterSNMPTrapsink() throws SNMPCommFailure {
    trapsinkSNMPCtrl.unregisterSNMPTrapsink();
  }

  @Override
  public void unregisterIpFromTrapsink(String ipAddress) throws SNMPCommFailure {
    ((SNMPCtrlImplF3Trapsink) trapsinkSNMPCtrl).unregisterIpFromTrapsink(ipAddress);
  }

  @Override
  public boolean isReregistrationSupported() {
    return trapsinkSNMPCtrl.isReregistrationSupported();
  }

  @Override
  public boolean isValidationUponReregistrationSupported() {
    return trapsinkSNMPCtrl.isValidationUponReregistrationSupported();
  }

  @Override
  public SNMPWriteAccessStatus getSNMPWriteAccessStatus() throws SNMPCommFailure {
    return trapsinkSNMPCtrl.getSNMPWriteAccessStatus();
  }

  @Override
  public boolean checkHostInTrapsink (IP ipAddress, List<TableEvent> eventsList) {
    return trapsinkSNMPCtrl.checkHostInTrapsink(ipAddress, eventsList);
  }

  @Override
  public String getTrapsinkTableOID () {
    return trapsinkSNMPCtrl.getTrapsinkTableOID();
  }

  @Override
  public boolean isTrapsink (IP ipAddress) throws SNMPCommFailure {
    return trapsinkSNMPCtrl.isTrapsink(ipAddress);
  }

  @Override
  public Set<VariableBinding> prepareSNMPTargetAddrVB (String receiverKey, String serverSNMPAddress, String trapsinkName) {
    //TODO clean up, this method is not used by SNMPCtrlImpl(Box type) due to introducation on IP class, the only class it is used in is SnmpCtrlImplF3Trapsink
    return null;
  }

  @Override
  public Set<VariableBinding> prepareSNMPTargetAddrVB(String receiverKey, IP serverSNMPAddress, String trapsinkName) {
    return trapsinkSNMPCtrl.prepareSNMPTargetAddrVB(receiverKey, serverSNMPAddress.toString(), trapsinkName);
  }

  @Override
  public Collection<VariableBinding> prepareSnmpTargetParamsVB () {
    return trapsinkSNMPCtrl.prepareSnmpTargetParamsVB();
  }

  @Override
  public void registerInTrapsink(IP ipAddress, int port) throws SNMPCommFailure {
    trapsinkSNMPCtrl.registerSNMPTrapsink(ipAddress.toString());
  }
}
