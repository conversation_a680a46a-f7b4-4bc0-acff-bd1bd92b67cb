/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: tomaszm
 */

package com.adva.nlms.mediation.config.fsp150cp_mx.fsp150cp;


import com.adva.common.util.IntHashMap;
import com.adva.common.util.lang.ValidationException;
import com.adva.nlms.common.event.TrapParameterID;
import com.adva.nlms.common.snmp.MIBFSP150CP;
import com.adva.nlms.common.util.BitFields;
import com.adva.nlms.mediation.common.transactions.NetTransactionManager;
import com.adva.nlms.mediation.common.transactions.ObjectInUseException;
import com.adva.nlms.mediation.config.ByteArrayDataCTRLImpl;
import com.adva.nlms.mediation.config.ConfigDataCTRL;
import com.adva.nlms.mediation.config.DataCTRL;
import com.adva.nlms.mediation.config.FTPDAO;
import com.adva.nlms.mediation.config.FakeEntityFactory;
import com.adva.nlms.mediation.config.IntDataCTRLImpl;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.NetworkElementDBImpl;
import com.adva.nlms.mediation.config.NetworkElementPersistenceHelper;
import com.adva.nlms.mediation.config.StringDataCTRLImpl;
import com.adva.nlms.mediation.config.fsp150cp_mx.ShelfFSP150CP_MXDBImpl;
import com.adva.nlms.mediation.config.fsp150cp_mx.ShelfFSP150CP_MXImpl;
import com.adva.nlms.mediation.config.fsp150cp_mx.fsp150cp.necomm.SNMPCtrlFSP150CP;
import com.adva.nlms.mediation.event.EventDTO;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPCommFailure;

import java.util.Iterator;
import java.util.List;

/**
 * Implementation class of a FSP 150MX shelf.
 */
class ShelfFSP150CPImpl extends ShelfFSP150CP_MXImpl implements ShelfFSP150CP
{
  /**
   * Constructor
   * @param neFSP150CP           The network element.
   * @param shelfFSP150CPDBImpl  The persistent shelf.
   */
  protected ShelfFSP150CPImpl(final NetworkElement neFSP150CP, final ShelfFSP150CPDBImpl shelfFSP150CPDBImpl)
  {
    super(neFSP150CP, shelfFSP150CPDBImpl);
  }


  /**
   * Recreates the transient FSP150MX shelf object.
   * @param networkElement       The network element.
   * @param shelfFSP150CPDBImpl  The persistent shelf.
   * @return the transient FSP150MX shelf object.
   */
  static ShelfFSP150CPImpl recreate(final NetworkElement networkElement, final ShelfFSP150CPDBImpl shelfFSP150CPDBImpl)
  {
    return new ShelfFSP150CPImpl(networkElement, shelfFSP150CPDBImpl);
  }


  /**
   * Initializes the configuration data CTRLs.
   * @param configDataCTRLList List to add the CTRLs.
   */
  @Override
  protected void initConfigDataCTRLs(final List<ConfigDataCTRL> configDataCTRLList)
  {
    // 1.) init for base class(es)!
    super.initConfigDataCTRLs(configDataCTRLList);

    // 2.) init for this class!
    configDataCTRLList.add(new BpduFilterCTRL());
    configDataCTRLList.add(new ProtTypeCTRL());
    configDataCTRLList.add(new UserString2CTRL());
    configDataCTRLList.add(new UserString3CTRL());
    configDataCTRLList.add(new PauseEnableCTRL());
  }


  /**
   * Returns a reference to the persistent data.
   * @return a reference to the persistent data.
   */
  public ShelfFSP150CPDBImpl getShelfFSP150CPDBImpl()
  {
    return (ShelfFSP150CPDBImpl) getEntityDBImpl();
  }

  //----------------------------------------------------------------------------
  //----------------------------------------------------------------------------
  protected class ProtTypeCTRL extends IntDataCTRLImpl implements ConfigDataCTRL
  {
    ProtTypeCTRL()
    {
      super(networkElement,
            getIndex().flip(),
            MIBFSP150CP.ChassisConfig.OID_PROT_TYPE,
            "fsp150EquipmentAttributeValueChange",
            TrapParameterID.fsp150ChassisConfigProtType);
    }

    @Override
    public int getIntValueFromDB()
    {
    	return ((ShelfFSP150CP_MXDBImpl) getEntityDBImpl()).getProtType();
    }

    @Override
    public void setValueInDB()
    {
      ShelfFSP150CP_MXDBImpl shelfDBImpl = (ShelfFSP150CP_MXDBImpl) getEntityDBImpl();
      //also update statusProt
      int configProt = value >> 4;
      int statusProt = value & 15;
      if (statusProt == 0)
        statusProt = shelfDBImpl.getProtState();
      shelfDBImpl.setProtState(statusProt);
      shelfDBImpl.setProtType(configProt);
      FTPFSP150CPDBImpl ftp = FTPDAO.getByNe(FTPFSP150CPDBImpl.class, getNeId());
      if (ftp != null) {
        ftp.setProtectionType(shelfDBImpl.getProtType());
        ftp.setProtectionStatus(shelfDBImpl.getProtState());
        ftp.setActiveNetwork(shelfDBImpl.getActiveNetwork());
      }
    }

    @Override
    public void notifyDataChange()
    {
      // 1.) notify ConfigData change!
      FakeEntityFactory.convertServerTrap(serverTrap);
      value >>=4;
      super.notifyDataChange();
    }

    @Override
    public boolean equals(Object obj)
    {
      if (obj == null) {
        return false;
      }

      boolean equalsResult = super.equals(obj);
      //TODO equals should not modify argument and send snmp request
      if (!equalsResult)
      {
        int statusProt = 0;
        try
        {
          IntHashMap hashMap = (((NetworkElement)neImpl).getSNMPCtrl().getObjectMap(MIBFSP150CP.ChassisStatus.OID_PROT_STATE, getSNMPIndexArray()));
          // hasMap: (key = int objectIndex, value = int ProtState) 
          Iterator iter = hashMap.values().iterator();
          if (iter.hasNext())
          {
            statusProt = (Integer)iter.next();
          }
        } catch (SNMPCommFailure ex)
        {
          //do nothing
        }
     
//      if (statusProt != 0)
//      {
        ((ProtTypeCTRL)obj).value <<= 4;
        ((ProtTypeCTRL)obj).value += statusProt;
//      }
      }
      return equalsResult;
    }
  }
  class UserString2CTRL extends StringDataCTRLImpl implements ConfigDataCTRL
  {
    UserString2CTRL()
    {
      super(networkElement,
            getIndex().flip(),
            MIBFSP150CP.ChassisConfig.OID_USER_STRING2,
            "fsp150EquipmentAttributeValueChange",
            TrapParameterID.fsp150ChassisConfigUserString2);
    }

    @Override
    public String getStringValueFromDB()
    {
      return ((ShelfFSP150CPDBImpl) getEntityDBImpl()).getUserString2();
    }

    @Override
    public void setValueInDB()
    {
      ((ShelfFSP150CPDBImpl) getEntityDBImpl()).setUserString2(value);
      // set system name (for EFM managed network element)!
      if (neImpl.isPeer())
      {
        ((NetworkElement)neImpl).getPersistenceHelper().updateNetworkElementDBImplEntity(new NetworkElementPersistenceHelper.NeEntityUpdate() {
          @Override
          public void update (NetworkElementDBImpl neDBImpl) {
            neDBImpl.setSysLocation(value);
          }
        });
      }
    }

    @Override
    public void notifyDataChange()
    {
      // 1.) notify ConfigData change!
      FakeEntityFactory.convertServerTrap(serverTrap);
      super.notifyDataChange();
    }
  }

  class UserString3CTRL extends StringDataCTRLImpl implements ConfigDataCTRL
  {
    UserString3CTRL()
    {
      super(networkElement,
            getIndex().flip(),
            MIBFSP150CP.ChassisConfig.OID_USER_STRING3,
            "fsp150EquipmentAttributeValueChange",
            TrapParameterID.fsp150ChassisConfigUserString3);
    }

    @Override
    public String getStringValueFromDB()
    {
      return ((ShelfFSP150CPDBImpl) getEntityDBImpl()).getUserString3();
    }

    @Override
    public void setValueInDB()
    {
      ((ShelfFSP150CPDBImpl) getEntityDBImpl()).setUserString3(value);
      // set system name (for EFM managed network element)!
      if (neImpl.isPeer()) {
        ((NetworkElement)neImpl).getPersistenceHelper().updateNetworkElementDBImplEntity(new NetworkElementPersistenceHelper.NeEntityUpdate() {
          @Override
          public void update (NetworkElementDBImpl neDBImpl) {
            neDBImpl.setSysContact(value);
          }
        });
      }
    }

    @Override
    public void notifyDataChange()
    {
      // 1.) notify ConfigData change!
      FakeEntityFactory.convertServerTrap(serverTrap);
      super.notifyDataChange();
    }
  }

  class PauseEnableCTRL extends IntDataCTRLImpl implements ConfigDataCTRL
  {
    PauseEnableCTRL()
    {
      super(networkElement,
            getIndex().flip(),
            MIBFSP150CP.ChassisConfig.OID_PAUSE_ENABLE,
            "fsp150EquipmentAttributeValueChange",
            TrapParameterID.fsp150ChassisConfigPauseEnable);
    }

    @Override
    public int getIntValueFromDB()
    {
      return ((ShelfFSP150CPDBImpl) getEntityDBImpl()).getPauseEnable();
    }

    @Override
    public void setValueInDB()
    {
      ((ShelfFSP150CPDBImpl) getEntityDBImpl()).setPauseEnable(value);
    }

    @Override
    public void notifyDataChange()
    {
      // 1.) notify ConfigData change!
      FakeEntityFactory.convertServerTrap(serverTrap);
      super.notifyDataChange();
    }
  }

  class BpduFilterCTRL extends ByteArrayDataCTRLImpl implements ConfigDataCTRL
  {
    BpduFilterCTRL()
    {
      super(networkElement,
            getIndex().flip(),
            MIBFSP150CP.ChassisConfig.OID_BPDU_FILTER,
            "fsp150EquipmentAttributeValueChange",
            TrapParameterID.fsp150ChassisConfigBpduFilter);
    }

    @Override
    public byte[] getByteArrayValueFromDB()
    {
      byte[] array = BitFields.intToBytes(((ShelfFSP150CPDBImpl) getEntityDBImpl()).getBpduFilter(), 2);
      if (array[0] == 0 && array[1] == 0)
        array = new byte[0];
      return array;
    }

    @Override
    public void setValueInDB()
    {
      final int bpduFilter = BitFields.bytesToInt(value);
      this.indexArray = new int[]{getIndex().flip().toInt()};
      ShelfFSP150CPDBImpl entityDBImpl = (ShelfFSP150CPDBImpl)getEntityDBImpl();
      entityDBImpl.setBpduFilter(bpduFilter);
    }

    /**
    * Sets the value of the event in database.
    */
    @Override
    public void setValueInDB(final EventDTO event)
    {
      value = BitFields.intToBytes((int) event.newValue,2);

      setValueInDB();
    }

    @Override
    public DataCTRL
    getValueViaSNMP()
    throws SNMPCommFailure
    {
      // 1.) get value!
      value = (byte[])getObjectViaSNMP();

      // 2.) return new data CTRL!
      return cloneDataCTRL();
    }

    @Override
    public void notifyDataChange()
    {
      // 1.) notify ConfigData change!
      FakeEntityFactory.convertServerTrap(serverTrap);
      super.notifyDataChange();
    }
  }

  @Override
  public void setBpduFilfterViaSNMP(int ...bpduFilter)
    throws SNMPCommFailure, ValidationException,ObjectInUseException
  {
    NetTransactionManager.checkReservedNe(networkElement);
    SNMPCtrlFSP150CP snmpCtrl = (SNMPCtrlFSP150CP) networkElement.getSNMPCtrl();
    ShelfFSP150CPDBImpl shelfDBImpl = this.getShelfFSP150CPDBImpl();
    byte[] oldBpduFilter = BitFields.intToBytes(shelfDBImpl.getBpduFilter(),2);
    int ifIndex = shelfDBImpl.getIfIndex();
    if (ifIndex == -1)//local Chassis
      snmpCtrl.setBpduFilter(index.toInt(), oldBpduFilter, bpduFilter);
    else
      if (checkRemState(ifIndex,snmpCtrl))
        snmpCtrl.setBpduFilter(index.toInt(), oldBpduFilter, bpduFilter);

  }

  @Override
  public String[] getBpduFilfter()
  {
    ShelfFSP150CPDBImpl shelfDBImpl = this.getShelfFSP150CPDBImpl();
    return shelfDBImpl.getBpduFilterArray();

  }

  /**
   * Sets the User String2.
   * @param newUserString: new value of userString1
   * @exception SNMPCommFailure : SNMP communication to NE failed.
   */
  @Override
  public void setUserString2ViaSNMP(String newUserString)
    throws SNMPCommFailure , ObjectInUseException
  {
    NetTransactionManager.checkReservedNe(networkElement);
    ShelfFSP150CPDBImpl shelfFSP150MXDB = (ShelfFSP150CPDBImpl) getEntityDBImpl();
    String oldUserString = shelfFSP150MXDB.getUserString2();
    ((SNMPCtrlFSP150CP)networkElement.getSNMPCtrl()).setChassisUserString(index.toInt(),newUserString,oldUserString,2);
  }

  /**
   * Return User String 2.
   * @return userString2
   */
  @Override
  public String getUserString2()
  {
    ShelfFSP150CPDBImpl shelfDBImpl = this.getShelfFSP150CPDBImpl();
    return shelfDBImpl.getUserString2();
  }

  /**
   * Sets the User String3.
   * @param newUserString: new value of userString1
   * @exception SNMPCommFailure : SNMP communication to NE failed.
   */
  @Override
  public void setUserString3ViaSNMP(String newUserString)
    throws SNMPCommFailure , ObjectInUseException
  {
    NetTransactionManager.checkReservedNe(networkElement);
    ShelfFSP150CPDBImpl shelfFSP150MXDB = (ShelfFSP150CPDBImpl) getEntityDBImpl();
    String oldUserString = shelfFSP150MXDB.getUserString3();
    ((SNMPCtrlFSP150CP)networkElement.getSNMPCtrl()).setChassisUserString(index.toInt(),newUserString,oldUserString,3);
  }

  /**
   * Return User String 3.
   * @return userString3
   */
  @Override
  public String getUserString3()
  {
    ShelfFSP150CPDBImpl shelfDBImpl = this.getShelfFSP150CPDBImpl();
    return shelfDBImpl.getUserString3();
  }

  /**
   * Sets Pause Enable on the current Chassis.
   * @param newPauseEnable: new PauseEnable
   * @exception SNMPCommFailure : SNMP communication to NE failed.
   */
  @Override
  public void setPauseEnable(int newPauseEnable)
    throws SNMPCommFailure , ObjectInUseException
  {
    NetTransactionManager.checkReservedNe(networkElement);
    ShelfFSP150CPDBImpl shelfFSP150MXDB = (ShelfFSP150CPDBImpl) getEntityDBImpl();
    int oldPauseEnable = shelfFSP150MXDB.getPauseEnable();
    ((SNMPCtrlFSP150CP)networkElement.getSNMPCtrl()).setPauseEnable(index.toInt(),newPauseEnable,oldPauseEnable);
  }

  /**
   *  Return Pause Enable.
   */
  @Override
  public int getPauseEnable()
  {
    ShelfFSP150CPDBImpl shelfDBImpl = this.getShelfFSP150CPDBImpl();
    return shelfDBImpl.getPauseEnable();
  }

  /**
   *  Return traffic management supports.
   */
  @Override
  public int getTmSupp()
  {
    ShelfFSP150CPDBImpl shelfDBImpl = this.getShelfFSP150CPDBImpl();
    return shelfDBImpl.getTmSupp();
  }

  /**
   * Return last port on which continuityTest was started.
   * @return lastTestingPort
   */
  public int getLastTestingPort()
  {
    return this.getShelfFSP150CPDBImpl().getLastTestingPort();
  }

  /**
   * Set last port on which continuityTest was started.
   * @param port  port index
   */
  public void setLastTestingPort(int port)
  {
    this.getShelfFSP150CPDBImpl().setLastTestingPort(port);
  }

  //----------------------------------------------------------------------------
}
