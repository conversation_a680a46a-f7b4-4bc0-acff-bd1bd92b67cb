/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: twitting
 */

package com.adva.nlms.mediation.config.polling;

import com.adva.nlms.driver_common.manager.DriverManager;
import com.adva.nlms.common.snmp.MDOperationFailedException;
import com.adva.nlms.mediation.common.transactions.InvalidPollingException;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.ne_comm.SNMPCtrl;
import com.adva.nlms.mediation.ne_comm.cmd.SNMPCommandException;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPCommFailure;
import com.adva.nlms.mediation.ne_comm.tasks.CheckNETypeCommand;
import com.adva.nlms.mediation.polling.api.PF;
import com.adva.nlms.mediation.polling.api.PollingParameters;
import com.adva.nlms.mediation.polling.PollingType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class InitSnmpCtrl implements PollingCommand {

  private static final Logger log = LoggerFactory.getLogger(InitSnmpCtrl.class);
  private DriverManager driverManager;

  public InitSnmpCtrl(DriverManager driverManager) {
    this.driverManager = driverManager;
  }

  @Override
  public void runPolling(PollingCommandParameters params) throws MDOperationFailedException, InvalidPollingException, SNMPCommFailure {
    NetworkElement ne = params.getNe();
    PollingParameters pp = params.getPollingParameters();
    if (!ne.hasIpAddress() || ne.isPeer()) {
      PF.getPollingService().unregister(params.getPollingUniqueId(), null);
      return;
    }

    if ( ! ne.hasSNMPCtrl()) {
      final SNMPCtrl snmpCTRL = ne.getConfigCtrl().getNECommCtrl().createSNMPCtrl(ne);
      checkNEType(snmpCTRL, pp);
    } else {
      log.info("NetworkElementBasis.initSNMPCtrl({}): Network element type check already done.", ne);
      if(pp instanceof InitSnmpCtrlParameters){
        ((InitSnmpCtrlParameters) pp).getPolling().run();
      }
      PF.getPollingService().unregister(params.getPollingUniqueId(), null);
    }
  }

  /**
   * Checks the type of a network element asynchronous.<br>
   * The method NetworkElementImpl.processNEType(...) will be called asynchronously.
   * @throws SNMPCommFailure  The request failed.
   */
  private void checkNEType(SNMPCtrl ctrl, PollingParameters pp) throws SNMPCommFailure {
    try {
      CheckNETypeCommand command = new CheckNETypeCommand(ctrl, driverManager);
      command.getCommandParams().setPollingType(PollingType.INIT_SNMP_CTRL.getType());
      command.getCommandParams().setPollingParameters(pp);
      if(pp instanceof InitSnmpCtrlParameters)
        command.getCommandParams().setOnSuccess(((InitSnmpCtrlParameters) pp).getPolling());
      ctrl.getCommander().run(command);
    } catch (SNMPCommandException e) {
      e.explodeSNMPCommFailure();
    }
  }
}
