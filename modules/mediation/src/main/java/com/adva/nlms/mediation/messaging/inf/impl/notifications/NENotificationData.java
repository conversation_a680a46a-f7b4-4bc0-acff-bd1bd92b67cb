/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: k<PERSON><PERSON><PERSON><PERSON>
 */

package com.adva.nlms.mediation.messaging.inf.impl.notifications;

import com.adva.nlms.common.config.netypes.NeTypeIds;
import com.adva.nlms.inf.api.notification.NotificationData;
import com.adva.nlms.mediation.messaging.inf.NENotificationHeader;
import com.adva.nlms.mediation.messaging.inf.NENotificationHeaderUpdater;

import java.io.Serializable;

class NENotificationData extends NotificationData implements NENotificationHeader, NENotificationHeaderUpdater, Serializable {

  private static final long serialVersionUID = 1L;

  private int neType;

  public int getNetworkElementType () {
    if (neType>= NeTypeIds.OPTICAL_ROUTER_THRESHOLD) {
      return NeTypeIds.OPTICAL_ROUTER_THRESHOLD;
    } else {
      return neType;
    }
  }

  public void setNeType (int neType) {
    this.neType = neType;
  }

  @Override
  public boolean equals (Object o) {
    if (this == o) return true;
    if (o == null || getClass() != o.getClass()) return false;
    if (!super.equals(o)) return false;

    NENotificationData that = (NENotificationData) o;

    if (neType != that.neType) return false;

    return true;
  }

  @Override
  public int hashCode () {
    int result = super.hashCode();
    result = 31 * result + neType;
    return result;
  }

  @Override
  public String toString () {
    StringBuilder sb = new StringBuilder();
    sb.append(" notifiedObjectId[").append(getNotifiedObjectId()).append(']');
    sb.append(" notifiedObjectClass[").append(getNotifiedObjectClass().getSimpleName()).append(']');
    sb.append(" notifiedObjectAid[").append(getNotifiedObjectAid()).append(']');
    sb.append(" neType[").append(getNetworkElementType()).append(']');
    return sb.toString();
  }
}
