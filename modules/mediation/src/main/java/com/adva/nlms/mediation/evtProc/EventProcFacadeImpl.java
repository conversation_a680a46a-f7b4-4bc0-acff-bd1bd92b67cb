/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: adaemmig
 */

package com.adva.nlms.mediation.evtProc;

import com.adva.fm.api.dto.AppNetworkMgmtEvent;
import com.adva.nlms.common.AlarmTrapType;
import com.adva.nlms.common.AlarmTypeHandler;
import com.adva.nlms.common.AlarmTypeHandlerImpl;
import com.adva.nlms.common.EventDescription;
import com.adva.nlms.common.NEUtils;
import com.adva.nlms.common.NO_TIMESTAMP;
import com.adva.nlms.common.NetworkAlarmTypeProperty;
import com.adva.nlms.common.config.EntityIndex;
import com.adva.nlms.common.config.ModuleType;
import com.adva.nlms.common.config.NetworkElementDTO;
import com.adva.nlms.common.config.netypes.NeTypeIds;
import com.adva.nlms.common.event.EventDetectionType;
import com.adva.nlms.common.event.EventSeverity;
import com.adva.nlms.common.event.EventStatus;
import com.adva.nlms.common.event.EventType;
import com.adva.nlms.common.event.TrapParameterID;
import com.adva.nlms.common.event.WorkingProtectionFlag;
import com.adva.nlms.common.event.types.CategoryType;
import com.adva.nlms.common.snmp.MIB;
import com.adva.nlms.common.snmp.MIBHelper;
import com.adva.nlms.common.snmp.NoSuchMDObjectException;
import com.adva.nlms.common.traps.FSP_NMTraps;
import com.adva.nlms.mediation.common.concurrent.MDFixedThreadPool;
import com.adva.nlms.mediation.common.event.WebSocketNotification;
import com.adva.nlms.mediation.common.util.InvMtosiCapableDefinition;
import com.adva.nlms.mediation.config.ConfigCtrlImpl;
import com.adva.nlms.mediation.config.EntityDBImpl;
import com.adva.nlms.mediation.config.EntityDBRefProvider;
import com.adva.nlms.mediation.config.ManagedObjectDAO;
import com.adva.nlms.mediation.config.ManagedObjectDBImpl;
import com.adva.nlms.mediation.config.ModuleDBImpl;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.NetworkElementDBImpl;
import com.adva.nlms.mediation.config.NetworkElementHdlrLocal;
import com.adva.nlms.mediation.config.NoSuchNetworkElementException;
import com.adva.nlms.mediation.config.ec.entity.equipment.plug.PlugEcDBImpl;
import com.adva.nlms.mediation.config.fsp_r7.utils.F7Utils;
import com.adva.nlms.mediation.config.sm.relatedservices.RelatedDataServicesEventDescriptor;
import com.adva.nlms.mediation.event.EventAssocObjectId;
import com.adva.nlms.mediation.event.EventDTO;
import com.adva.nlms.mediation.event.EventLogger;
import com.adva.nlms.mediation.event.EventMultiVarDTO;
import com.adva.nlms.mediation.event.EventProcessingActionHdlr;
import com.adva.nlms.mediation.evtProc.F3.BulkTrapParser;
import com.adva.nlms.mediation.evtProc.api.EventProcFacade;
import com.adva.nlms.mediation.evtProc.pipeline.EventProcessingManager;
import com.adva.nlms.mediation.infrastructure.concurrent.AdvaExecutors;
import com.adva.nlms.mediation.infrastructure.concurrent.NamedThreadFactory;
import com.adva.nlms.mediation.mltopologymodel.model.dao.MLTopologyElementDAO;
import com.adva.nlms.mediation.mltopologymodel.model.db.MLServiceDBImpl;
import com.adva.nlms.mediation.mltopologymodel.model.interfaces.MLTopologyMOReference;
import com.adva.nlms.mediation.mltopologymodel.mofacade.MLMoReferenceHelper;
import com.adva.nlms.mediation.sm.business.SMServiceBusiness;
import com.adva.nlms.mediation.sm.dao.ConnInfoDAO;
import com.adva.nlms.mediation.sm.model.AbstractConnectionDBImpl;
import com.adva.nlms.mediation.sm.model.eth.AbstractEthernetConnectionDBImpl;
import com.adva.nlms.mediation.topology.LineDBImpl;
import com.adva.nlms.mediation.topology.LineDao;
import com.adva.nlms.opticalrouter.api.polling.out.OpticalRouterNotification;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.snmp4j.PDU;
import org.snmp4j.PDUv1;
import org.snmp4j.smi.Counter64;
import org.snmp4j.smi.Integer32;
import org.snmp4j.smi.IpAddress;
import org.snmp4j.smi.OID;
import org.snmp4j.smi.OctetString;
import org.snmp4j.smi.SMIConstants;
import org.snmp4j.smi.UnsignedInteger32;
import org.snmp4j.smi.VariableBinding;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.Vector;
import java.util.concurrent.ScheduledExecutorService;

@Component
public class EventProcFacadeImpl implements EventProcFacade {

  //==============================================================================
  //=== STATIC fields ============================================================
  //==============================================================================

  private static final OID SNMP_TRAP_OID = new OID(MIB.SNMPV2.SnmpTrap.SNMP_TRAP_OID);
  private static final OID GENERIC_TRAPS = new OID(MIB.SNMPV2.OID_GENERIC_TRAPS);
  private static final EventMultiVarDTO[] EMPTY_EVENT_MULTIVAR_PROPS = new EventMultiVarDTO[0];

  private static final String S_STATE = "S-STATE";
  private static final String S_SERV = "S-SERV";
  private static final String SERVICE_LAYER_RMV = "SERVICE-LAYER-RMV";
  private static final String SERVICE_M_INTERMED = "SERVICE-M-INTERMED";
  private static final String DYING_GASP_TRAP_OID = "1.3.6.1.4.1.2544.1.20.1.4.1.1";

  public static final ScheduledExecutorService evExecutor = AdvaExecutors.newSingleThreadScheduledExecutor(new NamedThreadFactory("netconf-event"), false);

  //==============================================================================
  //=== References to Controllers, Handlers ======================================
  //==============================================================================

  private final Logger log = LogManager.getLogger(EventProcFacadeImpl.class.getName());
  private final Logger logSbi = EventLogger.getLogger(EventLogger.Category.itf_sbi);
  private final Logger logModDB = EventLogger.getLogger(EventLogger.Category.dbModify);
  private final AlarmTypeHandler alarmTypeHandler = AlarmTypeHandlerImpl.getInstance();

  @Autowired
  private TrapHandler trapHandler;
  @Autowired
  private NetworkElementHdlrLocal neHdlr;
  @Autowired
  private EventProcessingManager evtProcMgr;
  @Autowired
  private CorrelationTriggerChecker correlationTriggerChecker;
  @Autowired
  private LineDao lineDAO;
  @Autowired
  private RelatedDataServicesEventDescriptor relatedDataServicesEventDescriptor;
  @Autowired
  private MLTopologyElementDAO accessHelper;
  @Autowired
  private MLMoReferenceHelper moReferenceHelper;
  @Autowired
  private EvtProcHelper evtProcHelper;
  @Autowired
  private BulkTrapParser bulkTrapParser;

  public EventProcFacadeImpl() {
  }

  //==============================================================================
  //=== Public methods ===========================================================
  //==============================================================================

  //------------------------------------------------------------------------------
  //--- Add events for Pre-processing --------------------------------------------
  //------------------------------------------------------------------------------

  @Override
  public void addSnmpV1Trap(PDUv1 pdu, IpAddress peerIpAddress, byte[] securityName) {
    EvtProcInfo.Sample.RCV_EVENT_TRP.counter++;   // monitoring counter
    TrapHandler.MonitoringCounter.EVENTS_TRP.counter++;

    EventDTO event = new EventDTO();

    event.detectionType = EventDetectionType.TRAP;
    event.nmsTimeStamp = System.currentTimeMillis();

    setSourceNe(event, pdu.getAgentAddress().toString(), peerIpAddress.getInetAddress().getHostAddress());
    if(doNotAllowSnmpTrapsFromNe(event)){
      return;
    }
    event.setVbl((List<VariableBinding>) pdu.getVariableBindings());

    event.setIsSpecificTrap(pdu.getGenericTrap() == PDUv1.ENTERPRISE_SPECIFIC);
    event.enterprise = event.isSpecificTrap() ? trimOid(pdu.getEnterprise(), false).toDottedString() : MIB.SNMPV2.OID_GENERIC_TRAPS;
    event.setTrapID(event.isSpecificTrap() ? pdu.getSpecificTrap() : pdu.getGenericTrap());
    event.setOrigTrapId(event.getTrapID());

    trapHandler.handleTrap(event, (List<VariableBinding>) pdu.getVariableBindings());
  }

  @Override
  public void addSnmpV2Trap(PDU pdu, IpAddress peerIpAddress, byte[] securityName) {
    EvtProcInfo.Sample.RCV_EVENT_TRP.counter++;  // monitoring counter
    TrapHandler.MonitoringCounter.EVENTS_TRP.counter++;

    EventDTO event = new EventDTO();

    event.detectionType = EventDetectionType.TRAP;
    event.nmsTimeStamp = System.currentTimeMillis();

    setSourceNe(event, peerIpAddress.getInetAddress().getHostAddress());
    setSnmpTrapOid(event, pdu.getVariableBindings());

    if(doNotAllowSnmpTrapsFromNe(event)){
      return;
    }
    event.setVbl((List<VariableBinding>) pdu.getVariableBindings());

    event.setOrigTrapId(event.getTrapID());

    // we need to check if sourceNeID is not 0 becuase otherwise bulkTrapMap will be populated with invalid data which are not deleted in further trap processing
    // leading to possible outOfMemoryException
    if (bulkTrapParser.checkIfBulkTrap(event.enterprise, event.getTrapID()) && event.sourceNE_ID != 0)
      bulkTrapParser.handleBulkTrap(event, pdu, peerIpAddress);

    trapHandler.handleTrap(event, (List<VariableBinding>) pdu.getVariableBindings());
  }

  @Override
  public void addEventLogTrap(SNMPTrap snmpTrap, List<VariableBinding> vbList, EventDetectionType detectionType) {
    EvtProcInfo.Sample.RCV_EVENT_LOG.counter++;
    EventDTO event = new EventDTO();

    event.detectionType = detectionType;
    event.nmsTimeStamp = System.currentTimeMillis();

    setSourceNe(event, snmpTrap.ipAddress);
    event.setVbl(new Vector<>(vbList));

    event.setIsSpecificTrap(snmpTrap.isSpecificTrap);
    event.snmpTrapOid = snmpTrap.enterprise;
    event.enterprise = trimOid(new OID(snmpTrap.enterprise), true).toDottedString();
    event.setTrapID(snmpTrap.trapID);
    event.setOrigTrapId(event.getTrapID());

    event.neLogIndex = snmpTrap.logIndex;
    if(snmpTrap.timeStamp != null)
      event.neTimeStamp = MIBHelper.getTimestampFromByteArray(snmpTrap.timeStamp);
    else
      event.neTimeStamp = snmpTrap.timeStampLong;

    trapHandler.handleTrap(event, event.getVbl());
  }

  @Override
  public void addPollingEvent(EventDTO event) {
    EvtProcInfo.Sample.RCV_EVENT_UPD.counter++;   // monitoring counter
    setSourceNe(event);
    event.setIsSpecificTrap(true);

    trapHandler.handleTrap(event, null);
  }

  public void addEvent(AppNetworkMgmtEvent appEvent) {
    try {
      EventDTO event = evtProcHelper.createEvent(appEvent);
      evtProcMgr.addEventForProcessing(event);
    }
    catch (RuntimeException e) {
      log.error("addEvent failed ({}) appEvent: {}", e.getMessage(), appEvent);
    }
  }

  @Override
  public void add3PartyDeviceEvent(EventDTO eventDto) {
    eventDto.setSeqNbr(trapHandler.eventSeqNbr++);
    evtProcMgr.addEventForProcessing(eventDto);
  }


  //------------------------------------------------------------------------------
  //--- Add events for Pre-processing - specific methods -------------------------

  public void addEventDTOServerTrap (EventDTO serverTrap) {
    try {
      // either NE ID or IP address has to be set
      if (serverTrap.sourceNE_ID == 0) {
        serverTrap.sourceNE_ID = neHdlr.getIDByIPAddr(serverTrap.sourceNE_IP);
      } else if ((serverTrap.sourceNE_IP == null) || (serverTrap.sourceNE_IP.length() == 0)) {
        serverTrap.sourceNE_IP = neHdlr.getProperties(serverTrap.sourceNE_ID).ipAddress;
      }

      if (log.isInfoEnabled()) log.info("Polling event from server: " + serverTrap.getTrapID() +
        " concerning NE " + serverTrap.sourceNE_IP +
        " entity: " + serverTrap.objectIndex);
      if(serverTrap.neTimeStamp == 0)
        serverTrap.neTimeStamp = NO_TIMESTAMP.value; // neTimeStamp
      if(serverTrap.objectIndex == null) serverTrap.objectIndex = EntityIndex.ZERO;
      serverTrap.setNetworkElementProperties(neHdlr.getProperties(serverTrap.sourceNE_ID));


      if (!(F7Utils.getInstance().isF7(serverTrap.sourceNE_ID)
          && InvMtosiCapableDefinition.isMtosiNamingEnabled(serverTrap.sourceNE_ID,
          neHdlr.getNetworkElementType(serverTrap.sourceNE_ID)))) {
        serverTrap.setEntityDescription(serverTrap.getAidString());
      }

      addPollingEvent(serverTrap);

    } catch (RuntimeException re) {
      log.error("addEventDTOServerTrap {}", serverTrap, re);
    } catch (NoSuchNetworkElementException e) {
      log.error("addEventDTOServerTrap no-NE {}", serverTrap);
    }
  }

  /**
   * Starts the thread that adds a server generated event asynchronously.
   * @param serverTrap  The server trap.
   */
  @Override
  public void addAsyncEventDTOServerTrap(final EventDTO serverTrap) {
    MDFixedThreadPool.execute(new Runnable() {
      @Override
      public void run() {
        addEventDTOServerTrap(serverTrap);
      }
    });
  }

  @Override
  public void addSNMPTrap(SNMPTrap snmpTrap) {

    if (log.isDebugEnabled()) log.debug("FM:addSNMPTrap() trap-ID={}, source-IP={}", snmpTrap.trapID, snmpTrap.ipAddress);
    ArrayList<VariableBinding> vbList = new ArrayList<>();
    OID oid;

    for (int i= 0; i<snmpTrap.varBinds.length; ++i) {

      oid = new OID(snmpTrap.varBinds[i].objectID);

      switch (snmpTrap.varBinds[i].valType) {
        case MIB.FSP.NEEventLogVar.TYPE_INTEGER_32:
          vbList.add(new VariableBinding(oid, new Integer32((int)snmpTrap.varBinds[i].longValue)));
          break;
        case MIB.FSP.NEEventLogVar.TYPE_COUNTER64:
          vbList.add(new VariableBinding(oid, new Counter64(snmpTrap.varBinds[i].longValue)));
          break;
        case MIB.FSP.NEEventLogVar.TYPE_OCTET_STRING:
          vbList.add(new VariableBinding(oid, new OctetString(snmpTrap.varBinds[i].octets)));
          break;

        case MIB.FSP.NEEventLogVar.TYPE_IP_ADDRESS:
          try {
            vbList.add(new VariableBinding(oid, new IpAddress(new String(snmpTrap.varBinds[i].octets,"UTF-8"))));
          } catch (UnsupportedEncodingException e) {
            throw new Error("Unsupported encoding: UTF-8",e);
          }
          break;

        case MIB.FSP.NEEventLogVar.TYPE_OBJECT_ID:
          try {
            vbList.add(new VariableBinding(oid, new OID(new String((snmpTrap.varBinds[i].octets),"UTF-8"))));
          } catch (UnsupportedEncodingException e) {
            throw new Error("Unsupported encoding: UTF-8",e);
          }
          break;

        case MIB.FSP.NEEventLogVar.TYPE_UNSIGNED32:
          vbList.add(new VariableBinding(oid, new UnsignedInteger32(snmpTrap.varBinds[i].longValue)));
          break;

        default:
          if (log.isInfoEnabled()) log.info("received trap from trap log with unsupported syntax ID "+
            snmpTrap.varBinds[i].valType);
      }
    }

    addEventLogTrap(snmpTrap, vbList, EventDetectionType.TRAP_LOG);
  }

  /**
   * Adds an event, for testing only!!
   */
  @Override
  public void addSNMPTrap(EventDTO eventDTO) {
    try {
      // Find the NE
      eventDTO.setNetworkElementProperties(neHdlr.getNetworkElementDTOByIPAddress(eventDTO.sourceNE_IP));
    } catch (NoSuchMDObjectException x) {
      log.warn("handleTrap(): Source NE not found: {}", eventDTO.sourceNE_IP);
      return;
    }

    log.warn("addSNMPTrap-Test: trap source {}, NE type {}, trap: {}", eventDTO.sourceNE_IP,
        eventDTO.sourceNEType, eventDTO.getTrapID());

    // set some additional values from XML file
    AlarmTypeHandler alarmTypeHandler = AlarmTypeHandlerImpl.getInstance();
    AlarmTrapType alamTypeProperties = alarmTypeHandler.getTrapDescriptor(eventDTO.sourceNEType, eventDTO.getTrapID());

    eventDTO.shortName = alamTypeProperties.getAlarmShortName();
    eventDTO.alarmType = alamTypeProperties.getAlarmTypeID();
    eventDTO.category  = alamTypeProperties.getCategoryType();
    eventDTO.impairment = alamTypeProperties.isServiceAffecting();

    // Set event message
    eventDTO.setText(new StringBuilder(alamTypeProperties.getMessage(EventDescription.entityToBeDetermined)));

    // Supply event if any to the event queue
    try {
      evtProcMgr.addEventForProcessing(eventDTO);

      if (log.isInfoEnabled()) {
        if (eventDTO.neLogIndex == 0)
          log.info("Trap contained no NE time stamp.");
        else
          log.info("NE trap log index: {}; NE time stamp: {}", eventDTO.neLogIndex, new Date(eventDTO.neTimeStamp));
        log.info("Event supplied to event queue");
      }

    } catch (Exception e) {
      log.error(eventDTO, e);
    }
  }

  //------------------------------------------------------------------------------
  //--- Add events w/o Pre-processing --------------------------------------------
  //------------------------------------------------------------------------------

  @Override
  public void addServerActionEvent(EventProcessingActionHdlr.Action actionType, int neId, EventDTO.Param... params) {

    try {
      EventDTO event = new EventDTO(EventDetectionType.SERVER,
                                    null,
                                    neHdlr.getProperties(neId),
              System.currentTimeMillis(),
                                    NO_TIMESTAMP.value, // neTimeStamp
                                    0,                  // neLogIndex
                                    0, // trapID
                                    EventSeverity.NOT_REPORTED,
                                    EntityIndex.ZERO,
                                    TrapParameterID.customTrap,
                                    0,
                                    "",
                                    0,
                                    0,
                                    0,
                                    CategoryType.SYSTEM_EVENT,
                                    "");
      event.type = EventType.SYSTEM;
      event.setServerActionType(actionType);
      event.setParams(params);

      evtProcMgr.addEventForProcessing(event);
    }
    catch (NoSuchNetworkElementException nsne) {
      log.error("addServerActionEvent failed ", nsne);
    }
  }

  //------------------------------------------------------------------------------
  //--- Add events for DB storage ------------------------------------------------
  //------------------------------------------------------------------------------

  @Override
  public void addServerActionEvent4DB(EventProcessingActionHdlr.Action actionType, int neId, EventDTO.Param... params) {

    EventDTO event = evtProcHelper.createServerEventDTO(0, EventType.SYSTEM, EventSeverity.NOT_REPORTED, "", "", null, params);
    event.sourceNE_ID = neId;
    event.setServerActionType(actionType);

    evtProcMgr.addEventToDBBuffer(event);
  }

  @Override
  public void addDBEvent(EventDTO event) {
    event.setSeqNbr(trapHandler.eventSeqNbr++);
    if (event.isAlarm())
      evtProcMgr.addEventForProcessing(event);
    else
      evtProcMgr.addEventToDBBuffer(event);
  }

  @Override
  public void addServerAlarm(int trapID,
                             EventType type,
                             Object ne,
                             EventDTO.Param... params) {
    addServerAlarm(trapID, type, ne, EntityIndex.ZERO, org.apache.commons.lang3.StringUtils.EMPTY, org.apache.commons.lang3.StringUtils.EMPTY, params);
  }

  @Override
  public void addServerAlarm(int trapID,
                             EventType type,
                             Object ne,
                             EntityIndex objectIndex,
                             String shortDescription,
                             String fullDescription,
                             EventDTO.Param... params)
  {
    EventDTO event = evtProcHelper.createServerAlarmDTO(trapID, type, ne, params);
    if (event != null) {
      event.setEntityDescr(objectIndex, shortDescription, fullDescription);
      event.setSeqNbr(trapHandler.eventSeqNbr++);
      // add correlation trigger for server alarms (e.g. LINE_ALARM)
      correlationTriggerChecker.checkEventForTriggers(event);
      // add event to DB queue
      if (logModDB.isInfoEnabled())
        logModDB.info("FM:addServerAlarm {}", event);

      if (Arrays.stream(FSP_NMTraps.serviceClientAffectedIds).anyMatch(e -> e == trapID)
           || Arrays.stream(FSP_NMTraps.serviceNetworkAffectedIds).anyMatch(e -> e == trapID))
        evtProcMgr.addEventToDBBuffer(event);
      else
        evtProcMgr.addEventForProcessing(event);
    }
  }

  @Override
  public void addStatusEvent(final int trapID,
                             final EventType eventType,
                             final EventSeverity eventSeverityType,
                             final String guiDescriptionString,
                             final String guiCause,
                             final NetworkElement ne,
                             final String shortDescription,
                             final String fullDescription,
                             final EntityIndex objectIndex,
                             final EventDTO.Param... params) {
    if (logModDB.isInfoEnabled()) logModDB.info("FM:addStatusEvent() {}, {}, {}", guiCause, ne.getID(), shortDescription);

    EventDTO eventDTO = evtProcHelper.createStatusEventDTO(trapID, eventType, eventSeverityType, guiDescriptionString, guiCause, ne, params);
    eventDTO.setEntityDescr(objectIndex, shortDescription, fullDescription);

    evtProcMgr.addEventToDBBuffer(eventDTO);
  }

  @Override
  public void addServerEvent(int trapID,
                             EventType type,
                             Object ne,
                             EventDTO.Param... params)
  {
    addServerEvent(trapID, type, ne, EntityIndex.ZERO, org.apache.commons.lang3.StringUtils.EMPTY, org.apache.commons.lang3.StringUtils.EMPTY, params);
  }

  @Override
  public void addServerEvent(int trapId,
                             EventType eventType,
                             EventSeverity eventSeverity,
                             NetworkElement ne,
                             EntityIndex entityIndex,
                             String entityAidString,
                             String entityAlias,
                             String eventShortDescription,
                             String eventFullDescription)
  {
    EventDTO eventDto = evtProcHelper.createServerEventDTO(trapId, eventType, eventSeverity, eventFullDescription, eventShortDescription, ne,
        new EventDTO.Param.EntityAlias(entityAlias));
    eventDto.setEntityDescr(entityIndex, entityAidString, entityAidString);
    evtProcMgr.addEventToDBBuffer(eventDto);
  }

  @Override
  public void addServerEvent(int trapID,
                             EventType type,
                             Object ne,
                             EntityIndex objectIndex,
                             String shortDescription,
                             String fullDescription,
                             EventDTO.Param... params)
  {
    EventDTO event = evtProcHelper.createServerEventDTO(trapID, type, ne, params);
    if (event != null) {
      event.setEntityDescr(objectIndex, shortDescription, fullDescription);
      evtProcMgr.addEventToDBBuffer(event);
    }
  }

  @Override
  public void addServerEvent(final int trapID,
                             final EventType eventType,
                             final EventSeverity eventSeverityType,
                             final String guiDescriptionString,
                             final String guiCause,
                             final NetworkElement ne,
                             final EventDTO.Param... params)
  {
    if (logModDB.isInfoEnabled()) logModDB.info("FM:addStatusEvent() {}, {}",guiCause, ne.getID());

    EventDTO eventDTO = evtProcHelper.createServerEventDTO(trapID, eventType, eventSeverityType, guiDescriptionString, guiCause, ne, params);
    evtProcMgr.addEventToDBBuffer(eventDTO);
  }

  @Override
  public void addGlobalServerAlarm(int trapID,
                                   EventType type,
                                   EventDTO.Param... params) {
    addServerAlarm(trapID, type, null, EntityIndex.ZERO, org.apache.commons.lang3.StringUtils.EMPTY, org.apache.commons.lang3.StringUtils.EMPTY, params);
  }

  @Override
  public void addGlobalEvent(EventType type, EventSeverity severity, String parameter, String shortName) {
    if (logModDB.isInfoEnabled()) logModDB.info("FM:addGlobalEvent() {}", shortName);

    EventDTO eventDTO = evtProcHelper.createServerEventDTO(EventDTO.noTrapID, type, severity, parameter, shortName, null);
    evtProcMgr.addEventToDBBuffer(eventDTO);
  }

  @Override
  public void addGlobalEvent(EventType type, EventSeverity severity, String message, String shortName, Throwable exc) {
    StringBuilder msg = new StringBuilder(message);
    if (exc != null && exc.getMessage() != null) {
      msg.append(". Error cause: ").append(exc.getMessage());
    }
    addGlobalEvent(type, severity, msg.toString(), shortName);
  }

  @Override
  public void addLineAlarm(int trapID, EventType type, int lineId, EventDTO.Param[] params) {
    LineDBImpl lineDB = lineDAO.getLineByLineId(lineId);
    if (lineDB != null) {
      params = ArrayUtils.add(params, new EventDTO.Param.LineID(lineId));
      EventDTO alarmA = evtProcHelper.createServerAlarmDTO(trapID, type, lineDB.getAEndNE(), params);
      EventDTO alarmZ = evtProcHelper.createServerAlarmDTO(trapID, type, lineDB.getZEndNE(), params);

      if (alarmA != null && alarmZ != null) {    // add event to queue
        alarmA.setSeqNbr(trapHandler.eventSeqNbr++);
        alarmZ.setSeqNbr(trapHandler.eventSeqNbr++);

        evtProcMgr.addEventForProcessing(alarmA);
        evtProcMgr.addEventForProcessing(alarmZ);
      }
    } else {
      log.error("EventProcFacade.addLineAlarm failed: trapId={}, type={}, lineId={}", trapID, type, lineId);
    }
  }

  @Override
  public void addConnectionAlarm(int trapId, EventType eventType, int[] connIDs, int sourceNeID, int subnetID, EntityIndex objectIndex, int[] parentIDs, String entityDescr) {
    if (logModDB.isInfoEnabled()) logModDB.info("FM:addConnectionAlarm() {}, {}, {}, {}, {}", trapId, eventType,
        sourceNeID, Arrays.toString(connIDs), entityDescr);
    addConnectionAlarm(trapId, eventType, null, connIDs, sourceNeID, subnetID, objectIndex, parentIDs, entityDescr);
  }

  private void addConnectionAlarm(int trapID, EventType eventType, EventSeverity severity, int[] connIDs, int sourceNeID,
                                  int subnetID, EntityIndex objectIndex, int[] parentIDs, String entityDescr) {

    AlarmTypeHandler alarmTypeHandler = AlarmTypeHandlerImpl.getInstance();
    AlarmTrapType alarmTrapDescriptor = alarmTypeHandler.getTrapDescriptor(NeTypeIds.NETWORK_ELEMENT_TYPE_ANY, trapID);


    if (severity == null || severity == EventSeverity.UNKNOWN) {
      severity = alarmTrapDescriptor.getEventSeverityType(NetworkAlarmTypeProperty.SEVERITY_ASSIGNMENT.NO_SERVICE);
    }

    NetworkElementDTO networkElementProps = getNeProperties(sourceNeID, trapID);
    EventDTO event = new EventDTO(0, 0, trapID, 0, 0,
                eventType, false,
                severity,
                System.currentTimeMillis(),
                NO_TIMESTAMP.value,
                NO_TIMESTAMP.value,
                NO_TIMESTAMP.value,
                0, 0,
                new StringBuilder(),
                alarmTrapDescriptor.getAlarmShortName(),
                false, false, 0, "",
                EventStatus.Impairment.NONSERVICE_AFFECTING.getDbValue(),
                EventDetectionType.SERVER,
                EventStatus.Correlation.PRIMARY.getType(),
                0, 0,
                networkElementProps != null ? StringUtils.defaultString(networkElementProps.ipAddress) : "",
                sourceNeID,
                networkElementProps != null ? networkElementProps.type : 0,  // ne type
                subnetID == 0 ? ConfigCtrlImpl.get().getHandlers().getSubnetHdlr().getTopLevelSubnetID() : subnetID,
                0, ArrayUtils.EMPTY_INT_ARRAY, ArrayUtils.EMPTY_INT_ARRAY, "", "", EntityIndex.ZERO, ModuleType.NO_MODULE, "N/A", EntityIndex.ZERO, objectIndex, TrapParameterID.valueOf(0), 0, "", 0, 0,"","","", ArrayUtils.EMPTY_INT_ARRAY, EMPTY_EVENT_MULTIVAR_PROPS,"",false, WorkingProtectionFlag.NA,false,"", false, new int[]{0}, new int[]{0}, 0,"","","");

    // Clearing events don't need all that.
    if (eventType == EventType.RAISED) {
      event.setText(new StringBuilder(alarmTrapDescriptor.getMessage(EventDescription.entityToBeDetermined)));
      event.alarmType = alarmTrapDescriptor.getAlarmTypeID();
      event.impairment = alarmTrapDescriptor.isServiceAffecting();
      event.entityDescription = entityDescr;
    }

    event.category  = alarmTrapDescriptor.getCategoryType();
    event.alarmClass = alarmTrapDescriptor.getAlarmClass();
    event.connectionIDs = connIDs;
    event.parentsIDs = parentIDs;
    event.serviceName = relatedDataServicesEventDescriptor.prepareDescriptionFor(event, event.connectionIDs);
    event.setSeqNbr(trapHandler.eventSeqNbr++);

    evtProcMgr.addEventForProcessing(event);
  }

  @Override
  public void addConnectionEvent(EventType type, boolean securityEvent, EventSeverity severity, String parameter,
                                 String shortName, String sourceName, String connectionName, int parentID, int neId) {
    if (logModDB.isInfoEnabled()) logModDB.info("FM:addConnectionEvent() {}, {}", shortName, sourceName);
    int subnetID=0;
    if (shortName.equals(SERVICE_LAYER_RMV) || shortName.equals(SERVICE_M_INTERMED)) {
      NetworkElementDBImpl networkElementDB;
      try {
        networkElementDB = neHdlr.getNEByID(neId);
        subnetID=networkElementDB.getSubnet().getId();
      } catch (NoSuchNetworkElementException e) {
        logModDB.error("FM:addConnectionEvent() NE not found {}", neId);
      }
    }
    EventDTO event = evtProcHelper.getConnectionEventProperties(type, securityEvent, severity, parameter, shortName, 0, parentID, neId, subnetID);
    event.sourceName = sourceName;
    if (shortName.equals(SERVICE_LAYER_RMV) || shortName.equals(SERVICE_M_INTERMED) || shortName.equals(S_SERV)) {
      event.serviceName = connectionName;
    }
    evtProcMgr.addEventToDBBuffer(event);
  }

  @Override
  public void addConnectionEvent(EventType type, boolean securityEvent, EventSeverity severity, String parameter,
                                 String shortName, String sourceName, int connID, int parentID, int neId) {
    if (logModDB.isInfoEnabled()) logModDB.info("FM:addConnectionEvent() {}, {}", shortName, connID);

    AbstractConnectionDBImpl connectionDBImpl = SMServiceBusiness.queryConnectionById(connID);

    switch (shortName)	{
      // S-STATE: MODSAS (modify service admin state)
      // S-SERV: ADDSER, MODSER, DELSER, EQZSER
      case S_STATE:
        if (connectionDBImpl == null) {
          log.error("Cannot find connection, addConnectionEvent failed for {}", connID);
          return;
        }
        addConnectionEvent(type, securityEvent, severity, parameter, shortName, "", connID, parentID,
            connectionDBImpl.getStartNEID(), getSubnetId(connectionDBImpl.getStartModule(),connectionDBImpl.getStartNEID()));
        if (connectionDBImpl instanceof AbstractEthernetConnectionDBImpl)
          addConnectionEvent(type, securityEvent, severity, parameter, shortName, "", connID, parentID,
              connectionDBImpl.getPeerNEID(), getSubnetId(connectionDBImpl.getPeerModule(),connectionDBImpl.getPeerNEID()));
        break;
       case S_SERV:
        if (connectionDBImpl == null) {
          log.error("Cannot find connection, addConnectionEvent failed for {}", connID);
          return;
        }
        int subnetID = getSubnetId(connectionDBImpl.getStartModule(), 0);
        addConnectionEvent(type, securityEvent, severity,parameter,shortName, sourceName, connID, parentID, 0,
            subnetID == 0 ? new ConnInfoDAO().getSubnetIdFromConnection(connectionDBImpl) : subnetID);
        break;
      case SERVICE_LAYER_RMV, SERVICE_M_INTERMED:
        addConnectionEvent(type, securityEvent, severity, parameter, shortName, "", connID, parentID, neId, getSubnetId(null, neId));
        break;
      default:
        addConnectionEvent(type, securityEvent, severity, parameter, shortName, "", connID, parentID, 0, 0);
    }
  }

  private int getSubnetId(ModuleDBImpl moduleDB, int neId)  {
    if (moduleDB!= null)  {
      return EntityDBRefProvider.getNEDBImpl(moduleDB).getSubnet().getId();
    } else if (neId != 0) {
      try {
        return neHdlr.getNEByID(neId).getSubnet().getId();
      } catch (NoSuchNetworkElementException e) {
        logModDB.error("FM:addConnectionEvent() NE not found {}", neId);
      }
    }
    return ConfigCtrlImpl.get().getHandlers().getSubnetHdlr().getTopLevelSubnetID();
  }


  private void addConnectionEvent(EventType type, boolean securityEvent,
                                  EventSeverity severity,
                                  String parameter,
                                  String shortName,
                                  String sourceName,
                                  int connID, int parentID, int sourceneID, int subnetID) {

    EventDTO event = evtProcHelper.getConnectionEventProperties(type, securityEvent, severity, parameter, shortName, connID, parentID, sourceneID, subnetID);

    if (!shortName.equals(S_SERV)) {
      event.serviceName = relatedDataServicesEventDescriptor.prepareDescriptionFor(event, event.connectionIDs);
    }

    if (sourceName != null && !sourceName.isEmpty()) {
      event.sourceName = sourceName;
    }

    //populate new service reference based on Conn-ID
    MLTopologyMOReference ref = moReferenceHelper.getReferenceForConnection(connID);
    MLServiceDBImpl serviceDB = accessHelper.getTopoElementByMoReference(ref, MLServiceDBImpl.class);
    if (serviceDB!=null) {
      event.addMlTopologyObject(serviceDB.getId(), EventAssocObjectId.ObjectType.MLT_SERVICE);
    }


    evtProcMgr.addEventToDBBuffer(event);
  }

  public void addConnectivityServiceSecurityEvent(EventSeverity eventSeverity, String eventShortName, List<Integer> eventParentIds,
                                                  List<Integer> trailIds, String connectivityServiceName,
                                                  String customerName, String eventDescription, String connectionName,
                                                  int subnetId) {
    EventDTO event = evtProcHelper.getServiceSecurityEventDto(eventSeverity, eventShortName,
        eventParentIds, connectivityServiceName, customerName, eventDescription);
    if (subnetId > 0) {
      event.subnetID = subnetId;
    }
    trailIds.forEach(trailId -> event.addMlTopologyObject(trailId, EventAssocObjectId.ObjectType.MLT_SERVICE));
    if (connectionName != null && !connectionName.isEmpty()) {
      event.serviceName = connectionName; // The event serviceName attribute is the one used to display the value under the "connections" column
    }
    evtProcMgr.addEventToDBBuffer(event);
  }

  @Override
  public void addConnectivityServiceAlarm(int trapId, EventType type, int connectivityServiceId, int parentId,
                                                             String connectivityServiceName, int subnetId) {
    EventDTO event = evtProcHelper.createServerAlarmDTO(trapId, type, null);
    if (subnetId > 0) {
      event.subnetID = subnetId;
    }
    event.parentsIDs = new int[]{parentId};
    event.addMlTopologyObject(connectivityServiceId, EventAssocObjectId.ObjectType.EOD_CONNECTIVITY_SERVICE);
    event.sourceName = connectivityServiceName;
    evtProcMgr.addEventForProcessing(event);
  }

  @Override
  public void addLineEvent(EventType type, boolean securityEvent, EventSeverity severity, String aParameter, String zParameter, String shortName, int aEndNeID, int zEndNeID, int lineID) {
    if (logModDB.isInfoEnabled()) logModDB.info("FM:addLineEvent() {}, {}, {}, {}", shortName, aEndNeID, zEndNeID, lineID);

    //TODO caller shall create two times: addEvents( createSecurityEventDTO(...
    long retValue = 0;
    int[] neIDs = { aEndNeID, zEndNeID };
    NetworkElementDTO aEndNe=null;
    NetworkElementDTO zEndNe=null;
    try {
        aEndNe = neHdlr.getProperties(aEndNeID);
        zEndNe = neHdlr.getProperties(zEndNeID);
    } catch (NoSuchNetworkElementException nse) {
      // catch block is empty; checks and logs are done below
    }
    if (aEndNe == null || zEndNe == null) {
      logModDB.error("FM:addLineEvent() NE not found {}", (aEndNe == null ? aEndNeID : zEndNeID));
      return;
    }

    for (int neID : neIDs ) {
        // Set the Subnet ID for the counters.
      NetworkElementDTO ne;
        ne = (neID == aEndNeID) ? aEndNe : zEndNe;

        EventDTO eventDTO =
          new EventDTO(0,
                             0, 0, 0, 0,
                             type, securityEvent,
                             severity,
                             System.currentTimeMillis(),
                             NO_TIMESTAMP.value,
                             NO_TIMESTAMP.value,
                             NO_TIMESTAMP.value,
                             0, 0,
                             new StringBuilder((neID == aEndNeID) ? aParameter : zParameter),
                             shortName,
                             false, false, 0, "",
                             EventStatus.Impairment.NONSERVICE_AFFECTING.getDbValue(),
                             EventDetectionType.SERVER,
                             EventStatus.Correlation.PRIMARY.getType(),
                             0, 0,
                             ne.ipAddress, neID,
                             ne.type,
                             ne.subnet_ID,
                             lineID,
                             ArrayUtils.EMPTY_INT_ARRAY, ArrayUtils.EMPTY_INT_ARRAY, "", "", EntityIndex.ZERO, ModuleType.NO_MODULE, "N/A", EntityIndex.ZERO, EntityIndex.ZERO, TrapParameterID.valueOf(0), 0, "", 0, 0,"","","", ArrayUtils.EMPTY_INT_ARRAY, EMPTY_EVENT_MULTIVAR_PROPS,"",false, WorkingProtectionFlag.NA, false, "", false, new int[]{0}, new int[]{0}, 0, "", "", "");
      evtProcMgr.addEventToDBBuffer(eventDTO);
    }
  }

  @Override
  public void addSecurityEvent(EventSeverity severity,
                               String parameter,
                               String shortName,
                               String networkNodeID,    //TODO 30.10.2019 (AndreD) revert change and change caller if IP-related
                               int nodeID,
                               EventDTO.Param... params) {
    if (logModDB.isInfoEnabled()) {
      StringBuilder logMsgBuilder = new StringBuilder();
      logMsgBuilder.append("FM:addSecurityEvent() ").append(shortName).append(", ").append(networkNodeID). append(", ").append(nodeID);
      String logMsg = logMsgBuilder.toString().replaceAll("[\n\r]", "_");
      logModDB.info(logMsg);
    }

    EventDTO event = evtProcHelper.createSecurityEventDTO(severity, parameter, shortName);
    NetworkElementDTO ne = null;

    // set NE-/Subnet-ID
    if (null != networkNodeID && !networkNodeID.isEmpty() && !networkNodeID.equals("0")) {
      // check the node type of the child, if specified
        try {
          com.adva.nlms.common.TopologyNodeType nodetype = null;
          int networkNodeIdI = 0;
          try {
            networkNodeIdI = Integer.parseInt(networkNodeID);
            if(networkNodeIdI != 0) {
              nodetype = neHdlr.getType(networkNodeIdI);
            }
          } catch (NumberFormatException e){ // F8 is security event is connected by IP in keyStore not neId(db)
            networkNodeIdI = neHdlr.getIDByIPAddr(networkNodeID);
            nodetype = neHdlr.getType(networkNodeIdI);
          }
          if(networkNodeIdI != 0) {
            switch (nodetype) {
              case NETWORK_ELEMENT:
                try {
                  ne = neHdlr.getProperties(networkNodeIdI);
                  event.sourceNE_ID = networkNodeIdI;
                  event.sourceNEType = ne.type;
                  event.sourceNE_IP = ne.ipAddress;
                  event.subnetID = ne.subnet_ID;
                } catch (NoSuchNetworkElementException nse) {
                  logModDB.error("FM:addSecurityEvent() NE not found {}", nodetype.getId());
                  return;
                }
                break;
              case SUBNET:
                event.subnetID = networkNodeIdI;
                break;
              default:
                logModDB.error("FM:addSecurityEvent() unexpected TreeNodeType: {}", nodetype.getId());
            }
          }
        } catch (NoSuchMDObjectException e) {
          logModDB.warn("FM:addSecurityEvent() {}", e.getMessage());
          event.sourceName = networkNodeID;
        }

    }
    else {
      // Global-Event
      event.subnetID = ConfigCtrlImpl.get().getHandlers().getSubnetHdlr().getTopLevelSubnetID();
    }

    // set conn-/line-ID
    if (nodeID != 0) {
      try {
        com.adva.nlms.common.TopologyNodeType nodetype = neHdlr.getType(nodeID);
        switch (nodetype) {
          case LINE:
            event.lineID = nodeID;
            break;
          case SERVICE:
            event.connectionIDs = new int[]{nodeID};
            break;
          default:
            logModDB.error("FM:addSecurityEvent() unexpected TreeNodeType: {}", nodetype.getId());
        }
      } catch (NoSuchMDObjectException e) {
        logModDB.warn("FM:addSecurityEvent() {}", e.getMessage());
      }
    }

    event.setParams(params);
    if (networkNodeID != null && ne != null && NEUtils.isF3orF4Device(ne.type) && event.shortName.contains("ARC")) {
      event.moduleTypeName = getModuleType(Integer.parseInt(networkNodeID), event.entityDescription);
    }
    //populate new service reference based on Conn-ID
    if (event.hasClassicConnections()) {
      Arrays.stream(event.connectionIDs).forEach(v -> {
        MLTopologyMOReference ref = moReferenceHelper.getReferenceForConnection(v);
        MLServiceDBImpl serviceDB = accessHelper.getTopoElementByMoReference(ref, MLServiceDBImpl.class);
        if (serviceDB!=null) {
          event.addMlTopologyObject(serviceDB.getId(), EventAssocObjectId.ObjectType.MLT_SERVICE);
        }
      });
    }

    evtProcMgr.addEventToDBBuffer(event);
  }


  private String getModuleType(int nodeId,String aid) {
    ManagedObjectDBImpl managedObjectDB =  ManagedObjectDAO.getInstance().get(nodeId,aid);
    if (managedObjectDB instanceof EntityDBImpl entityDB) {
      if (entityDB instanceof PlugEcDBImpl) return entityDB.getParent().getParent().getAssignedTypeString();
      while (entityDB != null && !(entityDB instanceof ModuleDBImpl)) {
        entityDB = entityDB.getParent();
      }
      return entityDB != null ?   entityDB.getAssignedTypeString() : ModuleType.NO_MODULE.toString();
    }
    return ModuleType.NO_MODULE.toString();
  }


  @Override
  public Map<Integer, List<Integer[]>> getBulkTrapsMap() {
    return bulkTrapParser.getBulkTrapsMap();
  }

  public void removeBulkTrapEntry(int sourceNeId, int bulkTrapNeStartLogIndex, int bulkTrapNeEndLogIndex){
    bulkTrapParser.removeBulkTrapEntry(sourceNeId, bulkTrapNeStartLogIndex, bulkTrapNeEndLogIndex);
  }

  public void addWebSocketTrap(WebSocketNotification pdu, String ipAddress, EventDetectionType eventType) {
    EvtProcInfo.Sample.RCV_EVENT_TRP.counter++;  // monitoring counter
    TrapHandler.MonitoringCounter.EVENTS_TRP.counter++;

    EventDTO event = new EventDTO();

    event.detectionType = eventType;
    event.nmsTimeStamp = System.currentTimeMillis();

    setSourceNe(event, ipAddress);
    event.setIsSpecificTrap(true);
    event.setWebSocketNotification(pdu);
    trapHandler.handleTrap(event, new Vector<>());
  }

  @Override
  public void addOpticalRouterNotification(OpticalRouterNotification notification) {
    EventDTO event = new EventDTO();

    setSourceNe(event, notification.neId());

    event.detectionType = EventDetectionType.TRAP;
    event.nmsTimeStamp = notification.timestamp();
    event.setIsSpecificTrap(true);
    event.setOpticalRouterNotification(notification);

    trapHandler.handleTrap(event, new Vector<>());
  }

  @Override
  public void addPTPRemoteSlaveConnectionEvent(EventSeverity severity, String msg, String shortName, String rsConnectionName, int neId) {
    EventDTO eventDTO = evtProcHelper.createServerEventDTO(EventDTO.noTrapID, EventType.SYSTEM, severity, msg, shortName, null);
    eventDTO.entityDescription = rsConnectionName;
    eventDTO.entityFullDescription = rsConnectionName;
    eventDTO.sourceNE_ID = neId;

    try {
      NetworkElementDTO neProps = neHdlr.getProperties(neId);
      eventDTO.sourceNEType = neProps.type;
      eventDTO.sourceNE_IP = neProps.ipAddress;
      eventDTO.subnetID = neProps.subnet_ID;
      eventDTO.sourceName = neProps.neName + " (" + neProps.ipAddress + ")";
    } catch (NoSuchNetworkElementException e) {
      logModDB.warn("addPTPRemoteSlaveConnectionEvent - NE Not Found: neId={}", neId);
    }

    evtProcMgr.addEventToDBBuffer(eventDTO);
  }

  @Override
  public void addGnssFirewallActionInvokedEvent(EventSeverity severity, String entityDescription, String shortName, String description, int neId) {
    EventDTO eventDTO = evtProcHelper.createServerEventDTO(EventDTO.noTrapID, EventType.SYSTEM, severity, description, shortName, null);
    eventDTO.entityDescription = entityDescription;
    eventDTO.entityFullDescription = entityDescription;
    eventDTO.sourceNE_ID = neId;

    try {
      NetworkElementDTO neProps = neHdlr.getProperties(neId);
      eventDTO.sourceNEType = neProps.type;
      eventDTO.sourceNE_IP = neProps.ipAddress;
      eventDTO.subnetID = neProps.subnet_ID;
      eventDTO.sourceName = neProps.neName + " (" + neProps.ipAddress + ")";
    } catch (NoSuchNetworkElementException e) {
      logModDB.warn("addGnssFirewallActionInvokedEvent - NE Not Found: neId={}", neId);
    }

    evtProcMgr.addEventToDBBuffer(eventDTO);

  }

  public void setSourceNe(EventDTO event, String... sourceIps) {
    evtProcHelper.setSourceNe(event, sourceIps);
  }

  public void setSourceNe(EventDTO event, UUID... sourceIdentifiers) {
    evtProcHelper.setSourceNe(event, sourceIdentifiers);
  }

  //==============================================================================
  //=== Private methods ==========================================================
  //==============================================================================

  private void setSourceNe(EventDTO event) {
    if (event.sourceNE_ID == 0 && StringUtils.isEmpty(event.sourceNE_IP))
      return;

    try {
      NetworkElement ne;
      if (event.sourceNE_ID != 0)
        ne = neHdlr.getNetworkElement(event.sourceNE_ID);
      else
        ne = neHdlr.getNEImpl(event.sourceNE_IP);

      if (ne != null) {
        event.sourceNE_ID       = ne.getID();
        event.sourceNE_IP       = ne.getIPAddress();
        event.sourceNEType      = ne.getNetworkElementType();
        event.subnetID          = ne.getParentID();
      }
    } catch (NoSuchNetworkElementException x) {
      EvtProcInfo.Sample.DISCARD_EVENT_WRONG_IP.counter++; // monitoring counter
      EventLogger.logWarnOnce(logSbi, EventLogger.createCacheKey(event.sourceNE_IP),
                              "handleTrap(): Source NE not found", event, null);
    }
  }

  private void setSnmpTrapOid(EventDTO event, List<? extends VariableBinding> vbl) {
    event.setIsSpecificTrap(true);

    // Exceptional case: enterprise/Trap-ID for Juniper and Ray Express will be set by the specific TrapProcessors
    if (NEUtils.isJuniperDevice(event.getNeType()))
      return;

    if (vbl != null && vbl.size() >= 2 &&
        vbl.get(1).getOid().startsWith(SNMP_TRAP_OID) &&
        vbl.get(1).getVariable().getSyntax() == SMIConstants.SYNTAX_OBJECT_IDENTIFIER) {
      OID oid = (OID) vbl.get(1).getVariable();
      setSnmpTrapOidForEvent(event, oid);

      if (oid.startsWith(GENERIC_TRAPS)) {  //generic Trap
        event.setIsSpecificTrap(false);
        // decrement Trap-ID to be in line with SNMPv1 generic Trap-ID (see RFC 2089, 3.3 b.)
        event.setTrapID(event.getTrapID()-1);
      }

      vbl.remove(1);
      vbl.remove(0);
    }
    else {
      // enterprise is missing: tag this trap as customer trap
      event.enterprise = MIB.FSP.CUSTOM_TRAP_ENTERPRISE;
      event.setTrapID(MIB.FSP.SNMP_CUSTOM_TRAP_ID);
      String cacheKey = (vbl != null && !vbl.isEmpty()) ? vbl.get(0).toString() : event.sourceNE_IP;
      EventLogger.logWarnOnce(logSbi, EventLogger.createCacheKey(cacheKey),
                       "setSnmpTrapOid(): snmpTrapOID not found", event, event.getVbl());
    }
  }

  private void setSnmpTrapOidForEvent(EventDTO event, OID oid){
    event.snmpTrapOid = oid.toDottedString();
    event.setTrapID(oid.removeLast());
    event.enterprise = trimOid(oid, false).toDottedString();
  }

  private OID trimOid(OID oid, boolean removeLast) {
    if (oid != null && removeLast)
      oid.removeLast();
    if (oid != null && oid.isValid() && oid.get(oid.size() - 1) == 0)
      oid.removeLast();
    return oid;
  }

  private NetworkElementDTO getNeProperties(int sourceNeId, int trapId){
    NetworkElementDTO networkElementProps=null;
    if (trapId == FSP_NMTraps.DATA_SERV_AS_DOWN ||
        trapId == FSP_NMTraps.SERV_AS_DOWN ||
        Arrays.stream(FSP_NMTraps.serviceClientAffectedIds).anyMatch(e -> e == trapId) ||
        Arrays.stream(FSP_NMTraps.serviceNetworkAffectedIds).anyMatch(e -> e == trapId) ||
        trapId == FSP_NMTraps.MISMATCH_SERVICE_M_PV ||
        trapId == FSP_NMTraps.MISMATCH_SERVICE_M_RAPSMD_LEVEL ||
        (trapId == FSP_NMTraps.MISMATCH_SERVICE_M_EP_ED && sourceNeId == 0))
      return null;
    try {
      networkElementProps = neHdlr.getProperties(sourceNeId);
    } catch (NoSuchNetworkElementException e) {
      logModDB.warn("FM:addConnectionAlarm() NE Not Found neId={}, trapId={}", sourceNeId, trapId);
    }
    return networkElementProps;
  }

  private boolean doNotAllowSnmpTrapsFromNe(EventDTO event){
    int neType = event.sourceNEType;
    if(NEUtils.isF4Device(neType) && DYING_GASP_TRAP_OID.equals(event.snmpTrapOid)) {
      return false;
    }
    return NEUtils.isF4Device(neType) || NEUtils.isF8Device(neType);
  }
}
