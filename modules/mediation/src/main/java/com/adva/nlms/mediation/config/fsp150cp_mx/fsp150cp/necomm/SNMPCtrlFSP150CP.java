/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: piotrno
 */

package com.adva.nlms.mediation.config.fsp150cp_mx.fsp150cp.necomm;


import com.adva.nlms.mediation.common.serviceProvisioning.ContinuityTestResultPropertiesFSP150CP;
import com.adva.nlms.mediation.config.fsp150cp_mx.necomm.SNMPCtrlFSP150CP_MX;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPCommFailure;


/**
 * Handles SNMP requests for a FSP1500 network element.
 */
public interface SNMPCtrlFSP150CP extends SNMPCtrlFSP150CP_MX
{

  public void setPropertiesContinuityTest(int localPortIndex, int remotePortIndex) throws SNMPCommFailure;

  public void startContinuityTest(int localPortIndex, int remotePortIndex) throws SN<PERSON>CommFailure;

  public void stopContinuityTest(int remotePortIndex) throws SNMPCommFailure;

  public ContinuityTestResultPropertiesFSP150CP getContinuityTest(int localPortIndex, int remotePortIndex) throws SNMPCommFailure;

  public void setProtectionType (int shelfIndex, int value, int currentProtectionType) throws SNMPCommFailure;
  
  public void changePortState(int ifIndex, int portState) throws SNMPCommFailure;
  
  public int getPortState(int ifIndex) throws SNMPCommFailure;

  public void updateEFMPortState() throws SNMPCommFailure;
  
  /**
   * Method used to read peer's name
   * @return null if there is more than one peer, peer name if there is one.
   * @throws SNMPCommFailure
   */
  public String getPeerSysName() throws SNMPCommFailure;

}
