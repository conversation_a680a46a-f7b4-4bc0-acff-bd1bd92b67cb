/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: msteiner
 */

package com.adva.nlms.mediation.ne_comm;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Class that is used to designate the difference between 
 * the sets of objects according to their IDs.
 * If there is a set A with some objects which have unique IDs , 
 * and also have a set B with some objects which have unique IDs,  
 * then can be figure out these objects from set A which are not in set B or 
 * these objects from set B which are not in set A, 
 * depending on specific common way to determine their IDs
 * <AUTHOR>
 *
 * @param (A)
 * @param (B)
 */
abstract public class MergeMap  <A, B> {
  
    private Set<A> setA;
    private Set<B> setB;
    private Set<String> errors = null;
    private Logger log = null;
    private boolean throwException = false;
    
  public MergeMap(Set<A> setA,Set<B> setB) {
    if (setA == null) throw new NullPointerException("NPE: setA is null.");
    if (setB == null) throw new NullPointerException("NPE: setB is null.");
    this.setA = setA;
    this.setB = setB;
    this.log  = LogManager.getLogger(getClass().getPackage().getName());
  }
  
  /**
   * Checks whether MergeMapException throwing is active. 
   * @return
   */
  public boolean isThrowException() {
    return throwException;
  }

  /**
   * Sets active for throwing MergeMapException. 
   * @return
   */
  public void setThrowException(boolean throwException) {
    this.throwException = throwException;
  }


  /**
   * Sets specific logger. Default is from current package.
   * @param logger
   */
  public void setLogger(Logger logger) {
    this.log = logger;
  }
  
  public void setFirstSet(Set<A> setA) {
    
    if (setA == null) throw new NullPointerException("NPE: setA is null.");
    this.setA = setA;
  }
  
  public void setSecondSet(Set<B> setB) {
    if (setB == null) throw new NullPointerException("NPE: setB is null.");    
    this.setB = setB;
  }  
  
  /**
   * Gets objects from set A which are not in set B.
   * 
   * <blockquote><pre>
   * -= set A =-  -= set B =-   Result
   * [1 ALFA   ]  [         ]  [1 ALFA    ]
   * [2 BETA   ]  [2 BETA   ]  [          ]
   * [3 GAMMA  ]  [3 GAMMA  ]  [          ]
   * [         ]  [4 DELTA  ]  [          ] 
   * </pre></blockquote>
   * 
   * @return List of (A)
   */
  public List<A> substractBfromA() {
    Map<String,A> newMap = new HashMap<String,A>();
    for (A obj : setA) {
      newMap.put(getAObjectKey(obj), obj);
    }
    for (B obj : setB) {
      newMap.remove(getBObjectKey(obj));
    }
    return getResult(newMap);
  }

  /**
   * Gets A objects that are in set A and in set B
   * 
   * <blockquote><pre>
   * -= set A =-  -= set B =-   Result
   * [1 ALFA   ]  [         ]  [          ]
   * [2 BETA   ]  [2 BETA   ]  [ (A)BETA  ]
   * [3 GAMMA  ]  [3 GAMMA  ]  [ (A)GAMMA ]
   * [         ]  [4 DELTA  ]  [          ]
   * </pre></blockquote>
   * @return List of (A)
   */
  public List<A> commonSubsetOfAType() {
    Map<String,A> newMap = new HashMap<String,A>();

    for (A obj : setA) {
        for (B objB : setB) {
          if (getBObjectKey(objB).equals(getAObjectKey(obj))) {
            newMap.put(getAObjectKey(obj), obj);
          }
        }
    }
    return getResult(newMap);
  }

  /**
   * Gets B objects that are in set A and in set B
   *
   * <blockquote><pre>
   * -= set A =-  -= set B =-   Result
   * [1 ALFA   ]  [         ]  [          ]
   * [2 BETA   ]  [2 BETA   ]  [ (B)BETA  ]
   * [3 GAMMA  ]  [3 GAMMA  ]  [ (B)GAMMA ]
   * [         ]  [4 DELTA  ]  [          ]
   * </pre></blockquote>
   *
   * @return List of (B)
   */
  public List<B> commonSubsetOfBType() {
    Map<String,B> newMap = new HashMap<String,B>();

    for (A obj : setA) {
        for (B objB : setB) {
          if (getBObjectKey(objB).equals(getAObjectKey(obj))) {
            newMap.put(getBObjectKey(objB), objB);
          }
        }
    }
    return getResult(newMap);
  }

  /**
   * Gets objects from set B which are not in set A.
   * 
   * <blockquote><pre>
   * -= set A =-  -= set B =-   Result
   * [1 ALFA   ]  [         ]  [          ] 
   * [2 BETA   ]  [2 BETA   ]  [          ]
   * [3 GAMMA  ]  [3 GAMMA  ]  [          ]
   * [         ]  [4 DELTA  ]  [4 DELTA   ] 
   * </pre></blockquote>
   * 
   * @return List of (B)
   */  
  public List<B> substractAfromB() {
    Map<String,B> newMap = new HashMap<String,B>();
    for (B obj : setB) {
      newMap.put(getBObjectKey(obj), obj);
    }
    for (A obj : setA) {
      newMap.remove(getAObjectKey(obj));
    }
    return getResult(newMap);
  }  

  private <T> List<T> getResult(Map<String,T> newMap) {
    
    if (errors != null) {
      StringBuilder sb = new StringBuilder();
      for (String error : errors) {
        sb.append("\n").append(error);
      }
      log.error(sb);
      if (throwException) throw new MergeMapException(sb.toString());
    }
    
    List<T> list = new ArrayList<T>();
    Iterator<Map.Entry<String,T>> it = newMap.entrySet().iterator();
    while (it.hasNext()) {
      list.add((T)it.next().getValue());
    }
    return list;
  }
  
  protected void addException(String message) {
    if (errors == null) {
      errors = new HashSet<String>();
    }
    errors.add(message);
  }
  
  /**
   * The body of the method specifies the way of determining a unique identifier for an object <A>.
   * Identifier is stored as a String
   * @param a
   * @return
   */
  abstract public String getAObjectKey(A a);
  
  /**
   * The body of the method specifies the way of determining a unique identifier for an object <B>.
   * Identifier is stored as a String
   * @param b
   * @return
   */  
  abstract public String getBObjectKey(B b);
}
