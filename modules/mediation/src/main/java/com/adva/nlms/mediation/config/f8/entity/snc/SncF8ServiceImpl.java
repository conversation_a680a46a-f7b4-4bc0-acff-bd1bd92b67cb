/*
 * Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 * Owner: michalmi
 */

package com.adva.nlms.mediation.config.f8.entity.snc;

import com.adva.nlms.common.config.EntityIndex;
import com.adva.nlms.common.util.StringUtils;
import com.adva.nlms.mediation.common.AuthorizationException;
import com.adva.nlms.mediation.common.UntrustedCertificateException;
import com.adva.nlms.mediation.config.ec.entity.facility.ctp.ConnectionTerminationPointEcDAO;
import com.adva.nlms.mediation.config.f8.entity.EntityF8MO;
import com.adva.nlms.mediation.config.f8.entity.crossconnect.CrossConnectF8DBImpl;
import com.adva.nlms.mediation.ec.model.aos.svct.SncTypeIdentity;
import com.adva.nlms.mediation.ec.neComm.provision.common.EcResponseErrorMessage;
import com.adva.nlms.mediation.ec.neComm.provision.dto.DnmBody;
import com.adva.nlms.mediation.ec.neComm.provision.dto.missingAttr.AttributeDTO;
import com.adva.nlms.mediation.ec.neComm.provision.dto.missingAttr.AvailableEntityResponseDto;
import com.adva.nlms.mediation.ne.comm.rest.RestClientResolver;
import com.adva.nlms.mediation.ne.comm.rest.RestConfigurationException;
import com.adva.nlms.mediation.ec.neComm.rest.json.EcEntityWrapper;
import com.adva.nlms.mediation.ec.support.EcEntityIndex;
import com.adva.nlms.mediation.ne.comm.rest.RestClientApi;
import com.adva.nlms.mediation.topology.NetworkElementID;
import com.adva.nlms.resource.provision.f8.api.in.ObjectDoesNotExistException;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;
import jakarta.ws.rs.ProcessingException;
import jakarta.ws.rs.client.Entity;
import jakarta.ws.rs.core.Response;
import org.eclipse.jetty.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class SncF8ServiceImpl implements SncF8Service, SNCProvisionService {

  private static final Logger log = LoggerFactory.getLogger(SncF8ServiceImpl.class);

  private final SncF8Dao sncF8Dao;
  private final ConnectionTerminationPointEcDAO ctpEcDao;
  private final RestClientResolver restClientResolver;

  private static final String MEDIA_TYPE = "application/json; ext=nn";
  private static final String MEDIA_TYPE_PATCH = "application/json-patch+json; ext=nn";
  private static final String METHOD_PARAM = "mthd";
  private static final String METHOD_POST = "POST";
  private static final String AVAILABLE_ENTITY_BODY = "exi={\"iirel\":[\"dnm\"]}";
  private static final String ENTNAME_NODE = "entname";
  private static final String AENDLIST_NODE = "aendlist";
  private static final String ZENDLIST_NODE = "zendlist";
  private static final String SNCTYPE_NODE = "snctype";

  SncF8ServiceImpl(SncF8Dao sncF8Dao, ConnectionTerminationPointEcDAO ctpEcDao, RestClientResolver restClientResolver) {
    this.sncF8Dao = sncF8Dao;
    this.ctpEcDao = ctpEcDao;
    this.restClientResolver = restClientResolver;
  }

  @Override
  public SncF8MO getSnc(NetworkElementID neId, EntityIndex entityIndex) {
    Optional<CrossConnectF8DBImpl> sncOptional = sncF8Dao.findNeEntityByIndex(neId.getValue(), entityIndex);
    if (sncOptional.isPresent()) {
      CrossConnectF8DBImpl snc = sncOptional.get();
      List<EntityF8MO> aend = mapEndpointsToDTO(snc.getAendList(), neId);
      List<EntityF8MO> zend = mapEndpointsToDTO(snc.getZendList(), neId);
      return SncMapper.mapToDTO(snc, aend, zend);
    }
    return null;
  }

  @Override
  public Integer provisionSNC(NetworkElementID neID, String newSncURI, Set<String> aEndpointURIs, Set<String> zEndpointURIs, SncTypeIdentity sncType) throws ProvisionSNCException {
    Integer entName = findAvailableEntName(neID, newSncURI);
    provisionSncWithEntName(neID, newSncURI, aEndpointURIs, zEndpointURIs, entName, sncType);
    return entName;
  }

  private Integer findAvailableEntName(NetworkElementID neID, String newSncURI) {
    Integer entName = null;
    try{
      entName = retrieveAvailableSncEntName(neID, newSncURI);
    } catch (ProvisionSNCException e){
      log.error("Error while getting attr entName, {}", e.getMessage());
    }
    return entName;
  }

  private int retrieveAvailableSncEntName(NetworkElementID neID, String newSncURI) throws ProvisionSNCException {
    try {
      RestClientApi client = restClientResolver.getNeRestClient(neID);
      Map<String, String> queryParams = new HashMap<>();
      queryParams.put(METHOD_PARAM, METHOD_POST);
      DnmBody body = new DnmBody(AVAILABLE_ENTITY_BODY);
      try (Response response = client.options(newSncURI, EcEntityWrapper.wrapEntity(body), queryParams)) {
        checkIfResponseIsSuccess(response, newSncURI);
        return extractAvailableEntName(response);
      }
    } catch (UntrustedCertificateException | AuthorizationException | RestConfigurationException e) {
      throw new ProvisionSNCException("Could not send Snc request to device.", e);
    }
  }

  private void checkIfResponseIsSuccess(Response response, String newSncURI) throws ProvisionSNCException {
    if (!HttpStatus.isSuccess(response.getStatus())) {
      EcResponseErrorMessage errorMessage = new EcResponseErrorMessage(response, newSncURI);
      throw new ProvisionSNCException(errorMessage.getResponseMessage());
    }
  }

  private int extractAvailableEntName(Response response) {
    AvailableEntityResponseDto dto = response.readEntity(AvailableEntityResponseDto.class);
    Optional<AttributeDTO> entNameAttribute = dto.getAttrs().stream().filter(attributeDTO -> attributeDTO.getNn().equals(ENTNAME_NODE)).findFirst();
    if (entNameAttribute.isPresent() && entNameAttribute.get().getCtype().getVal().get(0) != null) {
      return Integer.parseInt(entNameAttribute.get().getCtype().getVal().get(0));
    }
    throw new ProcessingException("Could not obtain next available entName for SNC.");
  }

  private void provisionSncWithEntName(NetworkElementID neID, String newSncURI, Set<String> aEndpointURIs, Set<String> zEndpointURIs, Integer entName, SncTypeIdentity sncType) throws ProvisionSNCException {
    try {
      RestClientApi client = restClientResolver.getNeRestClient(neID);
      try (Response response = client.post(newSncURI, Entity.entity(buildProvisionSncBody(entName, aEndpointURIs, zEndpointURIs, sncType), MEDIA_TYPE), new HashMap<>())) {
        checkStatusCodeForProvision(response, newSncURI, neID);
      }
    } catch (UntrustedCertificateException | AuthorizationException | RestConfigurationException e) {
      log.error("Error while connecting with NE id: {}, {} ", neID, e.getMessage());
      throw new ProvisionSNCException(StringUtils.format("Could not send provision Snc request to device. Error while connecting with NE id: {}, {} ", neID, e.getMessage()));
    }
  }

  private String buildProvisionSncBody(Integer entName, Set<String> aEndpointURIs, Set<String> zEndpointURIs, SncTypeIdentity sncType) throws ProvisionSNCException {
    ObjectMapper objectMapper = new ObjectMapper();
    ArrayNode aEndArray = createEndpointUrisArrayNode(aEndpointURIs);
    ArrayNode zEndArray = createEndpointUrisArrayNode(zEndpointURIs);
    ObjectNode node = buildObjectNode(objectMapper, entName, aEndArray, zEndArray, sncType);

    return getFinalBodyJsonAsString(objectMapper, node);
  }

  private ArrayNode createEndpointUrisArrayNode(Set<String> xEndpointURIs) {
    ArrayNode xEndArray = JsonNodeFactory.instance.arrayNode();
    xEndpointURIs.forEach(xEndArray::add);

    return xEndArray;
  }

  private ObjectNode buildObjectNode(ObjectMapper objectMapper, Integer entName, ArrayNode aendArray, ArrayNode zendArray, SncTypeIdentity sncTypeEnum) {
    ObjectNode node = objectMapper.createObjectNode();
    node.put(ENTNAME_NODE, String.valueOf(entName));
    node.set(AENDLIST_NODE, aendArray);
    node.set(ZENDLIST_NODE, zendArray);
    String sncType = (sncTypeEnum != null) ? sncTypeEnum.getNickName() : SncTypeIdentity.SNC_TYPE_SIMPLE.getNickName();
    node.put(SNCTYPE_NODE, sncType);

    return node;
  }

  private String getFinalBodyJsonAsString(ObjectMapper objectMapper, ObjectNode node) throws ProvisionSNCException {
    String finalJsonAsString;

    try {
      finalJsonAsString = objectMapper.writeValueAsString(node);
    } catch (JsonProcessingException e) {
      log.error("Error while processing JSON, {}", e.getMessage());
      throw new ProvisionSNCException(StringUtils.format("Error while building JSON body, {}", e.getMessage()));
    }

    return finalJsonAsString;
  }

  private void checkStatusCodeForProvision(Response response, String newSncURI, NetworkElementID neID) throws ProvisionSNCException {
    int statusCode = response.getStatusInfo().getStatusCode();
    if (!HttpStatus.isSuccess(statusCode)) {
      String message = new EcResponseErrorMessage(response, newSncURI).getResponseMessage();
      throw new ProvisionSNCException(String.format("Failed to provision %s on neId %s, %s",
              newSncURI, neID.getValue(), HttpStatus.getCode(statusCode) + (message == null ? "" : ": " + message)));
    }
  }

  @Override
  public void deprovisionSNC(NetworkElementID neID, String sncURI) throws ProvisionSNCException, ObjectDoesNotExistException {
    Response response;

    try {
      RestClientApi client = restClientResolver.getNeRestClient(neID);
      response = client.delete(sncURI);

      checkStatusCodeForDeprovision(response, sncURI, neID);
    } catch (UntrustedCertificateException | AuthorizationException | RestConfigurationException e) {
      log.error("Could not send deprovision snc request to device. Error while connecting with NE id: {}, {} ", neID, e.getMessage());
    }
  }

  @Override
  public void modifySNC(NetworkElementID neID, String sncURI, LinkedHashSet<String> aEndpointURIs, LinkedHashSet<String> zEndpointURIs, String sncType) throws ProvisionSNCException {
    Response response;
    try {
      String modifySncBody = ModifySncBodyMapper.buildModifySncBody(
        aEndpointURIs,
        zEndpointURIs,
        SncTypeIdentity.fromNickName(sncType));

      RestClientApi client = restClientResolver.getNeRestClient(neID);
      response = client.patch(sncURI, Entity.entity(modifySncBody, MEDIA_TYPE_PATCH));

      int statusCode = response.getStatusInfo().getStatusCode();
      if (!HttpStatus.isSuccess(statusCode)) {
        String message = new EcResponseErrorMessage(response, sncURI).getResponseMessage();
        throw new ProvisionSNCException(String.format("Failed to modify %s on neId %s, %s",
                sncURI, neID, HttpStatus.getCode(statusCode) + (message == null ? "" : ": " + message)));
      }
    } catch (UntrustedCertificateException | AuthorizationException | RestConfigurationException e) {
      log.error("Error while connecting with NE id: {}, {} ", neID, e.getMessage());
      throw new ProvisionSNCException(StringUtils.format("Could not send modify Snc request to device. Error while connecting with NE id: {}, {} ", neID, e.getMessage()));
    } catch (NumberFormatException numberFormatException) {
      log.error("Failed to modify {} on neId {}. Wrong ent name. {}", sncURI, neID, numberFormatException);
      throw new ProvisionSNCException(StringUtils.format("Failed to modify {} on neId {}. Wrong ent name. {}", sncURI, neID, numberFormatException));
    }
  }

  private void checkStatusCodeForDeprovision(Response response, String sncURI, NetworkElementID neID) throws ProvisionSNCException, ObjectDoesNotExistException {
    int statusCode = response.getStatusInfo().getStatusCode();
    if (!HttpStatus.isSuccess(statusCode)) {
      EcResponseErrorMessage responseError = new EcResponseErrorMessage(response, sncURI);
      String message = responseError.getResponseMessage();
      if (responseError.isEntityDoesNotExistError()) {
        throw new ObjectDoesNotExistException(message);
      }
      throw new ProvisionSNCException(String.format("Failed to delete %s on neId %s, %s",
              sncURI, neID, HttpStatus.getCode(statusCode) + (message == null ? "" : ": " + message)));
    }
  }

  private List<EntityF8MO> mapEndpointsToDTO(List<String> aendList, NetworkElementID neId) {
    return aendList.stream()
            .map(index -> EcEntityIndex.getEcEntityIndex(index))
            .map(index -> ctpEcDao.getCtpByEntityIndex(neId.getValue(), index))
            .map(ctp -> new EntityF8MO(ctp.getNeID(), ctp.getEntityIndex(), ctp.getAidString(), ctp.getContainedIn()))
            .collect(Collectors.toList());
  }
}
