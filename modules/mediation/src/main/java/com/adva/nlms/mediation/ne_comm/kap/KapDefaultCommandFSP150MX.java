/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: twitting
 */

package com.adva.nlms.mediation.ne_comm.kap;

import com.adva.nlms.common.annotation.UsedByReflection;
import com.adva.nlms.common.snmp.MIB;
import com.adva.nlms.mediation.ne_comm.AbstractSNMPCtrlImpl;
import com.adva.nlms.mediation.ne_comm.cmd.action.ActionRequest;
import com.adva.nlms.mediation.ne_comm.cmd.action.CommandAction;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPCommFailure;
import org.snmp4j.util.TableEvent;

@UsedByReflection
public class KapDefaultCommandFSP150MX extends KapDefaultCommand<KapCommandParams> {

  public KapDefaultCommandFSP150MX(AbstractSNMPCtrlImpl abstractSNMPCtrl) {
    super(abstractSNMPCtrl);
  }

  @Override
  @ActionMethod(Actions.START)
  public CommandAction start() {
    return Actions.EVENT_LOG_TABLE;
  }

  @Override
  protected void addNESysTimeRequest() {
    //this device does not support ne sys time
  }

  @ActionMethod(Actions.EVENT_LOG_TABLE)
  @ActionRequest(oid = MIB.FSP.NEEventLog.OID_TIMESTAMP)
  public CommandAction processEventLogTable(final TableEvent lastTableEvent) throws SNMPCommFailure {
    checkErrorStatus(lastTableEvent);

    if (params.getTableEventList().isEmpty()) {
      params.neEventsLogged = 0;
    } else {
      params.neEventsLogged = params.getTableEventList().get(params.getTableEventList().size() - 1).getIndex().get(0);
    }

    return requestForSysTimeData();
  }

  /**
   * This device does not support NE time.
   * @param neTimestamp NE time
   * @throws com.adva.nlms.idl.mediation.OperationFailedException request failure
   */
  @Override
  protected void processCheckNETime(final byte[] neTimestamp) {
    if (log.isInfoEnabled()) log.info("checkNETime(): skipping check for " + networkElementFetcher.getNE(getNetworkElementId()) + "!");
  }

}
