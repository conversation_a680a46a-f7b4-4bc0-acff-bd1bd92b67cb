/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: tmalesinski
 */
package com.adva.nlms.mediation.config.polling.status;

import com.adva.nlms.mediation.common.transactions.InvalidPollingException;
import com.adva.nlms.mediation.config.ConfigCtrl;
import com.adva.nlms.mediation.config.DataManager;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPCommFailure;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Set;

public class OnlyStatusFieldsPollingWorker extends PollingStatusWorkerImpl {

  private static Logger log = LogManager.getLogger(OnlyStatusFieldsPollingWorker.class.getName());

  public OnlyStatusFieldsPollingWorker(ConfigCtrl configCtrl, NetworkElement networkElement) {
    super(configCtrl, networkElement, null);
  }

  @Override
  public void doPollingStatus(Set<DataManager> dataManagerSet, boolean includeStatusFields) throws SNMPCommFailure, InvalidPollingException {
    doStatusFieldsPolling(dataManagerSet);
  }
}