/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: adaemmig
 */

package com.adva.nlms.mediation.evtProc;

import com.adva.nlms.common.AlarmTrapType;
import com.adva.nlms.common.AlarmTypeHandler;
import com.adva.nlms.common.AlarmTypeHandlerImpl;
import com.adva.nlms.common.EventDescription;
import com.adva.nlms.common.NEUtils;
import com.adva.nlms.common.NetworkEventTypeHandler;
import com.adva.nlms.common.NetworkEventTypeHandlerImpl;
import com.adva.nlms.common.TrapType;
import com.adva.nlms.common.VarbindType;
import com.adva.nlms.common.config.EntityIndex;
import com.adva.nlms.common.config.netypes.NeTypeIds;
import com.adva.nlms.common.event.Direction;
import com.adva.nlms.common.event.EventSeverity;
import com.adva.nlms.common.event.EventType;
import com.adva.nlms.common.event.types.CategoryType;
import com.adva.nlms.common.snmp.MIB;
import com.adva.nlms.common.snmp.MIBALM;
import com.adva.nlms.common.snmp.MIBFSP150CC;
import com.adva.nlms.common.snmp.MIBHN4000;
import com.adva.nlms.common.traps.FSPGenericTrap;
import com.adva.nlms.common.util.StringUtils;
import com.adva.nlms.mediation.config.NetworkElementHdlrLocal;
import com.adva.nlms.mediation.config.fsp_r7.EntityIndexHelperF7;
import com.adva.nlms.mediation.event.EventDTO;
import com.adva.nlms.mediation.event.EventLogger;
import com.adva.nlms.mediation.event.EventMultiVarDTO;
import com.adva.nlms.mediation.event.MOAdapter;
import com.adva.nlms.mediation.evtProc.api.TrapParamConverter;
import com.adva.nlms.mediation.evtProc.definition.TrapModel.ResultCode;
import com.adva.nlms.mediation.evtProc.definition.TrapModel.VBParam;
import com.adva.nlms.mediation.evtProc.definition.TrapModel.VarbindKey;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.snmp4j.smi.Integer32;
import org.snmp4j.smi.OID;
import org.snmp4j.smi.VariableBinding;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.NoSuchElementException;
import java.util.Set;
import java.util.stream.Collectors;

public abstract class TrapProcessorAbstract implements TrapProcessor, AlarmProcessor, NotifcationProcessor, TrapParamConverter {
  protected static final Logger log = LogManager.getLogger(TrapProcessorAbstract.class.getName());
  protected static final Logger logSbi = EventLogger.getLogger(EventLogger.Category.itf_sbi);
  private static String ZeroInOIDEnd = ".0";

  //@Autowired
  protected NetworkElementHdlrLocal neHdlr;
  //@Autowired
  private MOAdapter moAdapter;
  //@Autowired
  protected TrapParser trapParser;
  //@Autowired
  protected VarbindUtilTool varbindUtilTool;

  //==============================================================================
  //=== Initialization ===========================================================
  //==============================================================================

  public void setNeHdlr(NetworkElementHdlrLocal neHdlr) {
    this.neHdlr = neHdlr;
  }

  public void setMoAdapter(MOAdapter moAdapter) {
    this.moAdapter = moAdapter;
  }

  public void setTrapParser(TrapParser trapParser) {
    this.trapParser = trapParser;
  }

  public void setVarbindUtilTool(VarbindUtilTool varbindUtilTool) {
    this.varbindUtilTool = varbindUtilTool;
  }

  //==============================================================================
  //=== TrapProcessor interface ==================================================
  //==============================================================================

  @Override
  public boolean isAlarm(EventDTO event, List<VariableBinding> vbl) {
    //status/configuration change event
    if(isConfOrStatPollUpdate(event, vbl))
      return false;
    else
      return isNESpecificAlarm(event, vbl);
  }

  private boolean isConfOrStatPollUpdate(EventDTO event, List<VariableBinding> vbl)
  {
    return ((vbl == null && event.severity != null && event.severity == EventSeverity.UNKNOWN));
  }

  protected abstract boolean isNESpecificAlarm(EventDTO event, List<VariableBinding> vbl);

  /**
   * Set mandatory values from varbind. Used only in SNMP version 2c and 3.
   * Remove first two varbinds and assign correct trap ID and enterprise.
   * This code follow the SNMPv2c and SNMPv3 standard.
   * @param event  The event.
   * @param vbl    The list of varbinds.
   * @return boolean
   */
  @Override
  public boolean setEventEnterpriseAndTrapId(final EventDTO event, final List<VariableBinding> vbl)
  {
    if(vbl == null || event.enterprise != null){
      removeLastZeroFromEnterprise(event);
      return false;
    }
    OID oid = (OID) vbl.get(1).getVariable();
    event.setTrapID(oid.removeLast());
    event.enterprise = oid.toDottedString();
    removeLastZeroFromEnterprise(event);
    if(event.enterprise.startsWith(MIB.SNMPV2.OID_GENERIC_TRAPS))
    {
      event.setIsSpecificTrap(false);
      /**
       * SNMP4j maps generic traps in described way:
       * org.snmp4j.PDUv1
       * public static final int 	AUTHENTICATIONFAILURE 	4
       * public static final int 	COLDSTART 	0
       * public static final int 	ENTERPRISE_SPECIFIC 	6
       * public static final int 	LINKDOWN 	2
       * public static final int 	LINKUP 	3
       * public static final int 	WARMSTART 	1
       *
       * When generic trap comes from SNMPv2c or I belive SNMPv3 it have
       * values from SNMPv2-MIB. In shortcut it have values grates than one
       * and that's way trapId should be decrease
       */
      event.setTrapID(event.getTrapID()-1);
      return true;
    }
    //Remove sysUpTime and enterprise to be consistent with SNMPv1.
    vbl.remove(0);
    vbl.remove(0);
    return true;
  }

  private void removeLastZeroFromEnterprise(final EventDTO event){
    if (event.enterprise != null && event.enterprise.endsWith(ZeroInOIDEnd))
      event.enterprise = event.enterprise.substring(0, event.enterprise.length() - ZeroInOIDEnd.length());
  }

  //==============================================================================
  //=== AlarmProcessor interface =================================================
  //==============================================================================

  @Override
  public AlarmTrapType getAlarmTrapDescriptor(TrapParameters trapParameters) {
    EventDTO event = trapParameters.getEvent();
    AlarmTypeHandler alarmTypeHandler = AlarmTypeHandlerImpl.getInstance();
    return alarmTypeHandler.getTrapDescriptor(event.sourceNEType, event.getTrapID());
  }

  @Override
  public void setAlarmCategory(final EventDTO event, final AlarmTrapType alarmTrapDescriptor)
  {
    if(event.category == null)
      event.category  = alarmTrapDescriptor.getCategoryType();
  }

  @Override
  public void setIsPotentiallyServiceAffecting(final EventDTO event, final AlarmTrapType alarmTrapDescriptor)
  {
    if(!event.impairment)
      event.impairment = alarmTrapDescriptor.isServiceAffecting();
  }

  @Override
  public final void setEventInstance(final EventDTO event, final AlarmTrapType alarmTrapDescriptor)
  {
    // Special handling for interfaces which are not modeled on SNMP level  (e.g. TIM)
    if (alarmTrapDescriptor.getInstance() != -1) {
      event.instance = alarmTrapDescriptor.getInstance();
    }
  }

  @Override
  public void setAlarmParametersFromVbl(TrapParameters trapParameters)
  {
    handleAlarmVbl(trapParameters);
    if(trapParameters.isValueAlreadySet()) return;
    setAlarmValues(trapParameters);
  }

  void handleAlarmVbl(final TrapParameters trapParameters) {
    setEntityAid(trapParameters);
    setEventObjectIndex(trapParameters);
    setMtpDisplayName(trapParameters);
  }

  protected void setEntityAid(final TrapParameters trapParameters){
    //set AID String in case we do not have this entry in database.
    //see:  com.adva.nlms.mediation.evtProc.Event.aidString
    final List<VariableBinding> vbl = trapParameters.getVbl();
    int identityTranslationIndex = varbindUtilTool.getVBIndexByOID(vbl, MIB.FSP.NEEventLog.OID_IDENTITY_TRANSLATION);
    if (identityTranslationIndex != VarbindUtilTool.UNKNOWN_POSITION ) {
      EventDTO event = trapParameters.getEvent();
      event.setAidString(varbindUtilTool.getStringValueFromVarbind(vbl, identityTranslationIndex));
    }
  }

  protected void setMtpDisplayName(final TrapParameters trapParameters){
    final List<VariableBinding> vbl = trapParameters.getVbl();
    int identityTranslationIndex = varbindUtilTool.getVBIndexByOID(vbl, MIBALM.MonitorPointTable.OID_NAME);

    if (identityTranslationIndex != VarbindUtilTool.UNKNOWN_POSITION ) {
      EventDTO event = trapParameters.getEvent();
      event.setMtpDisplayName(varbindUtilTool.getStringValueFromVarbind(vbl, identityTranslationIndex));
    }
  }

  protected void setEventObjectIndex(final TrapParameters trapParameters){
    final EventDTO event = trapParameters.getEvent();
    final List<VariableBinding> vbl = trapParameters.getVbl();
    event.objectIndex = !trapParameters.getEvent().is112() ? new EntityIndex(varbindUtilTool.getIndexFromVarbindOid(vbl, 0, 1, 2))
        : EntityIndexHelperF7.getEntityIndex(vbl.get(0).getOid());
  }

  protected void setAlarmValues(final TrapParameters alarmParameter) {
    setNeAlarmSeverityVbIndex(alarmParameter);
    final EventDTO event = alarmParameter.getEvent();
    event.severity = getTrapSeverityFromVarbind(alarmParameter);
    setTimestampAndLogIndex(alarmParameter);
  }

  //com.adva.nlms.common.snmp.MIB.FSP1500.IfCurrAlarm.OID_SEVERITY for 1500     todo add whole possible set of OIDs
  protected void setNeAlarmSeverityVbIndex(final TrapParameters trapParameters){
    trapParameters.setNeAlarmSeverityVbIndex(0);
  }

  protected EventSeverity getTrapSeverityFromVarbind(TrapParameters alarmParameter) {
    int neAlarmSeverityVbIndex = alarmParameter.getNeAlarmSeverityVbIndex();
    final EventDTO event = alarmParameter.getEvent();
    final List<VariableBinding> vbl = alarmParameter.getVbl();
    return getTrapSeverityFromVarbind(vbl, neAlarmSeverityVbIndex, event.sourceNEType);
  }

  @Override
  public void setAlarmParametersFromProperties(final TrapParameters trapParameters) {
    setAlarmTypeAndText(trapParameters);
    setAlarmDirection(trapParameters);
  }

  protected void setAlarmTypeAndText(final TrapParameters trapParameters) {
    setAlarmText(trapParameters);
    // Find out if raising or clearing
    setAlarmType(trapParameters);
  }

  protected void setAlarmText(final TrapParameters trapParameters){
    EventDTO event = trapParameters.getEvent();
    final AlarmTrapType alarmTrapDescriptor = trapParameters.getAlarmTrapDescriptor();
    if(event.getText() == null) // if text was assigned later do not overwrite it
      event.setText(new StringBuilder(alarmTrapDescriptor.getMessage(EventDescription.entityToBeDetermined)));
  }

  protected void setAlarmType(final TrapParameters trapParameters){
    EventDTO event = trapParameters.getEvent();
    final AlarmTrapType alarmTrapDescriptor = trapParameters.getAlarmTrapDescriptor();

    if (isRaisingAlarm(trapParameters)) {
      if (log.isDebugEnabled())
        log.debug("an alarm raising trap, value: " + event.newValue);
      event.type = EventType.RAISED;
      // check if it is a security event
      event.securityEvent = EvtProcHelper.isSecurityEvent(alarmTrapDescriptor.getSecurityEvent());
    } else {
      if (log.isDebugEnabled())
        log.debug("an alarm clearing trap, value: " + event.newValue);
      // The raising trap ID is sent to the event module with flag CLEARED
      event.type = EventType.CLEARED;
      event.setTrapID(alarmTrapDescriptor.getRaisingTrapNumber());
    }

    //Transient condition should be displayed as normal notification
    if(trapParameters.getEventType() == EventType.TRANSIENT) {
      trapParameters.getEvent().setHasAlarmDefinition(true);
      trapParameters.getEvent().type = EventType.TRANSIENT;
    }
  }

  protected boolean isRaisingAlarm(final TrapParameters trapParameters){
    EventDTO event = trapParameters.getEvent();
    final AlarmTrapType alarmTrapDescriptor = trapParameters.getAlarmTrapDescriptor();
    return (event.severity != EventSeverity.OK) &&
        alarmTrapDescriptor.getRaisingTrapNumber() == event.getTrapID();
  }

  protected void setAlarmDirection(final TrapParameters trapParameters) {
    EventDTO event = trapParameters.getEvent();
    final AlarmTrapType alarmTrapDescriptor = trapParameters.getAlarmTrapDescriptor();
    // do not overwrite if already set in setAlarmParametersFromVbl
    if (event.direction == Direction.NONE.getMIBValue()) {
      event.direction = alarmTrapDescriptor.getDirection().getMIBValue();
    }
  }

  /**
   * Get the trap severity from a varbind list if available.
   * @param vbl The varbind list of the trap.
   * @param vbIndex The index of the varbind which is expected to contain the severity.
   */
  protected EventSeverity getTrapSeverityFromVarbind (final List<VariableBinding> vbl, final int vbIndex, final int neType)
  {
    if (vbIndex < 0)
    {
      return EventSeverity.UNKNOWN;
    }

    if (vbl.size() <= vbIndex)
    {
      return EventSeverity.UNKNOWN;
    }
    int snmpSeverity = ((Integer32) vbl.get(vbIndex).getVariable()).getValue();
    if (neType == NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150CC){
      //CORRECT SEVERITY NUMBER
      switch(snmpSeverity){
        case MIBFSP150CC.CovaroAlmTable.SEVERITY_CLEARED:
          snmpSeverity = MIB.FSP.AlarmSeverity.SEVERITY_CLEARED;
          break;
        case MIBFSP150CC.CovaroAlmTable.SEVERITY_NOT_ALARMED://only events come.
        case MIBFSP150CC.CovaroAlmTable.SEVERITY_NOT_REPORTED://alarms and events not come at all.
          return EventSeverity.NOT_REPORTED;
      }
    }
    else if (neType == NeTypeIds.NETWORK_ELEMENT_TYPE_HN4000 || neType == NeTypeIds.NETWORK_ELEMENT_TYPE_HN400_STANDALONE)
    {
      switch(snmpSeverity){
        case MIBHN4000.Common.System.Alarm.CurrAlarm.SEVERITY_CRITICAL:
          snmpSeverity = MIB.FSP.AlarmSeverity.SEVERITY_CRITICAL;
          break;
        case MIBHN4000.Common.System.Alarm.CurrAlarm.SEVERITY_MAJOR:
          snmpSeverity = MIB.FSP.AlarmSeverity.SEVERITY_MAJOR;
          break;
        case MIBHN4000.Common.System.Alarm.CurrAlarm.SEVERITY_MINOR:
          snmpSeverity = MIB.FSP.AlarmSeverity.SEVERITY_MINOR;
          break;
        case MIBHN4000.Common.System.Alarm.CurrAlarm.SEVERITY_CLEARED:
          snmpSeverity = MIB.FSP.AlarmSeverity.SEVERITY_CLEARED;
          break;
        case MIBHN4000.Common.System.Alarm.CurrAlarm.SEVERITY_NOT_REPORTED:
        case MIBHN4000.Common.System.Alarm.CurrAlarm.SEVERITY_NOT_ALARMED:
          return EventSeverity.NOT_REPORTED;
      }
    }
    else if (snmpSeverity == MIB.FSP.AlarmSeverity.SEVERITY_NOT_REPORTED &&
        (NEUtils.isLocalF3Device(neType) ||
            neType == NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_R7
            || neType == NeTypeIds.NETWORK_ELEMENT_TYPE_OTS1000))
      return EventSeverity.NOT_REPORTED;

    NetworkEventTypeHandlerImpl networkEventTypeHandler = ((NetworkEventTypeHandlerImpl) NetworkEventTypeHandlerImpl.getInstance());
    return networkEventTypeHandler.getEventSeverityType(neType, snmpSeverity);
  }

  //==============================================================================
  //=== NotificationProcessor interface ==========================================
  //==============================================================================

  @Override
  public void setNotificationParametersFromProperties(TrapParameters trapParameters)
  {
    EventDTO event = trapParameters.getEvent();
    TrapType trapDescriptor = getTrapDescriptor(trapParameters);
    trapParameters.setTrapDescriptor(trapDescriptor);

    if (isCustomTrap(trapDescriptor)){
      event.addText("snmpTrapOID = " + event.enterprise + ".0." + event.getOrigTrapId());
      event.enterprise = trapDescriptor.getEnterprise();
      event.setTrapID(trapDescriptor.getTrapNumber());
    }
    event.type = EventType.TRANSIENT;
    event.securityEvent = EvtProcHelper.isSecurityEvent(trapDescriptor.getSecurityEvent());
    event.alarmClass = trapDescriptor.getAlarmClass();

    event.shortName = trapDescriptor.getEventShortName();
    if(event.category == null)
      event.category = trapDescriptor.getCategoryType();
    if(event.dbChgCategory == null)
      event.dbChgCategory = trapDescriptor.getDbChgCategoryType();
  }

  @Override
  public void handleTransientTrap(final TrapParameters trapParameters) {
    //default handling
    final EventDTO event = trapParameters.getEvent();
    int objIdxVbNbr = trapParameters.getTrapDescriptor().getObjectIndexVbNumber();
    List<VariableBinding> vbl = trapParameters.getVbl();
    event.objectIndex = !event.is112() ? new EntityIndex(varbindUtilTool.getIndexFromVarbindOid(vbl, objIdxVbNbr, 1, 1))
        : EntityIndexHelperF7.getEntityIndex(vbl.get(objIdxVbNbr).getOid());
    setDummyOffset(trapParameters);
  }

  @Override
  public void handleNoNewValueInNotification(final TrapParameters trapParameters)
  {
    final TrapType trapDescriptor = trapParameters.getTrapDescriptor();
    final EventDTO event = trapParameters.getEvent();
    event.setText(new StringBuilder(trapDescriptor.getMessage(EventDescription.entityToBeDetermined)));
    event.impairment = trapDescriptor.isServiceAffecting();
  }

  @Override
  public void setTimestampAndLogIndex(TrapParameters trapParameters) {
    final EventDTO event = trapParameters.getEvent();
    final List<VariableBinding> vbl = trapParameters.getVbl();
    if (vbl != null && event.neLogIndex == 0) {
      setNeLogIndexVbIndex(trapParameters);
      setNeTimeStampVbIndex(trapParameters);
      setEventLogIndex(trapParameters);
      setEventTimeStamp(trapParameters);
    }
  }

  @Override
  public void setEventTimeStamp(TrapParameters trapParameters) {
    trapParameters.getEvent().neTimeStamp = varbindUtilTool.getTimestampFromVarbind (trapParameters.getVbl(), trapParameters.getNeTimeStampVbIndex());
  }

  @Override
  public void setSpecificParametersForNotification(TrapParameters trapParameters) {
    // Empty. Override if necessary.
  }

  @Override
  public VarbindUtilTool getVarbindUtilTool() {
    return varbindUtilTool;
  }

  @Override
  public GenericEventDecorator getGenericEventDecorators(final TrapParameters trapParameters) {
    switch(trapParameters.getEvent().getTrapID())
    {
      case FSPGenericTrap.AUTH_FAIL:
        return new AuthFailureEventDecorator();
      default:
        return getDefaultEventDecorator();
    }
  }

  protected void setDummyOffset(final TrapParameters trapParameters){
    //likely valuevbNumber tags in networkEventMap should be refactored to use 0 base index enumeration.
    final TrapType trapDescriptor = trapParameters.getTrapDescriptor();
    final int valueVbNumber = trapDescriptor.getValueVbNumber();
    String trapName = trapDescriptor.getTrapName();
    if (valueVbNumber > 0 && !getTrapNameNotUsingOffset().contains(trapName))
      trapParameters.setVbOffset(1);
  }

  protected List<String> getTrapNameNotUsingOffset(){
    return Collections.emptyList();
  }

  protected void setNeLogIndexVbIndex(final TrapParameters trapParameters){
    int timestampIndex = getTimestampVbNumber(trapParameters);
    trapParameters.setLogIndexVbIndex(timestampIndex - trapParameters.getVbOffset());
  }

  private int getTimestampVbNumber(TrapParameters trapParameters) {
    VarbindType vbType = trapParameters.getVbType();
    return vbType != null ? vbType.getTimeStampVbNumber(trapParameters.getTrapDescriptor()) : trapParameters.getTrapDescriptor().getTimeStampVbNumber();
  }

  protected void setNeTimeStampVbIndex(final TrapParameters trapParameters){
    int timestampIndex = getTimestampVbNumber(trapParameters);
    trapParameters.setNeTimeStampVbIndex(timestampIndex - trapParameters.getVbOffset());
  }

  private TrapType getTrapDescriptor(TrapParameters trapParameters) {
    EventDTO event = trapParameters.getEvent();
    NetworkEventTypeHandler netEventTypeHandler = NetworkEventTypeHandlerImpl.getInstance();
    try{
       return netEventTypeHandler.getTrapDescriptor(event.sourceNEType, event.enterprise, event.getTrapID());
    } catch (NoSuchElementException ee) {
      if (event.enterprise != null){
        EventLogger.logWarnOnce(logSbi, EventLogger.createCacheKey(event.sourceNEType, event.enterprise, event.getTrapID()),
                              "getTrapDescriptor() Trap not defined", event, trapParameters.getVbl());
      }else{
        EventLogger.logWarnOnce(logSbi, EventLogger.createCacheKey(event.sourceNEType, event.getTrapID()),
                              "getTrapDescriptor() Trap not defined", event, trapParameters.getVbl());
      }
      return netEventTypeHandler.getTrapDescriptor(NeTypeIds.NETWORK_ELEMENT_TYPE_ANY, MIB.FSP.CUSTOM_TRAP_ENTERPRISE, MIB.FSP.SNMP_CUSTOM_TRAP_ID);
    }
  }

  private boolean isCustomTrap(TrapType trapType){
    return trapType.getCategoryType().equals(CategoryType.CUSTOM_NE_EVENT) &&
           trapType.getTrapNumber() == MIB.FSP.SNMP_CUSTOM_TRAP_ID;
  }

  protected void setEventLogIndex(TrapParameters trapParameters) {
    trapParameters.getEvent().neLogIndex =
            varbindUtilTool.getNeLogIndexFromVarbind(trapParameters.getVbl(), trapParameters.getLogIndexVbIndex());
  }

  protected GenericEventDecorator getDefaultEventDecorator() {
    return new DefaultEventDecorator();
  }

  protected void createMultiVarsFromVbl(EventDTO event, final Collection<VariableBinding> vbl,
                                        final Collection<String> firstNonMulitvarOids, int offset){
    int i = 0;
    int indexOfLastNonMultivarbind = getIndexOfLastNonMultivarbind(vbl, firstNonMulitvarOids);
    if(1 + offset < indexOfLastNonMultivarbind){
      event.multiVarList = new EventMultiVarDTO[indexOfLastNonMultivarbind - 1 - offset];
      while((i + 1 + offset) < indexOfLastNonMultivarbind){
        event.multiVarList[i++] = new EventMultiVarDTO();
      }
    }
  }

  protected void createMultiVarsFromVbl(EventDTO event, final Collection<VariableBinding> vbl,
                                        final Collection<String> firstNonMulitvarOids){
    createMultiVarsFromVbl(event, vbl, firstNonMulitvarOids, 0);
  }

  protected int getIndexOfLastNonMultivarbind(final Collection<VariableBinding> vbl, final Collection<String> firstNonMultivarOidsString){
    //both oid can be treated as the end of multivarbind information
    List<OID> firstNonMultivarOids = firstNonMultivarOidsString.stream()
      .map(OID::new)
      .collect(Collectors.toList());

    int index = -1;
    for(VariableBinding var : vbl){
      index ++ ;
      OID varOid = var.getOid();
      for(OID firsNonMultivarOid : firstNonMultivarOids){
        if(varOid.leftMostCompare(firsNonMultivarOid.size(), firsNonMultivarOid) == 0)
          return index;
      }
    }
    return index;
  }

  //==============================================================================
  //=== Trap Parser ==============================================================
  //==============================================================================

  /**
   * process the parsed varbind parameters
   * 1) check each parameter value
   * 2) set all correct parameters
   * 3) log errors for wrong parameters
   * Handle parsing errors:
   * - severity missing: error, type=NET
   * - Timestamp missing: error, netimestamp = -1
   * - logindex missing: error, nelogindex = 0
   * - object-index missing & restoration of object-index from object-name failed: type=NET, objectIndex=ZERO, aid="Unknown entity"
   */
  protected final void processVbParams(TrapParameters trapParameters, TrapParser.ParserResults parserResults) {
    EventDTO event = trapParameters.getEvent();
    List<VariableBinding> vbl = trapParameters.getVbl();
    Set<VarbindKey> keySet = parserResults.resultMap.keySet();

    for (VarbindKey vbKey : keySet) {
      if (parserResults.resultMap.get(vbKey) == ResultCode.VB_OK)
        checkVbParam(trapParameters, vbKey, parserResults);
      else
        checkVbParamFailure(trapParameters, vbKey, parserResults);

      // log errors
      ResultCode result = parserResults.resultMap.get(vbKey);
      if (result.isMoreSevereThan(ResultCode.OPTIONAL_VB_MISSING))
        EventLogger.logWarnOnce(logSbi, EventLogger.createCacheKey(event.sourceNE_ID, event.enterprise, event.getTrapID(), vbKey, result),
                                "Trap parser " + result, event, trapParameters.getVbl());

    }

    for (VarbindKey vbKey : keySet) {
      if (parserResults.resultMap.get(vbKey) == ResultCode.VB_OK)
        setVbParam(trapParameters, vbKey, parserResults);
      else
        handleVbParamFailure(trapParameters, vbKey, parserResults);
    }


  }

  /*
   * Checks the Varbind parameter. The method can be overridden to do device specific checks
   */
  protected void checkVbParam(TrapParameters trapParameters, VarbindKey vbKey, TrapParser.ParserResults parserResults) {
  }

  /*
   * Checks the Varbind parameter in case the parser detects an error. The method can be used to degrade a error condition.
   */
  protected void checkVbParamFailure(TrapParameters trapParameters, VarbindKey vbKey, TrapParser.ParserResults parserResults) {
    EventDTO event = trapParameters.getEvent();

    switch (vbKey) {
      case LOG_INDEX:
        if (event.neLogIndex > 0) {  // is set for re-generated events (KAP)
          ResultCode resultCode = parserResults.resultMap.get(vbKey);
          resultCode = (resultCode == ResultCode.MANDATORY_VB_MISSING) ? ResultCode.OPTIONAL_VB_MISSING :
                       (resultCode == ResultCode.MANDATORY_VB_TYPE_MISMATCH) ? ResultCode.OPTIONAL_VB_TYPE_MISMATCH : resultCode;
          parserResults.resultMap.put(vbKey, resultCode);
        }
        break;
    }
  }

  /*
   * Handle varbind errors. The method updates the eventDTO depending on the varbind and error code.
   * The method can be overridden to do device specific modifications
   */
  protected void handleVbParamFailure(TrapParameters trapParameters, VarbindKey vbKey, TrapParser.ParserResults parserResults) {
    EventDTO event = trapParameters.getEvent();

    switch (vbKey) {
      case OBJECT_INDEX:
        trapParameters.setEventType(EventType.TRANSIENT);  // eventDTO.type is set later
        event.objectIndex = EntityIndex.ZERO;
        if (!parserResults.vbExists(VarbindKey.OBJECT_NAME))
          event.setAidString("Unknown entity");
        break;

      case ALARM_SEVERITY:
        trapParameters.setEventType(EventType.TRANSIENT);  // eventDTO.type is set later
        event.severity = EventSeverity.UNKNOWN;
        break;
    }
  }

  /*
   * The method updates the eventDTO by the varbind value/index.
   * The method can be overridden to do device specific modifications
   */
  protected void setVbParam(TrapParameters trapParameters, VarbindKey vbKey, TrapParser.ParserResults parserResults) {
    EventDTO event = trapParameters.getEvent();
    List<VariableBinding> vbl = trapParameters.getVbl();
    int vbIndex = parserResults.keyMap.get(vbKey);

    switch (vbKey) {
      case TIMESTAMP:
        if (event.neTimeStamp <= 0)
          event.neTimeStamp = varbindUtilTool.getTimestampFromVarbind(vbl, vbIndex);
        break;

      case LOG_INDEX:
        if (event.neLogIndex <= 0)
          event.neLogIndex = varbindUtilTool.getNeLogIndexFromVarbind(vbl, vbIndex);
        break;

      case OBJECT_NAME:
        if (StringUtils.isEmpty(event.getAidString()))
          event.setAidString(varbindUtilTool.getStringValueFromVarbind(vbl, vbIndex));
        break;

      case OBJECT_INDEX:
        VBParam vbParam = parserResults.vbParams.get(vbIndex);
        event.objectIndex = ArrayUtils.isNotEmpty(vbParam.index.getValue()) ? new EntityIndex(vbParam.index.getValue()) : EntityIndex.ZERO;
        break;

      case ALARM_SEVERITY:
        event.severity = getTrapSeverityFromVarbind(vbl, vbIndex, event.sourceNEType);
        break;

      case ALARM_IMPAIRMENT:
        event.impairment = varbindUtilTool.getIntegerValueFromVarbind(vbl, vbIndex) == MIBFSP150CC.CovaroAlmTable.SERVICE_AFFECTING_TRUE;
        break;

      case ALARM_LOCATION:
        event.location = varbindUtilTool.getIntegerValueFromVarbind(vbl, vbIndex);
        break;

      case ALARM_DIRECTION:
        event.direction = varbindUtilTool.getIntegerValueFromVarbind(vbl, vbIndex);
        break;
    }
  }

  //==============================================================================
  //=== common methods ===========================================================
  //==============================================================================

  protected int getMibVariant(int neID) {
    return moAdapter.getNeValue(MOAdapter.NeKey.MIBVARIANT_BY_ID, neID);
  }

  protected int handleAdditionalInfo(List<VariableBinding> vbl, int valueVbNumberLast){
    return valueVbNumberLast;
  }
}

