<?xml version="1.0" encoding="UTF-8"?>
<!--
  -  Copyright 2023 Adtran Networks SE. All rights reserved.
  -
  -  Owner: askar<PERSON><PERSON>
  -->
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:util="http://www.springframework.org/schema/util"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
       http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd
       http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util.xsd">

  <context:annotation-config/>

  <bean class="com.adva.nlms.mediation.polling.PollingConfiguration"/>
  <bean class="com.adva.nlms.mediation.polling.db.PollingManagerDAO"/>

  <bean class="com.adva.nlms.mediation.polling.core.PollingCore"/>
  <bean id="priorityQueue" class="com.adva.nlms.mediation.polling.queue.GlobalPollingStarterQueueImpl">
    <property name="serverState" ref="serverStateImpl"/>
  </bean>
  <bean id="performanceQueue" class="com.adva.nlms.mediation.polling.queue.PerformancePollingStarterQueueImpl">
    <property name="serverState" ref="serverStateImpl"/>
  </bean>
  <bean class="com.adva.nlms.mediation.polling.PollingManagerInfo"/>
  <bean id="pollingFrameworkMonitoring" class="com.adva.nlms.mediation.polling.monitoring.PollingFrameworkMonitoring" lazy-init="true"/>
  <bean id="pollingFrameworkTestService" class="com.adva.nlms.mediation.polling.PollingFrameworkTestService" lazy-init="true"/>

  <bean id="limiter" class="com.adva.nlms.mediation.polling.PollingNodesLimiterImpl"/>
  <bean class="com.adva.nlms.mediation.polling.PollingManagerFactory"/>

  <bean id="pollingManagersController" class="com.adva.nlms.mediation.polling.PollingManagersControllerImpl"/>
  <bean class="com.adva.nlms.mediation.polling.PollingMBeanInitializer" init-method="initialize"/>
  <bean id="defaultPollingWaitConfig" class="com.adva.nlms.mediation.polling.conf.DefaultPollingWaitConfig" />
  <bean class="com.adva.nlms.mediation.polling.PollingOperations" />
  <bean class="com.adva.nlms.mediation.polling.ServerActivitiesPageHdlrImpl"/>
  <bean class="com.adva.nlms.mediation.polling.monitoring.PollingHealthMonitor"/>
  <bean class="com.adva.nlms.mediation.polling.monitoring.KAPMonitor"/>
  <bean class="com.adva.nlms.mediation.polling.core.ThreadPools"/>
  <bean class="com.adva.nlms.mediation.polling.core.PollingSchedule"/>
  <bean class="com.adva.nlms.mediation.polling.core.QueueProcessor"/>
  <bean class="com.adva.nlms.mediation.polling.core.DanglingPollings"/>
  <bean class="com.adva.nlms.mediation.polling.queue.ForcedPollingsHelper"/>
</beans>
