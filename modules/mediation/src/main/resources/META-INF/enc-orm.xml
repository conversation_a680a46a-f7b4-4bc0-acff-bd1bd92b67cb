<?xml version="1.0" encoding="UTF-8"?>
<!--
  -  Copyright 2023 Adtran Networks SE. All rights reserved.
  -
  -  Owner: m<PERSON><PERSON><PERSON>
  -
  -  $Id: orm.xml 137134 2018-08-16 21:45:13Z tedd $
  -->


<entity-mappings
        xmlns="http://www.eclipse.org/eclipselink/xsds/persistence/orm"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.eclipse.org/eclipselink/xsds/persistence/orm http://www.eclipse.org/eclipselink/xsds/eclipselink_orm_3_0.xsd"
        version="3.0">

    <persistence-unit-metadata>
        <persistence-unit-defaults>
            <entity-listeners>
                <entity-listener class="com.adva.nlms.mediation.common.persistence.objecthistory.AdvaGlobalEntityListener">
                    <post-persist method-name="postPersist"/>
                    <post-remove method-name="postRemove"/>
                    <post-update method-name="postUpdate"/>
                </entity-listener>
            </entity-listeners>
        </persistence-unit-defaults>
    </persistence-unit-metadata>
    <entity class="com.adva.nlms.mediation.config.ManagedObjectDBImpl">
        <!-- global id generator used by NMS -->
        <table-generator name="NMSIdGenerator" table="JDO_SEQUENCE"
                         pk-column-name="ID" pk-column-value="DEFAULT" value-column-name="SEQUENCE_VALUE"  allocation-size="1000" initial-value="1" />
        <entity-listeners>
            <entity-listener class="com.adva.nlms.mediation.config.polling.managedobject.SyncMODescPostPersistListener"/>
            <entity-listener class="com.adva.nlms.mediation.config.entity.managedobject.MOUpdateJPAListener"/>
            <entity-listener class="com.adva.nlms.mediation.config.entity.managedobject.MOCreationJPAListener"/>
            <entity-listener class="com.adva.nlms.mediation.config.entity.managedobject.MODeletionJPAListener"/>
        </entity-listeners>
    </entity>
    <entity class="com.adva.nlms.mediation.health.db.HealthMonitoringSampleDBImpl">
        <table-generator name="NMSIdHealthCenterGenerator" table="JDO_SEQUENCE"
                         pk-column-name="ID" pk-column-value="HC_GEN" value-column-name="SEQUENCE_VALUE"  allocation-size="1000" initial-value="1" />
    </entity>

    <entity class="com.adva.nlms.mediation.performance.PmRecordDBImpl">
        <table-generator name="NMSPmIdGenerator" table="JDO_SEQUENCE"
                         pk-column-name="ID" pk-column-value="PM_GEN" value-column-name="SEQUENCE_VALUE" allocation-size="1000" initial-value="1"/>
    </entity>

    <entity class="com.adva.nlms.mediation.event.EventDBImpl">
        <table-generator name="NMSIdEventGenerator" table="JDO_SEQUENCE" pk-column-value="EVENTS"
                         pk-column-name="ID"  value-column-name="SEQUENCE_VALUE"  allocation-size="1000" initial-value="1"/>
    </entity>

    <entity class="com.adva.nlms.mediation.config.ManagedObjectAttrDBImpl">
        <entity-listeners>
            <entity-listener class="com.adva.nlms.mediation.config.entity.managedobject.MOAttrCreationJPAListener"/>
            <entity-listener class="com.adva.nlms.mediation.config.entity.managedobject.MOAttrDeletionJPAListener"/>
            <entity-listener class="com.adva.nlms.mediation.config.entity.managedobject.MOAttrUpdateJPAListener"/>
        </entity-listeners>
    </entity>

    <entity class="com.adva.nlms.mediation.config.EntityDBImpl">
        <entity-listeners>
            <entity-listener class="com.adva.nlms.mediation.config.EntityDBImplListener"/>
        </entity-listeners>
    </entity>

    <entity class="com.adva.nlms.mediation.mltopologymodel.model.db.MLLayerExtensionDBImpl">
    </entity>

    <entity class="com.adva.nlms.mediation.config.hn4000.UniHN4000DBImpl">
        <entity-listeners>
            <entity-listener class="com.adva.nlms.mediation.config.hn4000.UniHN4000DBImplJPAListener"/>
        </entity-listeners>
    </entity>

    <entity class="com.adva.nlms.mediation.config.f3.entity.logicalport.vcpath.vt.VtVcPathF3DBImpl">
        <entity-listeners>
            <entity-listener class="com.adva.nlms.mediation.config.f3.entity.logicalport.LogicalPortF3JPAListener"/>
        </entity-listeners>
    </entity>

    <entity class="com.adva.nlms.mediation.config.f3.entity.logicalport.vcpath.sts.StsVcPathF3DBImpl">
        <entity-listeners>
            <entity-listener class="com.adva.nlms.mediation.config.f3.entity.logicalport.LogicalPortF3JPAListener"/>
        </entity-listeners>
    </entity>

    <entity class="com.adva.nlms.mediation.config.f3.entity.logicalport.extx.e3t3.E3T3F3DBImpl">
        <entity-listeners>
            <entity-listener class="com.adva.nlms.mediation.config.f3.entity.logicalport.LogicalPortF3JPAListener"/>
        </entity-listeners>
    </entity>

    <entity class="com.adva.nlms.mediation.config.PortDBImpl">
        <entity-listeners>
            <entity-listener class="com.adva.nlms.mediation.config.entity.port.PortDBImplListener"/>
        </entity-listeners>
    </entity>

    <entity class="com.adva.nlms.mediation.config.f3.entity.module.ocstm.OcStmCardF3DBImpl">
        <entity-listeners>
            <entity-listener class="com.adva.nlms.mediation.config.f3.entity.module.ocstm.OcStmCardF3JPAListener"/>
        </entity-listeners>
    </entity>

    <entity class="com.adva.nlms.mediation.config.fsp_r7.entity.facility.physical.PortExtensionDBImpl">
        <entity-listeners>
            <entity-listener class="com.adva.nlms.mediation.config.entity.managedobject.UpdateListener"/>
        </entity-listeners>
    </entity>

</entity-mappings>
