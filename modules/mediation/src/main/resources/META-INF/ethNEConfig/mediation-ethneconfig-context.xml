<?xml version="1.0" encoding="UTF-8"?>
<!--
-  Copyright 2023 Adtran Networks SE. All rights reserved.
-
-  Owner: askar<PERSON><PERSON>
-->
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:util="http://www.springframework.org/schema/util"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd

       http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util.xsd">

    <bean id="transferConfigurationFileObserver" class="com.adva.nlms.mediation.ethNEConfig.neFileTransfer.TransferConfigurationFileObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>

    <!--<bean id="dbObjectFactoryF3" class="com.adva.nlms.mediation.config.f3.entity.factory.impl.DBObjectFactoryF3Impl"/>-->
    <bean class="com.adva.nlms.mediation.ethNEConfig.function.FunctionManager">

        <property name="profileHandlerMap">
            <util:map>
                <entry key="#{T(com.adva.nlms.common.ethernetconfig.function.profile.Constants).POLICER_PROFILE.toString()}">
                    <util:list>
                        <bean class="com.adva.nlms.mediation.ethNEConfig.function.FunctionManager$PolicerProfileHandler"/>
                    </util:list>
                </entry>
                <entry key="#{T(com.adva.nlms.common.ethernetconfig.function.profile.Constants).ACL_PROFILE.toString()}">
                    <util:list>
                        <bean class="com.adva.nlms.mediation.ethNEConfig.function.FunctionManager$AclProfileHandler"/>
                    </util:list>
                </entry>
                <entry key="#{T(com.adva.nlms.common.ethernetconfig.function.profile.Constants).PORT_CPD_PROFILE.toString()}">
                    <util:list>
                        <bean class="com.adva.nlms.mediation.ethNEConfig.function.FunctionManager$PortCpdProfileHandler"/>
                    </util:list>
                </entry>
                <entry key="#{T(com.adva.nlms.common.ethernetconfig.function.profile.Constants).FLOW_CPD_PROFILE.toString()}">
                    <util:list>
                        <bean class="com.adva.nlms.mediation.ethNEConfig.function.FunctionManager$FlowPointCpdProfileHandler"/>
                    </util:list>
                </entry>
                <entry key="#{T(com.adva.nlms.common.ethernetconfig.function.profile.Constants).PRIO_MAP_PROFILE.toString()}">
                    <util:list>
                        <bean class="com.adva.nlms.mediation.ethNEConfig.function.FunctionManager$PrioMapProfileHandler"/>
                    </util:list>
                </entry>
                <entry key="#{T(com.adva.nlms.common.ethernetconfig.function.profile.Constants).INGRESS_PRIO_MAP_PROFILE.toString()}">
                    <util:list>
                        <bean class="com.adva.nlms.mediation.ethNEConfig.function.FunctionManager$PrioMapProfileHandler"/>
                    </util:list>
                </entry>
                <entry key="#{T(com.adva.nlms.common.ethernetconfig.function.profile.Constants).QUEUE_PROFILE.toString()}">
                    <util:list>
                        <bean class="com.adva.nlms.mediation.ethNEConfig.function.FunctionManager$QueueProfileHandler"/>
                        <bean class="com.adva.nlms.mediation.ethNEConfig.function.FunctionManager$EGMQueueProfileHandler"/>
                    </util:list>
                </entry>
                <entry key="#{T(com.adva.nlms.common.ethernetconfig.function.profile.Constants).PRIORITY_RATE_PROFILE.toString()}">
                    <util:list>
                        <bean class="com.adva.nlms.mediation.ethNEConfig.function.FunctionManager$PriorityRateProfileHandler"/>
                    </util:list>
                </entry>
                <entry key="#{T(com.adva.nlms.common.ethernetconfig.function.profile.Constants).RATE_PROFILE.toString()}">
                    <util:list>
                        <bean class="com.adva.nlms.mediation.ethNEConfig.function.FunctionManager$RateProfileHandler"/>
                    </util:list>
                </entry>
            </util:map>
        </property>

    </bean>

    <bean class="com.adva.nlms.mediation.ethNEConfig.TransferFileHdlrImpl" id="transferFileHdlr"/>
    <bean class="com.adva.nlms.mediation.ethNEConfig.db.TransferFileDAO" id="transferFileDAO"/>
</beans>