<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

    <bean class="com.adva.nlms.mediation.thresholdCrossingAlert.ThresholdCrossingAlertsCtrlImpl"/>

    <bean id="serviceDao" class="com.adva.nlms.mediation.thresholdCrossingAlert.TCAMonitorDaoServiceImpl"/>
    <bean id="standardDao" class="com.adva.nlms.mediation.thresholdCrossingAlert.TCAMonitorDaoStandardImpl"/>

    <bean id="serviceHelper" class="com.adva.nlms.mediation.thresholdCrossingAlert.TCAMonitorHelperServiceImpl"/>
    <bean id="standardHelper" class="com.adva.nlms.mediation.thresholdCrossingAlert.TCAMonitorHelperStandardImpl"/>

    <bean id="serviceHandler" class="com.adva.nlms.mediation.thresholdCrossingAlert.TCAMonitorHandlerImpl" scope="prototype">
        <constructor-arg name="tcaMonitorHelper" ref="serviceHelper"/>
    </bean>

    <bean id="standardHandler" class="com.adva.nlms.mediation.thresholdCrossingAlert.TCAMonitorHandlerImpl" scope="prototype">
        <constructor-arg name="tcaMonitorHelper" ref="standardHelper"/>
    </bean>

    <bean class="com.adva.nlms.mediation.thresholdCrossingAlert.TCAResyncHandler"/>


    <bean class="com.adva.nlms.mediation.thresholdCrossingAlert.ESAProbeDeletionObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
</beans>