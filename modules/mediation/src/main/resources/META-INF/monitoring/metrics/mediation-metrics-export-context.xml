<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~  Copyright 2023 Adtran Networks SE. All rights reserved.
  ~
  ~  Owner: msteiner
  -->

<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

    <bean id="jmxMetricsExporter" class="com.adva.nlms.mediation.monitoring.metrics.JmxMetricsExporter">
        <constructor-arg value="#{ T(com.adva.nlms.mediation.monitoring.metrics.GlobalMetricRegistry).getGlobalMetricRegistry()}"/>
    </bean>

    <bean  class="org.springframework.jmx.export.MBeanExporter" lazy-init="false">
        <property name="beans">
            <map>
                <entry key="com.adva.nlms.mediation:name=jmxMetricsExporter" value-ref="jmxMetricsExporter"/>
            </map>
        </property>
    </bean>

</beans>