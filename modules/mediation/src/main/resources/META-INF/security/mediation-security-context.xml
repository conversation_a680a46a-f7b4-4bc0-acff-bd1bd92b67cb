<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
        http://www.springframework.org/schema/context
        http://www.springframework.org/schema/context/spring-context-3.0.xsd">
    <context:annotation-config/>


    <bean id="groupDAO" class="com.adva.nlms.mediation.security.GroupDAO"/>
    <bean id="userDAO" class="com.adva.nlms.mediation.security.user.UserDAO"/>
    <bean class="com.adva.nlms.mediation.security.user.UserCtrlImpl"/>
    <bean class="com.adva.nlms.mediation.security.user.UserController"/>
    <bean class="com.adva.nlms.mediation.security.GroupCtrlImpl"/>
    <bean class="com.adva.nlms.mediation.security.GroupController"/>
    <bean id="elsSSOSpringConfiguration" class="com.adva.nlms.mediation.infrastructure.licensing.impl.elssso.ELSSSOSpringConfiguration"/>
    <bean id="networkView" class="com.adva.nlms.mediation.security.view.NetworkViewHdlrImpl"/>
    <bean id="customerView" class="com.adva.nlms.mediation.security.view.CustomerViewHdlrImpl"/>
    <bean class="com.adva.nlms.mediation.security.view.ViewDAO"/>
    <bean class="com.adva.nlms.mediation.security.SecurityDAO"/>
    <bean id="userNameServiceImpl" class="com.adva.nlms.mediation.security.user.UserIdentityServiceImpl"/>
    <bean id="userPropertiesService" class="com.adva.nlms.mediation.security.user.UserPropertiesServiceImpl"/>
    <bean id="userInfoHelper" class="com.adva.nlms.mediation.security.user.UserInfoHelperImpl"/>
    <bean class="com.adva.nlms.mediation.security.SecurtiyPropsDAO"/>
    <bean class="com.adva.nlms.mediation.security.group.GroupManagerImpl" />
    <bean class="com.adva.nlms.mediation.security.role.RoleManagerImpl" />
    <bean class="com.adva.nlms.mediation.security.permission.ApprovalManager" />
    <bean class="com.adva.nlms.mediation.security.permission.TwoManRuleApprovalNotifier" />
    <bean class="com.adva.nlms.mediation.security.permission.ApprovalProxyNotifier" />
    <bean class="com.adva.nlms.mediation.security.ActionManagerImpl" />
    <bean id="sessionManager" class="com.adva.nlms.mediation.security.session.SessionManager" />
    <bean class="com.adva.nlms.mediation.security.Login" />
    <bean class="com.adva.nlms.mediation.security.auth.AuthCommandFactory" />
    <bean class="com.adva.nlms.mediation.security.auth.AuthHelper" />
    <bean id="systemEventLoggingImpl" class="com.adva.nlms.mediation.security.event.SystemEventLoggingImpl" />
    <bean id="sessionHdlr" class="com.adva.nlms.mediation.security.session.SessionHdlr" />
    <bean class="com.adva.nlms.mediation.security.permission.ApproversElection" />
    <bean class="com.adva.nlms.mediation.config.neconfig.ssoaha.AdHocUsersRemovalSNMPHandlerImpl" />
    <bean class="com.adva.nlms.mediation.config.neconfig.ssoaha.EcAdHocUserDAO" />
    <bean class="com.adva.nlms.mediation.config.neconfig.ssoaha.AdHocUserCommonRoleResolver"/>
    <bean class="com.adva.nlms.mediation.config.neconfig.ssoaha.EcAdHocUserRoleMapper"/>
    <bean id="f7AdHocUserRoleMapper" class="com.adva.nlms.mediation.config.neconfig.ssoaha.F7AdHocUserRoleMapper"/>
    <bean class="com.adva.nlms.mediation.config.neconfig.ssoaha.AdHocUserHandlerSNMPImpl">
        <constructor-arg ref="ssoManager"/>
        <constructor-arg ref="passwordGenerator"/>
        <constructor-arg ref="f7AdHocUserRoleMapper"/>
    </bean>
    <bean class="com.adva.nlms.mediation.config.neconfig.ssoaha.AdHocUserHandlerRESTImpl"/>
    <bean class="com.adva.nlms.mediation.infrastructure.licensing.impl.flexera.FlexeraPropertiesSetCondition"/>
    <bean class="com.adva.nlms.mediation.security.ks.KeystoresDAO" />
    <bean class="com.adva.nlms.mediation.security.ca.CAHandlerImpl" />
    <bean class="com.adva.nlms.mediation.security.ca.CARestHandlerImpl" />
    <bean class="com.adva.nlms.mediation.security.ca.CaTokenHandlerImpl" />
    <bean class="com.adva.nlms.mediation.security.auth.EncMicroServicesTokenHandlerImpl" />
    <bean class="com.adva.nlms.mediation.security.api.trust.SystemTrustEventsProducer" />
    <bean class="com.adva.nlms.mediation.security.cert.MauthKeystoreHandlerImpl" />
    <bean class="com.adva.nlms.mediation.security.IdExtenderImpl" />
    <bean class="com.adva.nlms.mediation.security.crypto.EncSecureIdGenerator" />
    <bean class="com.adva.nlms.mediation.security.cert.CertificatesVerificationStateHandler" />
    <bean class="com.adva.nlms.mediation.security.api.permission.ServerPropertyAAChecker"/>
    <bean class="com.adva.nlms.mediation.security.SessionFilterService"/>
    <bean class="com.adva.nlms.mediation.security.api.user.CommonUserServiceImpl"/>
    <bean class="com.adva.nlms.mediation.topology.api.TopologySabotageServiceImpl"/>
    <bean class="com.adva.nlms.mediation.infrastructure.security.sabotage.impl.config.OperationLimitConfig"/>
    <bean class="com.adva.nlms.mediation.infrastructure.security.sabotage.impl.SabotageService"/>
    <bean class="com.adva.nlms.mediation.infrastructure.security.sabotage.impl.dao.SabotageProtectionEventDAO"/>
    <bean class="com.adva.nlms.mediation.infrastructure.security.sabotage.impl.cleanup.SabotageProtectionEventCleanup"/>
</beans>
