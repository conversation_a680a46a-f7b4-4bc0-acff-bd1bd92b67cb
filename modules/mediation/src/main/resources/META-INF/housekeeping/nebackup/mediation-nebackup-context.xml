<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
        http://www.springframework.org/schema/context
        http://www.springframework.org/schema/context/spring-context-3.0.xsd">
    <context:annotation-config/>
    <bean class="com.adva.nlms.mediation.housekeeping.nebackup.core.db.NeBackupDAO"/>
    <bean class="com.adva.nlms.mediation.housekeeping.nebackup.core.NEBackupManager"/>
    <bean class="com.adva.nlms.mediation.housekeeping.nebackup.core.FTPConnectionTester"/>
    <bean class="com.adva.nlms.mediation.housekeeping.nebackup.core.NEBackupController"/>
    <bean class="com.adva.nlms.mediation.housekeeping.nebackup.core.NEBackupPageHdlrImpl"/>
    <bean class="com.adva.nlms.mediation.housekeeping.nebackup.core.EncryptedBackupValidator"/>
    <bean class="com.adva.nlms.mediation.notifications.kafka.jobmanager.KafkaProducerConfig"/>

    <bean class="com.adva.nlms.mediation.housekeeping.nebackup.core.BackupInfoHelper" />
    <bean class="com.adva.nlms.mediation.housekeeping.nebackup.core.NEBackupDBDataProvider" />
    <bean class="com.adva.nlms.mediation.housekeeping.nebackup.core.RepositoryManagerProviderImpl"/>
    <bean class="com.adva.nlms.mediation.housekeeping.nebackup.core.RepositoryManagerPolling">
        <qualifier type="com.adva.nlms.mediation.polling.DomainPolling"/>
    </bean>
    <bean class="com.adva.nlms.mediation.housekeeping.nebackup.core.NeConfigBackupPolling">
        <qualifier type="com.adva.nlms.mediation.polling.NePolling"/>
    </bean>
    <bean class="com.adva.nlms.mediation.housekeeping.nebackup.sd.SupportDataBackupPolling">
        <qualifier type="com.adva.nlms.mediation.polling.NePolling"/>
    </bean>
    <bean class="com.adva.nlms.mediation.housekeeping.nebackup.sd.CompactSupportDataBackupPolling">
        <qualifier type="com.adva.nlms.mediation.polling.NePolling"/>
    </bean>

    <bean class="com.adva.nlms.mediation.housekeeping.nebackup.sd.SupportDataPageHdlrImpl"/>
    <bean class="com.adva.nlms.mediation.common.housekeeping.SpringAppContext"/>
    <bean class="com.adva.nlms.mediation.common.housekeeping.FileServerStateRepository"/>
    <bean class="com.adva.nlms.mediation.housekeeping.FileServerMonitor"/>
    <bean class="com.adva.nlms.mediation.housekeeping.profile.NeProfilePropertiesHdlr"/>
    <bean class="com.adva.nlms.mediation.housekeeping.profile.NeProfileUpdateDAO"/>
    <bean class="com.adva.nlms.mediation.housekeeping.profile.ProfileTransferPropertiesDBImpl"/>
<!--    <bean class="com.adva.nlms.mediation.common.housekeeping.transferclient.SftpClientFingerprintCache"/>-->

    <bean class="com.adva.nlms.mediation.housekeeping.inf.NeObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>

</beans>