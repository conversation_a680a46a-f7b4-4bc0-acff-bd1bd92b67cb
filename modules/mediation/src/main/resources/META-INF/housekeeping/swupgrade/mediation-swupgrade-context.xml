<?xml version="1.0" encoding="UTF-8"?>
<!--
  -  Copyright 2023 Adtran Networks SE. All rights reserved.
  -
  -  Owner: askar<PERSON><PERSON>
  -->
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:context="http://www.springframework.org/schema/context" xmlns:util="http://www.springframework.org/schema/util"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
       http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util.xsd
       http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd">
    <context:annotation-config/>

    <bean class="com.adva.nlms.mediation.servicelocator.QualifiedServicesRegistry">
        <constructor-arg type="java.lang.Class" value="com.adva.nlms.mediation.housekeeping.swupgrade.api.NetworkElementVersionsUpdateProcess"></constructor-arg>
        <qualifier value="forSwUpdateHdlr" />
    </bean>
    <bean class="com.adva.nlms.mediation.housekeeping.swupgrade.SwUpgradeHdlr"/>
    <bean class="com.adva.nlms.mediation.housekeeping.swupgrade.SWVersionCheckPollingCommand">
        <qualifier type="com.adva.nlms.mediation.polling.NePolling"/>
    </bean>
    <bean class="com.adva.nlms.mediation.housekeeping.swupgrade.SWUpgradeCtrl"/>
    <bean class="com.adva.nlms.mediation.housekeeping.swupgrade.SWUpgradeRunner"/>
    <bean class="com.adva.nlms.mediation.housekeeping.swupgrade.SWUpgradeThreadPool"/>
    <bean class="com.adva.nlms.mediation.housekeeping.swupgrade.SWUpgradeRunner$SWUpgradeRunnerExecutor"/>
    <bean class="com.adva.nlms.mediation.housekeeping.swupgrade.SWUpgradePageHdlrImpl"/>
    <bean class="com.adva.nlms.mediation.housekeeping.swupgrade.SWUpgradeRowBuilder"/>
    <bean id="swUpgradeDAO" class="com.adva.nlms.mediation.housekeeping.swupgrade.db.SWUpgradeDAO"/>

    <bean id="defaultConfig" class="com.adva.nlms.mediation.housekeeping.swupgrade.device.config.DefaultConfig"/>
    <bean id="readOnlyConfig" class="com.adva.nlms.mediation.housekeeping.swupgrade.device.config.ReadOnlyConfig"/>
    <bean id="noRevertConfig" class="com.adva.nlms.mediation.housekeeping.swupgrade.device.config.NoRevertConfig"/>
    <bean id="egmConfig" class="com.adva.nlms.mediation.housekeeping.swupgrade.device.config.EgmConfig"/>

    <bean class="com.adva.nlms.mediation.housekeeping.swupgrade.device.config.SWUpgradeConfigFactory">
        <property name="configMap">
            <map key-type="java.lang.Integer" value-type="com.adva.nlms.mediation.housekeeping.swupgrade.device.config.ISWUpgradeConfig">
                <entry>
                    <key>
                        <util:constant static-field="com.adva.nlms.common.config.netypes.NeTypeIds.NETWORK_ELEMENT_TYPE_OSA5331"/>
                    </key>
                    <ref bean="readOnlyConfig"/>
                </entry>
                <entry>
                    <key>
                        <util:constant static-field="com.adva.nlms.common.config.netypes.NeTypeIds.NETWORK_ELEMENT_TYPE_OSA5400SM"/>
                    </key>
                    <ref bean="noRevertConfig"/>
                </entry>
                <entry>
                    <key>
                        <util:constant static-field="com.adva.nlms.common.config.netypes.NeTypeIds.NETWORK_ELEMENT_TYPE_OSA5400TC"/>
                    </key>
                    <ref bean="noRevertConfig"/>
                </entry>
                <entry>
                    <key>
                        <util:constant static-field="com.adva.nlms.common.config.netypes.NeTypeIds.NETWORK_ELEMENT_TYPE_OSA5401XG"/>
                    </key>
                    <ref bean="noRevertConfig"/>
                </entry>
                <entry>
                    <key>
                        <util:constant static-field="com.adva.nlms.common.config.netypes.NeTypeIds.NETWORK_ELEMENT_TYPE_OSA5401"/>
                    </key>
                    <ref bean="noRevertConfig"/>
                </entry>
                <entry>
                    <key>
                        <util:constant static-field="com.adva.nlms.common.config.netypes.NeTypeIds.NETWORK_ELEMENT_TYPE_OSA5405I"/>
                    </key>
                    <ref bean="noRevertConfig"/>
                </entry>
                <entry>
                    <key>
                        <util:constant static-field="com.adva.nlms.common.config.netypes.NeTypeIds.NETWORK_ELEMENT_TYPE_OSA5405O"/>
                    </key>
                    <ref bean="noRevertConfig"/>
                </entry>
                <entry>
                    <key>
                        <util:constant static-field="com.adva.nlms.common.config.netypes.NeTypeIds.NETWORK_ELEMENT_TYPE_OSA5405MB"/>
                    </key>
                    <ref bean="noRevertConfig"/>
                </entry>
                <entry>
                    <key>
                        <util:constant static-field="com.adva.nlms.common.config.netypes.NeTypeIds.NETWORK_ELEMENT_TYPE_OSA5405P"/>
                    </key>
                    <ref bean="noRevertConfig"/>
                </entry>
                <entry>
                    <key>
                        <util:constant static-field="com.adva.nlms.common.config.netypes.NeTypeIds.NETWORK_ELEMENT_TYPE_OSA5405S"/>
                    </key>
                    <ref bean="noRevertConfig"/>
                </entry>
                <entry>
                    <key>
                        <util:constant static-field="com.adva.nlms.common.config.netypes.NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150EGX"/>
                    </key>
                    <ref bean="noRevertConfig"/>
                </entry>
                <entry>
                    <key>
                        <util:constant static-field="com.adva.nlms.common.config.netypes.NeTypeIds.NETWORK_ELEMENT_TYPE_OSA5548C_SSU60"/>
                    </key>
                    <ref bean="readOnlyConfig"/>
                </entry>
                <entry>
                    <key>
                        <util:constant static-field="com.adva.nlms.common.config.netypes.NeTypeIds.NETWORK_ELEMENT_TYPE_OSA5548C_SSU200"/>
                    </key>
                    <ref bean="readOnlyConfig"/>
                </entry>
                <entry>
                    <key>
                        <util:constant static-field="com.adva.nlms.common.config.netypes.NeTypeIds.NETWORK_ELEMENT_TYPE_OSA5548C_TSG60"/>
                    </key>
                    <ref bean="readOnlyConfig"/>
                </entry>
                <entry>
                    <key>
                        <util:constant static-field="com.adva.nlms.common.config.netypes.NeTypeIds.NETWORK_ELEMENT_TYPE_OSA5548C_TSG200"/>
                    </key>
                    <ref bean="readOnlyConfig"/>
                </entry>
                <entry>
                    <key>
                        <util:constant static-field="com.adva.nlms.common.config.netypes.NeTypeIds.NETWORK_ELEMENT_TYPE_OSA5335_PTPGM"/>
                    </key>
                    <ref bean="readOnlyConfig"/>
                </entry>
                <entry>
                    <key>
                        <util:constant static-field="com.adva.nlms.common.config.netypes.NeTypeIds.NETWORK_ELEMENT_TYPE_OSA3350"/>
                    </key>
                    <ref bean="readOnlyConfig"/>
                </entry>
                <entry>
                    <key>
                        <util:constant static-field="com.adva.nlms.common.config.netypes.NeTypeIds.NETWORK_ELEMENT_TYPE_OSA3300"/>
                    </key>
                    <ref bean="readOnlyConfig"/>
                </entry>
                <entry>
                    <key>
                        <util:constant static-field="com.adva.nlms.common.config.netypes.NeTypeIds.NETWORK_ELEMENT_TYPE_OSA3200"/>
                    </key>
                    <ref bean="readOnlyConfig"/>
                </entry>
                <entry>
                    <key>
                        <util:constant static-field="com.adva.nlms.common.config.netypes.NeTypeIds.NETWORK_ELEMENT_TYPE_OSA3250"/>
                    </key>
                    <ref bean="readOnlyConfig"/>
                </entry>
                <entry>
                    <key>
                        <util:constant static-field="com.adva.nlms.common.config.netypes.NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150EGM2"/>
                    </key>
                    <ref bean="egmConfig"/>
                </entry>
                <entry>
                    <key>
                        <util:constant static-field="com.adva.nlms.common.config.netypes.NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150EGM4"/>
                    </key>
                    <ref bean="egmConfig"/>
                </entry>
                <entry>
                    <key>
                        <util:constant static-field="com.adva.nlms.common.config.netypes.NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150EGM8"/>
                    </key>
                    <ref bean="egmConfig"/>
                </entry>
            </map>
        </property>
    </bean>

    <bean id="swUpgradeFactory" class="com.adva.nlms.mediation.housekeeping.swupgrade.device.SWUpgradeFactory"/>
    <bean class="com.adva.nlms.mediation.housekeeping.swupgrade.device.validate.SWValidateUpgradeRequestFactory">
        <constructor-arg ref="swUpgradeDAO"/>
        <constructor-arg ref="swFSPR7UnsupportedNEChecker"/>
    </bean>
    <bean id="swFSPR7UnsupportedNEChecker" class="com.adva.nlms.mediation.housekeeping.swupgrade.device.validate.SWFSPR7UnsupportedNEChecker">
        <constructor-arg ref="unsupportedNetworkElementsHandler"/>
    </bean>

    <bean id="f7Helper" class="com.adva.nlms.mediation.housekeeping.swupgrade.device.helper.SWUpgradeFSP_R7Helper"/>
    <bean id="restHelper" class="com.adva.nlms.mediation.housekeeping.swupgrade.device.helper.SWUpgradeRESTHelper"/>
    <bean id="cpHelper" class="com.adva.nlms.mediation.housekeeping.swupgrade.device.helper.SWUpgrade150CPHelper"/>
    <bean id="cmHelper" class="com.adva.nlms.mediation.housekeeping.swupgrade.device.helper.SWUpgrade150CMHelper">
        <property name="transferProtocolHelper" ref="f3TPH"/>
    </bean>

    <bean id="ccHelper" class="com.adva.nlms.mediation.housekeeping.swupgrade.device.helper.SWUpgrade150CCHelper"/>
    <bean id="hnHelper" class="com.adva.nlms.mediation.housekeeping.swupgrade.device.helper.SWUpgradeHN4000Helper"/>
    <bean id="almHelper" class="com.adva.nlms.mediation.housekeeping.swupgrade.device.helper.SWUpgradeAlmHelper"/>
    <bean id="egmHelper" class="com.adva.nlms.mediation.housekeeping.swupgrade.device.helper.SWUpgradeEGMHelper">
        <property name="transferProtocolHelper" ref="egmTPH"/>
    </bean>

    <bean id="f3TPH" class="com.adva.nlms.mediation.housekeeping.swupgrade.device.helper.F3TransferProtocolHelper"/>
    <bean id="egmTPH" class="com.adva.nlms.mediation.housekeeping.swupgrade.device.helper.EGMTransferProtocolHelper"/>
    <bean class="com.adva.nlms.mediation.housekeeping.swupgrade.DefaultPathBuilder"/>
    <bean id="fsp3000R7DeviceIdentifier" class="com.adva.nlms.mediation.housekeeping.swupgrade.FSP3000_R7DeviceIdentifier"/>
    <bean class="com.adva.nlms.mediation.housekeeping.swupgrade.FmwRequestValidator">
        <constructor-arg ref="swUpgradeFactory"/>
        <constructor-arg ref="fsp3000R7DeviceIdentifier"/>
    </bean>
</beans>