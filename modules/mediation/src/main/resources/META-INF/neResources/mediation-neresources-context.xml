<?xml version="1.0" encoding="UTF-8"?>
<!--
  -  Copyright 2023 Adtran Networks SE. All rights reserved.
  -
  -  Owner: askar<PERSON><PERSON>
  -->
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:util="http://www.springframework.org/schema/util"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
       http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd
       http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util.xsd">

  <context:annotation-config/>

  <bean class="com.adva.nlms.mediation.neResources.NEResourcesHdlrImpl"/>
  <bean lazy-init="true" class="com.adva.nlms.common.neResources.model.NodeTransformer"/>

  <bean lazy-init="true" class="com.adva.nlms.mediation.neResources.config.NeResourcesConfiguration"/>

  <bean lazy-init="true" class="com.adva.nlms.mediation.neResources.db.ResourcesProvider"/>

  <bean class="com.adva.nlms.mediation.neResources.db.F3DBObjectFetcher">
    <constructor-arg>
      <ref bean="dbObjectFactoryF3"/>
    </constructor-arg>
  </bean>

  <bean class="com.adva.nlms.mediation.neResources.db.EGMDBObjectFetcher">
    <constructor-arg>
      <ref bean="dbObjectFactoryEGM"/>
    </constructor-arg>
  </bean>

  <bean lazy-init="true" class="com.adva.nlms.mediation.neResources.db.DBObjectFetcherFactory">

    <property name="fetcherMap">
      <util:map>
        <entry key="#{T(com.adva.nlms.mediation.neResources.db.FetcherType).F3}">
          <ref bean="com.adva.nlms.mediation.neResources.db.F3DBObjectFetcher"/>
        </entry>
        <entry key="#{T(com.adva.nlms.mediation.neResources.db.FetcherType).EGM}">
          <ref bean="com.adva.nlms.mediation.neResources.db.EGMDBObjectFetcher"/>
        </entry>
        <entry key="#{T(com.adva.nlms.mediation.neResources.db.FetcherType).F7}">
          <bean class="com.adva.nlms.mediation.neResources.db.F7DBObjectFetcher"/>
        </entry>
        <entry key="#{T(com.adva.nlms.mediation.neResources.db.FetcherType).FSP1500}">
          <bean class="com.adva.nlms.mediation.neResources.db.FSP1500DBObjectFetcher"/>
        </entry>
        <entry key="#{T(com.adva.nlms.mediation.neResources.db.FetcherType).FSP150CP}">
          <bean class="com.adva.nlms.mediation.neResources.db.FSP150CPDBObjectFetcher"/>
        </entry>
        <!--<entry key="#{T(com.adva.nlms.mediation.neResources.db.FetcherType).FSP150CC}">
          <bean class="com.adva.nlms.mediation.neResources.db.FSP150CCDBObjectFetcher"/>
        </entry>-->
        <entry key="#{T(com.adva.nlms.mediation.neResources.db.FetcherType).FSP150CC_LEGACY}">
          <bean class="com.adva.nlms.mediation.neResources.db.FSP150CCLegacyDBObjectFetcher"/>
        </entry>
        <entry key="#{T(com.adva.nlms.mediation.neResources.db.FetcherType).ALM}">
          <bean class="com.adva.nlms.mediation.neResources.db.ALMDBObjectFetcher"/>
        </entry>
        <entry key="#{T(com.adva.nlms.mediation.neResources.db.FetcherType).XG3XX}">
          <bean class="com.adva.nlms.mediation.neResources.db.FSPXG3xxObjectFetcher"/>
        </entry>
        <entry key="#{T(com.adva.nlms.mediation.neResources.db.FetcherType).EC}">
          <bean class="com.adva.nlms.mediation.neResources.db.EcObjectFetcher"/>
        </entry>
        <entry key="#{T(com.adva.nlms.mediation.neResources.db.FetcherType).F4}">
          <bean class="com.adva.nlms.mediation.neResources.db.F4ObjectFetcher"/>
        </entry>
        <entry key="#{T(com.adva.nlms.mediation.neResources.db.FetcherType).OTHER}">
          <bean class="com.adva.nlms.mediation.neResources.db.DBObjectFetcherForOtherDevices"/>
        </entry>
      </util:map>
    </property>
  </bean>

  <bean lazy-init="true" class="com.adva.nlms.mediation.neResources.mapper.DataMapper"/>
  <bean lazy-init="true" class="com.adva.nlms.mediation.neResources.NodeBuilder"/>

<!--  <bean lazy-init="true" class="com.adva.nlms.mediation.report.ReportModelMapperImpl"/>-->

  <bean scope="prototype" class="com.adva.nlms.mediation.neResources.csv.BuildCsvOnDemandTask"/>
  <bean scope="prototype" class="com.adva.nlms.mediation.neResources.csv.BuildCsvRegularTask"/>

  <bean lazy-init="true" class="com.adva.nlms.mediation.neResources.csv.CsvOnDemandTaskRunner">
    <constructor-arg type="int" name="numberOfMainThreads">
      <value>1</value>
    </constructor-arg>
    <constructor-arg type="int" name="numberOfExecutorThreads">
      <value>4</value>
    </constructor-arg>
    <constructor-arg type="int" name="timeoutInMinutes">
      <value>10</value>
    </constructor-arg>
  </bean>

  <bean class="com.adva.nlms.mediation.neResources.ResourceReportPolling">
    <qualifier type="com.adva.nlms.mediation.polling.DomainPolling"/>
  </bean>

  <bean lazy-init="true" class="com.adva.nlms.mediation.neResources.csv.CsvRegularTaskRunner"/>
  <bean class="com.adva.nlms.mediation.neResources.NeResourcesInfo"/>

</beans>