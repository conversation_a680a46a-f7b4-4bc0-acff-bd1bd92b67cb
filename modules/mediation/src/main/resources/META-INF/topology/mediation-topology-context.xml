<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
        http://www.springframework.org/schema/context
        http://www.springframework.org/schema/context/spring-context-3.0.xsd">

    <context:annotation-config/>

    <bean class="com.adva.nlms.mediation.topology.lineproperties.LineCreationTermPointsServiceFactory" lazy-init="true">
        <property name="creators">
            <map>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_UNMANAGED}" value-ref="lineCreatorServiceUnmanagedNode"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_UNMANAGED_NETWORK}" value-ref="lineCreatorServiceUnmanagedNode"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_FSP_R7}" value-ref="lineCreatorServiceFspR7"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_F8}" value-ref="lineCreatorServiceF8"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_FSP1500}" value-ref="lineCreatorServiceFsp1500"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_FSP_150CM}" value-ref="lineCreatorServiceCM"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_FSP_150CM_CPMR}" value-ref="lineCreatorServiceCM"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_FSP_GE206}" value-ref="lineCreatorServiceF3"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_FSP_GE206F}" value-ref="lineCreatorServiceF3"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_FSP_GE201}" value-ref="lineCreatorServiceF3"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_FSP_GE112}" value-ref="lineCreatorServiceF3"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_FSP_GE104}" value-ref="lineCreatorServiceF3"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_FSP_GE114}" value-ref="lineCreatorServiceF3"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_FSP_GE114G}" value-ref="lineCreatorServiceF3"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_FSP_GE114S}" value-ref="lineCreatorServiceF3andTimingPorts"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_FSP_GE114H}" value-ref="lineCreatorServiceF3"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_FSP_GE114SH}" value-ref="lineCreatorServiceF3andTimingPorts"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_FSP_GE114PH}" value-ref="lineCreatorServiceF3"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_FSP_GE101PRO}" value-ref="lineCreatorServiceF3"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_FSP_GE201SE}" value-ref="lineCreatorServiceF3"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_FSP_XG210}" value-ref="lineCreatorServiceF3andTimingPorts"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_FSP_XG116}" value-ref="lineCreatorServiceF3"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_FSP_XG116H}" value-ref="lineCreatorServiceF3"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_FSP_XG118PROSH}" value-ref="lineCreatorServiceF3"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_FSP_XG118PROACSH}" value-ref="lineCreatorServiceF3"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_FSP_XG120}" value-ref="lineCreatorServiceF3"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_FSP_XG120PROSH}" value-ref="lineCreatorServiceF3"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_FSP_XG210C}" value-ref="lineCreatorServiceF3andTimingPorts"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_T1804}" value-ref="lineCreatorServiceF3"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_T3204}" value-ref="lineCreatorServiceF3"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_FSP_150EGX}" value-ref="lineCreatorServiceEGX"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_F3_EFM}" value-ref="lineCreatorServiceEfm"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_FSP_GE206V}" value-ref="lineCreatorServiceF3andTimingPorts"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_JUNIPER_MX}" value-ref="lineCreatorServiceJuniper"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_SYNC_PROB}" value-ref="lineCreatorServiceF3andTimingPorts"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_OSA5400SM}" value-ref="lineCreatorServiceF3andTimingPorts"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_OSA5400TC}" value-ref="lineCreatorServiceF3andTimingPorts"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_OSA5401XG}" value-ref="lineCreatorServiceF3andTimingPorts"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_OSA5401}" value-ref="lineCreatorServiceF3andTimingPorts"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_OSA5405I}" value-ref="lineCreatorServiceF3andTimingPorts"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_OSA5405O}" value-ref="lineCreatorServiceF3andTimingPorts"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_OSA5405MB}" value-ref="lineCreatorServiceF3andTimingPorts"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_OSA5405P}" value-ref="lineCreatorServiceF3andTimingPorts"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_OSA5405S}" value-ref="lineCreatorServiceF3andTimingPorts"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_OSA5548C_SSU60}" value-ref="lineCreatorServiceF3OSA"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_OSA5548C_SSU200}" value-ref="lineCreatorServiceF3OSA"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_OSA5548C_TSG60}" value-ref="lineCreatorServiceF3OSA"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_OSA5548C_TSG200}" value-ref="lineCreatorServiceF3OSA"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_OSA5335_PTPGM}" value-ref="lineCreatorServiceF3OSA"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_OSA3350}" value-ref="lineCreatorServiceF3andTimingPorts"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_OSA3300}" value-ref="lineCreatorServiceF3andTimingPorts"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_OSA3200}" value-ref="lineCreatorServiceF3andTimingPorts"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_OSA3250}" value-ref="lineCreatorServiceF3andTimingPorts"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_OSA5331}" value-ref="lineCreatorServiceF3andTimingPorts"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_OSA5411}" value-ref="lineCreatorServiceF3andTimingPorts"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_OSA5412}" value-ref="lineCreatorServiceF3andTimingPorts"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_OSA5420}" value-ref="lineCreatorServiceF3andTimingPorts"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_OSA5421}" value-ref="lineCreatorServiceF3andTimingPorts"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_OSA5422}" value-ref="lineCreatorServiceF3andTimingPorts"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_OSA5410XG}" value-ref="lineCreatorServiceF3andTimingPorts"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_OSA5430}" value-ref="lineCreatorServiceF3andTimingPorts"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_OSA5440}" value-ref="lineCreatorServiceF3andTimingPorts"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_FSP_GE112PRO}" value-ref="lineCreatorServiceF3"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_FSP_GE112PRO_M}" value-ref="lineCreatorServiceF3"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_FSP_GE112PRO_H}" value-ref="lineCreatorServiceF3"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_FSP_GO102PRO_S}" value-ref="lineCreatorServiceF3andTimingPorts"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_FSP_GO102PRO_SM}" value-ref="lineCreatorServiceF3andTimingPorts"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_FSP_GO102PRO_SP}" value-ref="lineCreatorServiceF3andTimingPorts"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_FSP_GE102PRO_H}" value-ref="lineCreatorServiceF3andTimingPorts"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_FSP_GE114PRO}" value-ref="lineCreatorServiceF3"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_FSP_GE114PRO_C}" value-ref="lineCreatorServiceF3"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_FSP_GE114PRO_SH}" value-ref="lineCreatorServiceF3andTimingPorts"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_FSP_GE114PRO_CSH}" value-ref="lineCreatorServiceF3andTimingPorts"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_FSP_GE114PRO_HE}" value-ref="lineCreatorServiceF3"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_FSP_150EGM2}" value-ref="lineCreatorServiceEGM"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_FSP_150EGM4}" value-ref="lineCreatorServiceEGM"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_FSP_150EGM8}" value-ref="lineCreatorServiceEGM"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_HN400_STANDALONE}" value-ref="lineCreatorServiceHN400"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_HN4000}" value-ref="lineCreatorServiceHN"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_HN400}" value-ref="lineCreatorServiceHN"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_FSP_XG304}" value-ref="lineCreatorServiceXG3xx"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_FSP_XG304f}" value-ref="lineCreatorServiceXG3xx"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_FSP_XG304u}" value-ref="lineCreatorServiceXG3xx"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_FSP_XG304uf}" value-ref="lineCreatorServiceXG3xx"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_FSP_XG308}" value-ref="lineCreatorServiceXG3xx"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_FSP_XG308f}" value-ref="lineCreatorServiceXG3xx"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_FSP_XG312f}" value-ref="lineCreatorServiceXG3xx"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_FSP_Z4806}" value-ref="lineCreatorServiceXG4xx"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_FSP_150CP}" value-ref="lineCreatorServiceCP"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_FSP_150MX}" value-ref="lineCreatorServiceCP"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_FSP_XG480}" value-ref="lineCreatorServiceF4"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_FSP_XG404}" value-ref="lineCreatorServiceF4"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_FSP_XG418}" value-ref="lineCreatorServiceF4"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_FSP_XG118PROCSH}" value-ref="lineCreatorServiceF4"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_FSP_XG490}" value-ref="lineCreatorServiceF4"/>

                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_FSP_XG108}" value-ref="lineCreatorServiceF3"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_FSP_XG108H}" value-ref="lineCreatorServiceF3"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_FSP_XG108SH}" value-ref="lineCreatorServiceF3"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_FSP_XO106}" value-ref="lineCreatorServiceF3"/>

                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_FSP_XJ128SH}" value-ref="lineCreatorServiceF3"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_FSP_GE104E}" value-ref="lineCreatorServiceF3"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_CUSTOM_PTP_BC}" value-ref="lineCreatorServiceUnmanagedNode"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_CUSTOM_GNSS_M}" value-ref="lineCreatorServiceUnmanagedNode"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_CUSTOM_GNSS_S}" value-ref="lineCreatorServiceUnmanagedNode"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_CUSTOM_GNSS_XS}" value-ref="lineCreatorServiceUnmanagedNode"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).NETWORK_ELEMENT_TYPE_CUSTOM_GNSS_XL}" value-ref="lineCreatorServiceUnmanagedNode"/>

                <entry key="#{T(com.adva.nlms.common.config.netypes.NeTypeIds).OPTICAL_ROUTER_THRESHOLD}" value-ref="lineCreatorServiceOpticalRouter"/>
            </map>
        </property>
    </bean>

    <bean id="lineCreatorServiceUnmanagedNode" class="com.adva.nlms.mediation.topology.lineproperties.LineCreationTermPointsUnmanaged" lazy-init="true"/>
    <bean id="lineCreatorServiceFspR7" class="com.adva.nlms.mediation.topology.lineproperties.LineCreationTermPointsFSP_R7" lazy-init="true"/>
    <bean id="lineCreatorServiceF4" class="com.adva.nlms.mediation.topology.lineproperties.f4.LineCreationTermPointsF4" lazy-init="true"/>
    <bean id="lineCreatorServiceF8" class="com.adva.nlms.mediation.topology.lineproperties.LineCreationTermPointsf8" lazy-init="true">
        <constructor-arg index="0" ref="entityDAO"/>
        <constructor-arg index="1" ref="fiberConnectionEcDAO"/>
        <constructor-arg index="2" name="lineDao" ref="lineDao"/>
    </bean>
    <bean id="lineCreatorServiceFsp1500" class="com.adva.nlms.mediation.topology.lineproperties.LineCreationTermPointsFSP1500" lazy-init="true"/>
    <bean id="lineCreatorServiceF3" class="com.adva.nlms.mediation.topology.lineproperties.LineCreationTermPointsF3" lazy-init="true"/>
    <bean id="lineCreatorServiceF3OSA" class="com.adva.nlms.mediation.topology.lineproperties.LineCreationTermPointsF3OSA" lazy-init="true"/>
    <bean id="lineCreatorServiceF3andTimingPorts" class="com.adva.nlms.mediation.topology.lineproperties.LineCreationTermPointsF3andTimingPorts" lazy-init="true"/>
    <bean id="lineCreatorServiceEGX" class="com.adva.nlms.mediation.topology.lineproperties.LineCreationTermPointsEGX" lazy-init="true"/>
    <bean id="lineCreatorServiceCM" class="com.adva.nlms.mediation.topology.lineproperties.LineCreationTermPointsCM" lazy-init="true"/>
    <bean id="lineCreatorServiceJuniper" class="com.adva.nlms.mediation.topology.lineproperties.LineCreationTermPointsJuniper" lazy-init="true"/>
    <bean id="lineCreatorServiceDefault" class="com.adva.nlms.mediation.topology.lineproperties.LineCreationTermPointsDefault" lazy-init="true"/>
    <bean id="lineCreatorServiceEGM" class="com.adva.nlms.mediation.topology.lineproperties.LineCreationTermPointsEGM" lazy-init="true"/>
    <bean id="lineCreatorServiceHN400" class="com.adva.nlms.mediation.topology.lineproperties.LineCreationTermPointsHN400" lazy-init="true"/>
    <bean id="lineCreatorServiceHN" class="com.adva.nlms.mediation.topology.lineproperties.LineCreationTermPointsHN" lazy-init="true"/>
    <bean id="lineCreatorServiceXG3xx" class="com.adva.nlms.mediation.topology.lineproperties.LineCreationTermPointsXG3xx" lazy-init="true"/>
    <bean id="lineCreatorServiceXG4xx" class="com.adva.nlms.mediation.topology.lineproperties.LineCreationTermPointsXG4xx" lazy-init="true"/>
    <bean id="lineCreatorServiceEfm" class="com.adva.nlms.mediation.topology.lineproperties.LineCreationTermPointEFM" lazy-init="true"/>
    <bean id="lineCreatorServiceCP" class="com.adva.nlms.mediation.topology.lineproperties.LineCreationTermPointsFSP150CP" lazy-init="true"/>
    <bean id="lineCreatorServiceOpticalRouter" class="com.adva.nlms.mediation.topology.lineproperties.LineCreationTermPointsOpticalRouter" lazy-init="true"/>

    <bean class="com.adva.nlms.mediation.topology.lineproperties.alm.AlmLinePorts" lazy-init="true"/>

    <bean class="com.adva.nlms.mediation.topology.LinkValidatorFSP_R7"/>
    <bean class="com.adva.nlms.mediation.topology.LinkValidatorFactory" />
    <bean class="com.adva.nlms.mediation.topology.TopologyGlobalPrefsHelper" />
    <bean class="com.adva.nlms.mediation.topology.linkloss.LinkLossEntitiesProvider" />
    <bean class="com.adva.nlms.mediation.topology.linkloss.F7LinkScanner" />
    <bean class="com.adva.nlms.mediation.topology.linkloss.F8LinkScanner" />

    <bean id="usageInfoContextProvider" class="com.adva.nlms.mediation.topology.lineproperties.UsageInfoContextProvider" lazy-init="true"/>

    <bean class="com.adva.nlms.mediation.topology.lineproperties.vlan.VlanHdlrHelper"/>
    <bean class="com.adva.nlms.mediation.topology.lineproperties.vlan.VlanHdlrFactory"/>
    <bean id="vLanHdlrF3" class="com.adva.nlms.mediation.topology.lineproperties.vlan.VLanHdlrF3"/>
    <bean id="vlanHdlrF4" class="com.adva.nlms.mediation.topology.lineproperties.vlan.VlanHdlrF4"/>
    <bean class="com.adva.nlms.mediation.topology.lineproperties.vlan.VlanHdlrEGX"/>

    <bean class="com.adva.nlms.mediation.topology.InsertNodeInLinkService"/>
    <bean class="com.adva.nlms.mediation.topology.InsertNodeInLinkRESTService"/>

    <bean id="eodNodeProvider" class="com.adva.nlms.mediation.topology.eod.NodeProvider" />
    <bean id="eodConnectionProvider" class="com.adva.nlms.mediation.mltopologymodel.resources.MLEodConnectionProvider" />

    <bean id="nrimLogicalLinksHdlr" class="com.adva.nlms.mediation.mltopologymodel.logicallinks.NrimLogicalLinksHdlr" />
    <bean class="com.adva.nlms.mediation.mltopologymodel.logicallinks.NrimTeAttributesProvider" />

    <bean id="linkObserverForLifCpManagement" class="com.adva.nlms.mediation.topology.lineproperties.lifcp.LinkObserverForLifCpManagement">
        <constructor-arg ref="logicalCPIfMOService" />
        <constructor-arg ref="lineDao" />
        <constructor-arg ref="managedObjectDAO" />
        <constructor-arg ref="domainPollingManagerOwner" />
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>

    <bean id="lineDeprovisioner" class="com.adva.nlms.mediation.topology.lineproperties.xponder.LineDeprovisioner">
    </bean>

    <bean class="com.adva.nlms.mediation.topology.lineproperties.xponder.LineDeprovisionCommand">
        <qualifier type="com.adva.nlms.mediation.polling.DomainPolling"/>
    </bean>

    <bean class="com.adva.nlms.mediation.topology.lineproperties.xponder.XponderLinkSupport" id="xponderLinkSupport">
        <constructor-arg ref="registeredNetworkElementsHdlr" />
        <constructor-arg ref="ptpDao" />
        <constructor-arg ref="buildXPonderResources" />
        <constructor-arg ref="xponderMessageUtil" />
        <constructor-arg ref="f7Utils"/>
        <constructor-arg ref="connectionWalker"/>
    </bean>

    <bean id="unassignedOlManagement" class="com.adva.nlms.mediation.topology.lineproperties.xponder.UnassignedOlManagement" />


    <bean id="buildXPonderResources" class="com.adva.nlms.mediation.topology.lineproperties.xponder.PrepareResourcesForXponder">
        <constructor-arg ref="lineDao"/>
        <constructor-arg ref="domainPollingManagerOwner"/>
        <constructor-arg ref="lineHdlr" />
        <constructor-arg ref="xponderMessageUtil" />
        <constructor-arg ref="resourcesConfigurator"/>
    </bean>

    <bean id="resourcesConfigurator" class="com.adva.nlms.mediation.topology.lineproperties.xponder.ResourcesConfigurator">
        <constructor-arg ref="unassignedOlManagement"/>
        <constructor-arg ref="ptpDao"/>
        <constructor-arg ref="lifsDao"/>
        <constructor-arg ref="commandFactory"/>
        <constructor-arg ref="wdmLifProvisioner"/>
        <constructor-arg ref="intraNeConnectionDAO"/>
        <constructor-arg ref="olDao"/>
    </bean>

    <bean class="com.adva.nlms.mediation.topology.paging.NetworkResourcePageHdleImpl" id="NetworkResourcePageHdleImpl" init-method="init">
        <constructor-arg type="com.adva.nlms.common.paging.PageArea" value="NETWORK_RESOURCE"/>
    </bean>
    <bean class="com.adva.nlms.mediation.topology.paging.SubnetResourcePageHdlrImpl" id="SubnetResourcePageHdlrImpl">
        <constructor-arg type="com.adva.nlms.common.paging.PageArea" value="NETWORK_RESOURCE"/>
    </bean>
    <bean class="com.adva.nlms.mediation.topology.paging.NetworkElementResourcePageHdlrImpl" id="NetworkElementResourcePageHdlrImpl">
        <constructor-arg type="com.adva.nlms.common.paging.PageArea" value="NETWORK_RESOURCE"/>
    </bean>
    <bean class="com.adva.nlms.mediation.topology.paging.LineResourcePageHdlrImpl" id="LineResourcePageHdlrImpl">
        <constructor-arg type="com.adva.nlms.common.paging.PageArea" value="NETWORK_RESOURCE"/>
    </bean>

    <bean id="wdmLifProvisioner" class="com.adva.nlms.mediation.config.fsp_r7.cp.provisioning.WdmLogicalInterfaceProvisioner">
        <constructor-arg ref="lifSnmpProvisioning" />
        <constructor-arg ref="wdmLifProvisioningMessages" />
    </bean>

    <bean id="otnLifProvisioner" class="com.adva.nlms.mediation.config.fsp_r7.cp.provisioning.OtnLogicalInterfaceProvisioner">
        <constructor-arg ref="lifSnmpProvisioning" />
        <constructor-arg ref="otnLifProvisioningMessages" />
    </bean>

    <bean id="xponderMessageUtil" class="com.adva.nlms.mediation.diagnostics.DiagnosticsUtils">
        <constructor-arg ref="messageManager" />
    </bean>

    <bean id="intraNeProvisioner" class="com.adva.nlms.mediation.topology.lineproperties.xponder.IntraNeProvisioner" />

    <bean id="commandFactory" class="com.adva.nlms.mediation.topology.lineproperties.xponder.base.CommandFactory" />
    <bean id="lineInfoFormModelProvider" class="com.adva.nlms.mediation.topology.LineInfoFormModelProvider" />

</beans>