<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~  Copyright 2023 Adtran Networks SE. All rights reserved.
  ~
  ~  Owner: msteiner
  -->

<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xmlns:tx="http://www.springframework.org/schema/tx"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
        http://www.springframework.org/schema/context
        http://www.springframework.org/schema/context/spring-context-3.0.xsd
        http://www.springframework.org/schema/tx
        http://www.springframework.org/schema/tx/spring-tx.xsd
        http://www.springframework.org/schema/aop
        http://www.springframework.org/schema/aop/spring-aop.xsd">

    <context:annotation-config/>

    <tx:annotation-driven transaction-manager="transactionManager" />

    <aop:aspectj-autoproxy />


    <bean id="nmsYpApiInstance" class="com.adva.yp.api.impl.YellowPageApiImpl" factory-method="createReadOnlyYellowPageApi">
        <constructor-arg ref="ypAccessor" />
    </bean>

    <bean id="ypGuavaCache" class="com.adva.nlms.common.yp.db.YPGuavaCache" />

    <bean id="ypAccessor" class="com.adva.yp.api.data_accessor.cache.YPCacheAccessor" factory-method="createReadOnlyYPCacheAccessor">
        <constructor-arg ref="ypDbThreadSafeReader" />
        <constructor-arg ref="ypGuavaCache" />
    </bean>

    <bean id="ypDbThreadSafeReader" class="com.adva.nlms.common.yp.YPDBThreadSafeReader" />

    <bean id="yellowPageRepository" scope="singleton" class="com.adva.nlms.common.yp.db.YellowPageRepository" >
        <constructor-arg ref="nmsYpApiInstance" />
    </bean>

    <bean class="org.springframework.beans.factory.config.MethodInvokingFactoryBean">
        <property name="staticMethod" value="com.adva.nlms.common.yp.db.YellowPageRepository.setInstance"/>
        <property name="arguments">
            <list>
                <ref bean="yellowPageRepository"/>
            </list>
        </property>
    </bean>

    <tx:advice id="txAdviceForYellowPagesApi" transaction-manager="transactionManager">
        <tx:attributes>
            <tx:method name="*" read-only="true"  propagation="SUPPORTS"/>
        </tx:attributes>
    </tx:advice>

    <aop:config>
        <aop:pointcut id="nmsYpApiInstanceOperation" expression="execution(* com.adva.yp.api.impl.YellowPageApiImpl.*(..))"/>
        <aop:advisor advice-ref="txAdviceForYellowPagesApi" pointcut-ref="nmsYpApiInstanceOperation"/>
    </aop:config>


    <bean id="hikariConfig" class="com.zaxxer.hikari.HikariConfig">
        <property name="poolName" value="springHikariCP"/>
        <!--<property name="connectionTestQuery" value="SELECT 1"/> DO not set validation query with hikari+h2 timeout will be altered
        see https://github.com/brettwooldridge/HikariCP/issues/800
        -->
        <property name="dataSourceClassName" value="org.h2.jdbcx.JdbcDataSource"/>
        <property name="maximumPoolSize" value="20"/>
        <property name="idleTimeout" value="300000"/>
        <property name="minimumIdle" value="10"/>
        <property name="readOnly" value="true"/>
        <property name="autoCommit" value="false"/>

        <property name="dataSourceProperties">
            <props>
                <prop key="url"><![CDATA[jdbc:h2:./db/yp/yp;ACCESS_MODE_DATA=r]]></prop>  <!--;TRACE_LEVEL_SYSTEM_OUT=3-->
                <prop key="user">adva</prop>
                <prop key="password">NeverChange</prop>
            </props>
        </property>
    </bean>

    <bean id="dataSource" class="com.zaxxer.hikari.HikariDataSource" destroy-method="close">
        <constructor-arg ref="hikariConfig"/>
    </bean>

    <bean id="transactionManager" class="org.springframework.orm.jpa.JpaTransactionManager">
        <property name="entityManagerFactory" ref="ypEntityManagerFactory" />
    </bean>

    <bean id="ypEntityManagerFactory" class="org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean">
        <property name="persistenceXmlLocation" value="classpath:/META-INF/persistence.xml" />
        <property name="dataSource" ref="dataSource" />
        <property name="persistenceUnitName" value="yp-db" />
        <property name="jpaVendorAdapter">
            <bean class="org.springframework.orm.jpa.vendor.EclipseLinkJpaVendorAdapter">
                <property name="showSql" value="true"/>
                <property name="generateDdl" value="false"/>
                <property name="databasePlatform" value="org.eclipse.persistence.platform.database.H2Platform"/>
            </bean>
        </property>
        <property name="jpaDialect">
            <bean class="org.springframework.orm.jpa.vendor.EclipseLinkJpaDialect" />
        </property>
        <property name="jpaPropertyMap">
            <props>
                <prop key="eclipselink.weaving">false</prop>
            </props>
        </property>
    </bean>
</beans>