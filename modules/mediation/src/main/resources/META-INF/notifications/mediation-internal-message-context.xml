<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:jms="http://www.springframework.org/schema/jms"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans-3.0.xsd


        http://www.springframework.org/schema/jms
	    http://www.springframework.org/schema/jms/spring-jms.xsd">

    <bean id="eventProducer" class="com.adva.nlms.mediation.event.EventMessageDispatcher"/>


    <jms:listener-container
            connection-factory="internalMessageConnectionFactory"
            acknowledge="transacted"
            concurrency="1"
            cache="consumer"
            destination-type="queue">
        <jms:listener destination="ni.nodes.message" ref="niNodesSystemObserver" method="onMessage"/>
    </jms:listener-container>

    <jms:listener-container
            connection-factory="internalMessageConnectionFactory"
            acknowledge="transacted"
            concurrency="1"
            cache="consumer"
            destination-type="queue">
        <jms:listener destination="tca.service.message" ref="serviceHandler" method="onMessage"/>
        <jms:listener destination="tca.standard.message" ref="standardHandler" method="onMessage"/>
        <jms:listener destination="maintenance.dbbackup.message" ref="DBBackupEventObserver" method="onMessage"/>
    </jms:listener-container>

    <jms:listener-container
            connection-factory="internalMessageConnectionFactory"
            acknowledge="transacted"
            destination-type="topic">
        <jms:listener destination="sm.creation-deletion.message" ref="eventStandardHandler" method="onMessage"/>
        <jms:listener destination="sm.oper-state-update.message" ref="ServiceStateCounterHdlr" method="onMessage"/>
    </jms:listener-container>

    <jms:listener-container connection-factory="internalMessageConnectionFactory"
                            acknowledge="auto" destination-type="topic">
        <jms:listener destination="plugins.messaging.statistics.broker" ref="statisticsPlugin" method="onMessage"/>
    </jms:listener-container>

    <jms:listener-container connection-factory="internalMessageConnectionFactory"
                            acknowledge="auto" destination-type="topic">
        <jms:listener destination="mo.device-change-operation.message" ref="operationManager" method="onMessage"/>
    </jms:listener-container>


    <bean id="internalMessageSender" class="com.adva.nlms.mediation.notifications.MDInternalMessageSenderImpl">
        <property name="destinations">
            <map key-type="com.adva.nlms.mediation.notifications.InternalMessageType" value-type="org.apache.activemq.command.ActiveMQDestination">
                <entry key="TCA_SERVICE_MESSAGE" value-ref="tcaServiceMessage"/>
                <entry key="TCA_STANDARD_MESSAGE" value-ref="tcaStandardMessage"/>
                <entry key="SERVICE_NEW_DEL_MESSAGE" value-ref="smCreationDeletionMessage"/>
                <entry key="SERVICE_UPDATE_MESSAGE" value-ref="smUpdateMessage"/>
                <entry key="SERVICE_OPERSTATE_UPDATE_MESSAGE" value-ref="smOperStateUpdateMessage"/>
                <entry key="DEVICE_CHANGE_OPERATION_MESSAGE" value-ref="deviceChangeOperationMessage"/>
                <entry key="MAINTENANCE_DBBACKUPEVENT_MESSAGE" value-ref="maintenanceDBBackupEventMessage"/>
                <entry key="NI_NODESYSTEMEVENT_MESSAGE" value-ref="niNodeSystemEventMessage"/>
            </map>
        </property>
    </bean>

    <bean id="niNodeSystemEventMessage" class="org.apache.activemq.command.ActiveMQQueue">
        <constructor-arg value="ni.nodes.message"/>
    </bean>

    <bean id="maintenanceDBBackupEventMessage" class="org.apache.activemq.command.ActiveMQQueue">
        <constructor-arg value="maintenance.dbbackup.message"/>
    </bean>

    <bean id="tcaServiceMessage" class="org.apache.activemq.command.ActiveMQQueue">
        <constructor-arg value="tca.service.message"/>
    </bean>

    <bean id="tcaStandardMessage" class="org.apache.activemq.command.ActiveMQQueue">
        <constructor-arg value="tca.standard.message"/>
    </bean>

    <bean id="smCreationDeletionMessage" class="org.apache.activemq.command.ActiveMQTopic">
        <constructor-arg value="sm.creation-deletion.message"/>
    </bean>
    <bean id="smUpdateMessage" class="org.apache.activemq.command.ActiveMQTopic">
        <constructor-arg value="sm.update.message"/>
    </bean>
    <bean id="smOperStateUpdateMessage" class="org.apache.activemq.command.ActiveMQTopic">
        <constructor-arg value="sm.oper-state-update.message"/>
    </bean>

    <bean id="deviceChangeOperationMessage" class="org.apache.activemq.command.ActiveMQTopic">
        <constructor-arg value="mo.device-change-operation.message"/>
    </bean>


    <bean id="statisticsPlugin" class="com.adva.nlms.mediation.notifications.statistics.StatisticsCommandImpl"/>

    <bean class="com.adva.nlms.mediation.notifications.InternalNotificationCommitObserver"/>

    <bean class="com.adva.nlms.mediation.mltopologymodel.api.notifications.KafkaProducerConfig"/>
    <bean class="com.adva.nlms.mediation.mltopologymodel.api.notifications.NrimKafkaConsumerConfig"/>
    <bean class="com.adva.nlms.mediation.mltopologymodel.api.notifications.NrimKafkaTopicConfig"/>
    <bean class="com.adva.nlms.mediation.mltopologymodel.api.notifications.ConnectivityServiceEventHandler"/>
    <bean class="com.adva.nlms.mediation.messaging.kafka.conf.KafkaMtlsConfig"/>
    <bean class="com.adva.nlms.mediation.messaging.kafka.conf.KafkaConfig"/>

    <bean class="com.adva.nlms.mediation.event.api.FmKafkaConsumerConfig"/>
    <bean class="com.adva.nlms.mediation.event.api.FmKafkaProducerConfig"/>
    <bean class="com.adva.nlms.mediation.event.api.FmKafkaTopicConfig"/>
    <bean class="com.adva.nlms.mediation.event.api.FmApiImpl"/>
    <bean class="com.adva.nlms.mediation.evtProc.api.EvtProcKafkaConsumerConfig"/>
    <bean class="com.adva.nlms.mediation.evtProc.api.EvtProcKafkaTopicConfig"/>

    <bean class="com.adva.nlms.mediation.topology.core.notifications.KafkaProducerConfig"/>
    <bean class="com.adva.nlms.mediation.topology.core.notifications.TopologyKafkaTopicConfig"/>
    <bean class="com.adva.nlms.mediation.topology.core.notifications.TenantsKafkaTopicConfig"/>
</beans>