<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~  Copyright 2023 Adtran Networks SE. All rights reserved.
  ~
  ~  Owner: mohitc
  ~
  -->

<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

    <bean id="mlServicePerformanceMonitoringFacade" class="com.adva.nlms.mediation.mltopologymodel.service.pm.MLServicePerformanceMonitoringFacade">
      <constructor-arg ref="processingStrategyManager"/>
      <constructor-arg type="com.adva.nlms.mediation.mltopologymodel.model.dao.MLTopologyElementDAO" ref="mlTopologyElementDAO"/>
    </bean>

</beans>