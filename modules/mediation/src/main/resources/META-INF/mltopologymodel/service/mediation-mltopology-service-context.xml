<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~  Copyright 2023 Adtran Networks SE. All rights reserved.
  ~
  ~  Owner: mohitc
  -->

<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

  <bean class="com.adva.nlms.mediation.mltopologymodel.service.MLAppContextProvider" init-method="init"/>
  <bean class="com.adva.nlms.mediation.mltopologymodel.sync.extension.name.MLServiceNameHelper" init-method="init" />
  <bean class="com.adva.nlms.mediation.mltopologymodel.sync.path.worker.monitorsec.MLMonitoringSectionFacade">
    <constructor-arg
        type="com.adva.nlms.mediation.mltopologymodel.sync.path.worker.monitorsec.path.AbstractPathMonitoringSectionHelper"
        ref="abstractPathMonitoringSectionHelper"/>

  </bean>

  <bean class="com.adva.nlms.mediation.mltopologymodel.sync.path.worker.monitorsec.path.AbstractPathMonitoringSectionHelper" id="abstractPathMonitoringSectionHelper">
    <constructor-arg
        type="com.adva.nlms.mediation.mltopologymodel.model.dao.MLTopologyElementDAO"
        ref="mlTopologyElementDAO"/>
    <constructor-arg
            type="com.adva.nlms.mediation.mltopologymodel.mofacade.MLMoReferenceHelper"
            ref="mlMoReferenceHelper"/>
  </bean>

  <bean class="com.adva.nlms.mediation.mltopologymodel.service.consistency.ServiceConsistencyCheckFacade" id="serviceConsistencyCheckFacade"/>

  <bean class="com.adva.nlms.mediation.mltopologymodel.service.consistency.checkers.MLServiceOperStateConsistencyChecker"/>

  <!--Class and New service Correlation Scheduler -->
  <bean class="com.adva.nlms.mediation.mltopologymodel.service.classicmlcorrelation.MLClassicNewCorrelationScheduler" id="mlClassicNewCorrelationScheduler"/>

  <!--Service Intent -->
  <bean class="com.adva.nlms.mediation.mltopologymodel.service.intent.ServiceIntentDAO"/>
  <bean class="com.adva.nlms.mediation.mltopologymodel.service.intent.facade.NEIdsForServiceProvider"/>

  <!-- Service notification create helpers -->
  <bean id="defaultServiceCreateNotificationHelper" class="com.adva.nlms.mediation.mltopologymodel.mofacade.notfhelpers.service.MLDefaultServiceCreateNotificationHelper"/>
  <bean class="com.adva.nlms.mediation.mltopologymodel.mofacade.notfhelpers.service.MLServiceCreateNotificationHelperFactory"/>

  <!-- Service notification update helpers -->
  <bean id="defaultServiceUpdateNotificationHelper" class="com.adva.nlms.mediation.mltopologymodel.mofacade.notfhelpers.service.MLDefaultServiceUpdateNotificationHelper"/>
  <bean class="com.adva.nlms.mediation.mltopologymodel.mofacade.notfhelpers.service.MLServiceUpdateNotificationHelperFactory"/>


  <import resource="pm/mediation-mltopology-service-pm-context.xml"/>
  <import resource="diagnostics/mediation-mltopology-service-diagnostics-context.xml"/>

  <!-- Protection related Domain Services -->
  <bean class="com.adva.nlms.mediation.mltopologymodel.service.protectionswitch.MLProtectionSwitchService"/>
</beans>