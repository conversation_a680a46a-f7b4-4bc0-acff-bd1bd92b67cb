<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright 2023 Adtran Networks SE. All rights reserved.
  ~
  ~ Owner: benjamint
  -->

<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
        http://www.springframework.org/schema/context
        http://www.springframework.org/schema/context/spring-context-3.0.xsd">
       <context:annotation-config/>
       <!-- Module Controller -->
       <bean class="com.adva.nlms.mediation.mltopologymodel.stateproc.operstate.MLOperStateCtrlImpl" />
       <bean class="com.adva.nlms.mediation.mltopologymodel.core.concurrent.MLConcurrentCtrlImpl" />
       <bean class="com.adva.nlms.mediation.mltopologymodel.core.notification.MLNotificationCtrlImpl" />

       <bean class="com.adva.nlms.mediation.mltopologymodel.mofacade.provisioning.MLProvisioningFacade" />
       <bean class="com.adva.nlms.mediation.mltopologymodel.mofacade.provisioning.f7.MLF7ProvisioningHdlr" />
       <bean class="com.adva.nlms.mediation.mltopologymodel.MLTopologyCtrlImpl"/>
       <bean id="mlServiceAdminStateController" class="com.adva.nlms.mediation.mltopologymodel.stateproc.adminstate.api.MLServiceAdminStateController"/>
       <bean id="connectionAdminStateChecker" class="com.adva.nlms.mediation.mltopologymodel.stateproc.adminstate.api.ConnectionAdminStateChecker"/>
       <bean id="connectionAdminStateProvider" class="com.adva.nlms.mediation.mltopologymodel.stateproc.adminstate.api.ConnectionAdminStateProvider"/>
       <bean id="dataAccessFacade" class="com.adva.nlms.mediation.mltopologymodel.stateproc.adminstate.mofacade.DataAccessFacadeDB"/>
       <bean class="com.adva.nlms.mediation.mltopologymodel.stateproc.adminstate.api.ServiceCreationAdminStateHandler" />
       <bean class="com.adva.nlms.mediation.mltopologymodel.stateproc.adminstate.api.LegacyMLAdminStateController"/>
       <bean class="com.adva.nlms.mediation.mltopologymodel.stateproc.adminstate.legacyimpl.BeanProvider"/>
       <bean class="com.adva.nlms.mediation.mltopologymodel.stateproc.operstate.MLOperStateOperationImpl"/>
       <bean class="com.adva.nlms.mediation.mltopologymodel.upgrade.MLPostInitUpgradeActionExecutor"/>
       <bean class="com.adva.nlms.mediation.mltopologymodel.servicetopologychecker.MLTopologyServiceTools"/>
       <bean class="com.adva.nlms.mediation.mltopologymodel.servicetopologychecker.MLTopologyCheckerUtils"/>
       <bean class="com.adva.nlms.mediation.mltopologymodel.servicetopologychecker.MLTopologyServiceMixedOrderChecker"/>
       <bean class="com.adva.nlms.mediation.mltopologymodel.servicetopologychecker.MLTopologyClassicServiceChecker"/>
       <bean class="com.adva.nlms.mediation.mltopologymodel.servicetopologychecker.MLTopologyClassicServiceDiagnosticChecker"/>
       <bean class="com.adva.nlms.mediation.mltopologymodel.servicetopologychecker.MLTopologyServiceAssociationChecker"/>
       <bean class="com.adva.nlms.mediation.mltopologymodel.servicetopologychecker.MLTopologyServiceDebugger"/>
       <bean class="com.adva.nlms.mediation.mltopologymodel.servicetopologychecker.MLTopologyMismatchChecker"/>
       <bean class="com.adva.nlms.mediation.mltopologymodel.api.MLServiceManagementFacade"/>
       <bean class="com.adva.nlms.mediation.mltopologymodel.api.MLNetworkResourceInventoryApiImpl"/>
       <bean class="com.adva.nlms.mediation.mltopologymodel.api.LogicalLinksApiImpl"/>

<!--
       <bean class="com.adva.nlms.mediation.mltopologymodel.resources.ServicePageHdlrImpl" id="lockedServicesPageHdlr">
              <constructor-arg type="com.adva.nlms.common.paging.PageArea" value="LOCKED_SERVICES"/>
       </bean>
       <bean class="com.adva.nlms.mediation.mltopologymodel.resources.ServicePageHdlrImpl" id="mfotServicesPageHdlr">
              <constructor-arg type="com.adva.nlms.common.paging.PageArea" value="MFOT_SERVICES"/>
       </bean>
-->
<!--
       <bean class="com.adva.nlms.mediation.mltopologymodel.resources.MLServicePageHdlrImpl" id="mlServicePageHdlr">
              <constructor-arg type="com.adva.nlms.common.paging.PageArea" value="SERVICES"/>
       </bean>
       <bean class="com.adva.nlms.mediation.mltopologymodel.resources.MLServicePageHdlrImpl" id="mlFaultedServicesPageHdlr">
              <constructor-arg type="com.adva.nlms.common.paging.PageArea" value="FAULTED_SERVICES"/>
       </bean>
-->
<!--
       <bean class="com.adva.nlms.mediation.mltopologymodel.resources.MLServicePageHdlr2Impl" id="mlLockedServicesPageHdlr2">
              <constructor-arg type="com.adva.nlms.common.paging.PageArea" value="LOCKED_SERVICES"/>
       </bean>
       <bean class="com.adva.nlms.mediation.mltopologymodel.resources.MLServicePageHdlr2Impl" id="mlMfotServicesPageHdlr2">
              <constructor-arg type="com.adva.nlms.common.paging.PageArea" value="MFOT_SERVICES"/>
       </bean>
-->

       <!--Lifecycle-->
       <bean class="com.adva.nlms.mediation.mltopologymodel.sync.lifecycle.MLLifecycleStateTransitionFactory"/>
       <bean class="com.adva.nlms.mediation.mltopologymodel.sync.lifecycle.MLLifecycleStateDAO"/>
       <bean class="com.adva.nlms.mediation.mltopologymodel.sync.lifecycle.DefaultLifecycleStateTransitionHelper" id="DefaultLifecycleStateTransitionHelper"/>

       <!--EndPoint-->
       <bean class="com.adva.nlms.mediation.mltopologymodel.sync.endpoint.MLEndPointHandler"/>
       <bean class="com.adva.nlms.mediation.mltopologymodel.sync.endpoint.MLConnectionEntitiesHandler"/>
       <bean class="com.adva.nlms.mediation.mltopologymodel.sync.endpoint.MLEodLinkHandler"/>
       <bean class="com.adva.nlms.mediation.mltopologymodel.sync.endpoint.MLNodeEdgePointHandler"/>
       <bean class="com.adva.nlms.mediation.mltopologymodel.sync.endpoint.MLModelBasedNodeEdgePointHandler"/>
       <bean class="com.adva.nlms.mediation.mltopologymodel.sync.endpoint.MLEndPointHelper"/>

       <bean class="com.adva.nlms.mediation.mltopologymodel.sync.extension.uiview.MLEodUiServiceViewCompactCepHelper" id="mlEodUiServiceViewCompactCepHelper"/>

       <!-- -->
       <bean id="mlMoReferenceHelper" class="com.adva.nlms.mediation.mltopologymodel.mofacade.MLMoReferenceHelper"/>
       <bean id="mlMoNotificationHdlr" class="com.adva.nlms.mediation.mltopologymodel.mofacade.MLMoNotificationHdlr" init-method="init"/>
       <bean class="com.adva.nlms.mediation.mltopologymodel.core.concurrent.MLTaskExecutorService2"/>
       <bean class="com.adva.nlms.mediation.mltopologymodel.mofacade.resync.LifecycleTransitionPostCommitHelper" id="LifecycleTransitionPostCommitHelper"/>
       <bean class="com.adva.nlms.mediation.mltopologymodel.mofacade.resync.MLCustomNameHandler" id="mlCustomNameHandler"/>
       <bean class="com.adva.nlms.mediation.mltopologymodel.mofacade.resync.AlarmResyncHelper" />
       <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.MLTopologyMoFacade" init-method="init" id = "mlTopologyMoFacade"/>
       <bean class="com.adva.nlms.mediation.mltopologymodel.model.dao.MLTopologySpecificNativeDAO" init-method="init"/>
       <bean class="com.adva.nlms.mediation.mltopologymodel.model.dao.MLTrailDAO"/>
       <bean class="com.adva.nlms.mediation.mltopologymodel.model.dao.MLTopologyLayerCustomNameDAO"/>
       <bean class="com.adva.nlms.mediation.mltopologymodel.model.dao.NrimLogicalLinkDAO"/>
       <bean class="com.adva.nlms.mediation.mltopologymodel.sync.entity.factory.ResyncNrimLogicalLinkTaskFactory"/>
       <bean class="com.adva.nlms.mediation.mltopologymodel.sync.entity.factory.NrimKafkaNotificationTaskFactory"/>
       <bean id="mlTopologyElementDAO" class="com.adva.nlms.mediation.mltopologymodel.model.dao.MLTopologyElementDAO" init-method="init"/>
       <bean class="com.adva.nlms.mediation.mltopologymodel.model.dao.MLProtectionDAO" />
       <bean class="com.adva.nlms.mediation.mltopologymodel.model.dao.MLNetworkResourceInventoryDAO" />
       <bean class="com.adva.nlms.mediation.mltopologymodel.model.dao.MLNILinkDAO"/>
       <bean id="pmProfileCounterDAO" class="com.adva.nlms.mediation.mltopologymodel.serviceDashboard.pmprofilecounter.PmProfileCounterDAO" init-method="init"/>
       <bean id="pmProfileDAO" class="com.adva.nlms.mediation.mltopologymodel.serviceDashboard.pmprofile.PmProfileDAO" init-method="init"/>
       <bean class="com.adva.nlms.mediation.mltopologymodel.mofacade.MLTopologyResyncFacade" init-method="init" id="mlTopologyResyncFacade"/>
       <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.builder.DefaultMoHelper"/>

       <!--Notification Observers-->
       <bean class="com.adva.nlms.mediation.interfaces.sdn.impl.observers.CustomerServiceGroupObserver">
              <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
       </bean>
       <bean class="com.adva.nlms.mediation.interfaces.sdn.impl.observers.CustomerObserver">
              <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
       </bean>
       <bean class="com.adva.nlms.mediation.interfaces.sdn.impl.observers.OchConnectionObserver">
              <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
       </bean>
       <bean class="com.adva.nlms.mediation.interfaces.sdn.impl.sm.observers.PortExtensionObserver">
              <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
       </bean>
       <bean class="com.adva.nlms.mediation.interfaces.sdn.impl.observers.LineObserver">
              <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
       </bean>
       <bean class="com.adva.nlms.mediation.mltopologymodel.core.notification.observer.MLNeObserver" id="MLNeObserver"/>
       <bean class="com.adva.nlms.mediation.mltopologymodel.core.notification.observer.MLTopoNotfObserver" id="MLTopoNotfObserver"/>
       <bean class="com.adva.nlms.mediation.mltopologymodel.core.notification.observer.MLTopoModuleObserver" id="MLTopoModuleObserver">
              <constructor-arg ref="mlMoNotificationHdlr"/>
       </bean>
       <bean class="com.adva.nlms.mediation.mltopologymodel.mofacade.notfhelpers.MLOperStateEventCache" id="MLOperStateEventCache">
              <constructor-arg ref="mlTopologyElementDAO"/>
       </bean>
<!--       <bean class="com.adva.nlms.mediation.mltopologymodel.mofacade.notfhelpers.MLOperStateObserver" id="MLOperStateObserver"/>-->
       <bean class="com.adva.nlms.mediation.mltopologymodel.core.notification.observer.MLServiceStateCounterObserver" id="MLServiceStateCounterObserver"/>
       <bean class="com.adva.nlms.mediation.mltopologymodel.core.notification.observer.ModuleManagementF7MoObserver" id="ModuleManagementF7MoObserver"/>
       <bean class="com.adva.nlms.mediation.mltopologymodel.core.notification.observer.ModuleManagementF8MoObserver" id="ModuleManagementF8MoObserver"/>
       <bean class="com.adva.nlms.mediation.mltopologymodel.core.notification.observer.ModuleManagementRouterMoObserver" id="ModuleManagementRouterMoObserver"/>
       <bean class="com.adva.nlms.mediation.mltopologymodel.core.notification.observer.ModuleManagementOpticalRouterMoObserver" id="ModuleManagementOpticalRouterMoObserver"/>
       <bean class="com.adva.nlms.mediation.mltopologymodel.core.notification.observer.MLPortMoObserver" id="MLPortMoObserver"/>
       <bean class="com.adva.nlms.mediation.mltopologymodel.core.notification.observer.MLServiceSMObserver" id="MLServiceSMObserver"/>
       <!--<bean class="com.adva.nlms.mediation.mltopologymodel.mofacade.notfhelpers.MLCrsMoObserver" id="MLCrsMoObserver"/>-->
       <bean class="com.adva.nlms.mediation.mltopologymodel.core.notification.observer.MLAttribMoObserver" id="MLAttribMoObserver"/>
       <bean class="com.adva.nlms.mediation.mltopologymodel.core.notification.observer.MLSecondaryStateObserver" />
       <bean class="com.adva.nlms.mediation.mltopologymodel.core.notification.observer.MLVirtualOtnPortObserver" />
       <bean class="com.adva.nlms.mediation.mltopologymodel.helpers.NeResponseStatusHelper" id="neResponseStatusHelper"/>
       <bean class="com.adva.nlms.mediation.mltopologymodel.core.notification.observer.MLNeResponseStateListener" id="mlNeResponseStateListener"/>
        <bean class="com.adva.nlms.mediation.mltopologymodel.core.notification.observer.NrimLogicalLinkObserver" />

       <!-- F8 protection resync -->
       <bean class="com.adva.nlms.mediation.mltopologymodel.f8resyncprotection.F8ResyncProtectionObserver" id="F8ResyncProtectionObserver"/>
       <bean class="com.adva.nlms.mediation.mltopologymodel.f8resyncprotection.F8ResyncProtectionRepository" id="F8ResyncProtectionRepository"/>
       <bean class="com.adva.nlms.mediation.mltopologymodel.f8resyncprotection.F8ResyncProtectionModelImpl" id="f8ResyncProtectionModelPort"/>

       <!-- Helpers for notification observers -->
       <bean class="com.adva.nlms.mediation.mltopologymodel.mofacade.notfhelpers.MLServiceResyncTaskHelper"/>
       <bean class="com.adva.nlms.mediation.mltopologymodel.mofacade.notfhelpers.OpticalServiceGuiNotifier"/>
       <bean class="com.adva.nlms.mediation.mltopologymodel.mofacade.notfhelpers.tasks.MLTopoDbChgNotifHdlr"/>
       <bean class="com.adva.nlms.mediation.mltopologymodel.helpers.MLHelperFacade" id="mlHelperFacade"/>
       <bean class="com.adva.nlms.mediation.mltopologymodel.helpers.MLBandwidthProfileHelper" id = "mlBandwidthProfileHelper"/>
       <bean class="com.adva.nlms.mediation.mltopologymodel.helpers.MLEodLayerProtocolHelper" />


       <!--
       DDM Observers are currently unused. Remove comment in the future for BT Packet SDN support.
       <bean class="com.adva.nlms.mediation.ddm.mofacade.notfhelpers.f3.DriverMOObservers"/>
       <bean class="com.adva.nlms.mediation.ddm.mofacade.notfhelpers.f3.SyncNodeMoObserver"/>
       -->

      <!-- NE mo helpers -->
       <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.builder.LineMoHelper" id="lineMoHelper"/>

       <!-- Helper Class -->
       <bean class="com.adva.nlms.mediation.mltopologymodel.helpers.MLF7Utils" id = "MLF7Utils"/>

       <!-- Layer Extension Helpers -->
       <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.builder.layerExtension.DefaultLayerExtensionHelper" id = "defaultLayerExtensionHelper"/>
       <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.builder.layerExtension.WdmLayersExtensionHelper" id = "wdmLayersExtensionHelper"/>

       <!-- Resync Helpers -->
       <import resource="mofacade/resync/mediation-mltopology-resync-context.xml"/>

       <!-- Service Helpers -->
       <import resource="service/mediation-mltopology-service-context.xml"/>

       <import resource="mofacade/modto/helpers/fsp1500/mediation-mltopology-fsp1500-context.xml"/>
       <import resource="mofacade/modto/helpers/uno/mediation-mltopology-uno-context.xml"/>
       <import resource="mofacade/modto/helpers/f7/mediation-mltopology-f7-context.xml"/>
       <import resource="mofacade/modto/helpers/f8/mediation-mltopology-f8-context.xml"/>
       <import resource="mofacade/modto/helpers/opticalRouter/mediation-mltopology-opticalRouter-context.xml"/>
       <import resource="mofacade/modto/helpers/juniper/mediation-mltopology-juniper-context.xml"/>
       <import resource="utilities/mediation-mltopology-utilities-context.xml"/>

       <!--Support for Monitoring Facades-->
       <import resource="mofacade/monitoring/mediation-mltopology-monitoring-context.xml"/>
       <!--Support for Processing strategies -->
       <import resource="mofacade/procstrategy/mediation-mltopology-proc-context.xml"/>
       <!-- Support for processes running dbconsistency automatically in the background -->
       <import resource="dbconsistency/mediation-mltopology-dbconsistency-context.xml"/>
</beans>
