<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">


    <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.builder.f7.ne.F7NeMoHelper" id = "F7NeMoHelper" lazy-init="true"/>
    <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.builder.f7.modules.F7ModuleMoHelper" id = "F7ModuleMoHelper" lazy-init="true"/>
    <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.builder.f7.performance.F7PerformanceMonitoringMoHelper" id = "F7PerformanceMonitoringMoHelper" lazy-init="true">
      <constructor-arg type="com.adva.nlms.mediation.mltopologymodel.model.dao.MLTopologyElementDAO" ref="mlTopologyElementDAO"/>
    </bean>


    <!-- ============== Connection point Mo Helpers ================== -->
    <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.builder.f7.connectionPoints.F7ConnectionPointMoHelperChooser" id = "F7ConnectionPointMoHelperChooser" lazy-init="true"/>
    <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.builder.f7.connectionPoints.DefaultF7ConnectionPointMoHelper" id = "DefaultF7ConnectionPointMoHelper" init-method="init" lazy-init="true"/>
    <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.builder.f7.connectionPoints.F7ConnectionPointAmplifierMoHelper" id = "F7ConnectionPointAmplifierMoHelper" init-method="init" lazy-init="true"/>
    <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.builder.f7.connectionPoints.F7ConnectionPointAttMoHelper" id = "F7ConnectionPointAttMoHelper" init-method="init" lazy-init="true"/>
    <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.builder.f7.connectionPoints.F7ConnectionPointCableMoHelper" id = "F7ConnectionPointCableMoHelper" init-method="init" lazy-init="true"/>
    <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.builder.f7.connectionPoints.F7ConnectionPointYCableMoHelper" id = "F7ConnectionPointYCableMoHelper" init-method="init" lazy-init="true"/>
    <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.builder.f7.connectionPoints.F7ConnectionPointChannelMoHelper" id = "F7ConnectionPointChannelMoHelper" init-method="init" lazy-init="true"/>
    <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.builder.f7.connectionPoints.F7ConnectionPointDCMMoHelper" id = "F7ConnectionPointDCMMoHelper" init-method="init" lazy-init="true"/>
    <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.builder.f7.connectionPoints.F7ConnectionPointFilter10CsmMoHelper" id = "F7ConnectionPointFilter10CsmMoHelper" init-method="init" lazy-init="true"/>
    <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.builder.f7.connectionPoints.F7ConnectionPointFilterMoHelper" id = "F7ConnectionPointFilterMoHelper" init-method="init" lazy-init="true"/>
    <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.builder.f7.connectionPoints.F7ConnectionPointOscmMoHelper" id = "F7ConnectionPointOscmMoHelper" init-method="init" lazy-init="true"/>
    <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.builder.f7.connectionPoints.F7ConnectionPointOsfmMoHelper" id = "F7ConnectionPointOsfmMoHelper" init-method="init" lazy-init="true"/>
    <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.builder.f7.connectionPoints.F7ConnectionPointRoadmMoHelper" id = "F7ConnectionPointRoadmMoHelper" init-method="init" lazy-init="true"/>
    <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.builder.f7.connectionPoints.F7ConnectionPointXCMoHelper" id = "F7ConnectionPointXCMoHelper" init-method="init" lazy-init="true"/>
    <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.builder.f7.connectionPoints.F7ConnectionPointPsmMoHelper" id = "F7ConnectionPointPsmMoHelper" init-method="init" lazy-init="true"/>
    <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.builder.f7.connectionPoints.F7ConnectionPointPsmRoadmMoHelper" id = "F7ConnectionPointPsmRoadmMoHelper" init-method="init" lazy-init="true"/>
    <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.builder.f7.connectionPoints.F7ConnectionPointMicroTermMoHelper" id = "F7ConnectionPointMicroTermMoHelper" init-method="init" lazy-init="true"/>
    <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.builder.f7.connectionPoints.F7ConnectionPointProtectionMoHelper" id = "F7ConnectionPointProtectionMoHelper" init-method="init" lazy-init="true"/>
    <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.builder.f7.connectionPoints.F7ConnectionPointSwitchMoHelper" id = "F7ConnectionPointSwitchMoHelper" init-method="init" lazy-init="true"/>
    <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.builder.f7.connectionPoints.connectionPointChannelMoHelpers.F7ConnectionPoint9TCEMoHelper" id = "F7ConnectionPoint9TCEMoHelper" init-method="init" lazy-init="true"/>
    <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.builder.f7.connectionPoints.connectionPointChannelMoHelpers.F7ConnectionPointMuxMoHelper" id = "F7ConnectionPointMuxMoHelper" init-method="init" lazy-init="true"/>
    <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.builder.f7.connectionPoints.connectionPointChannelMoHelpers.F7ConnectionPointGfpMuxMoHelper" id = "F7ConnectionPointGfpMuxMoHelper" init-method="init" lazy-init="true"/>
    <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.builder.f7.connectionPoints.connectionPointChannelMoHelpers.F7ConnectionPointADMChannelMoHelper" id = "F7ConnectionPointADMChannelMoHelper" init-method="init" lazy-init="true"/>
    <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.builder.f7.connectionPoints.connectionPointChannelMoHelpers.F7ConnectionPointHybridF8MoHelper" id = "F7ConnectionPointHybridF8MoHelper" init-method="init" lazy-init="true"/>
    <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.builder.f7.connectionPoints.connectionPointChannelMoHelpers.F7ConnectionPointHybridF8MetroFlexMoHelper" init-method="init" lazy-init="true"/>
    <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.builder.f7.connectionPoints.connectionPointChannelMoHelpers.F7ConnectionPointHybridF8OpenFab1200MoHelper" init-method="init" lazy-init="true"/>
    <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.builder.f7.connectionPoints.connectionPointChannelMoHelpers.F7ConnectionPointHybridF8SFlexMoHelper" init-method="init" lazy-init="true"/>
    <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.builder.f7.connectionPoints.connectionPointChannelMoHelpers.F7ConnectionPointHybridF8VCHMoHelper" id = "F7ConnectionPointHybridF8VCHMoHelper" init-method="init" lazy-init="true"/>
    <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.builder.f7.connectionPoints.connectionPointChannelMoHelpers.F7ConnectionPointOtnMuxMoHelper" id = "F7ConnectionPointOtnMuxMoHelper" init-method="init" lazy-init="true"/>
    <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.builder.f7.connectionPoints.connectionPointChannelMoHelpers.F7ConnectionPointOtnMuxMoHelperTI" id = "F7ConnectionPointOtnMuxMoHelperTI" init-method="init" lazy-init="true"/>
    <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.builder.f7.connectionPoints.connectionPointChannelMoHelpers.F7ConnectionPointFixedCustomMuxMoHelper" id = "F7ConnectionPointFixedCustomMuxMoHelper" init-method="init" lazy-init="true"/>
    <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.builder.f7.connectionPoints.connectionPointChannelMoHelpers.F7ConnectionPoint2TWCC2G7MoHelper" id = "F7ConnectionPoint2TWCC2G7MoHelper" init-method="init" lazy-init="true"/>
    <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.builder.f7.connectionPoints.connectionPointChannelMoHelpers.F7ConnectionPoint4TCC2G5MoHelper" id = "F7ConnectionPoint4TCC2G5MoHelper" init-method="init" lazy-init="true"/>
    <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.builder.f7.connectionPoints.connectionPointChannelMoHelpers.F7ConnectionPoint4TCC100GMoHelper" id = "F7ConnectionPoint4TCC100GMoHelper" init-method="init" lazy-init="true"/>
    <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.builder.f7.connectionPoints.F7ConnectionPointPathSplitterMoHelper" id = "F7ConnectionPointPathSplitterMoHelper" init-method="init" lazy-init="true"/>

    <!-- ============== Module Mo Helpers ================== -->
    <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.builder.f7.modules.AbstractF7ModuleMoHelper" id = "AbstractF7ModuleMoHelper" lazy-init="true"/>
    <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.builder.f7.modules.F7ModuleAmpMoHelper" id = "F7ModuleAmpMoHelper" lazy-init="true"/>
    <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.builder.f7.modules.F7ModuleCableMoHelper" id = "F7ModuleCableMoHelper" lazy-init="true"/>
    <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.builder.f7.modules.F7ModuleYCableMoHelper" id = "F7ModuleYCableMoHelper" lazy-init="true"/>
    <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.builder.f7.modules.F7ModuleChannelMoHelper" id = "F7ModuleChannelMoHelper" lazy-init="true"/>
    <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.builder.f7.modules.F7ModuleDCMMoHelper" id = "F7ModuleDCMMoHelper" lazy-init="true"/>
    <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.builder.f7.modules.F7ModuleFilterMoHelper" id = "F7ModuleFilterMoHelper" lazy-init="true"/>
    <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.builder.f7.modules.F7ModuleFilter8CSMUMoHelper" id = "F7ModuleFilter8CSMUMoHelper" lazy-init="true"/>
    <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.builder.f7.modules.F7ModuleFilter1CSMUMoHelper" id = "F7ModuleFilter1CSMUMoHelper" lazy-init="true"/>
    <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.builder.f7.modules.F7ModuleOsfmMoHelper" id = "F7ModuleOsfmMoHelper" lazy-init="true"/>
    <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.builder.f7.modules.F7ModuleOtdrMoHelper" id = "F7ModuleOtdrMoHelper" lazy-init="true"/>
    <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.builder.f7.modules.F7ModuleProtectionMoHelper" id = "F7ModuleProtectionMoHelper" lazy-init="true"/>
    <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.builder.f7.modules.F7ModulePsmMoHelper" id = "F7ModulePsmMoHelper" lazy-init="true"/>
    <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.builder.f7.modules.F7ModuleRoadmMoHelper" id = "F7ModuleRoadmMoHelper" lazy-init="true"/>
    <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.builder.f7.modules.F7ModuleMicroRoadmMoHelper" id = "F7ModuleMicroRoadmMoHelper" lazy-init="true"/>
    <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.builder.f7.modules.F7ModulePsmMroadmMoHelper" id = "F7ModulePsmMroadmMoHelper" lazy-init="true" init-method="init"/>
    <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.builder.f7.modules.F7ModuleMicroTermMoHelper" id = "F7ModuleMicroTermMoHelper" lazy-init="true"/>
    <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.builder.f7.modules.F7ModuleSwitchMoHelper" id = "F7ModuleSwitchMoHelper" lazy-init="true"/>
    <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.builder.f7.modules.F7ModuleXcMoHelper" id = "F7ModuleXcMoHelper" lazy-init="true"/>
    <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.builder.f7.modules.F7ModuleAttMoHelper" id = "F7ModuleAttMoHelper" lazy-init="true"/>
    <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.builder.f7.modules.F7ModulePathSplitterMoHelper" id = "F7ModulePathSplitterMoHelper" lazy-init="true"/>

    <!-- ============== Channel CrossConnect Helpers ================== -->
    <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.builder.f7.modules.crossconnects.F7CrossConnectRegenHelper" id = "F7CrossConnectRegenHelper" lazy-init="true"/>
    <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.builder.f7.modules.crossconnects.F7CrossConnectADMMuxponderHelper" id = "F7CrossConnectADMMuxponderHelper" lazy-init="true"/>
    <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.builder.f7.modules.crossconnects.F7CrossConnectF8MuxponderHelper" id = "F7CrossConnectF8MuxponderHelper" lazy-init="true"/>
    <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.builder.f7.modules.crossconnects.F7CrossConnectTransponderHelper" id = "F7CrossConnectTransponderHelper" lazy-init="true"/>
    <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.builder.f7.modules.crossconnects.F7CrossConnectMuxMoHelper" id = "F7CrossConnectMuxMoHelper" lazy-init="true"/>
    <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.builder.f7.modules.crossconnects.F7CrossConnectFixedCustomMuxHelper" id = "F7CrossConnectFixedCustomMuxHelper" lazy-init="true"/>
    <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.builder.f7.modules.crossconnects.F7CrossConnect4WCC10GHelper" id = "F7CrossConnect4WCC10GHelper" lazy-init="true"/>
    <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.builder.f7.modules.crossconnects.F7CrossConnect6WCA28GHelper" id = "F7CrossConnect6WCA28GHelper" lazy-init="true"/>
    <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.builder.f7.modules.crossconnects.F7CrossConnect2TWCC2G7Helper" id = "F7CrossConnect2TWCC2G7Helper" lazy-init="true" init-method="init"/>
    <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.builder.f7.modules.crossconnects.F7CrossConnectPsmMroadmHelper" id = "F7CrossConnectPsmMroadmHelper" lazy-init="true"/>
    <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.builder.f7.modules.crossconnects.F7CrossConnect4WCC100GHelper" id = "F7CrossConnect4WCC100GHelper" lazy-init="true"/>

    <!-- ============== Port MO Helpers ================== -->
    <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.builder.f7.ports.F7ProtectionSwitchMoHelper" id="f7ProtectionSwitchMoHelper"/>


    <!-- ============== Utilities ================== -->
    <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.builder.f7.utils.F7VchUtils" init-method="init" lazy-init="true"/>
    <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.builder.f7.connectionPoints.paych.F7PayChDecorator" lazy-init="true"/>

    <!--================ Channel VCH connection point helpers ===================== -->
    <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.builder.f7.connectionPoints.channelvch.F7VchChannelGfpHelper" id="F7VchChannelGfpHelper"/>
    <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.builder.f7.connectionPoints.channelvch.F7VchChannelNetworkHelper" id="F7VchChannelNetworkHelper"/>

    <!--================ Network OTL connection point helpers ===================== -->
    <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.builder.f7.connectionPoints.otl.F7OtlConnPointHelper" lazy-init="true"/>


</beans>
