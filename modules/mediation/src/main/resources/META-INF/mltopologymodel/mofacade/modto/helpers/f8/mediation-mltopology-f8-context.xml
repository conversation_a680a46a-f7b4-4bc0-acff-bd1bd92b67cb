<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~  Copyright 2023 Adtran Networks SE. All rights reserved.
  ~
  ~  Owner: mohitc
  -->

<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

    <!-- ============== Device Mo Helper ================== -->
    <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.builder.f8.F8NeMoHelper" id = "F8NeMoHelper" lazy-init="true"/>

    <!-- ============== Module Mo Helpers ================== -->
    <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.builder.f8.F8ChannelModuleMoHelper" id = "F8ChannelModuleMoHelper" lazy-init="true"/>
    <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.builder.f8.F8AccessFlexMoHelper" id = "F8AccessFlexMoHelper" lazy-init="true"/>
    <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.builder.f8.F8MetroFlexMoHelper" id = "F8MetroFlexMoHelper" lazy-init="true"/>
    <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.builder.f8.F8RoadmMoHelper" id = "F8RoadmMoHelper" lazy-init="true"/>
    <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.builder.f8.F8AmplifierMoHelper" id = "F8AmplifierMoHelper" lazy-init="true"/>
    <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.builder.f8.F8SwitchModuleMoHelper" id = "F8SwitchModuleMoHelper" lazy-init="true"/>
    <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.builder.f8.F8AlienCardMoHelper" id = "F8AlienCardMoHelper" lazy-init="true"/>
    <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.builder.f8.F8ShuffleModuleMoHelper" id = "F8ShuffleModuleMoHelper" lazy-init="true"/>
    <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.builder.f8.F8SplitterCouplerMoHelper" id = "F8SplitterCouplerMoHelper" lazy-init="true"/>
    <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.builder.f8.F8PathSplitterMoHelper" id = "F8PathSplitterMoHelper" lazy-init="true"/>
    <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.builder.f8.F8FilterMoHelper" id = "F8FilterMoHelper" lazy-init="true"/>
    <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.builder.f8.F8CarrierModuleMoHelper" id = "F8CarrierModuleMoHelper" lazy-init="true"/>
    <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.builder.f8.F8ProtectionSwitchMoHelper" id = "F8ProtectionSwitchMoHelper" lazy-init="true"/>
    <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.builder.f8.F8BroadcastModuleMoHelper" id = "F8BroadcastModuleMoHelper" lazy-init="true"/>
    <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.builder.f8.F8YCableMoHelper" id = "F8YCableMoHelper" lazy-init="true"/>
    <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.builder.f8.F8CimValuesCollector" id = "f8CimValuesCollector"/>
    <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.builder.f8.parameter.CimValueExtractor" id = "cimValueExtractor"/>
</beans>