<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">


    <bean class="com.adva.nlms.mediation.mltopologymodel.mofacade.monitoring.f7.MLF7MonitoringFacade"
          id="mlF7MonitoringFacade" lazy-init="true">
        <constructor-arg ref="powerLevelFacade"/>
    </bean>

    <bean class="com.adva.nlms.mediation.mltopologymodel.mofacade.monitoring.MLF8MonitoringFacade"
          id="mlF8MonitoringFacade" lazy-init="true">
    </bean>

    <bean class="com.adva.nlms.mediation.mltopologymodel.mofacade.monitoring.MonitoringStrategyManager"
          id="MonitoringStrategyManager" lazy-init="true">
        <constructor-arg ref="mlF7MonitoringFacade"/>
    </bean>

</beans>