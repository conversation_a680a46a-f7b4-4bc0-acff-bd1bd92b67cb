<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">


       <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.factory.MLF7ProcessingStrategy" id="MLF7ProcessingStrategy" lazy-init="true"/>
       <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.factory.MLOpticalRouterProcessingStrategy" id="MLOpticalRouterProcessingStrategy" lazy-init="true"/>
       <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.builder.f8.MLF8ProcessingStrategy" id="MLF8ProcessingStrategy" lazy-init="true"/>
       <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.factory.MLUnoProcessingStrategy" id="MLUnoProcessingStrategy" lazy-init="true"/>
       <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.factory.MLFsp1500ProcessingStrategy" id="MLFsp1500ProcessingStrategy" lazy-init="true"/>
       <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.factory.MLJuniperProcessingStrategy" id="MLJuniperProcessingStrategy" lazy-init="true"/>
       <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.factory.MLF4ProcessingStrategy" id="MLF4ProcessingStrategy" lazy-init="true"/>
       <bean class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.factory.MLUnknownProcessingStrategy" id="MLUnknownProcessingStrategy" lazy-init="true"/>
       <bean id="processingStrategyManager" class="com.adva.nlms.mediation.mltopologymodel.momediation.proc.ProcessingStrategyManager" init-method="init" lazy-init="true"/>

</beans>