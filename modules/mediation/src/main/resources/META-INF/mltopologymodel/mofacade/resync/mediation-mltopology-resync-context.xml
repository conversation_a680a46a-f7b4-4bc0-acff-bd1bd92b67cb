<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright 2023 Adtran Networks SE. All rights reserved.
  ~
  ~ Owner: benjamint
  -->

<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">


  <!-- Resync Helpers -->
  <bean class="com.adva.nlms.mediation.mltopologymodel.sync.entity.worker.AbstractNodeResyncHelper" id="AbstractNodeResyncHelper"/>
  <bean class="com.adva.nlms.mediation.mltopologymodel.sync.entity.worker.AbstractModuleResyncHelper" id="AbstractModuleResyncHelper"/>
  <bean class="com.adva.nlms.mediation.mltopologymodel.sync.entity.worker.AbstractConnectionPointResyncHelper" init-method="init" id="AbstractConnectionPointResyncHelper"/>
  <bean class="com.adva.nlms.mediation.mltopologymodel.sync.entity.worker.AbstractLinkResyncHelper" id="AbstractLinkResyncHelper"/>
  <bean class="com.adva.nlms.mediation.mltopologymodel.sync.entity.worker.AbstractCrossConnectResyncHelper" id="AbstractCrossConnectResyncHelper"/>
  <bean class="com.adva.nlms.mediation.mltopologymodel.sync.path.worker.AbstractTrailResyncHelper" id="AbstractTrailResyncHelper"/>
  <bean class="com.adva.nlms.mediation.mltopologymodel.sync.path.worker.MtpServiceResyncHelper" id="MtpServiceResyncHelper"/>
  <bean class="com.adva.nlms.mediation.mltopologymodel.sync.entity.worker.AbstractAdaptationResyncHelper" id="AbstractAdaptationResyncHelper"/>
  <bean class="com.adva.nlms.mediation.mltopologymodel.mofacade.resync.AbstractSegmentAdaptationResyncHelper" id="AbstractSegmentAdaptationResyncHelper"/>
  <bean class="com.adva.nlms.mediation.mltopologymodel.mofacade.resync.AbstractPathMonitoringSectionResyncHelper" id="AbstractPathMonitoringSectionResyncHelper"/>

  <bean class="com.adva.nlms.mediation.mltopologymodel.sync.path.worker.AbstractTrailSimpleResyncHelper" id="AbstractTrailSimpleResyncHelper" lazy-init="true"/>

  <bean class="com.adva.nlms.mediation.mltopologymodel.mofacade.resync.BandwidthProfileResyncHelper" id="BandwidthProfileResyncHelper"/>
  <bean class="com.adva.nlms.mediation.mltopologymodel.mofacade.resync.PlannedNodeResyncHelper" id="PlannedNodeResyncHelper"/>

  <bean class="com.adva.nlms.mediation.mltopologymodel.sync.extension.uiview.AbstractUiServiceViewResyncHelper" id="abstractUiServiceViewResyncHelper">
    <constructor-arg ref="mlTopologyElementDAO"/>
    <constructor-arg ref="mlMoReferenceHelper"/>
    <constructor-arg ref="mlServiceHelper"/>
    <constructor-arg ref="connectionDTOBuilderFactory"/>
    <constructor-arg ref="mlEodUiServiceViewCompactCepHelper"/>
  </bean>

</beans>
