<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
        http://www.springframework.org/schema/context
        http://www.springframework.org/schema/context/spring-context-3.0.xsd">

    <context:annotation-config/>

    <bean class="com.adva.nlms.mediation.ne_comm.ec.websocket.ECWebSocketOrchestrator"/>

    <bean id="f8RestBroker" class="com.adva.nlms.mediation.ec.neComm.rest.EcRestBroker"/>

    <bean class="com.adva.nlms.mediation.ne_comm.f8.rest.F8RestClientCreator">
        <constructor-arg ref="f8RestBroker"/>
    </bean>

    <bean class="com.adva.nlms.mediation.ne_comm.ec.websocket.CreateECWebSocketPolling">
        <qualifier type="com.adva.nlms.mediation.polling.DomainPolling"/>
    </bean>

    <bean class="com.adva.nlms.mediation.ec.neComm.rest.RecreateMissedEventRestPolling">
        <qualifier type="com.adva.nlms.mediation.polling.DomainPolling"/>
    </bean>

    <bean class="com.adva.nlms.mediation.ec.neComm.rest.HttpSessionKeepAlivePolling">
        <qualifier type="com.adva.nlms.mediation.polling.DomainPolling"/>
    </bean>

    <bean class="com.adva.nlms.mediation.ne_comm.ec.nedata.EcNeSystemTimeHelper"/>
</beans>