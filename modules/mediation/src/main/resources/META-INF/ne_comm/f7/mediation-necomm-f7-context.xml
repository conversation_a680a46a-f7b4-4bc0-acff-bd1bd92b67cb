<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
        http://www.springframework.org/schema/context
        http://www.springframework.org/schema/context/spring-context-3.0.xsd">

    <context:annotation-config/>

    <!-- krystian verify if those should be separate public handlers or something below -->
    <bean class="com.adva.nlms.mediation.config.fsp_r7.cp.OtnPathsSnmpProvisioning"/>
    <bean class="com.adva.nlms.mediation.config.fsp_r7.cp.OtnTunnelSnmpProvisioning" />

    <!-- snmp facades -->
    <bean class="com.adva.nlms.mediation.ne_comm.f7.cp.OtnTunnelSnmpFacade" />
    <bean class="com.adva.nlms.mediation.ne_comm.f7.cp.OtnCnxSnmpFacade" />
    <bean class="com.adva.nlms.mediation.ne_comm.f7.cp.OtnPathElementSnmpFacade" />
    <bean class="com.adva.nlms.mediation.ne_comm.f7.cp.OtnPathSnmpFacade" />
    <bean class="com.adva.nlms.mediation.ne_comm.f7.cp.DefaultControlPlaneSnmpFacade" />

    <!-- snmp value mappers -->
    <bean name="otnModification" class="com.adva.nlms.mediation.ne_comm.f7.cp.binders.OtnTunnelModificationSnmpValueBinder" />
    <bean name="otnCreation" class="com.adva.nlms.mediation.ne_comm.f7.cp.binders.OtnTunnelSnmpValueBinder" />

    <!-- utils -->
    <bean id="cpOtnUtils" class="com.adva.nlms.mediation.ne_comm.f7.cp.utils.CpOtnUtils" factory-method="getInstance" />
    <bean id="cpUtils" class="com.adva.nlms.mediation.ne_comm.f7.cp.utils.CpUtils" factory-method="getInstance" />

    <!-- privilege change -->
    <bean class="com.adva.nlms.mediation.ne_comm.f7.privileges.PrivilegeChangeConstantsF7"/>

    <bean class="com.adva.nlms.mediation.ne_comm.f7.cp.rest.CpRestClientFactory"/>
    <bean class="com.adva.nlms.mediation.ne_comm.f7.cp.rest.CpRestBroker"/>

    <bean id="f7RestConnectionChecker" class="com.adva.nlms.mediation.ne_comm.f7.cp.rest.F7RestConnectionChecker">
        <constructor-arg ref="com.adva.nlms.mediation.ne_comm.f7.cp.rest.CpRestBroker" />
    </bean>
</beans>