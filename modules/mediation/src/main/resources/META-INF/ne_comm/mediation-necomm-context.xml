<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright 2023 Adtran Networks SE. All rights reserved.
  ~
  ~ Owner: <PERSON>z<PERSON>
  -->

<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
        http://www.springframework.org/schema/context
        http://www.springframework.org/schema/context/spring-context-3.0.xsd">

    <!-- com.adva.nlms.mediation -->

    <context:annotation-config/>

    <bean class="com.adva.nlms.mediation.ne_comm.NECommCtrlImpl" id="neCommCtrl"/>
    <bean id="statusPollingF3SNMPCtrl" class="com.adva.nlms.mediation.ne_comm.StatusPollingF3SNMPCtrlImpl" scope="prototype"/>
    <bean id="statusPollingOSA5548CCtrl" class="com.adva.nlms.mediation.ne_comm.StatusPollingOSA5548CCtrlImpl" scope="prototype"/>
    <bean id="statusPollingEGMSNMPCtrl" class="com.adva.nlms.mediation.ne_comm.StatusPollingEGMSNMPCtrlImpl" scope="prototype"/>
    <bean id="statusPollingOSA540XSNMPCtrl" class="com.adva.nlms.mediation.ne_comm.StatusPollingOSA540XSNMPCtrlImpl" scope="prototype"/>

    <bean class="com.adva.nlms.mediation.ne_comm.configuration.snmp.SNMPHandler"/>
    <bean class="com.adva.nlms.mediation.ne_comm.configuration.snmp.SNMPHandlerRESTController"/>
    <bean class="com.adva.nlms.mediation.ne_comm.session.DcnSessionController"/>
    <bean class="com.adva.nlms.mediation.ne_comm.configuration.snmp.SNMPPropertiesHdlr"/>
    <bean class="com.adva.nlms.mediation.ne_comm.configuration.snmp.NECtrlSNMPProfileChangeObserver"/>
    <bean class="com.adva.nlms.mediation.ne_comm.configuration.cli.CLIHandler"/>
    <bean class="com.adva.nlms.mediation.ne_comm.configuration.snmp.SNMPConfigurationFormModelProvider"/>
    <bean class="com.adva.nlms.mediation.ne_comm.configuration.snmp.SnmpStatisticsFormModelProvider"/>
    <bean class="com.adva.nlms.mediation.ne_comm.configuration.snmp.SNMPProfileFormModelProvider"/>
    <bean class="com.adva.nlms.mediation.ne_comm.configuration.snmp.NBISNMPFormModelProvider"/>

    <bean class="com.adva.nlms.mediation.ne_comm.monitoring.snmp.SnmpStatisticsNeDeleteObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>

    <bean class="com.adva.nlms.mediation.ec.neComm.NeInitialDiscoveryEC">
        <constructor-arg ref="boxRestClientFactory"/>
        <constructor-arg ref="httpPropertiesService"/>
    </bean>

    <bean id="boxRestClientFactory" class="com.adva.nlms.mediation.ne_comm.nerest.api.RestClientFactory" lazy-init="true"/>
    <bean class="com.adva.nlms.mediation.ne_comm.nerest.api.discovery.NERestDiscoveryManager" />

    <bean id="httpPropertiesService" class="com.adva.nlms.mediation.ne_comm.HTTPPropertiesService" lazy-init="true" >
        <property name="topologyNodeDAO" ref="com.adva.nlms.mediation.topology.TopologyNodeDAO"/>
        <property name="neHdlr" ref="networkElementHdlr"/>
        <property name="secureProtocolsStatelessWorker" ref="com.adva.nlms.mediation.config.neconfig.impl.SecureProtocolsStatelessWorkerImpl"/>
        <property name="publisher" ref="currentCtx"/>
        <property name="connectionCheckers">
            <map key-type="java.lang.Integer" value-type="com.adva.nlms.mediation.ne_comm.ConnectionChecker">
                <entry key="#{T(com.adva.nlms.common.config.netypes.NEType).FSP_3000R7.getTypeId()}" value-ref="f7RestConnectionChecker"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NEType).F8.getTypeId()}" value-ref="ecRestConnectionChecker"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NEType).FSP_XG480.getTypeId()}" value-ref="ecRestConnectionChecker"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NEType).FSP_XG404.getTypeId()}" value-ref="ecRestConnectionChecker"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NEType).FSP_XG418.getTypeId()}" value-ref="ecRestConnectionChecker"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NEType).FSP_XG118PROCSH.getTypeId()}" value-ref="ecRestConnectionChecker"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NEType).FSP_XG490.getTypeId()}" value-ref="ecRestConnectionChecker"/>
                <entry key="#{T(com.adva.nlms.common.config.netypes.NEType).OPTICAL_ROUTER.getTypeId()}" value-ref="opticalRouterConnectionChecker"/>
            </map>
        </property>
    </bean>

    <import resource="f7/mediation-necomm-f7-context.xml" />
    <import resource="f3/mediation-necomm-f3-context.xml"/>
    <import resource="f8/mediation-necomm-f8-context.xml"/>

    <bean id="netConfPropertiesService" class="com.adva.nlms.mediation.ne_comm.NetConfPropertiesService" lazy-init="true" >
        <property name="topologyNodeDAO" ref="com.adva.nlms.mediation.topology.TopologyNodeDAO"/>
        <property name="publisher" ref="currentCtx"/>
    </bean>


    <bean class="com.adva.nlms.mediation.ne_comm.netConf.NeNetConfCredentialsChangedListener">
        <constructor-arg ref="networkElementHdlr"/>
        <constructor-arg ref="netConfPropertiesService"/>
    </bean>

    <bean class="com.adva.nlms.mediation.ne_comm.SNMPAdapterProviderImpl"/>
    <bean id="networkElementDiscovery" class="com.adva.nlms.mediation.ne_comm.pv.NetworkElementDiscoveryFacade"/>
    <bean id="networkElementStatus" class="com.adva.nlms.mediation.ne_comm.NetworkElementStatusListenerImpl" factory-method="getInstance"/>

    <bean id="cpr" class="com.adva.nlms.mediation.config.driver.CommunicationPropertiesResolver"/>

    <bean id="remoteDriverFactory" class="com.adva.nlms.mediation.config.driver.remote.RemoteDriverFactory"/>

    <bean id="driversRepository" class="com.adva.nlms.driver_common.manager.DriversRepository">
        <constructor-arg ref="driverLoader"/>
        <constructor-arg ref="cpr"/>
        <constructor-arg ref="networkElementStatus"/>
        <constructor-arg ref="remoteDriverFactory"/>
        <constructor-arg ref="capabilitiesRegistry"/>
    </bean>
    <bean id="driverLoader" class="com.adva.nlms.driver_common.manager.LocalDriverLoader"/>
    <bean class="com.adva.nlms.driver_common.manager.DriverManager">
        <constructor-arg ref="driversRepository"/>
        <constructor-arg ref="networkElementDiscovery"/>
        <constructor-arg type="java.lang.String" value="${jms.transportProtocol}://${jms.url}:${jms.port}?wireFormat.maxInactivityDuration=60000&amp;wireFormat.maxInactivityDurationInitalDelay=60000${jms.additional.args}"/>
    </bean>
</beans>