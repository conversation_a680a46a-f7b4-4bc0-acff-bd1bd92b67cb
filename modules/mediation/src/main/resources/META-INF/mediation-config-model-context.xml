<?xml version="1.0" encoding="UTF-8"?>
<!--
 -  Copyright 2023 Adtran Networks SE. All rights reserved.
 -
 -  Owner: tomaszm
 -
 -  $Id$
 -->
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
                          http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
	                      http://www.springframework.org/schema/context
	                      http://www.springframework.org/schema/context/spring-context-3.0.xsd">

    <context:annotation-config/>

    <bean class="com.adva.nlms.common.NetworkEventTypeHandlerImpl" factory-method="getInstance"/>
    <bean class="com.adva.nlms.mediation.config.model.event.NetworkEventTrapMapperImpl"/>
    <bean class="com.adva.nlms.mediation.config.model.event.ServerTrapManagerImpl"/>

    <!-- MEDIATION PART -->

    <!-- NE CONTEXTS LIST -->

    <bean name="modelManager" class="com.adva.nlms.mediation.config.model.ModelManagerImpl"/>
    <!--<import resource="mediation-config-model-scan-context.xml"/>-->
    <bean name="dbImplToScanMappingManager" class="com.adva.nlms.mediation.config.entity.mapping.DBImplToScanMappingManager"/>
    <bean name="propertyMappingHierarchyHandler" class="com.adva.nlms.mediation.config.entity.mapping.PropertyMappingHierarchyHandler">
        <constructor-arg ref="dbImplToScanMappingManager"/>
    </bean>

    <bean name="managedObjectControllerManager" class="com.adva.nlms.mediation.config.f3.discovery.scan.MOControllerManager"/>
    <bean name="moDescriptionSupplier" class="com.adva.nlms.mediation.config.f3.entity.MODescriptionSupplier"/>

    <bean name="genericEntityUpdater" class="com.adva.nlms.mediation.config.f3.common.GenericEntityUpdater">
        <constructor-arg ref="propertyMappingHierarchyHandler"/>
        <constructor-arg ref="registeredTreeNodesHdlr"/>
    </bean>

    <bean class="com.adva.nlms.mediation.config.model.scanobject.ScanObjectManager"/>
    <bean class="com.adva.nlms.mediation.config.model.controller.ScanObjectQueryProcessorImpl"/>
    <bean class="com.adva.nlms.mediation.config.model.controller.ScanObjectBasedServerTrapNotifier"/>

    <!--<bean class="com.adva.nlms.mediation.config.model.controller.ScanDataResyncControllerImpl" factory-method="getInstance"/>-->

    <bean id="dataCtrlBasedController" class="com.adva.nlms.mediation.config.model.controller.DataPollingControllerImpl"/>
    <bean id="dataCtrlBasedControllerFSP1500" class="com.adva.nlms.mediation.config.model.controller.DataPollingControllerFSP1500Impl"/>
    <bean id="dataCtrlBasedControllerFSP_R7" class="com.adva.nlms.mediation.config.model.controller.DataPollingControllerFSP_R7Impl"/>
    <bean id="scanObjectBasedController" class="com.adva.nlms.mediation.config.model.controller.ScanDataPollingControllerImpl"/>
    <bean id="dataPollingSwitchController" class="com.adva.nlms.mediation.config.model.controller.DataPollingSwitchControllerImpl"/>

    <bean class="com.adva.nlms.mediation.config.model.controller.DataPollingControllerFactory" factory-method="getInstance">
        <property name="dataPollingViaScanObjectsEnabled" value="true"/>
    </bean>

    <bean class="com.adva.nlms.mediation.config.model.query.QueryBuilderImpl"/>

</beans>