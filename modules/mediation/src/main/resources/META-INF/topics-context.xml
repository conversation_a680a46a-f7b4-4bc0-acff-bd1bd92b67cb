<?xml version="1.0" encoding="UTF-8"?>
<!--
  -  Copyright 2012 ADVA AG Optical Networking. All rights reserved.
  -
  -  Owner: v<PERSON><PERSON><PERSON>
  -->

<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
	   http://www.springframework.org/schema/beans/spring-beans-3.0.xsd">

    <bean id="eventUpdate" class="org.apache.activemq.command.ActiveMQTopic">
        <constructor-arg value="topic.eventUpdate"/>
    </bean>

    <bean id="objectEventState" class="org.apache.activemq.command.ActiveMQTopic">
        <constructor-arg value="topic.objectEventState"/>
    </bean>

    <bean id="topologyChange" class="org.apache.activemq.command.ActiveMQTopic">
        <constructor-arg value="topic.topologyChange"/>
    </bean>

    <bean id="generalChange" class="org.apache.activemq.command.ActiveMQTopic">
        <constructor-arg value="topic.generalChange"/>
    </bean>

    <bean id="syncTopologyChange" class="org.apache.activemq.command.ActiveMQTopic">
        <constructor-arg value="topic.syncTopologyChange"/>
    </bean>

    <bean id="serverActivity" class="org.apache.activemq.command.ActiveMQTopic">
        <constructor-arg value="topic.eventUpdate"/>
    </bean>

    <bean id="progressStatus" class="org.apache.activemq.command.ActiveMQTopic">
        <constructor-arg value="topic.progressStatus"/>
    </bean>

    <bean id="pmTemplateAssignmentStatus" class="org.apache.activemq.command.ActiveMQTopic">
        <constructor-arg value="topic.pmTemplateAssignmentStatus"/>
    </bean>

    <bean id="messageProperties" class="org.apache.activemq.command.ActiveMQTopic">
        <constructor-arg value="topic.messageProperties"/>
    </bean>

    <bean id="updateStatus" class="org.apache.activemq.command.ActiveMQTopic">
        <constructor-arg value="topic.updateStatus"/>
    </bean>

    <bean id="reportUpdate" class="org.apache.activemq.command.ActiveMQTopic">
        <constructor-arg value="topic.reportUpdate"/>
    </bean>

    <bean id="securityUpdate" class="org.apache.activemq.command.ActiveMQTopic">
        <constructor-arg value="topic.securityUpdate"/>
    </bean>

    <bean id="cryptoUpdate" class="org.apache.activemq.command.ActiveMQTopic">
        <constructor-arg value="topic.cryptoUpdate"/>
    </bean>

    <bean id="pollingPropertiesChange" class="org.apache.activemq.command.ActiveMQTopic">
        <constructor-arg value="topic.pollingPropertiesChange"/>
    </bean>

    <bean id="haApplyConfigurationStatus" class="org.apache.activemq.command.ActiveMQTopic">
        <constructor-arg value="topic.haApplyConfigurationStatus"/>
    </bean>

    <bean id="pcaStatus" class="org.apache.activemq.command.ActiveMQTopic">
        <constructor-arg value="topic.pcaStatus"/>
    </bean>

    <bean id="cuaStatus" class="org.apache.activemq.command.ActiveMQTopic">
        <constructor-arg value="topic.cuaStatus"/>
    </bean>

    <bean id="approvalStatus" class="org.apache.activemq.command.ActiveMQTopic">
        <constructor-arg value="topic.approvalStatus"/>
    </bean>

    <bean id="userNotification" class="org.apache.activemq.command.ActiveMQTopic">
        <constructor-arg value="topic.userNotification"/>
    </bean>

     <bean id="general" class="org.apache.activemq.command.ActiveMQTopic">
        <constructor-arg value="topic.general"/>
    </bean>

</beans>