<?xml version="1.0" encoding="UTF-8"?>
<!--
-  Copyright 2023 Adtran Networks SE. All rights reserved.
-
-  Owner: erinis
-
-  $Id: $
-->
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">
    <bean id="geoServerSettingsDAO" class="com.adva.nlms.mediation.gistransfer.geoserver.settings.GeoServerSettingsDAO"/>
    <bean id="gisAppTransferCtrl" class="com.adva.nlms.mediation.gistransfer.GISAppTransferCtrlImpl"/>

    <bean id="xmlCodec" class="com.adva.nlms.mediation.gistransfer.geoserver.rest.codecs.impl.XmlCodecImpl"/>
    <bean id="geoServerRestBroker" class="com.adva.nlms.mediation.gistransfer.geoserver.rest.GeoServerRestBrokerImpl" scope="prototype"/>
    <bean id="geoServerRequestValidator" class="com.adva.nlms.mediation.gistransfer.geoserver.validation.GeoServerRequestValidatorImpl"/>
    <bean id="almCreationValidator" class="com.adva.nlms.mediation.gistransfer.fiberplant.AlmCreationValidator">
        <constructor-arg ref="fbrPlantHdlr" />
    </bean>
    <bean id="geoServerHdlr" class="com.adva.nlms.mediation.gistransfer.geoserver.GeoServerHdlrImpl"/>
    <bean id="fiberTopologyHdlr" class="com.adva.nlms.mediation.gistransfer.topology.FiberTopologyHdlr"/>
    <bean id="fiberTopologyProvider" class="com.adva.nlms.mediation.gistransfer.topology.FiberTopologyProvider" />
    <bean id="fbrPlantHdlrController" class="com.adva.nlms.mediation.gistransfer.fiberplant.FbrPlantHdlrController"/>
    <bean id="fbrPlantHdlr" class="com.adva.nlms.mediation.gistransfer.fiberplant.FbrPlantHdlrImpl"/>
    <bean id="pinPointHdlr" class="com.adva.nlms.mediation.gistransfer.fiberplant.PinPointHdlr"/>
    <bean id="fiberAlarmDecorator" class="com.adva.nlms.mediation.gistransfer.fiberplant.FiberAlarmDecoratorImpl">
        <constructor-arg ref="registeredNetworkElementsHdlr"/>
        <constructor-arg ref="pinPointHdlr"/>
        <constructor-arg ref="famCtrl"/>
    </bean>
    <bean id="fbrPlantAlmHdlrChooser" class="com.adva.nlms.mediation.gistransfer.fiberplant.FbrPlantAlmHdlrChooser"/>
    <bean id="geoManagerALMCreator" class="com.adva.nlms.mediation.gistransfer.fiberplant.GeoManagerALMCreator">
        <constructor-arg ref="httpPropertiesService"/>
        <constructor-arg ref="portDAO"/>
    </bean>
    <bean id="gisAlmSync" class="com.adva.nlms.mediation.gistransfer.fiberplant.GisAlmSynchronization">
        <constructor-arg ref="fbrPlantHdlr"/>
        <constructor-arg ref="geoServerHdlr"/>
        <constructor-arg ref="mdMessageSender" />
        <constructor-arg ref="geoManagerALMCreator" />
        <constructor-arg ref="geoServerRequestValidator" />
    </bean>
    <bean class="com.adva.nlms.mediation.gistransfer.fiberplant.GisSynchronizationPollingCommand">
        <qualifier type="com.adva.nlms.mediation.polling.DomainPolling"/>
        <constructor-arg ref="fbrPlantHdlr"/>
        <constructor-arg ref="eventDBQueryHdlrImpl"/>
        <constructor-arg ref="availabilityManager"/>
        <constructor-arg ref="gisAlmSync"/>
        <constructor-arg ref="reflectorFamRecordDAO"/>
    </bean>
    <bean class="com.adva.nlms.mediation.gistransfer.fiberplant.FiberRoutePageHdlr"/>
    <bean class="com.adva.nlms.mediation.gistransfer.fiberplant.BuildingPageHdlr"/>
    <bean class="com.adva.nlms.mediation.gistransfer.fiberplant.RegionPageHdlr"/>
    <bean class="com.adva.nlms.mediation.gistransfer.fiberplant.DuctPageHdlr"/>
    <bean class="com.adva.nlms.mediation.gistransfer.fiberplant.AccessPointPageHdlr"/>
    <bean class="com.adva.nlms.mediation.gistransfer.fiberplant.FaultLocationsPageHdlr"/>
    <bean class="com.adva.nlms.mediation.gistransfer.fiberplant.MonitorPointPageHdlr"/>
    <bean class="com.adva.nlms.mediation.gistransfer.fiberplant.BuildingAlmDevPageHdlr"/>
    <bean class="com.adva.nlms.mediation.gistransfer.fiberplant.FaultedRoutePageHdlr"/>
    <bean class="com.adva.nlms.mediation.gistransfer.fiberplant.FiberRoutePathElementPageHdlr"/>
    <bean class="com.adva.nlms.mediation.gistransfer.fiberplant.subroute.SubRoutesPageHdlr"/>
    <bean class="com.adva.nlms.mediation.gistransfer.GeoServerNetworkElementActionsCheckerImpl"/>
    <bean class="com.adva.nlms.mediation.gistransfer.AlmDeletionSynchronousObserver"/>
    <bean id="availabilityManager" class="com.adva.nlms.mediation.gistransfer.GeoServerAvailabilityManagerImpl">
        <constructor-arg ref="licenseManager"/>
        <constructor-arg ref="geoServerSettingsDAO"/>
    </bean>
</beans>