<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">


    <bean id="ge4EccLineCardEventHandler" class="com.adva.nlms.mediation.config.fsp20X.entity.module.linecard.ge4ecc.Ge4EccLineCardEventHandler"/>
    <bean id="ge4SccLineCardEventHandler" class="com.adva.nlms.mediation.config.fsp20X.entity.module.linecard.ge4scc.Ge4SccLineCardEventHandler"/>
    <bean id="f3SyncRefEventHandlerFSP150CM" class="com.adva.nlms.mediation.config.f3.entity.syncref.F3SyncRefEventHandlerFSP150CM"/>
    <bean id="ampConfigEventHandler" class="com.adva.nlms.mediation.config.f3.entity.ampconfig.AMPConfigEventHandler"/>
    <bean id="staticRouteEventHandler" class="com.adva.nlms.mediation.config.f3.entity.staticroute.StaticRouteEventHandler"/>
    <bean id="pseudoWireE1T1CardEventHandler" class="com.adva.nlms.mediation.config.f3.entity.module.linecard.e1t1.PseudoWireE1T1CardEventHandler"/>
    <bean id="scuF3EventHandler" class="com.adva.nlms.mediation.config.f3.entity.module.scu.ScuF3EventHandler"/>
    <bean id="scuTFSP150CMEventHandler" class="com.adva.nlms.mediation.config.fsp150cm.entity.module.scut.ScuTFSP150CMEventHandler"/>
    <bean id="protectionGroupFSP150CMEventHandler" class="com.adva.nlms.mediation.config.f3.entity.pg.ProtectionGroupFSP150CMEventHandler"/>
    <bean id="ntuFSP150CMEventHandler" class="com.adva.nlms.mediation.config.fsp150cm.entity.module.ntu.NTUFSP150CMEventHandler"/>
    <bean id="pollingDelayForSfp" class="com.adva.nlms.mediation.config.f3.entity.port.net.PollingDelayPolicy">
        <constructor-arg index="0" type="long" value="#{T(com.adva.nlms.common.property.FNMPropertyFactory).getPropertyAsInt(T(com.adva.nlms.common.property.FNMPropertyConstants).CM_SFP_POLLING_DELAY, 2000)}"/>
    </bean>
    <bean id="portF3AccEventHandlerWithDelay" class="com.adva.nlms.mediation.config.f3.entity.port.acc.PortF3AccEventHandler">
        <constructor-arg index="0" ref="pollingDelayForSfp"/>
    </bean>
    <bean id="portF3NetEventHelperWithDelay" class="com.adva.nlms.mediation.config.f3.entity.port.net.PortF3NetEventHelper">
        <constructor-arg index="0" ref="pollingDelayForSfp"/>
    </bean>
    <bean id="ntefsp150CMEventHandler" class="com.adva.nlms.mediation.config.fsp150cm.entity.module.nte.NTEFSP150CMEventHandler"/>
    <bean id="ptpClockProbeEventHandler" class="com.adva.nlms.mediation.config.f3.entity.sj.ptpclock.PTPClockProbeEventHandler"/>
    <bean id="clockProbeEventHandler" class="com.adva.nlms.mediation.config.f3.entity.sj.clockprobe.ClockProbeEventHandler"/>
    <bean id="sjScheduleGroupEventHandler" class="com.adva.nlms.mediation.config.f3.entity.sj.sg.SJScheduleGroupEventHandler"/>
    <bean id="ptpClockHisEventHandler" class="com.adva.nlms.mediation.config.f3.entity.sj.ptpclock.history.PTPClockHisEventHandler"/>
    <bean id="clockProbeHisEventHandler" class="com.adva.nlms.mediation.config.f3.entity.sj.clockprobe.history.ClockProbeHisEventHandler"/>
    <bean id="ptpNetworkProbeEventHandler" class="com.adva.nlms.mediation.config.f3.entity.sj.ptpnetwork.PTPNetworkProbeEventHandler"/>
    <bean id="userMTIEMaskEventHandler" class="com.adva.nlms.mediation.config.f3.entity.sj.usermtiemask.UserMTIEMaskEventHandler"/>
    <bean id="lagF3EventHandler" class="com.adva.nlms.mediation.config.f3.entity.lag.LagF3EventHandler"/>
    <bean id="maNetEventHandler" class="com.adva.nlms.mediation.config.f3.entity.cfm.manet.MANetEventHandler"/>
    <bean id="maCompEventHandler" class="com.adva.nlms.mediation.config.f3.entity.cfm.macomp.MACompEventHandler"/>
    <bean id="mdEventHandler" class="com.adva.nlms.mediation.config.f3.entity.cfm.md.MDEventHandler"/>
    <bean id="mepEventHandler" class="com.adva.nlms.mediation.config.f3.entity.cfm.mep.MEPEventHandler"/>
    <bean id="mepListEventHandler" class="com.adva.nlms.mediation.config.f3.entity.cfm.mameplist.MaMepListEventHandler"/>
    <bean id="elineFlowEventHandler" class="com.adva.nlms.mediation.config.f3.entity.serviceflow.eline.ElineFlowEventHandler"/>
    <bean id="mpFlowEventHandler" class="com.adva.nlms.mediation.config.f3.entity.serviceflow.mp.MPFlowF3EventHandler"/>
    <bean id="flowPointEventHandler" class="com.adva.nlms.mediation.config.f3.entity.flowpoint.egx.FlowPointEventHandler"/>
    <bean id="qosShaperV2EventHandler" class="com.adva.nlms.mediation.config.f3.entity.shaper.qosshaperv2.QOSShaperV2EventHandler"/>
    <bean id="qosPolicerV2EventHandler" class="com.adva.nlms.mediation.config.f3.entity.policer.qospolicerv2.QOSPolicerV2EventHandler"/>
    <bean id="flowPointOAMEventHandler" class="com.adva.nlms.mediation.config.f3.entity.flowpoint.flowpointoam.FlowPointOAMEventHandler"/>
    <bean id="qosTrafficPortShaperEventHandler" class="com.adva.nlms.mediation.config.f3.entity.shaper.qostrafficportshaper.QOSTrafficPortShaperEventHandler"/>
    <bean id="esaProbeF3EventHandler" class="com.adva.nlms.mediation.config.f3.entity.esaprobe.EsaProbeF3EventHandler"/>
    <bean id="esaProbeCosConfigF3EventHandler" class="com.adva.nlms.mediation.config.f3.entity.esaprobecosconfig.ESAProbeCosConfigF3EventHandler"/>
    <bean id="esaProbeMultiMepF3EventHandler" class="com.adva.nlms.mediation.config.f3.entity.esaprobemultimep.ESAProbeMultiMepF3EventHandler"/>
    <bean id="esaReflectorEventHandler" class="com.adva.nlms.mediation.config.f3.entity.esaprobereflector.ESAReflectorEventHandler"/>
    <bean id="esaProbeScheduleGroupF3EventHandler" class="com.adva.nlms.mediation.config.f3.entity.esaprobeschedulegroup.ESAProbeScheduleGroupF3EventHandler"/>
    <bean id="ethernetTrafficPortF3EventHandler" class="com.adva.nlms.mediation.config.f3.entity.port.trafficport.EthernetTrafficPortF3EventHandler"/>
    <bean id="ethernet1x10GCardF3EventHandler" class="com.adva.nlms.mediation.config.f3.entity.module.linecard.e1x10.Ethernet1x10GCardF3EventHandler"/>
    <bean id="ethernet10x1GCardF3EventHandler" class="com.adva.nlms.mediation.config.f3.entity.module.linecard.e10x1.Ethernet10x1GCardF3EventHandler"/>
    <bean id="ethernet1x10GHighPerformanceCardEventHandler" class="com.adva.nlms.mediation.config.f3.entity.module.linecard.e1x10hp.Ethernet1x10GHighPerformanceCardEventHandler"/>
    <bean id="ethernet10x1GHighPerformanceCardEventHandler" class="com.adva.nlms.mediation.config.f3.entity.module.linecard.e10x1hp.Ethernet10x1GHighPerformanceCardEventHandler"/>
    <bean id="ethernetFE36ECardEventHandler" class="com.adva.nlms.mediation.config.f3.entity.module.linecard.fe36e.EthernetFE36ECardEventHandler"/>
    <bean id="swfF3EventHandler" class="com.adva.nlms.mediation.config.f3.entity.module.swf.SwfF3EventHandler"/>
    <bean id="vmServerModuleEventHandler" class="com.adva.nlms.mediation.config.f3.entity.module.vm.ServerModuleEventHandler"/>
    <bean id="sessionMonitorEventHandler" class="com.adva.nlms.mediation.config.f3.entity.olm.session.SessionMonitorEventHandler"/>
    <bean id="stuF3EventHandler" class="com.adva.nlms.mediation.config.f3.entity.module.stu.StuF3EventHandler"/>
    <bean id="stuHF3EventHandler" class="com.adva.nlms.mediation.config.f3.entity.module.stuh.StuHF3EventHandler"/>
    <bean id="amiF3EventHandler" class="com.adva.nlms.mediation.config.f3.entity.module.ami.AmiF3EventHandler"/>
    <bean id="stiF3EventHandler" class="com.adva.nlms.mediation.config.f3.entity.module.sti.StiF3EventHandler"/>
    <bean id="stiHF3EventHandler" class="com.adva.nlms.mediation.config.f3.entity.module.stih.StiHF3EventHandler"/>
    <bean id="nemiF3EventHandler" class="com.adva.nlms.mediation.config.f3.entity.module.nemi.NemiF3EventHandler"/>
    <bean id="mgntF3EventHandler" class="com.adva.nlms.mediation.config.f3.entity.module.mgnt.MgntF3EventHandler"/>
    <bean id="csmF3EventHandler" class="com.adva.nlms.mediation.config.fspsyncprob.entity.module.csm.CsmF3EventHandler"/>
    <bean id="auxF3EventHandler" class="com.adva.nlms.mediation.config.fspsyncprob.entity.module.auxCard.AuxF3EventHandler"/>
    <bean id="mbGNSSLCEventHandler" class="com.adva.nlms.mediation.config.fspsyncprob.entity.module.mbGnss.MbGNSSLCEventHandler"/>
    <bean id="stlLCEventHandler" class="com.adva.nlms.mediation.config.fspsyncprob.entity.module.stlCard.StlLCEventHandler"/>
    <bean id="teluCardEventHandler" class="com.adva.nlms.mediation.config.fspsyncprob.entity.module.osa33xxtelucard.TeluCardEventHandler"/>
    <bean id="metuCardEventHandler" class="com.adva.nlms.mediation.config.fspsyncprob.entity.module.osa33xxmetucard.MetuCardEventHandler"/>
    <bean id="ppsCardEventHandler" class="com.adva.nlms.mediation.config.fspsyncprob.entity.module.osa33xxppscard.PpsCardEventHandler"/>
    <bean id="cbuCardEventHandler" class="com.adva.nlms.mediation.config.fspsyncprob.entity.module.osa33xxcbucard.CbuCardEventHandler"/>
    <bean id="rfuCardEventHandler" class="com.adva.nlms.mediation.config.fspsyncprob.entity.module.osa33xxrfucard.RfuCardEventHandler"/>
    <bean id="ipuCardEventHandler" class="com.adva.nlms.mediation.config.fspsyncprob.entity.module.osa33xxipucard.IpuCardEventHandler"/>
    <bean id="pduCardEventHandler" class="com.adva.nlms.mediation.config.fspsyncprob.entity.module.osa33xxpducard.PduCardEventHandler"/>
    <bean id="batuCardEventHandler" class="com.adva.nlms.mediation.config.fspsyncprob.entity.module.osa33xxbatucard.BatuCardEventHandler"/>
    <bean id="lcdCardEventHandler" class="com.adva.nlms.mediation.config.fspsyncprob.entity.module.osa33xxlcdcard.LcdCardEventHandler"/>
    <bean id="estuCardEventHandler" class="com.adva.nlms.mediation.config.fspsyncprob.entity.module.osa33xxestucard.EstuCardEventHandler"/>
    <bean id="compositeClockCardEventHandler" class="com.adva.nlms.mediation.config.fspsyncprob.entity.module.compositeClock.CompositeClockCardEventHandler"/>
    <bean id="irigCardEventHandler" class="com.adva.nlms.mediation.config.fspsyncprob.entity.module.irigCard.IrigCardEventHandler"/>
    <bean id="displayCardEventHandler" class="com.adva.nlms.mediation.config.fspsyncprob.entity.module.displayCard.DisplayCardEventHandler"/>
    <bean id="clkx4lpnCardEventHandler" class="com.adva.nlms.mediation.config.fspsyncprob.entity.module.clkx4lpnCard.Clkx4lpnCardEventHandler"/>
    <bean id="avsCardEventHandler" class="com.adva.nlms.mediation.config.fspsyncprob.entity.module.avsCard.AvsCardEventHandler"/>
    <bean id="bitsx16ProtectedCardEventHandler" class="com.adva.nlms.mediation.config.fspsyncprob.entity.module.bitsx16protectedCard.Bitsx16ProtectedCardEventHandler"/>
    <bean id="irigOpticalOutputPortEventHandler" class="com.adva.nlms.mediation.config.f3.entity.port.irigopticaloutputport.IrigOpticalOutputPortEventHandler"/>
    <bean id="irigRelayOutputPortEventHandler" class="com.adva.nlms.mediation.config.f3.entity.port.irigrelayoutputport.IrigRelayOutputPortEventHandler"/>
    <bean id="irigRS422OutputPortEventHandler" class="com.adva.nlms.mediation.config.f3.entity.port.irigrs422outputport.IrigRS422OutputPortEventHandler"/>
    <bean id="irigOutputGroupEventHandler" class="com.adva.nlms.mediation.config.f3.entity.port.irigoutputgroup.IrigOutputGroupEventHandler"/>
    <bean id="irigOutputUnitPortEventHandler" class="com.adva.nlms.mediation.config.f3.entity.port.irigoutputunitport.IrigOutputUnitPortEventHandler"/>
    <bean id="irigDcls1PpsPortEventHandler" class="com.adva.nlms.mediation.config.f3.entity.port.irigdcls1ppsport.IrigDcls1PpsPortEventHandler"/>
    <bean id="irigAmClkPortEventHandler" class="com.adva.nlms.mediation.config.f3.entity.port.irigamclkport.IrigAmClkPortEventHandler"/>
    <bean id="irigBInputPortEventHandler" class="com.adva.nlms.mediation.config.f3.entity.port.irigbinputport.IrigBInputPortEventHandler"/>
    <bean id="clk4OutputPortGroupEventHandler" class="com.adva.nlms.mediation.config.f3.entity.port.clk4outputportgroup.Clk4OutputPortGroupEventHandler"/>
    <bean id="avsOutputPortEventHandler" class="com.adva.nlms.mediation.config.f3.entity.port.avsoutputport.AvsOutputPortEventHandler"/>
    <bean id="stlModuleEventHandler" class="com.adva.nlms.mediation.config.f3.entity.port.stlmodule.StlModuleEventHandler"/>
    <bean id="compositeClockInputPortEventHandler" class="com.adva.nlms.mediation.config.f3.entity.port.compositeclockinput.CompositeClockInputPortEventHandler"/>
    <bean id="compositeClockGroupEventHandler" class="com.adva.nlms.mediation.config.f3.entity.port.compositeclockgroup.CompositeClockGroupEventHandler"/>
    <bean id="ptpSystemSlavesEventHandler" class="com.adva.nlms.mediation.config.f3.entity.ptp.systemslaves.PtpSystemSlavesEventHandler"/>

    <bean id="powerSupplyF3EventHandler" class="com.adva.nlms.mediation.config.f3.entity.module.psu.PowerSupplyF3EventHandler"/>
    <bean id="cpmrPowerSupplyF3EventHandler" class="com.adva.nlms.mediation.config.f3.entity.module.psu.CpmrPowerSupplyF3EventHandler"/>
    <bean id="fanF3EventHandler" class="com.adva.nlms.mediation.config.f3.entity.module.fan.FanF3EventHandler"/>
    <bean id="cpmrFanF3EventHandler" class="com.adva.nlms.mediation.config.f3.entity.module.fan.CpmrFanF3EventHandler"/>
    <bean id="shelfF3EventHandler" class="com.adva.nlms.mediation.config.f3.entity.shelf.ShelfF3EventHandler"/>
    <bean id="systemEventHandler" class="com.adva.nlms.mediation.config.f3.entity.SystemEventHandler"/>
    <bean id="stsVcPathF3EventHandler" class="com.adva.nlms.mediation.config.f3.entity.logicalport.vcpath.sts.StsVcPathF3EventHandler"/>
    <bean id="vtVcPathF3EventHandler" class="com.adva.nlms.mediation.config.f3.entity.logicalport.vcpath.vt.VtVcPathF3EventHandler"/>
    <bean id="e1T1F3EventHandler" class="com.adva.nlms.mediation.config.f3.entity.logicalport.extx.e1t1.E1T1F3EventHandler"/>
    <bean id="e3T3F3EventHandler" class="com.adva.nlms.mediation.config.f3.entity.logicalport.extx.e3t3.E3T3F3EventHandler"/>
    <bean id="vcgF3EventHandler" class="com.adva.nlms.mediation.config.f3.entity.logicalport.vcg.VcgF3EventHandler"/>
    <bean id="logicalPortTcaEventHandler" class="com.adva.nlms.mediation.config.f3.entity.logicalport.LogicalPortTcaEventHandler"/>
    <bean id="ifPortEventHandler" class="com.adva.nlms.mediation.config.f3.entity.port.IfPortEventHandler"/>
    <bean id="protectionGroupPortF3EventHandler" class="com.adva.nlms.mediation.config.f3.entity.pgport.ProtectionGroupPortF3EventHandler"/>
    <bean id="f3DatabaseSyncEventHandlerXG210" class="com.adva.nlms.mediation.config.f3.entity.databasesynctrap.F3DatabaseSyncTrapXg210"/>
    <bean id="protectionGroupF3EventHandler" class="com.adva.nlms.mediation.config.f3.entity.pg.ProtectionGroupF3EventHandler"/>
    <bean id="f3DatabaseSyncTrapEventHandler" class="com.adva.nlms.mediation.config.f3.entity.databasesynctrap.F3DatabaseSyncTrapEventHandler"/>
    <bean id="f3DatabaseSyncTrapOSAEventHandler" class="com.adva.nlms.mediation.config.f3.entity.databasesynctrap.F3DatabaseSyncTrapOSAEventHandler"/>
    <bean id="physicalEntityEventHandler" class="com.adva.nlms.mediation.config.f3.entity.PhysicalEntityEventHandler"/>
    <bean id="qosPolicerProfileEventHandler" class="com.adva.nlms.mediation.config.f3.entity.policer.qospolicerprofile.QosPolicerProfileEventHandler"/>
    <bean id="qosQueueProfileEventHandler" class="com.adva.nlms.mediation.config.f3.entity.shaper.qosqueueprofile.QosQueueProfileEventHandler"/>
    <bean id="erpGroupF3EventHandler" class="com.adva.nlms.mediation.config.f3.entity.erp.ErpGroupF3EventHandler"/>
    <bean id="ingressPrioMapEventHandler" class="com.adva.nlms.mediation.config.f3.entity.priomap.ingress.IngressPrioMapEventHandler"/>
    <bean id="egressPrioMapEventHandler" class="com.adva.nlms.mediation.config.f3.entity.priomap.egress.EgressPrioMapEventHandler"/>
    <bean id="protectedFlowF3EventHandler" class="com.adva.nlms.mediation.config.f3.entity.protectedflow.ProtectedFlowF3EventHandler"/>
    <bean id="managementTunnelEventHandler" class="com.adva.nlms.mediation.config.f3.entity.managementtunnel.ManagementTunnelEventHandler"/>
    <bean id="f3SyncEventHandler" class="com.adva.nlms.mediation.config.f3.entity.sync.F3SyncEventHandler"/>
    <bean id="f3SyncRefEventHandler" class="com.adva.nlms.mediation.config.f3.entity.syncref.F3SyncRefEventHandler"/>
    <bean id="trafficAclProfileEventHandler" class="com.adva.nlms.mediation.config.f3.entity.trafficaclprofile.TrafficAclProfileEventHandler"/>
    <bean id="gps10MHzPortEventHandler" class="com.adva.nlms.mediation.config.f3.entity.port.gps10mhz.Gps10MHzPortEventHandler"/>
    <bean id="f3PulsePerSecondPortEventHandler" class="com.adva.nlms.mediation.config.f3.entity.port.pulsepersecond.F3PulsePerSecondPortEventHandler"/>
    <bean id="f3TimeOfDayPortEventHandler" class="com.adva.nlms.mediation.config.f3.entity.port.timeofday.F3TimeOfDayPortEventHandler"/>
    <bean id="f3TimeClockEventHandler" class="com.adva.nlms.mediation.config.f3.entity.timeclock.F3TimeClockEventHandler"/>
    <bean id="f3TimeClockRefEventHandler" class="com.adva.nlms.mediation.config.f3.entity.timeclockref.F3TimeClockRefEventHandler"/>
    <bean id="f3GPSReceiverPortEventHandler" class="com.adva.nlms.mediation.config.f3.entity.port.gpsreceiver.F3GPSReceiverPortEventHandler"/>
    <bean id="ptpAccFlowPointEventHandler" class="com.adva.nlms.mediation.config.f3.entity.ptp.flowpoint.acc.PTPAccFlowPointEventHandler"/>
    <bean id="ptpNetFlowPointEventHandler" class="com.adva.nlms.mediation.config.f3.entity.ptp.flowpoint.net.PTPNetFlowPointEventHandler"/>
    <bean id="ntpAccFlowPointEventHandler" class="com.adva.nlms.mediation.config.f3.entity.ntp.flowpoint.acc.NTPAccFlowPointEventHandler"/>
    <bean id="ntpNetFlowPointEventHandler" class="com.adva.nlms.mediation.config.f3.entity.ntp.flowpoint.net.NTPNetFlowPointEventHandler"/>
    <bean id="masterClockEventHandler" class="com.adva.nlms.mediation.config.f3.entity.ptp.mc.MasterClockEventHandler"/>
    <bean id="ntpClockEventHandler" class="com.adva.nlms.mediation.config.f3.entity.ntp.clock.NtpClockEventHandler"/>
    <bean id="masterClockOSA540xEventHandler" class="com.adva.nlms.mediation.config.osa540X.ptp.mc.MasterClockOSA540xEventHandler"/>
    <bean id="ptpClockOSA540xEventHandler" class="com.adva.nlms.mediation.config.osa540X.ptp.ptpclock.PtpClockOSA540xEventHandler"/>
    <bean id="ptpPortOSA540xEventHandler" class="com.adva.nlms.mediation.config.osa540X.ptp.ptpport.PtpPortOSA540xEventHandler"/>
    <bean id="ocslaveVirtualPortOSA540xEventHandler" class="com.adva.nlms.mediation.config.osa540X.ptp.ocsvirtualport.OCSlaveVirtualPortOSA540xEventHandler"/>
    <bean id="masterClockInterfaceOSA540xEventHandler" class="com.adva.nlms.mediation.config.osa540X.ptp.mci.MasterClockInterfaceOSA540xEventHandler"/>
    <bean id="masterVirtualPortOSA540xEventHandler" class="com.adva.nlms.mediation.config.osa540X.ptp.mvp.MasterVirtualPortOSA540xEventHandler"/>
    <bean id="pTPTPFlowPointOSA540xEventHandler" class="com.adva.nlms.mediation.config.osa540X.ptp.ptpflow.PTPTPFlowPointOSA540xEventHandler"/>
    <bean id="ethernetTrafficPortOSA540xEventHandler" class="com.adva.nlms.mediation.config.osa540X.entity.port.traffic.EthernetTrafficPortOSA540xEventHandler"/>
    <bean id="sOOCOSA540xEventHandler" class="com.adva.nlms.mediation.config.osa540X.ptp.sooc.SOOCOSA540xEventHandler"/>
    <bean id="oCSlaveOSA540xEventHandler" class="com.adva.nlms.mediation.config.osa540X.ptp.ocs.OCSlaveOSA540xEventHandler"/>
    <bean id="sJScheduleGroupOSA540xEventHandler" class="com.adva.nlms.mediation.config.osa540X.sj.scheduler.SJScheduleGroupOSA540xEventHandler"/>
    <bean id="pTPClockProbeOSA540xEventHandler" class="com.adva.nlms.mediation.config.osa540X.sj.ptpClockProbe.PTPClockProbeOSA540xEventHandler"/>
    <bean id="clockProbeOSA540xEventHandler" class="com.adva.nlms.mediation.config.osa540X.sj.clockProbe.ClockProbeOSA540xEventHandler"/>
    <bean id="pTPClockProbeHisOSA540xEventHandler" class="com.adva.nlms.mediation.config.osa540X.sj.ptpClockProbe.PTPClockProbeHisOSA540xEventHandler"/>
    <bean id="clockProbeHisOSA540xEventHandler" class="com.adva.nlms.mediation.config.osa540X.sj.clockProbe.ClockProbeHisOSA540xEventHandler"/>
    <bean id="nteF3EventHandler" class="com.adva.nlms.mediation.config.f3.entity.module.nte.NteF3EventHandler"/>
    <bean id="flowF3EventHandler" class="com.adva.nlms.mediation.config.f3.entity.flow.FlowF3EventHandler"/>
    <bean id="netPortQOSShaperEventHandler" class="com.adva.nlms.mediation.config.f3.entity.shaper.netportshaper.NetPortQOSShaperEventHandler"/>
    <bean id="qOSAccPortFPShaperF3EventHandler" class="com.adva.nlms.mediation.config.f3.entity.shaper.accportfpshaper.QOSAccPortFPShaperF3EventHandler"/>
    <bean id="qOSNetPortFPShaperF3EventHandler" class="com.adva.nlms.mediation.config.f3.entity.shaper.netportfpshaper.QOSNetPortFPShaperF3EventHandler"/>
    <bean id="accPortQOSShaperEventHandler" class="com.adva.nlms.mediation.config.f3.entity.shaper.accportshaper.AccPortQOSShaperEventHandler"/>
    <bean id="qosFlowPolicerF3EventHandler" class="com.adva.nlms.mediation.config.f3.entity.policer.qospolicer.QOSFlowPolicerF3EventHandler"/>
    <bean id="qosShaperF3EventHandler" class="com.adva.nlms.mediation.config.f3.entity.shaper.qosshaper.QOSShaperF3EventHandler"/>
    <bean id="ocSlaveEventHandler" class="com.adva.nlms.mediation.config.f3.entity.ptp.ocslave.OCSlaveEventHandler"/>
    <bean id="soocEventHandler" class="com.adva.nlms.mediation.config.f3.entity.ptp.sooc.SOOCEventHandler"/>
    <bean id="ocSlaveVirtualPortEventHandler" class="com.adva.nlms.mediation.config.f3.entity.ptp.ocslave.vp.OCSlaveVirtualPortEventHandler"/>
    <bean id="ptptpFlowPointEventHandler" class="com.adva.nlms.mediation.config.f3.entity.ptp.flowpoint.tp.PTPTPFlowPointEventHandler"/>
    <bean id="tcEventHandler" class="com.adva.nlms.mediation.config.f3.entity.ptp.tc.TCEventHandler"/>
    <bean id="tcVirtualPortEventHandler" class="com.adva.nlms.mediation.config.f3.entity.ptp.tc.vp.TCVirtualPortEventHandler"/>
    <bean id="staticRemoteSlaveEventHandler" class="com.adva.nlms.mediation.config.f3.entity.ptp.rs.srs.StaticRemoteSlaveEventHandler"/>
    <bean id="dynamicRemoteSlaveEventHandler" class="com.adva.nlms.mediation.config.f3.entity.ptp.rs.drs.DynamicRemoteSlaveEventHandler"/>
    <bean id="dynamicRemoteSlaveOSA540xEventHandler" class="com.adva.nlms.mediation.config.osa540X.ptp.dsr.DynamicRemoteSlaveOSA540xEventHandler"/>
    <bean id="dynamicRemoteSlaveOSAEventHandler" class="com.adva.nlms.mediation.config.f3.entity.ptp.rs.drs.DynamicRemoteSlaveOSAEventHandler"/>
    <bean id="l2dynamicRemoteSlaveEventHandler" class="com.adva.nlms.mediation.config.f3.entity.ptp.rs.l2drs.L2DynamicRemoteSlaveEventHandler"/>
    <bean id="masterClockInterfaceEventHandler" class="com.adva.nlms.mediation.config.f3.entity.ptp.mci.MasterClockInterfaceEventHandler"/>
    <bean id="ntpClockInterfaceEventHandler" class="com.adva.nlms.mediation.config.f3.entity.ntp.clockinterface.NtpClockInterfaceEventHandler"/>
    <bean id="boundaryClockEventHandler" class="com.adva.nlms.mediation.config.f3.entity.ptp.bc.BoundaryClockEventHandler"/>
    <bean id="masterVirtualPortEventHandler" class="com.adva.nlms.mediation.config.f3.entity.ptp.mvp.MasterVirtualPortEventHandler"/>
    <bean id="portCpdProfileEventHandler" class="com.adva.nlms.mediation.config.f3.entity.portcpdprofile.PortCpdProfileEventHandler"/>
    <bean id="flowPointCpdProfileEventHandler" class="com.adva.nlms.mediation.config.f3.entity.flowpointcpdprofile.FlowPointCpdProfileEventHandler"/>
    <bean id="trafficPortCpdEventHandler" class="com.adva.nlms.mediation.config.f3.entity.trafficportcpd.TrafficPortCpdEventHandler"/>
    <bean id="flowPointCpdEventHandler" class="com.adva.nlms.mediation.config.f3.entity.flowpointcpd.FlowPointCpdEventHandler"/>
    <bean id="ethernetTrafficPortVlanLpbEventHandler" class="com.adva.nlms.mediation.config.f3.entity.port.trafficport.vlanlpbk.EthernetTrafficPortVlanLpbEventHandler"/>
    <bean id="erpUnitF3EventHandler" class="com.adva.nlms.mediation.config.f3.entity.erpunit.ErpUnitF3EventHandler"/>
    <bean id="lldpPostProcessEventHandler" class="com.adva.nlms.mediation.config.lldp.LldpPostProcessEventHandler"/>
    <bean id="prioMapV2ProfileEventHandler" class="com.adva.nlms.mediation.config.f3.entity.profile.priomapv2.PrioMapV2ProfileEventHandler"/>
    <bean id="moduleCPMRFSP150CMEventHandler" class="com.adva.nlms.mediation.config.f3.entity.module.cpmr.ModuleCPMRFSP150CMEventHandler"/>
    <bean id="moduleGE102ProHEFMEventHandler" class="com.adva.nlms.mediation.config.f3.entity.module.efm.ModuleGE102ProHEFMEventHandler"/>
    <bean id="syncEPGEventHandler" class="com.adva.nlms.mediation.config.fspsyncprob.syncEProtectionGroup.SyncEPGEventHandler"/>
    <bean id="timeClockPGEventHandler" class="com.adva.nlms.mediation.config.fspsyncprob.timeClockProtectionGroup.TimeClockPGEventHandler"/>
    <bean id="ptpMciPGEventHandler" class="com.adva.nlms.mediation.config.fspsyncprob.ptpMciProtectionGroup.PtpMciPGEventHandler"/>
    <bean id="ocStmCardF3EventHandlerEGX" class="com.adva.nlms.mediation.config.f3.entity.module.ocstm.OcStmCardF3EventHandler">
        <constructor-arg value="#{T (com.adva.nlms.mediation.config.fsp150egx.neComm.OcnStmScanAndResyncGroupFSP150EGX$Mode).PSWPhysicalEntities}"></constructor-arg>
        <constructor-arg value="#{T (com.adva.nlms.mediation.config.fsp150egx.neComm.OcnStmScanAndResyncGroupFSP150EGX$Mode).PSWFullPath}"></constructor-arg>
    </bean>
    <bean id="ocnStmLineF3EventHandlerEGX" class="com.adva.nlms.mediation.config.f3.entity.port.ocnstm.OcnStmLineF3EventHandler">
        <constructor-arg value="#{T (com.adva.nlms.mediation.config.fsp150egx.neComm.OcnStmScanAndResyncGroupFSP150EGX$Mode).OCNSTMLine}"></constructor-arg>
    </bean>
    <bean id="ocnStmLineF3EventHandler" class="com.adva.nlms.mediation.config.f3.entity.port.ocnstm.OcnStmLineF3EventHandler">
        <constructor-arg value="#{T (com.adva.nlms.mediation.config.f3.neComm.OcnStmScanAndResyncGroup$Mode).OCNSTMLine}"></constructor-arg>
    </bean>

    <bean id="satopEventHandlerEGX" class="com.adva.nlms.mediation.config.f3.entity.satop.SatopEventHandler">
        <constructor-arg value="#{T (com.adva.nlms.mediation.config.fsp150egx.neComm.OcnStmScanAndResyncGroupFSP150EGX$Mode).Satop}"></constructor-arg>
    </bean>


    <bean id="satopEventHandler" class="com.adva.nlms.mediation.config.f3.entity.satop.SatopEventHandler">
        <constructor-arg value="#{T (com.adva.nlms.mediation.config.f3.neComm.OcnStmScanAndResyncGroup$Mode).Satop}"></constructor-arg>
    </bean>
    <bean id="peerNetworkElementEventHandlerFSP150CM" class="com.adva.nlms.mediation.config.fsp150cm.entity.PeerNetworkElementEventHandlerFSP150CM"/>
    <bean id="lagPortF3EventHandlerFSP150EGX" class="com.adva.nlms.mediation.config.fsp150egx.entity.lagport.LagPortF3EventHandlerFSP150EGX"/>
    <bean id="pseudoWireOcnStmCardEventHandler" class="com.adva.nlms.mediation.config.fsp150egx.entity.module.linecard.pwe.PseudoWireOcnStmCardEventHandler"/>
    <bean id="pseudoWireOcnStmCardEventHandler20x" class="com.adva.nlms.mediation.config.fsp20X.entity.module.linecard.pwe.PseudoWireOcnStmCardEventHandler"/>
    <bean id="portBitsFSP150EGXEventHandler" class="com.adva.nlms.mediation.config.fsp150egx.entity.port.bits.PortBitsFSP150EGXEventHandler"/>
    <bean id="mspProtectionGroupF3EventHandler" class="com.adva.nlms.mediation.config.fsp150egx.entity.msp.MspProtectionGroupF3EventHandler"/>
    <bean id="portF3AccEventHandler" class="com.adva.nlms.mediation.config.f3.entity.port.acc.PortF3AccEventHandler"/>
    <bean id="portF3NetEventHelper" class="com.adva.nlms.mediation.config.f3.entity.port.net.PortF3NetEventHelper"/>
    <bean id="lagPortF3EventHandler" class="com.adva.nlms.mediation.config.f3.entity.lagport.LagPortF3EventHandler"/>
    <bean id="accPrioMapEventHandler" class="com.adva.nlms.mediation.config.f3.entity.priomap.acc.AccPrioMapEventHandler"/>
    <bean id="netPrioMapEventHandler" class="com.adva.nlms.mediation.config.f3.entity.priomap.net.NetPrioMapEventHandler"/>
    <bean id="portBitsF3EventHandler" class="com.adva.nlms.mediation.config.f3.entity.port.bits.PortBitsF3EventHandler"/>
    <bean id="portBits8GroupF3EventHandler" class="com.adva.nlms.mediation.config.f3.entity.port.bits8group.PortBits8GroupF3EventHandler"/>
    <bean id="portPps16GroupF3EventHandler" class="com.adva.nlms.mediation.config.f3.entity.port.pps16group.PortPps16GroupF3EventHandler"/>
    <bean id="portClk16GroupF3EventHandler" class="com.adva.nlms.mediation.config.f3.entity.port.clk16group.PortClk16GroupF3EventHandler"/>
    <bean id="portTodPps16GroupF3EventHandler" class="com.adva.nlms.mediation.config.f3.entity.port.todpps16group.PortTodPps16GroupF3EventHandler"/>
    <bean id="ethernetXG1XCCEventHandler" class="com.adva.nlms.mediation.config.f3.entity.module.linecard.exg1xcc.EthernetXG1XCCEventHandler"/>
    <bean id="ethernetXG1SCCEventHandler" class="com.adva.nlms.mediation.config.f3.entity.module.linecard.exg1scc.EthernetXG1SCCEventHandler"/>
    <bean id="ethernetGE8SCCCardEventHandler" class="com.adva.nlms.mediation.config.f3.entity.module.linecard.ege8scc.EthernetGE8SCCCardEventHandler"/>
    <bean id="ethernetGE8SCryptoCCCardEventHandler" class="com.adva.nlms.mediation.config.f3.entity.module.linecard.ege8scryptocc.EthernetGE8SCryptoCCCardEventHandler"/>
    <bean id="ethernetGE8ECCCardEventHandler" class="com.adva.nlms.mediation.config.f3.entity.module.linecard.ege8ecc.EthernetGE8ECCCardEventHandler"/>
    <bean id="Bits16PortCardEventHandler" class="com.adva.nlms.mediation.config.f3.entity.module.linecard.bits16port.Bits16PortCardEventHandler"/>
    <bean id="Pps16PortCardEventHandler" class="com.adva.nlms.mediation.config.f3.entity.module.linecard.pps16port.Pps16PortCardEventHandler"/>
    <bean id="Clk16PortCardEventHandler" class="com.adva.nlms.mediation.config.f3.entity.module.linecard.clk16port.Clk16PortCardEventHandler"/>
    <bean id="TodPps16PortCardEventHandler" class="com.adva.nlms.mediation.config.f3.entity.module.linecard.todpps16port.TodPps16PortCardEventHandler"/>
    <bean id="gE4PortCardEventHandler" class="com.adva.nlms.mediation.config.f3.entity.module.linecard.ge4port.GE4PortCardEventHandler"/>
    <bean id="e1T1EventHandlerFSPTxx04" class="com.adva.nlms.mediation.config.fspTxx04.entity.e1t1.E1T1EventHandlerFSPTxx04"/>
    <bean id="e3T3EventHandlerFSPTxx04" class="com.adva.nlms.mediation.config.fspTxx04.entity.e3t3.E3T3EventHandlerFSPTxx04"/>
    <bean id="vcgF3EventHandlerFSPTxx04" class="com.adva.nlms.mediation.config.fspTxx04.entity.vcg.VcgF3EventHandlerFSPTxx04"/>

    <bean id="gpsReceiverPostEventHandlerOSA5331" class="com.adva.nlms.mediation.config.osa5331.entity.gpsreceiver.GPSReceiverPostEventHandlerOSA5331"/>
    <bean id="masterClockOSAAggregateEventHandler" class="com.adva.nlms.mediation.config.osa5331.entity.ptp.mc.MasterClockOSAAggregateEventHandler"/>

    <bean id="ptpClockEventHandler" class="com.adva.nlms.mediation.config.f3.entity.ptp.clock.PtpClockEventHandler"/>
    <bean id="portClockEventHandler" class="com.adva.nlms.mediation.config.f3.entity.ptp.port.PortClockEventHandler"/>
    <bean id="l3PortClockEventHandler" class="com.adva.nlms.mediation.config.f3.entity.ptp.port.L3PortClockEventHandler"/>


    <bean id="devSpecificPostProcessEventHandlerImpl" class="com.adva.nlms.mediation.config.entity.DevSpecificPostProcessEventHandlerImplImpl"/>
    <bean id="sysNamePostProcessEventHandler" class="com.adva.nlms.mediation.config.entity.SysNamePostProcessEventHandlerImpl"/>
    <bean id="sysContactSysLocationPostProcessEventHandler" class="com.adva.nlms.mediation.config.entity.SysContactSysLocationPostProcessEventHandlerImpl"/>
    <bean id="coldWarmStartPostProcessEventHandler" class="com.adva.nlms.mediation.config.entity.ColdWarmStartPostProcessEventHandler"/>
    <bean id="interfaceEquipmentPostProcessEventHandler" class="com.adva.nlms.mediation.config.entity.InterfaceEquipmentPostProcessEventHandlerImpl"/>

    <bean id="entityPhysicalEventHandler" class="com.adva.nlms.mediation.config.osa5548c.entity.EntityPhysicalEventHandler"/>

    <bean id="secureFlowF3EventHandler" class="com.adva.nlms.mediation.config.f3.entity.secureflow.SecureFlowF3EventHandler"/>
    <bean id="secureFlowRxSCF3EventHandler" class="com.adva.nlms.mediation.config.f3.entity.secureflowrxsc.SecureFlowRxSCF3EventHandler"/>
    <bean id="secureFlowTxSCF3EventHandler" class="com.adva.nlms.mediation.config.f3.entity.secureflowtxsc.SecureFlowTxSCF3EventHandler"/>

    <bean id="l3FlowPointEventHandler" class="com.adva.nlms.mediation.config.f3.entity.l3flowpoint.L3FlowPointEventHandler"/>
    <bean id="l3QosPolicerEventHandler" class="com.adva.nlms.mediation.config.f3.entity.l3qospolicer.L3QosPolicerEventHandler"/>
    <bean id="l3QosShaperEventHandler" class="com.adva.nlms.mediation.config.f3.entity.l3qosshaper.L3QosShaperEventHandler"/>
    <bean id="l3TrafficIPInterfaceEventHandler" class="com.adva.nlms.mediation.config.f3.entity.l3trafficipinterface.L3TrafficIPInterfaceEventHandler"/>

    <bean id="commonLegacyEventHandlerList"
          class="org.springframework.beans.factory.config.ListFactoryBean">
        <property name="sourceList">
            <list>
                <ref bean="devSpecificPostProcessEventHandlerImpl" />
                <ref bean="sysNamePostProcessEventHandler" />
                <ref bean="sysContactSysLocationPostProcessEventHandler" />
                <ref bean="interfaceEquipmentPostProcessEventHandler" />
                <ref bean="interfaceEquipmentPostProcessEventHandler" />
            </list>
        </property>
    </bean>
    <bean id="prioMapCOSEventHandler" class="com.adva.nlms.mediation.config.f3.entity.priomap.profile.PrioMapCOSEventHandler"/>
    <bean id="prioMapPriEventHandler" class="com.adva.nlms.mediation.config.f3.entity.priomap.profile.PrioMapPriEventHandler"/>
    <bean id="connectGuardConfigurationEventHandler" class="com.adva.nlms.mediation.config.f3.entity.connectguard.ConnectGuardConfigurationEventHandler"/>
    <bean id="keyExchangeProfileEventHandler" class="com.adva.nlms.mediation.config.f3.entity.connectguard.keyexchangeprofile.KeyExchangeProfileEventHandler"/>
    <bean id="redundancyGroupEventHandler" class="com.adva.nlms.mediation.config.f3.entity.redundancy.RedundancyGroupEventHandler"/>
    <bean id="accFlowPointQOSShaperEventHandler" class="com.adva.nlms.mediation.config.f3.entity.shaper.accfpshaper.QOSAccFlowPointShaperF3EventHandler"/>
    <bean id="netFlowPointQOSShaperEventHandler" class="com.adva.nlms.mediation.config.f3.entity.shaper.netfpshaper.QOSNetFlowPointShaperF3EventHandler"/>
    <!--shg support postpone to 9.5-->
    <bean id="splitHorizonGroupEventHandler" class="com.adva.nlms.mediation.config.f3.entity.shg.group.SplitHorizonGroupF3EventHandler"/>
    <bean id="splitHorizonGroupMemberEventHandler" class="com.adva.nlms.mediation.config.f3.entity.shg.member.SplitHorizonGroupMemberF3EventHandler"/>
    <bean id="qOSAccFlowPointPolicerF3EventHandler" class="com.adva.nlms.mediation.config.f3.entity.policer.qosflowpointpolicer.accfpqospolicer.QOSAccFlowPointPolicerF3EventHandler"/>
    <bean id="qOSNetFlowPointPolicerF3EventHandler" class="com.adva.nlms.mediation.config.f3.entity.policer.qosflowpointpolicer.netfpqospolicer.QOSNetFlowPointPolicerF3EventHandler"/>
    <bean id="flowPointNetEventHandler" class="com.adva.nlms.mediation.config.f3.entity.flowpoint.net.FlowPointNetEventHandler"/>
    <bean id="flowPointAccEventHandler" class="com.adva.nlms.mediation.config.f3.entity.flowpoint.acc.FlowPointAccEventHandler"/>
    <bean id="privilegeChangeEventHandler" class="com.adva.nlms.mediation.config.f3.entity.security.PrivilegeChangeEventHandler"/>
    <bean id="policerEnvelopeEventHandler" class="com.adva.nlms.mediation.config.f3.entity.policerenvelope.PolicerEnvelopeF3EventHandler"/>

    <bean id="f3GPSReceiverPortOSAEventHandler" class="com.adva.nlms.mediation.config.f3.entity.port.gpsreceiver.F3GSPReceiverPortOSAEventHandler"/>
    <bean id="elpGroupF3EventHandler" class="com.adva.nlms.mediation.config.f3.entity.elpgroup.ElpGroupF3EventHandler"/>
    <bean id="elpProtectedFlowF3EventHandler" class="com.adva.nlms.mediation.config.f3.entity.elpprotectedflow.ElpProtectedFlowF3EventHandler"/>
    <bean id="elpUnitF3EventHandler" class="com.adva.nlms.mediation.config.f3.entity.elpunit.ElpUnitF3EventHandler"/>
    <bean id="vxlanSegmentF3EventHandler" class="com.adva.nlms.mediation.config.f3.entity.vxlan.vxlansegment.VXLANSegmentF3EventHandler"/>
    <bean id="vtepF3EventHandler" class="com.adva.nlms.mediation.config.f3.entity.vxlan.vtep.VtepF3EventHandler"/>
    <bean id="greTunnelF3EventHandler" class="com.adva.nlms.mediation.config.f3.entity.gre.gretunnel.GreTunnelF3EventHandler"/>
    <bean id="greIpInterfaceF3EventHandler" class="com.adva.nlms.mediation.config.f3.entity.gre.greipinterface.GreIpInterfaceF3EventHandler"/>
    <!--<bean id="eoMplsPwF3EventHandler" class="com.adva.nlms.mediation.config.f3.entity.eomplspw.EoMplsPwF3EventHandler"/>-->

    <bean id="f3FpQosShaperEventHandler" class="com.adva.nlms.mediation.config.f3.entity.f3fpqosshaper.F3FpQosShaperEventHandler"/>
    <bean id="f3FpQosPolicerEventHandler" class="com.adva.nlms.mediation.config.f3.entity.f3fpqospolicer.F3FpQosPolicerEventHandler"/>
    <bean id="f3PrcEventHandler" class="com.adva.nlms.mediation.config.f3.entity.prc.F3PrcEventHandler"/>
    <bean id="vtepIpInterfaceF3EventHandler" class="com.adva.nlms.mediation.config.f3.entity.vxlan.vtepipinterface.VtepIpInterfaceF3EventHandler"/>
    <bean id="nteGE102ProHEventHandler" class="com.adva.nlms.mediation.config.fsp1XX.entity.nte.NteGE102ProHEventHandler"/>
    <bean id="f3FipsEventHandler" class="com.adva.nlms.mediation.config.f3.entity.fips.F3FipsEventHandler"/>
    <bean id="powerSupplyGE102ProHEventHandler" class="com.adva.nlms.mediation.config.fsp1XX.entity.psu.PowerSupplyGE102ProHEventHandler"/>

    <bean id="f3SystemFeatureEventHandler" class="com.adva.nlms.mediation.config.f3.entity.systemfeaturemanagement.F3SystemFeatureEventHandler"/>

    <bean id="wifiDongleConfigEventHandler" class="com.adva.nlms.mediation.config.f3.entity.wifidongle.WifiDongleEventHandler"/>
    <bean id="usb3GDCNPortEventHandler" class="com.adva.nlms.mediation.config.f3.entity.usb3gdcnport.Usb3GDCNPortEventHandler"/>
    <bean id="satStreamF3EventHandler" class="com.adva.nlms.mediation.config.f3.entity.sat.stream.SatStreamF3EventHandler"/>
    <bean id="twampSessionSenderEventHandler" class="com.adva.nlms.mediation.config.f3.entity.twamp.TwampSessionSenderEventHandler"/>
    <bean id="ntpRemoteServerEventHandler" class="com.adva.nlms.mediation.config.f3.entity.ntp.remoteserver.NtpRemoteServerEventHandler"/>
    <bean id="f3SystemClockEventHandler" class="com.adva.nlms.mediation.config.f3.entity.systemclock.F3SystemClockEventHandler"/>
    <bean id="remoteAuthServerEventHandler" class="com.adva.nlms.mediation.config.f3.entity.security.remoteauthserver.RemoteAuthServerEventHandler"/>
    <bean id="ntpRemoteClientEventHandler" class="com.adva.nlms.mediation.config.f3.entity.ntp.remoteclient.NtpRemoteClientEventHandler"/>
    <bean id="ntpTrackedClientEventHandler" class="com.adva.nlms.mediation.config.f3.entity.ntp.trackedclient.NtpTrackedClientEventHandler"/>

</beans>