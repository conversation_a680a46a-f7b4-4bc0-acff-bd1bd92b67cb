<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

    <!-- mo components configuration -->
    <bean id="arcStateHandlerFSP150CP_MX" class="com.adva.nlms.mediation.config.fsp150cp_mx.ArcStateHandlerFSP150CP_MX"/>

    <!-- pollings -->
    <bean id="sysInfoPollingFSP150CP" class="com.adva.nlms.mediation.config.polling.sysinfo.SysInfoPollingDefault">
        <constructor-arg name="sysInfoTrapIdString" value="fsp150NeAttributeValueChange"/>
    </bean>

    <bean class="com.adva.nlms.mediation.config.fsp150cp_mx.capabilities.CPInternalNECapabilities" />
    <bean class="com.adva.nlms.mediation.config.fsp150cp_mx.capabilities.CPNECapabilities" />
    <bean class="com.adva.nlms.mediation.config.fsp150cp_mx.capabilities.FSPCPMXSSOCapabilities"/>

    <bean id="networkElementPollingWorkerFSP150CPMXBeanContainer" class="com.adva.nlms.mediation.config.fsp150cp_mx.NetworkElementPollingWorkerFSP150CPMXBeanContainer"/>

</beans>