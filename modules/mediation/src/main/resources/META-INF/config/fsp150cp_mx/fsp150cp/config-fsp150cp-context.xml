<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:util="http://www.springframework.org/schema/util"
       xmlns:p="http://www.springframework.org/schema/p"
       xmlns:configuration="http://cxf.apache.org/transports/http/configuration"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util.xsd http://cxf.apache.org/transports/http/configuration http://cxf.apache.org/schemas/configuration/http-conf.xsd">

    <!-- pollings -->
    <bean id="sysInfoPollingFSP150CPEfm" class="com.adva.nlms.mediation.config.fsp150cp_mx.fsp150cp.polling.sysinfo.SysInfoPollingFSP150CP_EFM"/>

    <!-- observers -->
    <bean class="com.adva.nlms.mediation.config.fsp150cp_mx.fsp150cp.FDFrFSP150CPDeletionObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
</beans>