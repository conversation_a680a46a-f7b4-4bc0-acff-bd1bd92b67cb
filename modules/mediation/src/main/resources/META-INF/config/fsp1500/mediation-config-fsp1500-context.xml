<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

    <!-- mo components configuration-->
    <bean id="arcStateHandlerFSP1500" class="com.adva.nlms.mediation.config.fsp1500.ArcStateHandlerFSP1500"/>


    <!-- dao -->
    <bean class="com.adva.nlms.mediation.config.fsp1500.dao.PortFSP1500DAO" />

    <!-- pollings -->
    <bean id="pollingConfigurationWorkerFSP1500" class="com.adva.nlms.mediation.config.fsp1500.polling.configuration.PollingConfigurationWorkerFSP1500" scope="prototype"/>

    <!-- handlers -->
    <bean class="com.adva.nlms.mediation.config.fsp1500.servicediscovery.PortHdlrFSP1500" />

    <bean class="com.adva.nlms.mediation.config.fsp1500.capabilities.FSP1500InternalNECapabilities"/>
    <bean class="com.adva.nlms.mediation.config.fsp1500.capabilities.FSP1500NECapabilities"/>
</beans>