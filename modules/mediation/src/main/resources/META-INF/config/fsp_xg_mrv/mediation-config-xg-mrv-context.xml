<?xml version="1.0" encoding="UTF-8"?>
<!--
  -  Copyright 2023 Adtran Networks SE. All rights reserved.
  -
  -  Owner: tomaszm
  -->
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:util="http://www.springframework.org/schema/util"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
       http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util.xsd">
    <context:annotation-config/>
    <import resource="mediation-config-xg-mrv-dao-context.xml"/>
    <util:list id="mrvEventHandlers" value-type="com.adva.nlms.mediation.config.entity.IPostProcessEventHandler">
        <bean id="mrvEventHandler" class="com.adva.nlms.mediation.config.fsp_xg_mrv.MRVEventHandler"/>
        <bean id="linkUpLinkDownXGMRVEventHandler" class="com.adva.nlms.mediation.config.fsp_xg_mrv.entity.LinkUpLinkDownXGMRVEventHandler" />
    </util:list>
    <util:list id="z4806EventHandlers" value-type="com.adva.nlms.mediation.config.entity.IPostProcessEventHandler">
        <bean id="z4806EventHandler" class="com.adva.nlms.mediation.config.fsp_z4806.Z4806EventHandler"/>
        <bean id="linkUpLinkDownXGMRVEventHandler" class="com.adva.nlms.mediation.config.fsp_xg_mrv.entity.LinkUpLinkDownXGMRVEventHandler" />
    </util:list>


    <bean class="com.adva.nlms.mediation.config.fsp_xg_mrv.sm.ServiceManagerMOFacadeXGMRV"/>

    <bean id="xg3xxAdminStateSetter" class="com.adva.nlms.mediation.config.sm.impl.eth.AdminStateSetterImpl">
        <property name="moClasses">
            <map key-type="java.lang.Class">
                <entry key="com.adva.nlms.mediation.config.fsp_xg_mrv.entity.xg3xx.service.ServiceXG3xxDBImpl">
                    <value>#{T(com.adva.nlms.common.snmp.MIBMRV$osEthServTable).OID_ADMIN_STATUS}</value>
                </entry>
                <entry key="com.adva.nlms.mediation.config.fsp_xg_mrv.entity.xg3xx.port.PortXG3xxDBImpl">
                    <value>#{T(com.adva.nlms.common.snmp.MIB$If).OID_ADMIN_STATUS}</value>
                </entry>
            </map>
        </property>
    </bean>
    <bean class="com.adva.nlms.mediation.config.fsp_xg_mrv.entity.observer.PortXG4xxOperStateUpdateObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>

    <bean class="com.adva.nlms.mediation.config.fsp_xg_mrv.entity.observer.PortXG3xxOperStateUpdateObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>

    <bean id="sysInfoPollingFSPXGMRV" class="com.adva.nlms.mediation.config.polling.sysinfo.SysInfoPollingDefault">
        <constructor-arg name="sysInfoTrapIdString" value="cmStateChangeTrap"/>
    </bean>

    <bean class="com.adva.nlms.mediation.config.fsp_xg_mrv.capabilities.XGMRVInternalNECapabilities"/>
    <bean class="com.adva.nlms.mediation.config.fsp_xg_mrv.capabilities.XGMRVNECapabilities"/>
    <bean class="com.adva.nlms.mediation.config.fsp_xg_mrv.capabilities.XGMRVSnmpCapabilities"/>
    <bean class="com.adva.nlms.mediation.config.fsp_xg_mrv.capabilities.XGMRVEPDCapabilities"/>
    <bean class="com.adva.nlms.mediation.config.fsp_z4806.capabilities.Z4806InternalNECapabilities"/>
    <bean class="com.adva.nlms.mediation.config.fsp_z4806.capabilities.Z4806NECapabilities"/>
    <bean class="com.adva.nlms.mediation.config.fsp_xg_mrv.capabilities.XGMRVLldpCapabilities"/>

    <bean id="networkElementPollingWorkerMRVBeanContainer" class="com.adva.nlms.mediation.config.fsp_xg_mrv.NetworkElementPollingWorkerMRVBeanContainer"/>

</beans>