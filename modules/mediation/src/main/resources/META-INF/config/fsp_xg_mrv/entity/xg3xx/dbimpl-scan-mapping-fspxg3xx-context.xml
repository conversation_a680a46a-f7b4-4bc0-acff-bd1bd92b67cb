<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

    <bean class="org.springframework.beans.factory.config.MethodInvokingFactoryBean">
        <property name="targetObject" ref="dbImplToScanMappingManager"/>
        <property name="targetMethod" value="registerMapping"/>
        <property name="arguments">
            <list>
                <list value-type="com.adva.nlms.mediation.config.entity.mapping.DBImplToScanMapping">
                    <bean class="com.adva.nlms.mediation.config.fsp_xg_mrv.entity.xg3xx.shelf.ShelfXG3xxScanMapping"/>
                    <bean class="com.adva.nlms.mediation.config.fsp_xg_mrv.entity.xg3xx.module.ModuleXG3xxScanMapping"/>
                    <bean class="com.adva.nlms.mediation.config.fsp_xg_mrv.entity.xg3xx.port.PortXG3xxScanMapping"/>
                    <bean class="com.adva.nlms.mediation.config.fsp_xg_mrv.entity.xg3xx.module.psu.PowerSupplyXG3xxScanMapping"/>
                    <bean class="com.adva.nlms.mediation.config.fsp_xg_mrv.entity.xg3xx.module.fan.FanXG3xxScanMapping"/>
                    <bean class="com.adva.nlms.mediation.config.fsp_xg_mrv.entity.xg3xx.service.ServiceXG3xxScanMapping"/>
                </list>
                <list value-type="com.adva.nlms.common.config.netypes.NEType">
                    <value>FSP_XG304</value>
                    <value>FSP_XG304u</value>
                    <value>FSP_XG308</value>
                    <value>FSP_XG304f</value>
                    <value>FSP_XG304uf</value>
                    <value>FSP_XG308f</value>
                    <value>FSP_XG312f</value>
                </list>
            </list>
        </property>
    </bean>

    <bean class="org.springframework.beans.factory.config.MethodInvokingFactoryBean">
        <property name="targetObject" ref="managedObjectControllerManager"/>
        <property name="targetMethod" value="registerMapping" />
        <property name="arguments" >
            <list>
                <list value-type="com.adva.nlms.mediation.config.f3.entity.ManagedObjectController">
                    <bean class="com.adva.nlms.mediation.config.fsp_xg_mrv.entity.xg3xx.shelf.ShelfXG3xxController"/>
                    <bean class="com.adva.nlms.mediation.config.fsp_xg_mrv.entity.xg3xx.module.ModuleXG3xxController"/>
                    <bean class="com.adva.nlms.mediation.config.fsp_xg_mrv.entity.xg3xx.port.PortXG3xxController"/>
                    <bean class="com.adva.nlms.mediation.config.fsp_xg_mrv.entity.xg3xx.module.psu.PowerSupplyXG3xxController"/>
                    <bean class="com.adva.nlms.mediation.config.fsp_xg_mrv.entity.xg3xx.module.sfp.SfpXG3xxController"/>
                    <bean class="com.adva.nlms.mediation.config.fsp_xg_mrv.entity.xg3xx.module.fan.FanXG3xxController"/>
                    <bean class="com.adva.nlms.mediation.config.fsp_xg_mrv.entity.xg3xx.service.ServiceXG3xxController"/>
                    <bean class="com.adva.nlms.mediation.config.fsp_xg_mrv.entity.xg3xx.soam.dm.SoamDmXG3xxController"/>
                    <bean class="com.adva.nlms.mediation.config.fsp_xg_mrv.entity.xg3xx.soam.lm.SoamLmXG3xxController"/>
                </list>
                <list value-type="com.adva.nlms.common.config.netypes.NEType">
                    <value>FSP_XG304</value>
                    <value>FSP_XG304u</value>
                    <value>FSP_XG308</value>
                    <value>FSP_XG304f</value>
                    <value>FSP_XG304uf</value>
                    <value>FSP_XG308f</value>
                    <value>FSP_XG312f</value>
                </list>
            </list>
        </property>
    </bean>

</beans>