<?xml version="1.0" encoding="UTF-8"?>
<!--
  -  Copyright 2023 Adtran Networks SE. All rights reserved.
  -
  -  Owner: tomaszm
  -->
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:util="http://www.springframework.org/schema/util"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
       http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util.xsd">
    <context:annotation-config/>
    <util:list id="xg1xxEventHandlers" value-type="com.adva.nlms.mediation.config.entity.IPostProcessEventHandler">
        <ref bean="physicalEntityEventHandler"/>
        <ref bean="shelfF3EventHandler"/>
        <ref bean="nteF3EventHandler"/>
        <ref bean="esaProbeScheduleGroupF3EventHandler"/>
        <ref bean="esaProbeF3EventHandler"/>
        <ref bean="esaProbeCosConfigF3EventHandler"/>
        <ref bean="esaProbeMultiMepF3EventHandler"/>
        <ref bean="esaReflectorEventHandler"/>

        <ref bean="ethernetTrafficPortF3EventHandler"/>
        <ref bean="flowPointEventHandler"/>
        <ref bean="qosShaperV2EventHandler"/>
        <ref bean="qosPolicerV2EventHandler"/>
        <ref bean="qosTrafficPortShaperEventHandler"/>

        <ref bean="lagF3EventHandler"/>
        <ref bean="lagPortF3EventHandlerFSP150EGX"/>
        <!--<ref bean="lagPortF3EventHandler"/>-->

        <ref bean="ifPortEventHandler"/>
        <ref bean="systemEventHandler"/>
        <ref bean="softwareActivationEventHandler"/>
        <ref bean="fanF3EventHandler"/>

        <ref bean="protectionGroupF3EventHandler"/>
        <ref bean="protectionGroupPortF3EventHandler"/>

        <ref bean="f3DatabaseSyncTrapEventHandler"/>

        <ref bean="accPrioMapEventHandler"/>
        <ref bean="netPrioMapEventHandler"/>

        <ref bean="maNetEventHandler"/>
        <ref bean="maCompEventHandler"/>
        <ref bean="mepEventHandler"/>
        <ref bean="mdEventHandler"/>
        <ref bean="mepListEventHandler"/>
        <ref bean="erpGroupF3EventHandler"/>
        <ref bean="erpUnitF3EventHandler"/>
        <ref bean="lldpPostProcessEventHandler"/>

        <ref bean="splitHorizonGroupEventHandler"/>
        <ref bean="splitHorizonGroupMemberEventHandler"/>
        <ref bean="mpFlowEventHandler"/>
        <ref bean="prioMapV2ProfileEventHandler"/>
        <ref bean="prioMapPriEventHandler"/>
        <ref bean="prioMapCOSEventHandler"/>
        <ref bean="privilegeChangeEventHandler"/>
        <ref bean="elpGroupF3EventHandler"/>
        <ref bean="elpProtectedFlowF3EventHandler"/>
        <ref bean="elpUnitF3EventHandler"/>
        <ref bean="f3FpQosShaperEventHandler"/>
        <ref bean="f3FpQosPolicerEventHandler"/>
        <ref bean="policerEnvelopeEventHandler"/>
        <ref bean="ptptpFlowPointEventHandler"/>
        <ref bean="f3FipsEventHandler"/>
        <ref bean="managementTunnelEventHandler"/>

        <ref bean="wifiDongleConfigEventHandler"/>
        <ref bean="usb3GDCNPortEventHandler"/>

        <ref bean="portBitsF3EventHandler"/>
        <ref bean="f3PulsePerSecondPortEventHandler"/>
        <ref bean="f3TimeOfDayPortEventHandler"/>

        <ref bean="f3SyncEventHandler"/>
        <ref bean="f3SyncRefEventHandler"/>

        <ref bean="ptpClockEventHandler"/>
        <ref bean="portClockEventHandler"/>
        <ref bean="boundaryClockEventHandler"/>
        <ref bean="masterClockInterfaceEventHandler"/>
        <ref bean="masterVirtualPortEventHandler"/>
        <ref bean="ocSlaveVirtualPortEventHandler"/>
        <ref bean="soocEventHandler"/>
        <ref bean="ocSlaveEventHandler"/>

        <ref bean="staticRemoteSlaveEventHandler"/>
        <ref bean="dynamicRemoteSlaveEventHandler"/>
        <ref bean="l2dynamicRemoteSlaveEventHandler"/>

        <ref bean="vmServerModuleEventHandler"/>

        <ref bean="satStreamF3EventHandler"/>
        <ref bean="twampSessionSenderEventHandler"/>

        <ref bean="peerNetworkElementEventHandlerFSP150CM"/>
        <ref bean="moduleGE102ProHEFMEventHandler"/>
        <ref bean="powerSupplyGE102ProHEventHandler"/>

    </util:list>

    <!--<bean class="com.adva.nlms.mediation.config.fsp210.sm.ServiceManagerMOFacadeXG210"/>-->
    <bean class="com.adva.nlms.mediation.config.fsp_xg1xx.capabilities.XG1XXEPDCapabilities" />
    <bean class="com.adva.nlms.mediation.config.fsp_xg1xx.capabilities.XG1XXInternalNECapabilities" />
    <bean class="com.adva.nlms.mediation.config.fsp_xg1xx.capabilities.XG1XXNetworkElementCapabilities" />
</beans>