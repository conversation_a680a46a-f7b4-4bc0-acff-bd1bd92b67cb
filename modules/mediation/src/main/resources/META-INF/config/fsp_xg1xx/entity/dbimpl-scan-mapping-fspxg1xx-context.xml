<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

    <bean class="org.springframework.beans.factory.config.MethodInvokingFactoryBean">
    <property name="targetObject" ref="dbImplToScanMappingManager"/>
    <property name="targetMethod" value="registerMapping" />
    <property name="arguments" >
        <list>
            <list value-type="com.adva.nlms.mediation.config.entity.mapping.DBImplToScanMapping">
                <bean class="com.adva.nlms.mediation.config.fsp_xg1xx.entity.trafficport.EthernetTrafficPortF3DBImplScanMapping"/>
                <bean class="com.adva.nlms.mediation.config.f3.entity.module.nte.NteF3DBImplScanMapping">
                    <constructor-arg type="java.lang.Class" value="com.adva.nlms.mediation.config.model.definition.f3.ScanTablesF3New.EthernetNtexg116CardTable"/>
                </bean>
            </list>
            <list value-type="com.adva.nlms.common.config.netypes.NEType">
                <value>FSP_XG116</value>
            </list>
        </list>
    </property>
    </bean>
    <bean class="org.springframework.beans.factory.config.MethodInvokingFactoryBean">
        <property name="targetObject" ref="dbImplToScanMappingManager"/>
        <property name="targetMethod" value="registerMapping" />
        <property name="arguments" >
            <list>
                <list value-type="com.adva.nlms.mediation.config.entity.mapping.DBImplToScanMapping">
                    <bean class="com.adva.nlms.mediation.config.fsp_xg1xx.entity.trafficport.EthernetTrafficPortF3DBImplScanMapping"/>
                    <bean class="com.adva.nlms.mediation.config.f3.entity.module.nte.NteF3DBImplScanMapping">
                        <constructor-arg type="java.lang.Class" value="com.adva.nlms.mediation.config.model.definition.f3.ScanTablesF3New.EthernetNtexg116HCardTable"/>
                    </bean>
                </list>
                <list value-type="com.adva.nlms.common.config.netypes.NEType">
                    <value>FSP_XG116H</value>
                </list>
            </list>
        </property>
    </bean>

    <bean class="org.springframework.beans.factory.config.MethodInvokingFactoryBean">
        <property name="targetObject" ref="dbImplToScanMappingManager"/>
        <property name="targetMethod" value="registerMapping" />
        <property name="arguments" >
            <list>
                <list value-type="com.adva.nlms.mediation.config.entity.mapping.DBImplToScanMapping">
                    <bean class="com.adva.nlms.mediation.config.fsp_xg1xx.entity.trafficport.EthernetTrafficPortF3DBImplScanMapping"/>
                    <bean class="com.adva.nlms.mediation.config.f3.entity.module.nte.NteF3DBImplScanMapping">
                        <constructor-arg type="java.lang.Class" value="com.adva.nlms.mediation.config.model.definition.f3.ScanTablesF3New.EthernetNtexg120CardTable"/>
                    </bean>
                </list>
                <list value-type="com.adva.nlms.common.config.netypes.NEType">
                    <value>FSP_XG120</value>
                </list>
            </list>
        </property>
    </bean>

    <bean class="org.springframework.beans.factory.config.MethodInvokingFactoryBean">
        <property name="targetObject" ref="dbImplToScanMappingManager"/>
        <property name="targetMethod" value="registerMapping" />
        <property name="arguments" >
            <list>
                <list value-type="com.adva.nlms.mediation.config.entity.mapping.DBImplToScanMapping">
                    <bean class="com.adva.nlms.mediation.config.fsp_xg1xx.entity.trafficport.EthernetTrafficPortF3DBImplScanMapping"/>
                    <bean class="com.adva.nlms.mediation.config.f3.entity.module.nte.NteF3DBImplScanMapping">
                        <constructor-arg type="java.lang.Class" value="com.adva.nlms.mediation.config.model.definition.f3.ScanTablesF3New.EthernetNTEXG120PROSHCardTable"/>
                    </bean>
                </list>
                <list value-type="com.adva.nlms.common.config.netypes.NEType">
                    <value>FSP_XG120PROSH</value>
                </list>
            </list>
        </property>
    </bean>

    <bean class="org.springframework.beans.factory.config.MethodInvokingFactoryBean">
        <property name="targetObject" ref="dbImplToScanMappingManager"/>
        <property name="targetMethod" value="registerMapping" />
        <property name="arguments" >
            <list>
                <list value-type="com.adva.nlms.mediation.config.entity.mapping.DBImplToScanMapping">
                    <bean class="com.adva.nlms.mediation.config.fsp_xg1xx.entity.trafficport.EthernetTrafficPortF3DBImplScanMapping"/>
                    <bean class="com.adva.nlms.mediation.config.f3.entity.module.nte.NteF3DBImplScanMapping">
                        <constructor-arg type="java.lang.Class" value="com.adva.nlms.mediation.config.model.definition.f3.ScanTablesF3New.EthernetNtexg118PROSHCardTable"/>
                    </bean>
                </list>
                <list value-type="com.adva.nlms.common.config.netypes.NEType">
                    <value>FSP_XG118PROSH</value>
                </list>
            </list>
        </property>
    </bean>

    <bean class="org.springframework.beans.factory.config.MethodInvokingFactoryBean">
        <property name="targetObject" ref="dbImplToScanMappingManager"/>
        <property name="targetMethod" value="registerMapping" />
        <property name="arguments" >
            <list>
                <list value-type="com.adva.nlms.mediation.config.entity.mapping.DBImplToScanMapping">
                    <bean class="com.adva.nlms.mediation.config.fsp_xg1xx.entity.trafficport.EthernetTrafficPortF3DBImplScanMapping"/>
                    <bean class="com.adva.nlms.mediation.config.f3.entity.module.nte.NteF3DBImplScanMapping">
                        <constructor-arg type="java.lang.Class" value="com.adva.nlms.mediation.config.model.definition.f3.ScanTablesF3New.EthernetNtexg118PROACSHCardTable"/>
                    </bean>
                </list>
                <list value-type="com.adva.nlms.common.config.netypes.NEType">
                    <value>FSP_XG118PROACSH</value>
                </list>
            </list>
        </property>
    </bean>


    <bean class="org.springframework.beans.factory.config.MethodInvokingFactoryBean">
        <property name="targetObject" ref="managedObjectControllerManager"/>
        <property name="targetMethod" value="registerMapping" />
        <property name="arguments" >
            <list>
                <list value-type="com.adva.nlms.mediation.config.f3.entity.ManagedObjectController">
                    <bean class="com.adva.nlms.mediation.config.fsp_xg1xx.entity.controllers.NteXg116Controller"/>
                </list>
                <list value-type="com.adva.nlms.common.config.netypes.NEType">
                    <value>FSP_XG116</value>
                </list>
            </list>
        </property>
    </bean>
    <bean class="org.springframework.beans.factory.config.MethodInvokingFactoryBean">
        <property name="targetObject" ref="managedObjectControllerManager"/>
        <property name="targetMethod" value="registerMapping" />
        <property name="arguments" >
            <list>
                <list value-type="com.adva.nlms.mediation.config.f3.entity.ManagedObjectController">
                    <bean class="com.adva.nlms.mediation.config.fsp_xg1xx.entity.controllers.NteXg116HController"/>
                </list>
                <list value-type="com.adva.nlms.common.config.netypes.NEType">
                    <value>FSP_XG116H</value>
                </list>
            </list>
        </property>
    </bean>
    <bean class="org.springframework.beans.factory.config.MethodInvokingFactoryBean">
        <property name="targetObject" ref="managedObjectControllerManager"/>
        <property name="targetMethod" value="registerMapping" />
        <property name="arguments" >
            <list>
                <list value-type="com.adva.nlms.mediation.config.f3.entity.ManagedObjectController">
                    <bean class="com.adva.nlms.mediation.config.fsp_xg1xx.entity.controllers.NteXg120Controller"/>
                </list>
                <list value-type="com.adva.nlms.common.config.netypes.NEType">
                    <value>FSP_XG120</value>
                </list>
            </list>
        </property>
    </bean>
    <bean class="org.springframework.beans.factory.config.MethodInvokingFactoryBean">
        <property name="targetObject" ref="managedObjectControllerManager"/>
        <property name="targetMethod" value="registerMapping" />
        <property name="arguments" >
            <list>
                <list value-type="com.adva.nlms.mediation.config.f3.entity.ManagedObjectController">
                    <bean class="com.adva.nlms.mediation.config.fsp_xg1xx.entity.controllers.NteXg120PROSHController"/>
                </list>
                <list value-type="com.adva.nlms.common.config.netypes.NEType">
                    <value>FSP_XG120PROSH</value>
                </list>
            </list>
        </property>
    </bean>
    <bean class="org.springframework.beans.factory.config.MethodInvokingFactoryBean">
        <property name="targetObject" ref="managedObjectControllerManager"/>
        <property name="targetMethod" value="registerMapping" />
        <property name="arguments" >
            <list>
                <list value-type="com.adva.nlms.mediation.config.f3.entity.ManagedObjectController">
                    <bean class="com.adva.nlms.mediation.config.fsp_xg1xx.entity.controllers.NteXg118PROSHController"/>
                </list>
                <list value-type="com.adva.nlms.common.config.netypes.NEType">
                    <value>FSP_XG118PROSH</value>
                </list>
            </list>
        </property>
    </bean>
    <bean class="org.springframework.beans.factory.config.MethodInvokingFactoryBean">
        <property name="targetObject" ref="managedObjectControllerManager"/>
        <property name="targetMethod" value="registerMapping" />
        <property name="arguments" >
            <list>
                <list value-type="com.adva.nlms.mediation.config.f3.entity.ManagedObjectController">
                    <bean class="com.adva.nlms.mediation.config.fsp_xg1xx.entity.controllers.NteXg118PROACSHController"/>
                </list>
                <list value-type="com.adva.nlms.common.config.netypes.NEType">
                    <value>FSP_XG118PROACSH</value>
                </list>
            </list>
        </property>
    </bean>
</beans>