<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

    <!-- mo components configuration -->
    <bean id="arcStateHandlerFSP150CC" class="com.adva.nlms.mediation.config.fsp150cc.ArcStateHandlerFSP150CC"/>
    <bean id="networkElementPollingWorkerFSP150CCBeanContainer" class="com.adva.nlms.mediation.config.fsp150cc.NetworkElementPollingWorkerFSP150CCBeanContainer"/>

    <bean id="sysInfoPollingFSP150CC" class="com.adva.nlms.mediation.config.polling.sysinfo.SysInfoPollingDefault">
        <constructor-arg name="sysInfoTrapIdString" value="fsp150NeAttributeValueChange"/>
    </bean>




    <bean id="ifIndexBasedSNMPIndexDeterminator"
          class="com.adva.nlms.mediation.config.sm.impl.eth.IfIndexBasedSNMPIndexDeterminator"/>

    <bean id="ccAdminStateSetter" class="com.adva.nlms.mediation.config.sm.impl.eth.AdminStateSetterImpl"/>



    <bean class="com.adva.nlms.mediation.config.fsp150cc.dbconsistency.DBConsistencyCheckEthService"/>

    <bean class="com.adva.nlms.mediation.config.fsp150cc.capabilities.FSP150CCSSOCapabilities"/>
    <bean class="com.adva.nlms.mediation.config.fsp150cc.capabilities.FSP150CCInternalNECapabilities"/>
    <bean class="com.adva.nlms.mediation.config.fsp150cc.capabilities.FSP150CCNECapabilities"/>
</beans>