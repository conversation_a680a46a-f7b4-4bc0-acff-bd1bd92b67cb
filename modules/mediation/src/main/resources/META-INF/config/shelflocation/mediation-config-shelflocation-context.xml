<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:util="http://www.springframework.org/schema/util"
       xmlns:p="http://www.springframework.org/schema/p"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
        http://www.springframework.org/schema/context
        http://www.springframework.org/schema/context/spring-context-3.0.xsd
        http://www.springframework.org/schema/util
        http://www.springframework.org/schema/util/spring-util-3.0.xsd">
    <context:annotation-config/>


    <bean class="com.adva.nlms.mediation.config.shelflocation.ShelfLocationRestService">
        <constructor-arg ref="registeredNetworkElementsHdlr"/>
        <constructor-arg ref="shelfLocationServiceFactory"/>
        <constructor-arg ref="shelfLocationDAO"/>
    </bean>

    <bean class="com.adva.nlms.mediation.config.shelflocation.ShelfLocationServiceFactory" id="shelfLocationServiceFactory">
        <constructor-arg ref="shelfLocationServiceDefaultImpl"/>
        <constructor-arg ref="shelfLocationServiceF7Impl"/>
        <constructor-arg ref="shelfLocationServiceEcImpl"/>
    </bean>

    <bean class="com.adva.nlms.mediation.config.shelflocation.ShelfLocationDAO" id="shelfLocationDAO"/>

    <bean class="com.adva.nlms.mediation.config.shelflocation.ShelfLocationServiceDefaultImpl" id="shelfLocationServiceDefaultImpl">
        <constructor-arg ref="shelfLocationDAO"/>
    </bean>
</beans>