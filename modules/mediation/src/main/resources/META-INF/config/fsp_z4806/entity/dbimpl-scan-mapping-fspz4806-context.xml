<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~  Copyright 2023 Adtran Networks SE. All rights reserved.
  ~
  ~  Owner: tomaszw
  -->

<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

    <bean class="org.springframework.beans.factory.config.MethodInvokingFactoryBean">
        <property name="targetObject" ref="dbImplToScanMappingManager"/>
        <property name="targetMethod" value="registerMapping"/>
        <property name="arguments">
            <list>
                <list value-type="com.adva.nlms.mediation.config.entity.mapping.DBImplToScanMapping">
                    <bean class="com.adva.nlms.mediation.config.fsp_ea.entity.fan.FanEAScanMapping"/>
                    <bean class="com.adva.nlms.mediation.config.fsp_ea.entity.psu.PowerSupplyEAScanMapping"/>
                    <bean class="com.adva.nlms.mediation.config.fsp_ea.entity.linecard.LineCardEAScanMapping"/>
                    <bean class="com.adva.nlms.mediation.config.fsp_ea.entity.port.PortEAScanMapping"/>
                </list>
                <list value-type="com.adva.nlms.common.config.netypes.NEType">
                    <value>FSP_Z4806</value>
                </list>
            </list>
        </property>
    </bean>

    <bean class="org.springframework.beans.factory.config.MethodInvokingFactoryBean">
        <property name="targetObject" ref="managedObjectControllerManager"/>
        <property name="targetMethod" value="registerMapping" />
        <property name="arguments" >
            <list>
                <list value-type="com.adva.nlms.mediation.config.f3.entity.ManagedObjectController">
                    <bean class="com.adva.nlms.mediation.config.fsp_ea.entity.shelf.ShelfEAController"/>
                    <bean class="com.adva.nlms.mediation.config.fsp_ea.entity.slot.SlotEAController"/>
                    <bean class="com.adva.nlms.mediation.config.fsp_z4806.entity.fan.FanZ4806Controller"/>
                    <bean class="com.adva.nlms.mediation.config.fsp_ea.entity.psu.PowerSupplyEAController"/>
                    <bean class="com.adva.nlms.mediation.config.fsp_ea.entity.linecard.LineCardEAController"/>
                    <bean class="com.adva.nlms.mediation.config.fsp_ea.entity.port.PortEAController"/>
                    <bean class="com.adva.nlms.mediation.config.fsp_ea.entity.module.sfp.SfpEAController"/>
                </list>
                <list value-type="com.adva.nlms.common.config.netypes.NEType">
                    <value>FSP_Z4806</value>
                </list>
            </list>
        </property>
    </bean>

</beans>
