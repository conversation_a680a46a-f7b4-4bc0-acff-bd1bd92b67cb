<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~  Copyright 2023 Adtran Networks SE. All rights reserved.
  ~
  ~  Owner: polykarposc
  -->

<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:util="http://www.springframework.org/schema/util"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
       http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util.xsd">
  <context:annotation-config/>
  <bean class="com.adva.nlms.mediation.config.osa3350.entity.module.ModuleOSA3350DAO"/>
  <bean class="com.adva.nlms.mediation.config.osa3350.entity.port.PortOSA3350DAO"/>
  <util:list id="osa33X0EventHandlers" value-type="com.adva.nlms.mediation.config.entity.IPostProcessEventHandler">
    <ref bean="mgntF3EventHandler"/>
    <ref bean="f3PrcEventHandler"/>
    <ref bean="portBitsF3EventHandler"/>
    <ref bean="f3PulsePerSecondPortEventHandler"/>
    <ref bean="softwareActivationEventHandler"/>
    <!--<ref bean="physicalEntityEventHandler"/>-->
    <!--<ref bean="f3GPSReceiverPortOSAEventHandler"/>-->
    <!--<ref bean="soocEventHandler"/>-->
    <!--<ref bean="systemEventHandler"/>-->
    <!--<ref bean="tcEventHandler"/>-->
    <!--<ref bean="masterClockInterfaceEventHandler"/>-->
    <!--<ref bean="masterClockEventHandler"/>-->
    <!--<ref bean="f3SyncEventHandler"/>-->
    <!--<ref bean="f3SyncRefEventHandler"/>-->
    <!--<ref bean="f3TimeClockEventHandler"/>-->
    <!--<ref bean="f3TimeClockRefEventHandler"/>-->
    <!--<ref bean="ptpClockEventHandler"/>-->
    <!--<ref bean="portClockEventHandler"/>-->
    <!--<ref bean="entityPhysicalEventHandler"/>-->
    <!--<ref bean="redundancyGroupEventHandler"/>-->
    <!--<ref bean="f3DatabaseSyncTrapOSAEventHandler"/>-->
    <!--<ref bean="dynamicRemoteSlaveOSAEventHandler"/>-->
    <!--<ref bean="l2dynamicRemoteSlaveEventHandler"/>-->
    <!--<ref bean="ifPortEventHandler"/>-->
    <!--<ref bean="portF3AccEventHandler"/>-->
  </util:list>

  <bean class="com.adva.nlms.mediation.config.osa3350.capabilities.OSA33X0SSOCapabilities"/>
  <bean class="com.adva.nlms.mediation.config.osa3350.capabilities.OSA33X0InternalNECapabilities"/>
  <bean class="com.adva.nlms.mediation.config.osa3350.capabilities.OSA33X0NECapabilities"/>
</beans>


