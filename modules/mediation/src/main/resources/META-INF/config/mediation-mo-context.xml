<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright 2023 Adtran Networks SE. All rights reserved.
  ~
  ~ Owner: <PERSON><PERSON><PERSON>
  -->

<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
        http://www.springframework.org/schema/context
        http://www.springframework.org/schema/context/spring-context-3.0.xsd">
    <context:annotation-config/>

    <import resource="entity/mediation-config-entity-context.xml" />
    <import resource="security/privilege/mediation-config-security-privilege-context.xml"/>
    <import resource="shelflocation/mediation-config-shelflocation-context.xml"/>

    <!-- MO MBean -->
    <!-- If you want to expose any information via JMX interface just put it here -->
    <bean class="com.adva.nlms.mediation.config.NetworkElementHdlrInfo"/>
    <bean id="genericBeanContainer" class="com.adva.nlms.mediation.config.NetworkElementBeanContainer"/>
    <bean class="com.adva.nlms.mediation.config.NeEntityDescWorkerBeanContainer"/>
    <bean id="networkElementPropertiesBeanContainer" class="com.adva.nlms.mediation.config.NetworkElementPropertiesBeanContainer"/>
    <bean id="networkElementPollingWorkerBeanContainer" class="com.adva.nlms.mediation.config.NetworkElementPollingWorkerBeanContainer"/>
    <bean class="com.adva.nlms.mediation.config.moDesc.MODescriptionFactory" factory-method="getInstance"/>

    <!-- SR logs configuration -->
    <bean class="com.adva.nlms.mediation.config.sr.logconfig.SRLogConfigurationManager"/>
    <bean id="supportedNeTypeProvider" class="com.adva.nlms.mediation.SupportedNeTypeProvider"/>
    <import resource="f3/entity/event-handlers-f3-context.xml" />
    <import resource="ovn/entity/event-handlers-egm-context.xml" />
    <import resource="../housekeeping/swupgrade/event/event-handlers-swupgrade-context.xml" />
    <import resource="mediation-dbimpl-scan-mapping-context.xml" />

    <import resource="nettransaction/nettransaction-context.xml"/>
    <!-- import Spring context for specific devices -->
    <import resource="fsp_r7/mediation-config-f7-context.xml" />
    <import resource="fsp1500/mediation-config-fsp1500-context.xml" />
    <import resource="f3/mediation-config-f3-context.xml" />
    <import resource="driver/mediation-config-driver-context.xml" />
    <import resource="fsp150egx/mediation-config-fsp150egx-context.xml" />
    <import resource="f3_efm/mediation-config-f3-efm-context.xml" />
    <import resource="fsp1XX/mediation-config-fsp1xx-context.xml" />
    <import resource="hn400/mediation-config-hn400-context.xml" />
    <import resource="fsp210/mediation-config-xg210-context.xml" />
    <import resource="fsp_xg1xx/mediation-config-xg1xx-context.xml" />
    <import resource="fsp150cm/mediation-config-fsp150cm-context.xml" />
    <import resource="fspsyncprob/mediation-config-fspsyncprob-context.xml" />
    <import resource="fsp20X/mediation-config-fsp20x-context.xml" />
    <import resource="osa5331/mediation-config-osa5331-context.xml" />
    <import resource="fspTxx04/mediation-config-fspTxx04-context.xml" />
    <import resource="hn4000/mediation-config-hn4000-context.xml" />
    <import resource="fsp150cp_mx/fsp150mx/config-fsp150mx-context.xml" />
    <import resource="fsp150cp_mx/config-fsp150cp_mx-context.xml" />
    <import resource="fsp150cp_mx/fsp150cp/config-fsp150cp-context.xml" />
    <import resource="fsp150cc/config-fsp150cc-context.xml" />
    <import resource="fsp150cc/fsp150ccleg/config-fsp150ccleg-context.xml" />
    <import resource="unmanaged/mediation-config-unmanaged-context.xml" />
    <import resource="mtosi/mtosi-mo-facade-context.xml" />
    <import resource="mtosi/egx-mtosi-mo-facade-context.xml" />
    <import resource="mtosi/efm-mtosi-mo-facade-context.xml" />
    <import resource="mtosi/ge11x-mtosi-mo-facade-context.xml" />
    <import resource="mtosi/xg210-mtosi-mo-facade-context.xml" />
    <import resource="mtosi/xg1xx-mtosi-mo-facade-context.xml" />
    <import resource="mtosi/ge20X-mtosi-mo-facade-context.xml" />
    <import resource="mtosi/osa-mtosi-mo-facade-context.xml" />
    <import resource="mtosi/fsp150cm-mtosi-mo-facade-context.xml" />
    <import resource="osa5548c/mediation-config-osa5548c-context.xml"/>
    <import resource="osa33X0/mediation-config-osa33X0-context.xml"/>
    <import resource="osa3230b/mediation-config-osa3230b-context.xml"/>
    <import resource="ovn/mediation-config-fsp150egm-context.xml"/>
    <import resource="osa540X/mediation-config-osa540X-context.xml" />
    <import resource="fsp_xg_mrv/mediation-config-xg-mrv-context.xml" />
    <import resource="f4/mediation-config-f4-context.xml" />
    <import resource="f8/mediation-config-f8-context.xml" />
    <import resource="pwr/mediation-config-pwr-context.xml" />
    <import resource="classpath*:META-INF/com/adva/nlms/mediation/config/f8/croma/provision/f8-croma-context.xml" />
    <import resource="classpath*:META-INF/com/adva/nlms/mediation/mo/inventory/f8-mo-inventory-context.xml" />
    <import resource="classpath*:META-INF/com/adva/nlms/mediation/mo/inventory/mo-inventory-context.xml" />
    <import resource="sr/service/mtosi-service-context.xml"/>
    <import resource="alm/mediation-alm-context.xml" />
    <import resource="opticalrouter/mediation-opticalrouter-context.xml" />
    <import resource="custom/mediation-custom-context.xml" />
    <import resource="neprofile/mediation-config-profile-context.xml" />
    <import resource="juniper/mediation-config-juniper-context.xml" />
    <import resource="symmetricon/mediation-config-symmetricon-context.xml"/>
    <import resource="customgnss/mediation-customgnss-context.xml"/>
    <import resource="customptpbc/mediation-customptpbc-context.xml"/>
    <import resource="mediation-mo-dao-context.xml"/>
    <!-- import lldp module -->
    <import resource="lldp/lldp-context.xml" />
    <import resource="classpath*:META-INF/mo-app-mltopo-context.xml" />

    <!--Ne Response state change notification handler -->
    <bean class="com.adva.nlms.mediation.config.NeResponseStateHdlr" init-method="start"/>
    <!-- mo layer components -->
    <bean id="registeredNetworkElementsHdlr" class="com.adva.nlms.mediation.config.RegisteredNetworkElementsHdlr"/>

    <bean class="com.adva.nlms.mediation.config.arc.ArcStateHandlerManager"/>
    <!--<bean class="com.adva.nlms.mediation.config.neconfig.SingleSignOnWorker" />-->
    <bean id="uriCreator" class="com.adva.nlms.mediation.config.fsp_r7.uricreation.UriCreator" >
        <constructor-arg type="java.lang.Class" value="com.adva.nlms.mediation.config.NetworkElementHdlrLocal"></constructor-arg>
    </bean>

    <bean id="ssoManager" class="com.adva.nlms.mediation.config.neconfig.SSOManager" />

    <bean class="com.adva.nlms.mediation.config.util.event.EventToDBObjectLinkerImpl" scope="prototype"/>

    <bean class="com.adva.nlms.mediation.config.NEECalculator"/>

    <bean class="com.adva.nlms.mediation.config.neconfig.impl.SecureProtocolsStatelessWorkerImpl"/>

    <bean id="communicationProtocolsConf" class="com.adva.nlms.mediation.config.neconfig.impl.CommunicationProtocolsConfigurationImpl" scope="prototype"/>

    <bean class="com.adva.nlms.mediation.config.mofacade.DtoRepositoryInitialization"/>
    <bean class="com.adva.nlms.mediation.config.mofacade.NetworkElementIdentityFormModelProvider"/>
    <bean class="com.adva.nlms.mediation.config.mofacade.InfoFormModelProvider"/>
    <bean class="com.adva.nlms.mediation.config.mofacade.HTTPFormModelProvider"/>
    <bean class="com.adva.nlms.mediation.config.mofacade.EODFormModelProvider"/>
    <!-- factories -->
    <bean class="com.adva.nlms.mediation.config.NetworkElementFactoryImpl"/>
    <bean class="com.adva.nlms.mediation.config.nefactory.NetworkElementDBImplFactoryImpl"/>
    <bean class="com.adva.nlms.mediation.config.dbconsistency.DBConsistencyCheckFactory"/>

    <!-- facades -->
    <bean class="com.adva.nlms.mediation.config.mtosi.LegacyMtosiMOFacadeImpl"/>

    <!-- SMMOFacade -->
    <bean id="smMoFacade" name="smMoFacade" class="com.adva.nlms.mediation.config.ServiceManagerMOFacadeManagerImpl"/>
    <bean class="com.adva.nlms.mediation.config.NetworkElementProviderImpl"/>
    <bean class="com.adva.nlms.mediation.config.entity.factory.NeDbObjectFactoryProviderImpl"/>
    <bean class="com.adva.nlms.mediation.config.model.NeModelManagerProviderImpl"/>
    <bean id="shelfCountProvider" class="com.adva.nlms.mediation.config.ShelfCountProvider"/>
    <bean class="com.adva.nlms.mediation.config.sm.impl.eth.SnmpObjectManager"/>

    <bean id="smOperationHandler" class="com.adva.nlms.mediation.config.mtosi.OperationHandler">
        <property name="deleteOperation">
            <map key-type="java.lang.Class">
                <entry key="com.adva.nlms.mediation.config.dto.attr.FlowF3Attr" value-ref="deleteFlowF3Operation"/>
                <entry key="com.adva.nlms.mediation.config.dto.attr.ESAProbeF3MIBAttr" value-ref="deleteESAProbeF3Operation"/>
                <entry key="com.adva.nlms.mediation.config.dto.attr.ErpGroupF3Attr" value-ref="deleteERPGroupF3Operation"/>
                <entry key="com.adva.nlms.mediation.config.dto.attr.SatopAttr" value-ref="deleteSatopOperation"/>
                <entry key="com.adva.nlms.mediation.config.dto.attr.ESAProbeScheduleGroupF3Attr" value-ref="deleteESAProbeScheduleGroupF3Operation"/>
                <entry key="com.adva.nlms.mediation.config.dto.attr.ElineFlowF3Attr" value-ref="deleteElineFlowF3Operation"/>
                <entry key="com.adva.nlms.mediation.config.dto.attr.MPFlowF3Attr" value-ref="deleteMPFlowF3Operation"/>
                <entry key="com.adva.nlms.mediation.config.dto.attr.FlowPointF3Attr" value-ref="deleteFlowPointOperation"/>
                <entry key="com.adva.nlms.mediation.config.dto.attr.FlowPointAccF3Attr" value-ref="deleteFlowPointAccF3Operation"/>
                <entry key="com.adva.nlms.mediation.config.dto.attr.FlowPointNetF3Attr" value-ref="deleteFlowPointNetF3Operation"/>
                <entry key="com.adva.nlms.mediation.config.f3.entity.logicalport.vcpath.vt.VtVcPathF3Attr" value-ref="deleteMO"/>
                <entry key="com.adva.nlms.mediation.config.f3.entity.logicalport.vcpath.sts.StsVcPathF3Attr" value-ref="deleteMO"/>
                <entry key="com.adva.nlms.mediation.config.f3.entity.logicalport.extx.AbstractExTxF3Attr" value-ref="deleteMO"/>

            </map>
        </property>
        <property name="modifyOperation">
            <map key-type="java.lang.Class">
                <entry key="com.adva.nlms.mediation.config.dto.attr.FlowF3Attr" value-ref="modifyFlowF3Operation"/>
                <entry key="com.adva.nlms.mediation.config.dto.attr.PortF3AccAttr" value-ref="modifyPortF3AccOperation"/>
                <entry key="com.adva.nlms.mediation.config.dto.attr.LldpPortConfigAdvaExtAttr" value-ref="modifyPortConfigAdvaOperation"/>
                <entry key="com.adva.nlms.mediation.config.dto.attr.MPFlowF3Attr" value-ref="modifyMPFlowF3Operation"/>
                <entry key="com.adva.nlms.mediation.config.dto.attr.ElineFlowF3Attr" value-ref="modifyElineFlowPointF3Operation"/>
                <entry key="com.adva.nlms.mediation.config.dto.attr.FlowPointAccF3Attr" value-ref="modifyFlowPointAccF3Operation"/>
                <entry key="com.adva.nlms.mediation.config.dto.attr.FlowPointF3Attr" value-ref="modifyFlowPointF3Operation"/>
                <entry key="com.adva.nlms.mediation.config.dto.attr.FlowPointNetF3Attr" value-ref="modifyFlowPointNetF3Operation"/>
            </map>
        </property>
    </bean>


    <bean class="com.adva.nlms.mediation.config.sm.impl.eth.EthServiceManagerMOFacadeFacadeDispatcher"/>
    <bean id="objectIndicesBasedSNMPIndexDeterminator" class="com.adva.nlms.mediation.config.sm.impl.eth.ObjectIndicesBasedSNMPIndexDeterminator"/>

    <bean id="defaultSnmpIndexDeterminators" class="com.adva.nlms.mediation.config.sm.impl.eth.SnmpIndexDeterminators">
        <constructor-arg type="com.adva.nlms.mediation.config.sm.impl.eth.SNMPIndexDeterminator">
            <bean class="com.adva.nlms.mediation.config.sm.impl.eth.ObjectIndicesBasedSNMPIndexDeterminator"/>
        </constructor-arg>
    </bean>
    <!-- SMMMOFacade End -->

    <bean class="com.adva.nlms.mediation.config.mofacade.reports.impl.ReportsMOFacadeManagerImpl"/>

    <!-- default configuration -->
    <bean id="arcStateHandlerDefault" class="com.adva.nlms.mediation.config.arc.ArcStateHandlerDefaultImpl"/>

    <!-- pollings -->
    <bean id="defaultPollingConfig" class="com.adva.nlms.mediation.polling.conf.DefaultPollingWaitConfig"/>

    <bean id="sysInfoPollingDefault" class="com.adva.nlms.mediation.config.polling.sysinfo.SysInfoPollingDefault" primary="true"/>

    <bean id="pollingConfigurationWorkerDefault" class="com.adva.nlms.mediation.config.polling.configuration.BasisPollingConfigurationWorker" scope="prototype"/>


    <bean class="com.adva.nlms.mediation.config.sr.logconfig.MOEventsSRObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean class="com.adva.nlms.mediation.config.sr.logconfig.NEEventsSRObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean class="com.adva.nlms.mediation.config.sr.logconfig.MAnetObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>

    <bean id="dataExportService" class="com.adva.nlms.mediation.config.DataExportService"/>

    <bean class="com.adva.nlms.mediation.config.event.MOPostProcessEventDelegate"/>
    <bean id = "moSynchronousEventProcessor" class="com.adva.nlms.mediation.config.event.MOSynchronousEventProcessor"/>

    <!-- identification by mac serial -->
    <bean class="com.adva.nlms.mediation.config.neIdentification.MacSerialIdentificationService" id="macSerialIdentificationService">
        <constructor-arg ref="networkElementDAO"/>
        <constructor-arg ref="eventsHelper"/>
        <constructor-arg ref="topologyNodeHdlr"/>
    </bean>
    <bean class="com.adva.nlms.mediation.config.neIdentification.EventsHelper" id="eventsHelper"/>
    <bean class="com.adva.nlms.mediation.config.neIdentification.NEIdDataUpdateObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
        <constructor-arg ref="macSerialIdentificationService"/>
        <constructor-arg ref="networkElementHdlr"/>
    </bean>

    <bean class="com.adva.nlms.mediation.config.polling.MACAndSerialCheckPolling">
        <qualifier type="com.adva.nlms.mediation.polling.NePolling"/>
        <constructor-arg  ref="macSerialIdentificationService"/>
    </bean>

    <bean class="com.adva.nlms.mediation.event.message.MessageManagerImpl" id="messageManager"/>

    <bean class="com.adva.nlms.mediation.config.polling.SynchronizationEcDataModelPolling">
        <qualifier type="com.adva.nlms.mediation.polling.DomainPolling"/>
        <constructor-arg ref="ecModelGenerator"/>
        <constructor-arg ref="registeredNetworkElementsHdlr"/>
        <constructor-arg ref="messageManager"/>
    </bean>

    <bean class="com.adva.nlms.mediation.config.polling.FindAndDiscoverPlannedNodePolling">
        <qualifier type="com.adva.nlms.mediation.polling.NePolling"/>
    </bean>

    <bean class="com.adva.nlms.mediation.config.polling.managedobject.MODescriptionsSynchronizer" factory-method="getInstance"/>

    <bean id="advaUsiExtractor" class="com.adva.nlms.mediation.config.countryOfOrigin.AdvaUsiExtractor"/>

    <bean class="com.adva.nlms.mediation.config.CountryOfOriginProperty">
        <constructor-arg ref="advaUsiExtractor"/>
    </bean>
    <bean class="com.adva.nlms.mediation.config.alm.ExportFpPollingCommand">
        <qualifier type="com.adva.nlms.mediation.polling.NePolling"/>
    </bean>
    <bean class="com.adva.nlms.mediation.config.alm.ExportFaAutoPollingCommand">
        <qualifier type="com.adva.nlms.mediation.polling.NePolling"/>
    </bean>
    <bean class="com.adva.nlms.mediation.config.arc.ArcStateEntityDBImplUpdateObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>

    <bean id="profileNeDao" class="com.adva.nlms.mediation.config.neprofile.ProfileNEDAOImpl"/>
    <bean id="neSearchForProfiles" class="com.adva.nlms.mediation.config.neprofile.NESearchForProfiles"/>


<!--   consistency checkers: -->
    <bean class="com.adva.nlms.mediation.config.dbconsistency.checkers.EntityObjectHierarchyChecker"/>
    <bean class="com.adva.nlms.mediation.config.dbconsistency.checkers.EntityObjectPortMapConsistencySubChecker"/>
    <bean class="com.adva.nlms.mediation.config.dbconsistency.checkers.EntityObjectConsistencyRenovator"/>
    <bean class="com.adva.nlms.mediation.config.dbconsistency.checkers.EntityObjectConsistencySubChecker"/>
    <bean class="com.adva.nlms.mediation.config.dbconsistency.checkers.EntityObjectOwnershipIssuesSubChecker"/>
    <bean class="com.adva.nlms.mediation.config.dbconsistency.checkers.MODescriptionsConsistencyCheck"/>
    <bean class="com.adva.nlms.mediation.config.dbconsistency.checkers.EthServicesMOCorrelationChecker"/>
    <bean class="com.adva.nlms.mediation.config.dbconsistency.checkers.PortTraceDBConsistencyChecker"/>

    <bean class="com.adva.nlms.mediation.config.DataManagerFactory" factory-method="getInstance"/>
<!-- EC(f8/f4) related beans -->
    <import resource="ec/mediation-config-ec-context.xml" />

    <bean id="csvNameAttributeDescriptionDecorator" class="com.adva.nlms.mediation.config.CsvNameAttributeDescriptionDecorator"/>
    <bean id="shortDescriptionAttributeDecorator" class="com.adva.nlms.mediation.config.ShortDescriptionAttributeDecorator" factory-method="getInstance"/>
    <bean id="mtosiNameAttributeDescriptionDecorator" class="com.adva.nlms.mediation.config.MtosiNameAttributeDescriptionDecorator" factory-method="getInstance"/>
    <bean id="fullDescriptionAttributeDecorator" class="com.adva.nlms.mediation.config.FullDescriptionAttributeDecorator" factory-method="getInstance"/>

    <bean class="com.adva.nlms.mediation.config.FanDataManagerCreator"/>
    <bean class="com.adva.nlms.mediation.config.PowerSupplyDataManagerCreator"/>
</beans>