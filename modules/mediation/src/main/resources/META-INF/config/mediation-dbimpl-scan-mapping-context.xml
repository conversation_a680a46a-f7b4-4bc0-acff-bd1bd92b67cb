<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

    <import resource="f3/entity/dbimpl-scan-mapping-f3-context.xml"/>
    <import resource="fsp1XX/entity/dbimpl-scan-mapping-1xx-context.xml"/>
    <import resource="fsp20X/entity/dbimpl-scan-mapping-20x-context.xml"/>
    <import resource="fsp150cm/entity/dbimpl-scan-mapping-fsp150cm-context.xml"/>
    <import resource="fsp150egx/entity/dbimpl-scan-mapping-fsp150egx-context.xml"/>
    <import resource="fsp210/entity/dbimpl-scan-mapping-fsp210-context.xml"/>
    <import resource="fspTxx04/entity/dbimpl-scan-mapping-fsptxx04-context.xml"/>
    <import resource="fspsyncprob/entity/dbimpl-scan-mapping-syncprobe-context.xml"/>
    <import resource="osa5548c/entity/dbimpl-scan-mapping-osa5548-context.xml"/>
    <import resource="osa5331/entity/dbimpl-scan-mapping-osa5331-context.xml"/>
    <import resource="ovn/entity/dbimpl-scan-mapping-egm-context.xml"/>
    <import resource="f3_efm/entity/dbimpl-scan-mapping-efm-context.xml"/>
    <import resource="fsp_xg1xx/entity/dbimpl-scan-mapping-fspxg1xx-context.xml"/>
    <import resource="hn400/entity/dbimpl-scan-mapping-hn400-context.xml"/>
    <import resource="osa540X/entity/dbimpl-scan-mapping-OSA540x-context.xml"/>
    <import resource="fsp_xg_mrv/entity/xg3xx/dbimpl-scan-mapping-fspxg3xx-context.xml"/>
    <import resource="fsp_z4806/entity/dbimpl-scan-mapping-fspz4806-context.xml"/>
</beans>
