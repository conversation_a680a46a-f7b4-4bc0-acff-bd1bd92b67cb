<?xml version="1.0" encoding="UTF-8"?>
<!--
  -  Copyright 2023 Adtran Networks SE. All rights reserved.
  -
  -  Owner: gerassimosm
  -->
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:util="http://www.springframework.org/schema/util"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
       http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util.xsd">
    <context:annotation-config/>
    <util:list id="osa3230bEventHandlers" value-type="com.adva.nlms.mediation.config.entity.IPostProcessEventHandler">
      <bean id="osa3230bEventHandler" class="com.adva.nlms.mediation.config.osa3230b.entity.OSA3230BEventHandler"/>
      <ref bean="softwareActivationEventHandler"/>
    </util:list>

    <bean id="sysInfoPollingOSA3230B" class="com.adva.nlms.mediation.config.osa3230b.polling.SysInfoPollingOSA3230B"/>

    <bean class="com.adva.nlms.mediation.config.osa3230b.capabilities.OSA3230BInternalNECapabilities"/>
    <bean class="com.adva.nlms.mediation.config.osa3230b.capabilities.OSA3230BNECapabilities"/>
</beans>