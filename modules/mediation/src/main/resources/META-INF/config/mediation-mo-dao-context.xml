<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright 2023 Adtran Networks SE. All rights reserved.
  ~
  ~ Owner: mateuszma
  -->
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans-3.0.xsd">

    <bean id="abstractModuleDAOManager" class="com.adva.nlms.mediation.config.entity.module.AbstractModuleDAOManager"/>



    <bean class="com.adva.nlms.mediation.config.NetworkElementDAO" id="networkElementDAO"/>
    <bean class="com.adva.nlms.mediation.config.EntityDAO" id="entityDAO" />
    <bean class="com.adva.nlms.mediation.config.EntityCRUD" id="entityCRUD"/>
    <bean class="com.adva.nlms.mediation.config.entity.slot.SlotDAO" />
    <bean id="portDAO" class="com.adva.nlms.mediation.config.PortDAO"/>
    <bean class="com.adva.nlms.mediation.config.ModuleDAO"/>
    <bean class="com.adva.nlms.mediation.config.ManagedObjectDAO" factory-method="getInstance" id="managedObjectDAO"/>
    <bean class="com.adva.nlms.mediation.config.dto.ManagedObjectDTODAO"/>
    <bean class="com.adva.nlms.mediation.config.dto.NetworkElementDTODAO"/>
    <bean class="com.adva.nlms.mediation.config.MOManagedObjectDAOImpl"  id="MOManagedObjectDAO"/>
    <bean class="com.adva.nlms.mediation.config.MOEntityDAOImpl"  id="MOEntityDAO"/>

    <bean class="com.adva.nlms.mediation.ne_comm.CliPropertiesService" id="cliPropertiesService"/>

</beans>