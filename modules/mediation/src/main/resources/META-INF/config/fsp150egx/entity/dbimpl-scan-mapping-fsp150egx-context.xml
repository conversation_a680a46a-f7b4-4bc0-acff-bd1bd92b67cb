<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

    <bean class="org.springframework.beans.factory.config.MethodInvokingFactoryBean">
        <property name="targetObject" ref="dbImplToScanMappingManager"/>
        <property name="targetMethod" value="registerMapping" />
        <property name="arguments" >
            <list>
                <list value-type="com.adva.nlms.mediation.config.entity.mapping.DBImplToScanMapping">
                    <bean name="egressPrioMapDBImplScanMapping" class="com.adva.nlms.mediation.config.f3.entity.priomap.egress.EgressPrioMapDBImplScanMappingEGX"/>
                    <bean name="ingressPrioMapDBImplScanMapping" class="com.adva.nlms.mediation.config.f3.entity.priomap.ingress.IngressPrioMapDBImplScanMappingEGX"/>
                </list>
                <list value-type="com.adva.nlms.common.config.netypes.NEType">
                    <value>FSP_150EGX</value>
                </list>
            </list>
        </property>
    </bean>

    <bean class="org.springframework.beans.factory.config.MethodInvokingFactoryBean">
        <property name="targetObject" ref="managedObjectControllerManager"/>
        <property name="targetMethod" value="registerMapping" />
        <property name="arguments" >
            <list>
                <list value-type="com.adva.nlms.mediation.config.f3.entity.ManagedObjectController">
                    <bean class="com.adva.nlms.mediation.config.f3.entity.logicalport.extx.e1t1.E1T1F3Controller"/>
                </list>
                <list value-type="com.adva.nlms.common.config.netypes.NEType">
                    <value>FSP_150EGX</value>
                </list>
            </list>
        </property>
    </bean>

    <bean class="org.springframework.beans.factory.config.MethodInvokingFactoryBean">
        <property name="targetObject" ref="managedObjectControllerManager"/>
        <property name="targetMethod" value="registerMapping" />
        <property name="arguments" >
            <list>
                <list value-type="com.adva.nlms.mediation.config.f3.entity.ManagedObjectController">
                    <bean class="com.adva.nlms.mediation.config.f3.entity.logicalport.extx.e3t3.E3T3F3Controller"/>
                </list>
                <list value-type="com.adva.nlms.common.config.netypes.NEType">
                    <value>FSP_150EGX</value>
                </list>
            </list>
        </property>
    </bean>


</beans>