<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright 2023 Adtran Networks SE. All rights reserved.
  ~
  ~ Owner: <PERSON>z<PERSON>
  -->
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:util="http://www.springframework.org/schema/util"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
       http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util.xsd">

    <context:annotation-config/>
    <bean id="dbObjectFactoryFSP150EGX" class="com.adva.nlms.mediation.config.fsp150egx.factory.impl.DBObjectFactoryFSP150EGXImpl"/>

    <bean id="fsp150EGXPollingConfig" class="com.adva.nlms.mediation.config.fsp150egx.NetworkElementFSP150EGXPollingConfiguration"/>
    <bean class="com.adva.nlms.mediation.config.fsp150egx.entity.module.linecard.pwe.PseudoWireOcnStmCardDAOFSP150EGX">
        <constructor-arg ref="pseudoWireOcnStmCardDAO"/>
    </bean>
    <bean class="com.adva.nlms.mediation.config.fsp150egx.entity.module.ocstm.OcStmCardF3DAOFSP150EGX">
        <constructor-arg ref="ocStmCardDAO"/>
    </bean>
    <util:list id="egxEventHandlers" value-type="com.adva.nlms.mediation.config.entity.IPostProcessEventHandler">
        <ref bean="ocnStmLineF3EventHandlerEGX"/>
        <ref bean="ocStmCardF3EventHandlerEGX"/>
        <ref bean="satopEventHandlerEGX"/>
        <ref bean="lagF3EventHandler"/>
        <ref bean="maNetEventHandler"/>
        <ref bean="maCompEventHandler"/>
        <ref bean="mdEventHandler"/>
        <ref bean="mepEventHandler"/>
        <ref bean="mepListEventHandler"/>
        <ref bean="elineFlowEventHandler"/>
        <ref bean="mpFlowEventHandler"/>
        <ref bean="flowPointEventHandler"/>
        <ref bean="qosShaperV2EventHandler"/>
        <ref bean="qosPolicerV2EventHandler"/>
        <ref bean="flowPointOAMEventHandler"/>
        <ref bean="qosTrafficPortShaperEventHandler"/>
        <ref bean="esaProbeScheduleGroupF3EventHandler"/>
        <ref bean="esaProbeF3EventHandler"/>
        <ref bean="esaProbeCosConfigF3EventHandler"/>
        <ref bean="esaProbeMultiMepF3EventHandler"/>
        <ref bean="esaReflectorEventHandler"/>
        <ref bean="lagPortF3EventHandlerFSP150EGX"/>
        <ref bean="ethernetTrafficPortF3EventHandler"/>
        <ref bean="ethernet1x10GCardF3EventHandler"/>
        <ref bean="ethernet10x1GCardF3EventHandler"/>
        <ref bean="ethernet1x10GHighPerformanceCardEventHandler"/>
        <ref bean="ethernet10x1GHighPerformanceCardEventHandler"/>
        <ref bean="ethernetFE36ECardEventHandler"/>
        <ref bean="swfF3EventHandler"/>
        <ref bean="stuF3EventHandler"/>
        <ref bean="stuHF3EventHandler"/>
        <ref bean="amiF3EventHandler"/>
        <ref bean="stiF3EventHandler"/>
        <ref bean="stiHF3EventHandler"/>
        <ref bean="nemiF3EventHandler"/>
        <ref bean="powerSupplyF3EventHandler"/>
        <ref bean="fanF3EventHandler"/>
        <ref bean="shelfF3EventHandler"/>
        <ref bean="systemEventHandler"/>
        <ref bean="softwareActivationEventHandler"/>
        <ref bean="stsVcPathF3EventHandler"/>
        <ref bean="vtVcPathF3EventHandler"/>
        <ref bean="e1T1F3EventHandler"/>
        <ref bean="e3T3F3EventHandler"/>
        <ref bean="vcgF3EventHandler"/>
        <ref bean="logicalPortTcaEventHandler"/>
        <ref bean="ifPortEventHandler"/>
        <ref bean="pseudoWireOcnStmCardEventHandler"/>
        <ref bean="portBitsFSP150EGXEventHandler"/>
        <ref bean="protectionGroupPortF3EventHandler"/>
        <ref bean="protectionGroupF3EventHandler"/>
        <ref bean="mspProtectionGroupF3EventHandler"/>
        <ref bean="f3DatabaseSyncTrapEventHandler"/>
        <ref bean="physicalEntityEventHandler"/>
        <ref bean="qosPolicerProfileEventHandler"/>
        <ref bean="qosQueueProfileEventHandler"/>
        <ref bean="erpGroupF3EventHandler"/>
        <ref bean="ingressPrioMapEventHandler"/>
        <ref bean="egressPrioMapEventHandler"/>
        <ref bean="protectedFlowF3EventHandler"/>
        <ref bean="managementTunnelEventHandler"/>
        <ref bean="f3SyncEventHandler"/>
        <ref bean="f3SyncRefEventHandler"/>
        <ref bean="trafficAclProfileEventHandler"/>
        <ref bean="gps10MHzPortEventHandler"/>
        <ref bean="f3PulsePerSecondPortEventHandler"/>
        <ref bean="f3TimeOfDayPortEventHandler"/>
        <ref bean="ocSlaveEventHandler"/>
        <ref bean="soocEventHandler"/>
        <ref bean="ocSlaveVirtualPortEventHandler"/>
        <ref bean="ptptpFlowPointEventHandler"/>
        <ref bean="tcEventHandler"/>
        <ref bean="tcVirtualPortEventHandler"/>
        <ref bean="staticRemoteSlaveEventHandler"/>
        <ref bean="dynamicRemoteSlaveEventHandler"/>
        <ref bean="masterClockInterfaceEventHandler"/>
        <ref bean="boundaryClockEventHandler"/>
        <ref bean="masterVirtualPortEventHandler"/>
        <ref bean="portCpdProfileEventHandler"/>
        <ref bean="flowPointCpdProfileEventHandler"/>
        <ref bean="trafficPortCpdEventHandler"/>
        <ref bean="flowPointCpdEventHandler"/>
        <ref bean="ethernetTrafficPortVlanLpbEventHandler"/>
        <ref bean="peerNetworkElementEventHandlerFSP150CM"/>
        <ref bean="erpUnitF3EventHandler"/>
        <ref bean="lldpPostProcessEventHandler"/>
        <ref bean="prioMapV2ProfileEventHandler"/>
        <ref bean="ptpClockEventHandler"/>
        <ref bean="portClockEventHandler"/>
        <ref bean="splitHorizonGroupEventHandler"/>
        <ref bean="splitHorizonGroupMemberEventHandler"/>
        <!-- System Feature -->
        <ref bean="f3SystemFeatureEventHandler"/>

    </util:list>

    <bean class="com.adva.nlms.mediation.config.fsp150egx.sm.impl.ServiceManagerMOFacadeFSP150EGX"/>
    <bean class="com.adva.nlms.mediation.config.fsp150egx.capabilities.EGXEPDCapabilities"/>
    <bean class="com.adva.nlms.mediation.config.fsp150egx.capabilities.EGXInternalNECapabilities"/>
    <bean class="com.adva.nlms.mediation.config.fsp150egx.capabilities.EGXNECapabilities"/>
</beans>