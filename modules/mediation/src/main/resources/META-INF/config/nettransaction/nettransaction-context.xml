<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd">

    <context:annotation-config/>

    <bean class="com.adva.nlms.mediation.config.nettransaction.operation.DtoProvisioningClientRepository"/>
    <bean class="com.adva.nlms.mediation.config.nettransaction.operation.OperationExecutorFactory"/>
    <bean class="com.adva.nlms.mediation.config.nettransaction.manager.DefaultOperationRunner"/>
    <bean class="com.adva.nlms.mediation.config.nettransaction.manager.OperationExecutorPolling"/>

    <bean id="modelGenRef" class = "com.adva.nlms.mediation.config.mofacade.rest.RestProvisioningClient"/>
    <bean id="restTransactionManager" class = "com.adva.nlms.mediation.config.mofacade.rest.RestTransactionManager"/>

    <bean id="operationManager" class="com.adva.nlms.mediation.config.nettransaction.manager.OperationManager">
        <constructor-arg ref="registeredTreeNodesHdlr"/>
    </bean>

</beans>