<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright 2025 Adtran Networks SE. All rights reserved.
  ~
  ~ Owner: annaj
  ~
  ~ $Id: $
  -->
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
       http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd">
    <context:annotation-config/>

    <bean class="com.adva.nlms.mediation.config.opticalrouter.capabilities.OpticalRouterNECapabilities"/>
    <bean class="com.adva.nlms.mediation.config.opticalrouter.capabilities.OpticalRouterNEInternalCapabilities"/>

    <bean id="opticalRouterConnectionChecker" class="com.adva.nlms.mediation.config.opticalrouter.testconnection.OpticalRouterConnectionChecker"/>

    <bean id="communicationProtocolsConfigurationOpticalRouter" class="com.adva.nlms.mediation.config.opticalrouter.discovery.CommunicationProtocolsConfigurationOpticalRouters" scope="prototype"/>

    <bean class="com.adva.nlms.mediation.config.opticalrouter.NetworkElementOpticalRouterBeanContainer" />
    <bean class="com.adva.nlms.mediation.config.opticalrouter.polling.RemoteOperationsMonitor" />
    <bean class="com.adva.nlms.mediation.config.opticalrouter.listener.RemoteOperationsListener" />
</beans>