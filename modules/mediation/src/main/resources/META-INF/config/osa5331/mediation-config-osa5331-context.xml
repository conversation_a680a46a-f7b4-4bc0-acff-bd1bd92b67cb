<?xml version="1.0" encoding="UTF-8"?>
<!--
  -  Copyright 2023 Adtran Networks SE. All rights reserved.
  -
  -  Owner: twitting
  -->
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:util="http://www.springframework.org/schema/util"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
       http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util.xsd">

    <context:annotation-config/>

    <bean class="com.adva.nlms.mediation.config.osa5331.entity.ptp.mc.MasterClockOSAAggregateDAO"/>

    <util:list id="osa5331EventHandlers" value-type="com.adva.nlms.mediation.config.entity.IPostProcessEventHandler">
        <ref bean="gpsReceiverPostEventHandlerOSA5331"/>
        <ref bean="masterClockOSAAggregateEventHandler"/>
        <ref bean="softwareActivationEventHandler"/>
    </util:list>


    <bean class="com.adva.nlms.mediation.config.osa5331.capabilities.OSA5331InternalNECapabilities"/>
    <bean class="com.adva.nlms.mediation.config.osa5331.capabilities.OSA5331NECapabilities"/>
</beans>