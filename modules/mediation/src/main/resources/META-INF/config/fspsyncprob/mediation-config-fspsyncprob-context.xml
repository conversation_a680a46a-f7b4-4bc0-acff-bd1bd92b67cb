<?xml version="1.0" encoding="UTF-8"?>
        <!--
  ~ Copyright 2023 Adtran Networks SE. All rights reserved.
  ~
  ~ Owner: benjamint
  -->
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:util="http://www.springframework.org/schema/util"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
       http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util.xsd">
<context:annotation-config/>
<util:list id="syncProbEventHandlers" value-type="com.adva.nlms.mediation.config.entity.IInvPostProcessEventHandler">
    <ref bean="physicalEntityEventHandler"/>
    <ref bean="shelfF3EventHandler"/>
    <ref bean="portF3NetEventHelper"/>
    <ref bean="portF3AccEventHandler"/>
    <ref bean="nteF3EventHandler"/>
    <ref bean="f3PulsePerSecondPortEventHandler"/>
    <ref bean="f3TimeClockEventHandler"/>
    <ref bean="f3GPSReceiverPortEventHandler"/>
    <ref bean="gps10MHzPortEventHandler"/>
    <ref bean="flowF3EventHandler"/>
    <ref bean="ocSlaveVirtualPortEventHandler"/>
    <ref bean="soocEventHandler"/>
    <ref bean="masterVirtualPortEventHandler"/>
    <ref bean="ocSlaveEventHandler"/>
    <ref bean="tcVirtualPortEventHandler"/>
    <ref bean="ifPortEventHandler"/>
    <ref bean="systemEventHandler"/>
    <ref bean="softwareActivationEventHandler"/>
    <ref bean="powerSupplyF3EventHandler"/>
    <ref bean="fanF3EventHandler"/>
    <ref bean="staticRemoteSlaveEventHandler"/>
    <ref bean="dynamicRemoteSlaveEventHandler"/>
    <ref bean="masterClockInterfaceEventHandler"/>
    <ref bean="clockProbeHisEventHandler"/>
    <ref bean="sjScheduleGroupEventHandler"/>
    <ref bean="ptpClockProbeEventHandler"/>
    <ref bean="f3TimeOfDayPortEventHandler"/>
    <ref bean="ptpAccFlowPointEventHandler"/>
    <ref bean="ptpNetFlowPointEventHandler"/>
    <ref bean="ntpAccFlowPointEventHandler"/>
    <ref bean="ntpNetFlowPointEventHandler"/>
    <ref bean="tcEventHandler"/>
    <ref bean="clockProbeEventHandler"/>
    <ref bean="f3SyncEventHandler"/>
    <ref bean="f3SyncRefEventHandler"/>
    <ref bean="f3TimeClockRefEventHandler"/>
    <ref bean="boundaryClockEventHandler"/>
    <ref bean="masterClockEventHandler"/>
    <ref bean="ptpClockHisEventHandler"/>
    <ref bean="ptpNetworkProbeEventHandler"/>
    <ref bean="portBitsF3EventHandler"/>
    <ref bean="ptpClockEventHandler"/>
    <ref bean="portClockEventHandler"/>
    <ref bean="l3PortClockEventHandler"/>
    <ref bean="l2dynamicRemoteSlaveEventHandler"/>
    <ref bean="portBits8GroupF3EventHandler"/>
    <ref bean="portPps16GroupF3EventHandler"/>
    <ref bean="portClk16GroupF3EventHandler"/>
    <ref bean="portTodPps16GroupF3EventHandler"/>
    <ref bean="Bits16PortCardEventHandler"/>
    <ref bean="Pps16PortCardEventHandler"/>
    <ref bean="Clk16PortCardEventHandler"/>
    <ref bean="TodPps16PortCardEventHandler"/>
    <ref bean="gE4PortCardEventHandler"/>
    <ref bean="csmF3EventHandler"/>
    <ref bean="auxF3EventHandler"/>
    <ref bean="mbGNSSLCEventHandler"/>
    <ref bean="stlLCEventHandler"/>
    <ref bean="teluCardEventHandler"/>
    <ref bean="metuCardEventHandler"/>
    <ref bean="ppsCardEventHandler"/>
    <ref bean="cbuCardEventHandler"/>
    <ref bean="rfuCardEventHandler"/>
    <ref bean="ipuCardEventHandler"/>
    <ref bean="pduCardEventHandler"/>
    <ref bean="batuCardEventHandler"/>
    <ref bean="lcdCardEventHandler"/>
    <ref bean="estuCardEventHandler"/>
    <ref bean="compositeClockCardEventHandler"/>
    <ref bean="irigCardEventHandler"/>
    <ref bean="displayCardEventHandler"/>
    <ref bean="clkx4lpnCardEventHandler"/>
    <ref bean="avsCardEventHandler"/>
    <ref bean="bitsx16ProtectedCardEventHandler"/>
    <ref bean="irigOpticalOutputPortEventHandler"/>
    <ref bean="irigRelayOutputPortEventHandler"/>
    <ref bean="irigRS422OutputPortEventHandler"/>
    <ref bean="irigOutputGroupEventHandler"/>
    <ref bean="irigOutputUnitPortEventHandler"/>
    <ref bean="irigBInputPortEventHandler"/>
    <ref bean="clk4OutputPortGroupEventHandler"/>
    <ref bean="avsOutputPortEventHandler"/>
    <ref bean="stlModuleEventHandler"/>
    <ref bean="compositeClockInputPortEventHandler"/>
    <ref bean="compositeClockGroupEventHandler"/>
    <ref bean="ptpSystemSlavesEventHandler"/>
    <ref bean="syncEPGEventHandler"/>
    <ref bean="timeClockPGEventHandler"/>
    <ref bean="ptpMciPGEventHandler"/>
    <ref bean="redundancyGroupEventHandler"/>
    <!-- System Feature -->
    <ref bean="f3SystemFeatureEventHandler"/>
    <ref bean="staticRouteEventHandler"/>
    <ref bean="ntpRemoteServerEventHandler"/>
    <ref bean="f3SystemClockEventHandler"/>
    <ref bean="remoteAuthServerEventHandler"/>
    <ref bean="ntpRemoteClientEventHandler"/>
    <ref bean="ntpTrackedClientEventHandler"/>
    <ref bean="managementTunnelEventHandler"/>
    <ref bean="ntpClockEventHandler"/>
    <ref bean="ntpClockInterfaceEventHandler"/>
    <ref bean="userMTIEMaskEventHandler"/>
</util:list>

    <bean class="com.adva.nlms.mediation.config.fspsyncprob.capabilities.Osa54xxInternalNECapabilities"/>
    <bean class="com.adva.nlms.mediation.config.fspsyncprob.capabilities.OsaSoftsyncInternalNECapabilities"/>
    <bean class="com.adva.nlms.mediation.config.fspsyncprob.capabilities.Osa54xxNECapabilities"/>
    <bean class="com.adva.nlms.mediation.config.fspsyncprob.capabilities.OsaSoftsyncNECapabilities"/>
    <bean class="com.adva.nlms.mediation.config.osa542x.capabilities.OSA542XShelfViewCapabilities"/>
    <bean class="com.adva.nlms.mediation.config.osa54CR.capabilities.OSA54CRShelfViewCapabilities"/>
    <bean class="com.adva.nlms.mediation.config.osa3350.capabilities.OSA33X0ShelfViewCapabilities"/>
    <bean class="com.adva.nlms.mediation.config.osa540X.capabilities.OSA540XShelfViewCapabilities"/>

</beans>