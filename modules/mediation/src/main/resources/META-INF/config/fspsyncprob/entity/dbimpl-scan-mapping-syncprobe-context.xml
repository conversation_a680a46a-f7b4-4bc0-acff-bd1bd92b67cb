<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

    <bean class="org.springframework.beans.factory.config.MethodInvokingFactoryBean">
        <property name="targetObject" ref="dbImplToScanMappingManager"/>
        <property name="targetMethod" value="registerMapping" />
        <property name="arguments" >
            <list>
                <list value-type="com.adva.nlms.mediation.config.entity.mapping.DBImplToScanMapping">
                    <bean class="com.adva.nlms.mediation.config.f3.entity.module.nte.NteF3DBImplScanMapping">
                        <constructor-arg type="java.lang.Class" value="com.adva.nlms.mediation.config.model.definition.fspsyncprob.ScanTablesFSPSyncProb$NteTable"/>
                    </bean>
                </list>
                <list value-type="com.adva.nlms.common.config.netypes.NEType">
                    <value>SYNC_PROB</value>
                </list>
            </list>
        </property>
    </bean>
    <bean class="org.springframework.beans.factory.config.MethodInvokingFactoryBean">
        <property name="targetObject" ref="dbImplToScanMappingManager"/>
        <property name="targetMethod" value="registerMapping" />
        <property name="arguments" >
            <list>
                <list value-type="com.adva.nlms.mediation.config.entity.mapping.DBImplToScanMapping">
                    <bean class="com.adva.nlms.mediation.config.f3.entity.module.nte.NteF3DBImplScanMapping">
                        <constructor-arg type="java.lang.Class" value="com.adva.nlms.mediation.config.model.definition.fspsyncprob.ScanTablesFSPSyncProb$NteOsa5420Table"/>
                    </bean>
                </list>
                <list value-type="com.adva.nlms.common.config.netypes.NEType">
                    <value>OSA5420</value>
                </list>
            </list>
        </property>
    </bean>
    <bean class="org.springframework.beans.factory.config.MethodInvokingFactoryBean">
        <property name="targetObject" ref="dbImplToScanMappingManager"/>
        <property name="targetMethod" value="registerMapping" />
        <property name="arguments" >
            <list>
                <list value-type="com.adva.nlms.mediation.config.entity.mapping.DBImplToScanMapping">
                    <bean class="com.adva.nlms.mediation.config.f3.entity.module.nte.NteF3DBImplScanMapping">
                        <constructor-arg type="java.lang.Class" value="com.adva.nlms.mediation.config.model.definition.fspsyncprob.ScanTablesFSPSyncProb$NteOsa5421Table"/>
                    </bean>
                </list>
                <list value-type="com.adva.nlms.common.config.netypes.NEType">
                    <value>OSA5421</value>
                </list>
            </list>
        </property>
    </bean>
    <bean class="org.springframework.beans.factory.config.MethodInvokingFactoryBean">
        <property name="targetObject" ref="dbImplToScanMappingManager"/>
        <property name="targetMethod" value="registerMapping" />
        <property name="arguments" >
            <list>
                <list value-type="com.adva.nlms.mediation.config.entity.mapping.DBImplToScanMapping">
                    <bean class="com.adva.nlms.mediation.config.f3.entity.module.nte.NteF3DBImplScanMapping">
                        <constructor-arg type="java.lang.Class" value="com.adva.nlms.mediation.config.model.definition.fspsyncprob.ScanTablesFSPSyncProb$NteOsa5412Table"/>
                    </bean>
                </list>
                <list value-type="com.adva.nlms.common.config.netypes.NEType">
                    <value>OSA5412</value>
                </list>
            </list>
        </property>
    </bean>
    <bean class="org.springframework.beans.factory.config.MethodInvokingFactoryBean">
        <property name="targetObject" ref="dbImplToScanMappingManager"/>
        <property name="targetMethod" value="registerMapping" />
        <property name="arguments" >
            <list>
                <list value-type="com.adva.nlms.mediation.config.entity.mapping.DBImplToScanMapping">
                    <bean class="com.adva.nlms.mediation.config.f3.entity.module.nte.NteF3DBImplScanMapping">
                        <constructor-arg type="java.lang.Class" value="com.adva.nlms.mediation.config.model.definition.fspsyncprob.ScanTablesFSPSyncProb$NteOsa5422Table"/>
                    </bean>
                </list>
                <list value-type="com.adva.nlms.common.config.netypes.NEType">
                    <value>OSA5422</value>
                </list>
            </list>
        </property>
    </bean>
    <bean class="org.springframework.beans.factory.config.MethodInvokingFactoryBean">
        <property name="targetObject" ref="dbImplToScanMappingManager"/>
        <property name="targetMethod" value="registerMapping" />
        <property name="arguments" >
            <list>
                <list value-type="com.adva.nlms.mediation.config.entity.mapping.DBImplToScanMapping">
                    <bean class="com.adva.nlms.mediation.config.fspsyncprob.entity.module.csm.CsmF3DBImplScanMapping"/>

                </list>
                <list value-type="com.adva.nlms.common.config.netypes.NEType">
                    <value>OSA5430</value>
                    <value>OSA5440</value>
                </list>
            </list>
        </property>
    </bean>

    <bean class="org.springframework.beans.factory.config.MethodInvokingFactoryBean">
        <property name="targetObject" ref="managedObjectControllerManager"/>
        <property name="targetMethod" value="registerMapping" />
        <property name="arguments" >
            <list>
                <list value-type="com.adva.nlms.mediation.config.f3.entity.ManagedObjectController">
                    <bean class="com.adva.nlms.mediation.config.fspsyncprob.entity.module.linecard.ge4port.GE4PortCardControllerOSA"/>
                </list>
                <list value-type="com.adva.nlms.common.config.netypes.NEType">
                    <value>OSA5440</value>
                    <value>OSA5430</value>
                </list>
            </list>
        </property>
    </bean>


    <bean class="org.springframework.beans.factory.config.MethodInvokingFactoryBean">
        <property name="targetObject" ref="managedObjectControllerManager"/>
        <property name="targetMethod" value="registerMapping" />
        <property name="arguments" >
            <list>
                <list value-type="com.adva.nlms.mediation.config.f3.entity.ManagedObjectController">
                    <bean class="com.adva.nlms.mediation.config.fspsyncprob.entity.module.nte.NteOSA5410Controller"/>
                </list>
                <list value-type="com.adva.nlms.common.config.netypes.NEType">
                    <value>SYNC_PROB</value>
                </list>
            </list>
        </property>
    </bean>


    <bean class="org.springframework.beans.factory.config.MethodInvokingFactoryBean">
        <property name="targetObject" ref="managedObjectControllerManager"/>
        <property name="targetMethod" value="registerMapping" />
        <property name="arguments" >
            <list>
                <list value-type="com.adva.nlms.mediation.config.f3.entity.ManagedObjectController">
                    <bean class="com.adva.nlms.mediation.config.fspsyncprob.entity.module.nte.NteOSA5411Controller"/>
                </list>
                <list value-type="com.adva.nlms.common.config.netypes.NEType">
                    <value>OSA5411</value>
                </list>
            </list>
        </property>
    </bean>


    <bean class="org.springframework.beans.factory.config.MethodInvokingFactoryBean">
        <property name="targetObject" ref="managedObjectControllerManager"/>
        <property name="targetMethod" value="registerMapping" />
        <property name="arguments" >
            <list>
                <list value-type="com.adva.nlms.mediation.config.f3.entity.ManagedObjectController">
                    <bean class="com.adva.nlms.mediation.config.fspsyncprob.entity.module.nte.NteOSA5412Controller"/>
                </list>
                <list value-type="com.adva.nlms.common.config.netypes.NEType">
                    <value>OSA5412</value>
                </list>
            </list>
        </property>
    </bean>


    <bean class="org.springframework.beans.factory.config.MethodInvokingFactoryBean">
        <property name="targetObject" ref="managedObjectControllerManager"/>
        <property name="targetMethod" value="registerMapping" />
        <property name="arguments" >
            <list>
                <list value-type="com.adva.nlms.mediation.config.f3.entity.ManagedObjectController">
                    <bean class="com.adva.nlms.mediation.config.fspsyncprob.entity.module.nte.NteOSA5420Controller"/>
                </list>
                <list value-type="com.adva.nlms.common.config.netypes.NEType">
                    <value>OSA5420</value>
                </list>
            </list>
        </property>
    </bean>


    <bean class="org.springframework.beans.factory.config.MethodInvokingFactoryBean">
        <property name="targetObject" ref="managedObjectControllerManager"/>
        <property name="targetMethod" value="registerMapping" />
        <property name="arguments" >
            <list>
                <list value-type="com.adva.nlms.mediation.config.f3.entity.ManagedObjectController">
                    <bean class="com.adva.nlms.mediation.config.fspsyncprob.entity.module.nte.NteOSA5421Controller"/>
                </list>
                <list value-type="com.adva.nlms.common.config.netypes.NEType">
                    <value>OSA5421</value>
                </list>
            </list>
        </property>
    </bean>


    <bean class="org.springframework.beans.factory.config.MethodInvokingFactoryBean">
        <property name="targetObject" ref="managedObjectControllerManager"/>
        <property name="targetMethod" value="registerMapping" />
        <property name="arguments" >
            <list>
                <list value-type="com.adva.nlms.mediation.config.f3.entity.ManagedObjectController">
                    <bean class="com.adva.nlms.mediation.config.fspsyncprob.entity.module.nte.NteOSA5422Controller"/>
                </list>
                <list value-type="com.adva.nlms.common.config.netypes.NEType">
                    <value>OSA5422</value>
                </list>
            </list>
        </property>
    </bean>

    <bean class="org.springframework.beans.factory.config.MethodInvokingFactoryBean">
        <property name="targetObject" ref="managedObjectControllerManager"/>
        <property name="targetMethod" value="registerMapping" />
        <property name="arguments" >
            <list>
                <list value-type="com.adva.nlms.mediation.config.f3.entity.ManagedObjectController">
                    <bean class="com.adva.nlms.mediation.config.fspsyncprob.entity.module.nte.NteOSA5410XGController"/>
                </list>
                <list value-type="com.adva.nlms.common.config.netypes.NEType">
                    <value>OSA5410XG</value>
                </list>
            </list>
        </property>
    </bean>

</beans>