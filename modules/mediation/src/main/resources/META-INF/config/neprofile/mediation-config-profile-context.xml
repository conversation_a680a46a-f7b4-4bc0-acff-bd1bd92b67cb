<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~  Copyright 2023 Adtran Networks SE. All rights reserved.
  ~
  ~  Owner: szymonw
  -->
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
       http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd">

    <context:annotation-config/>


    <bean class="com.adva.nlms.mediation.config.neprofile.masterprofile.dao.MasterProfileDAOImpl" />
    <bean class="com.adva.nlms.mediation.config.neprofile.masterprofile.dao.MasterProfileDataSynchronizerImpl" />

    <bean class="com.adva.nlms.mediation.config.neprofile.masterprofile.decoder.MasterProfileHeaderDecoderImpl"/>
    <bean class="com.adva.nlms.mediation.config.neprofile.masterprofile.decoder.MasterProfileHeaderEncoderImpl" />

    <bean class="com.adva.nlms.mediation.config.neprofile.masterprofile.ftp.FTPClientProviderImpl" />
    <bean class="com.adva.nlms.mediation.config.neprofile.masterprofile.ftp.FTPOperationServiceImpl"/>
    <bean class="com.adva.nlms.mediation.config.neprofile.masterprofile.ftp.MasterProfileFTPDataProviderImpl"/>

    <bean class="com.adva.nlms.mediation.config.neprofile.masterprofile.inf.MasterProfileCreationObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean class="com.adva.nlms.mediation.config.neprofile.masterprofile.inf.MasterProfileDeleteObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver" />
    </bean>
    <bean class="com.adva.nlms.mediation.config.neprofile.masterprofile.inf.MasterProfileAttributesObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver" />
    </bean>

    <bean class="com.adva.nlms.mediation.config.neprofile.masterprofile.paging.PageSynchronizer"/>
    <bean class="com.adva.nlms.mediation.config.neprofile.masterprofile.paging.MasterProfilePageHdlrImpl"/>

    <bean class="com.adva.nlms.mediation.config.neprofile.masterprofile.MasterProfileSynchronizePollingCommand" >
        <qualifier type="com.adva.nlms.mediation.polling.DomainPolling"/>
    </bean>
    <bean class="com.adva.nlms.mediation.config.neprofile.masterprofile.MasterProfileScheduledDownloadPolling"/>
    <bean class="com.adva.nlms.mediation.config.neprofile.masterprofile.MasterProfileServiceImpl"/>
    <bean class="com.adva.nlms.mediation.config.neprofile.masterprofile.MasterProfileOperationsMonitor"/>
    <bean class="com.adva.nlms.mediation.config.neprofile.masterprofile.MasterProfileHeaderModifier"/>

    <bean class="com.adva.nlms.mediation.config.neprofile.masterprofile.f8.api.download.MasterProfileF8DownloadPolling"/>
<!--    <bean id="masterProfileDownloadAsyncF8" class="com.adva.nlms.mediation.config.neprofile.masterprofile.f8.impl.download.MasterProfileF8ServiceImpl">-->
<!--        <constructor-arg ref="restClientResolver"/>-->
<!--    </bean>-->
    <bean id="profileNeF8ServiceImpl" class="com.adva.nlms.mediation.config.neprofile.asyncEcRest.ProfileNeF8ServiceImpl">
        <constructor-arg ref="restClientResolver"/>
    </bean>

    <bean class="com.adva.nlms.mediation.config.neprofile.paging.PageSynchronizer"/>
    <bean class="com.adva.nlms.mediation.config.neprofile.inf.ProfileNEAttributesObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver" />
    </bean>

    <bean id="masterProfileF8SpringConfig" class="com.adva.nlms.mediation.config.neprofile.masterprofile.f8.impl.MasterProfileF8SpringConfig"/>
</beans>