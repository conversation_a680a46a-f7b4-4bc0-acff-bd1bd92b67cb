<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright 2023 Adtran Networks SE. All rights reserved.
  ~
  ~ Owner: benjamint
  -->
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:util="http://www.springframework.org/schema/util"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
       http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util.xsd">

    <context:annotation-config/>


    <util:list id="fsp150cmEventHandlers" value-type="com.adva.nlms.mediation.config.entity.IInvPostProcessEventHandler">
        <ref bean="flowF3EventHandler"/>
        <ref bean="protectionGroupPortF3EventHandler"/>
        <ref bean="moduleCPMRFSP150CMEventHandler"/>
        <ref bean="ntefsp150CMEventHandler"/>
        <ref bean="qosShaperF3EventHandler"/>
        <ref bean="protectionGroupFSP150CMEventHandler"/>
        <ref bean="portBitsF3EventHandler"/>
        <ref bean="ampConfigEventHandler"/>
        <ref bean="f3SyncRefEventHandlerFSP150CM"/>
        <ref bean="staticRouteEventHandler"/>
        <ref bean="scuF3EventHandler"/>
        <ref bean="scuTFSP150CMEventHandler"/>
        <ref bean="nemiF3EventHandler"/>
        <ref bean="powerSupplyF3EventHandler"/>
        <ref bean="fanF3EventHandler"/>
        <ref bean="shelfF3EventHandler"/>
        <ref bean="systemEventHandler"/>
        <ref bean="softwareActivationEventHandler"/>
        <ref bean="ifPortEventHandler"/>
        <ref bean="physicalEntityEventHandler"/>
        <ref bean="ntuFSP150CMEventHandler"/>
        <ref bean="peerNetworkElementEventHandlerFSP150CM"/>
        <ref bean="managementTunnelEventHandler"/>
        <ref bean="f3SyncEventHandler"/>
        <ref bean="portF3AccEventHandlerWithDelay"/>
        <ref bean="portF3NetEventHelperWithDelay"/>
        <!-- System Feature -->
        <ref bean="f3SystemFeatureEventHandler"/>
    </util:list>


    <util:list id="fsp150cmEFMEventHandlers" value-type="com.adva.nlms.mediation.config.entity.IInvPostProcessEventHandler">
        <ref bean="flowF3EventHandler"/>
        <ref bean="moduleCPMRFSP150CMEventHandler"/>
        <ref bean="qosShaperF3EventHandler"/>
        <ref bean="protectionGroupFSP150CMEventHandler"/>
        <ref bean="cpmrPowerSupplyF3EventHandler"/>
        <ref bean="cpmrFanF3EventHandler"/>
        <ref bean="shelfF3EventHandler"/>
        <ref bean="systemEventHandler"/>
        <ref bean="softwareActivationEventHandler"/>
        <ref bean="ifPortEventHandler"/>
        <ref bean="physicalEntityEventHandler"/>
        <ref bean="portF3AccEventHandlerWithDelay"/>
        <ref bean="portF3NetEventHelperWithDelay"/>
    </util:list>

    <bean class="com.adva.nlms.mediation.config.fsp150cm.dbconsistency.FlowAndShaperConsistencyChecker"/>
    <bean class="com.adva.nlms.mediation.config.fsp150cm.dbconsistency.PGConsistencyChecker"/>
    <bean class="com.adva.nlms.mediation.config.f3.dbconsistency.FDFrConsistencyChecker"/>
    <bean class="com.adva.nlms.mediation.config.fsp150cm.capabilities.CMInternalNECapabilities"/>
    <bean class="com.adva.nlms.mediation.config.fsp150cm.capabilities.CMNECapabilities"/>

</beans>