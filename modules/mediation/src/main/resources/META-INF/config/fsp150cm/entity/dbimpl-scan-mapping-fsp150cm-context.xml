<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

    <bean class="org.springframework.beans.factory.config.MethodInvokingFactoryBean">
        <property name="targetObject" ref="dbImplToScanMappingManager"/>
        <property name="targetMethod" value="registerMapping" />
        <property name="arguments" >
            <list>
                <list value-type="com.adva.nlms.mediation.config.entity.mapping.DBImplToScanMapping">
                    <bean class="com.adva.nlms.mediation.config.f3.entity.module.nte.NteF3DBImplScanMapping">
                        <constructor-arg type="java.lang.Class" value="com.adva.nlms.mediation.config.model.definition.fsp150cm.ScanTablesFSP150CM$PrepareEthernetNteCardDatamap"/>
                    </bean>
                </list>
                <list value-type="com.adva.nlms.common.config.netypes.NEType">
                    <value>FSP_150CM</value>
                </list>
            </list>
        </property>
    </bean>

    <bean class="org.springframework.beans.factory.config.MethodInvokingFactoryBean">
        <property name="targetObject" ref="dbImplToScanMappingManager"/>
        <property name="targetMethod" value="registerMapping" />
        <property name="arguments" >
            <list>
                <list value-type="com.adva.nlms.mediation.config.entity.mapping.DBImplToScanMapping">
                    <bean class="com.adva.nlms.mediation.config.fsp150cm.entity.fan.Fan150CMDBImplScanMapping">
                    </bean>
                </list>
                <list value-type="com.adva.nlms.common.config.netypes.NEType">
                    <value>FSP_150CM</value>
                </list>
            </list>
        </property>
    </bean>

    <bean class="org.springframework.beans.factory.config.MethodInvokingFactoryBean">
        <property name="targetObject" ref="dbImplToScanMappingManager"/>
        <property name="targetMethod" value="registerMapping" />
        <property name="arguments" >
            <list>
                <list value-type="com.adva.nlms.mediation.config.entity.mapping.DBImplToScanMapping">
                    <bean class="com.adva.nlms.mediation.config.fsp150cm.entity.psu.PowerSupply150CMDBImplScanMapping">
                    </bean>
                </list>
                <list value-type="com.adva.nlms.common.config.netypes.NEType">
                    <value>FSP_150CM</value>
                </list>
            </list>
        </property>
    </bean>

    <bean class="org.springframework.beans.factory.config.MethodInvokingFactoryBean">
        <property name="targetObject" ref="managedObjectControllerManager"/>
        <property name="targetMethod" value="registerMapping" />
        <property name="arguments" >
            <list>
                <list value-type="com.adva.nlms.mediation.config.f3.entity.ManagedObjectController">
                    <bean class="com.adva.nlms.mediation.config.fsp150cm.entity.controllers.Nte150cmController"/>
                </list>
                <list value-type="com.adva.nlms.common.config.netypes.NEType">
                    <value>FSP_150CM</value>
                </list>
            </list>
        </property>
    </bean>

</beans>