<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

    <!-- OSA5400 -->
    <bean class="org.springframework.beans.factory.config.MethodInvokingFactoryBean">
        <property name="targetObject" ref="dbImplToScanMappingManager"/>
        <property name="targetMethod" value="registerMapping" />
        <property name="arguments" >
            <list>
                <list value-type="com.adva.nlms.mediation.config.entity.mapping.DBImplToScanMapping">
                    <bean class="com.adva.nlms.mediation.config.osa540X.entity.module.NteOSA540xDBImplScanMapping">
                        <constructor-arg type="java.lang.Class" value="com.adva.nlms.mediation.config.model.definition.fspsyncprob.ScanTablesFSPSyncProb$NteOsa5400Table"/>
                    </bean>
                </list>
                <list value-type="com.adva.nlms.common.config.netypes.NEType">
                    <value>OSA5400SM</value>
                    <value>OSA5400TC</value>
                </list>
            </list>
        </property>
    </bean>

    <!-- OSA5401 -->
    <bean class="org.springframework.beans.factory.config.MethodInvokingFactoryBean">
        <property name="targetObject" ref="dbImplToScanMappingManager"/>
        <property name="targetMethod" value="registerMapping" />
        <property name="arguments" >
            <list>
                <list value-type="com.adva.nlms.mediation.config.entity.mapping.DBImplToScanMapping">
                    <bean class="com.adva.nlms.mediation.config.osa540X.entity.module.NteOSA540xDBImplScanMapping">
                        <constructor-arg type="java.lang.Class" value="com.adva.nlms.mediation.config.model.definition.fspsyncprob.ScanTablesFSPSyncProb$NteOsa5401Table"/>
                    </bean>
                </list>
                <list value-type="com.adva.nlms.common.config.netypes.NEType">
                    <value>OSA5401</value>
                    <value>OSA5401XG</value>
                </list>
            </list>
        </property>
    </bean>

    <!-- OSA5405x -->
    <bean class="org.springframework.beans.factory.config.MethodInvokingFactoryBean">
        <property name="targetObject" ref="dbImplToScanMappingManager"/>
        <property name="targetMethod" value="registerMapping" />
        <property name="arguments" >
            <list>
                <list value-type="com.adva.nlms.mediation.config.entity.mapping.DBImplToScanMapping">
                    <bean class="com.adva.nlms.mediation.config.osa540X.entity.module.NteOSA540xDBImplScanMapping">
                        <constructor-arg type="java.lang.Class" value="com.adva.nlms.mediation.config.model.definition.fspsyncprob.ScanTablesFSPSyncProb$NteOsa5405Table"/>
                    </bean>
                </list>
                <list value-type="com.adva.nlms.common.config.netypes.NEType">
                    <value>OSA5405I</value>
                    <value>OSA5405O</value>
                    <value>OSA5405MB</value>
                    <value>OSA5405P</value>
                    <value>OSA5405S</value>
                </list>
            </list>
        </property>
    </bean>

    <bean class="org.springframework.beans.factory.config.MethodInvokingFactoryBean">
        <property name="targetObject" ref="dbImplToScanMappingManager"/>
        <property name="targetMethod" value="registerMapping" />
        <property name="arguments" >
            <list>
                <list value-type="com.adva.nlms.mediation.config.entity.mapping.DBImplToScanMapping">
                </list>
                <list value-type="com.adva.nlms.common.config.netypes.NEType">
                    <value>OSA5400SM</value>
                    <value>OSA5400TC</value>
                    <value>OSA5401XG</value>
                    <value>OSA5401</value>
                    <value>OSA5405I</value>
                    <value>OSA5405O</value>
                    <value>OSA5405MB</value>
                    <value>OSA5405P</value>
                    <value>OSA5405S</value>
                </list>
            </list>
        </property>
    </bean>

    <bean class="org.springframework.beans.factory.config.MethodInvokingFactoryBean">
        <property name="targetObject" ref="managedObjectControllerManager"/>
        <property name="targetMethod" value="registerMapping" />
        <property name="arguments" >
            <list>
                <list value-type="com.adva.nlms.mediation.config.f3.entity.ManagedObjectController">
                    <bean class="com.adva.nlms.mediation.config.osa540X.controller.NteOSA5400Controller"/>
                    <bean class="com.adva.nlms.mediation.config.osa540X.entity.port.gps.F3GPSReceiverPortOSA540xController"/>
                    <bean class="com.adva.nlms.mediation.config.osa540X.timeclock.F3TimeClockOSA540xController"/>
                    <bean class="com.adva.nlms.mediation.config.osa540X.ptp.dsr.DynamicRemoteSlaveOSA540xController"/>
                    <bean class="com.adva.nlms.mediation.config.osa540X.ptp.mc.MasterClockOSA540xController"/>
                    <bean class="com.adva.nlms.mediation.config.osa540X.ptp.ptpclock.PtpClockOSA540xController"/>
                    <bean class="com.adva.nlms.mediation.config.osa540X.ptp.ptpport.PtpPortOSA540xController"/>
                    <bean class="com.adva.nlms.mediation.config.osa540X.ptp.mci.MasterClockInterfaceOSA540xController"/>
                    <bean class="com.adva.nlms.mediation.config.osa540X.ptp.sooc.SOOCOSA540xController"/>
                    <bean class="com.adva.nlms.mediation.config.osa540X.ptp.ocs.OCSlaveOSA540xController"/>
                    <bean class="com.adva.nlms.mediation.config.osa540X.ptp.ocsvirtualport.OCSlaveVirtualPortOSA540xController"/>
                    <bean class="com.adva.nlms.mediation.config.osa540X.ptp.mvp.MasterVirtualPortOSA540xController"/>
                    <bean class="com.adva.nlms.mediation.config.osa540X.entity.module.ModuleOSA5400Controller"/>
                    <bean class="com.adva.nlms.mediation.config.osa540X.entity.port.PortOSA540XController"/>
                    <bean class="com.adva.nlms.mediation.config.osa540X.ptp.ptpflow.PTPTPFlowPointOsa540xController"/>
                </list>
                <list value-type="com.adva.nlms.common.config.netypes.NEType">
                    <value>OSA5400SM</value>
                    <value>OSA5400TC</value>
                </list>
            </list>
        </property>
    </bean>

    <bean class="org.springframework.beans.factory.config.MethodInvokingFactoryBean">
        <property name="targetObject" ref="managedObjectControllerManager"/>
        <property name="targetMethod" value="registerMapping" />
        <property name="arguments" >
            <list>
                <list value-type="com.adva.nlms.mediation.config.f3.entity.ManagedObjectController">
                    <bean class="com.adva.nlms.mediation.config.osa540X.controller.NteOSA5401Controller"/>
                    <bean class="com.adva.nlms.mediation.config.osa540X.entity.port.gps.F3GPSReceiverPortOSA540xController"/>
                    <bean class="com.adva.nlms.mediation.config.osa540X.timeclock.F3TimeClockOSA540xController"/>
                    <bean class="com.adva.nlms.mediation.config.osa540X.ptp.dsr.DynamicRemoteSlaveOSA540xController"/>
                    <bean class="com.adva.nlms.mediation.config.osa540X.ptp.mc.MasterClockOSA540xController"/>
                    <bean class="com.adva.nlms.mediation.config.osa540X.ptp.ptpclock.PtpClockOSA540xController"/>
                    <bean class="com.adva.nlms.mediation.config.osa540X.ptp.ptpport.PtpPortOSA540xController"/>
                    <bean class="com.adva.nlms.mediation.config.osa540X.ptp.mci.MasterClockInterfaceOSA540xController"/>
                    <bean class="com.adva.nlms.mediation.config.osa540X.ptp.sooc.SOOCOSA540xController"/>
                    <bean class="com.adva.nlms.mediation.config.osa540X.ptp.ocs.OCSlaveOSA540xController"/>
                    <bean class="com.adva.nlms.mediation.config.osa540X.ptp.ocsvirtualport.OCSlaveVirtualPortOSA540xController"/>
                    <bean class="com.adva.nlms.mediation.config.osa540X.ptp.mvp.MasterVirtualPortOSA540xController"/>
                    <bean class="com.adva.nlms.mediation.config.osa540X.entity.module.ModuleOSA5400Controller"/>
                    <bean class="com.adva.nlms.mediation.config.osa540X.entity.port.PortOSA540XController"/>
                    <bean class="com.adva.nlms.mediation.config.osa540X.ptp.ptpflow.PTPTPFlowPointOsa540xController"/>
                </list>
                <list value-type="com.adva.nlms.common.config.netypes.NEType">
                    <value>OSA5401</value>
                    <value>OSA5401XG</value>
                </list>
            </list>
        </property>
    </bean>

    <bean class="org.springframework.beans.factory.config.MethodInvokingFactoryBean">
        <property name="targetObject" ref="managedObjectControllerManager"/>
        <property name="targetMethod" value="registerMapping" />
        <property name="arguments" >
            <list>
                <list value-type="com.adva.nlms.mediation.config.f3.entity.ManagedObjectController">
                    <bean class="com.adva.nlms.mediation.config.osa540X.controller.NteOSA5405Controller"/>
                    <bean class="com.adva.nlms.mediation.config.osa540X.timeclock.F3TimeClockOSA540xController"/>
                    <bean class="com.adva.nlms.mediation.config.osa540X.ptp.dsr.DynamicRemoteSlaveOSA540xController"/>
                    <bean class="com.adva.nlms.mediation.config.osa540X.ptp.mc.MasterClockOSA540xController"/>
                    <bean class="com.adva.nlms.mediation.config.osa540X.ptp.ptpclock.PtpClockOSA540xController"/>
                    <bean class="com.adva.nlms.mediation.config.osa540X.ptp.ptpport.PtpPortOSA540xController"/>
                    <bean class="com.adva.nlms.mediation.config.osa540X.ptp.mci.MasterClockInterfaceOSA540xController"/>
                    <bean class="com.adva.nlms.mediation.config.osa540X.ptp.sooc.SOOCOSA540xController"/>
                    <bean class="com.adva.nlms.mediation.config.osa540X.ptp.ocs.OCSlaveOSA540xController"/>
                    <bean class="com.adva.nlms.mediation.config.osa540X.ptp.ocsvirtualport.OCSlaveVirtualPortOSA540xController"/>
                    <bean class="com.adva.nlms.mediation.config.osa540X.ptp.mvp.MasterVirtualPortOSA540xController"/>
                    <bean class="com.adva.nlms.mediation.config.osa540X.entity.module.ModuleOSA5405Controller"/>
                    <bean class="com.adva.nlms.mediation.config.osa540X.entity.port.PortOSA540XController"/>
                    <bean class="com.adva.nlms.mediation.config.osa540X.ptp.ptpflow.PTPTPFlowPointOsa540xController"/>
                </list>
                <list value-type="com.adva.nlms.common.config.netypes.NEType">
                    <value>OSA5405I</value>
                    <value>OSA5405O</value>
                </list>
            </list>
        </property>
    </bean>

    <bean class="org.springframework.beans.factory.config.MethodInvokingFactoryBean">
        <property name="targetObject" ref="managedObjectControllerManager"/>
        <property name="targetMethod" value="registerMapping" />
        <property name="arguments" >
            <list>
                <list value-type="com.adva.nlms.mediation.config.f3.entity.ManagedObjectController">
                    <bean class="com.adva.nlms.mediation.config.osa540X.controller.NteOSA5405Controller"/>
                    <!--<bean class="com.adva.nlms.mediation.config.osa540X.entity.port.bits.PortBitsOSA540xController"/>-->
                    <bean class="com.adva.nlms.mediation.config.osa540X.entity.port.gps.F3GPSReceiverPortOSA540xController"/>
                    <bean class="com.adva.nlms.mediation.config.osa540X.timeclock.F3TimeClockOSA540xController"/>
                    <bean class="com.adva.nlms.mediation.config.osa540X.ptp.dsr.DynamicRemoteSlaveOSA540xController"/>
                    <bean class="com.adva.nlms.mediation.config.osa540X.ptp.mc.MasterClockOSA540xController"/>
                    <bean class="com.adva.nlms.mediation.config.osa540X.ptp.ptpclock.PtpClockOSA540xController"/>
                    <bean class="com.adva.nlms.mediation.config.osa540X.ptp.ptpport.PtpPortOSA540xController"/>
                    <bean class="com.adva.nlms.mediation.config.osa540X.ptp.mci.MasterClockInterfaceOSA540xController"/>
                    <bean class="com.adva.nlms.mediation.config.osa540X.ptp.sooc.SOOCOSA540xController"/>
                    <bean class="com.adva.nlms.mediation.config.osa540X.ptp.ocs.OCSlaveOSA540xController"/>
                    <bean class="com.adva.nlms.mediation.config.osa540X.ptp.ocsvirtualport.OCSlaveVirtualPortOSA540xController"/>
                    <bean class="com.adva.nlms.mediation.config.osa540X.ptp.mvp.MasterVirtualPortOSA540xController"/>
                    <bean class="com.adva.nlms.mediation.config.osa540X.entity.module.ModuleOSA5405Controller"/>
                    <bean class="com.adva.nlms.mediation.config.osa540X.entity.port.PortOSA540XController"/>
                    <bean class="com.adva.nlms.mediation.config.osa540X.ptp.ptpflow.PTPTPFlowPointOsa540xController"/>
                </list>
                <list value-type="com.adva.nlms.common.config.netypes.NEType">
                   <value>OSA5405MB</value>
                   <value>OSA5405P</value>
                   <value>OSA5405S</value>
                </list>
            </list>
        </property>
    </bean>

</beans>