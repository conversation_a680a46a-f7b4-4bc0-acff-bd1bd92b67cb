<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:util="http://www.springframework.org/schema/util"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
       http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util.xsd">
    <context:annotation-config/>
    <util:list id="osa540XEventHandlers" value-type="com.adva.nlms.mediation.config.entity.IPostProcessEventHandler">
        <ref bean="shelfF3EventHandler"/>
        <ref bean="f3GPSReceiverPortEventHandler"/>
        <ref bean="f3PulsePerSecondPortEventHandler"/>
        <ref bean="masterClockInterfaceOSA540xEventHandler"/>
        <ref bean="masterClockOSA540xEventHandler"/>
        <ref bean="ptpClockOSA540xEventHandler"/>
        <ref bean="ptpPortOSA540xEventHandler"/>
        <ref bean="ocslaveVirtualPortOSA540xEventHandler"/>
        <ref bean="masterVirtualPortOSA540xEventHandler"/>
        <ref bean="pTPTPFlowPointOSA540xEventHandler"/>
        <ref bean="ethernetTrafficPortOSA540xEventHandler"/>
        <ref bean="gps10MHzPortEventHandler"/>
        <ref bean="portBitsF3EventHandler"/>
        <ref bean="nteF3EventHandler"/>
        <ref bean="f3SyncEventHandler"/>
        <ref bean="f3SyncRefEventHandler"/>
        <ref bean="f3TimeClockEventHandler"/>
        <ref bean="sOOCOSA540xEventHandler"/>
        <ref bean="oCSlaveOSA540xEventHandler"/>
        <ref bean="sJScheduleGroupOSA540xEventHandler"/>
        <ref bean="pTPClockProbeOSA540xEventHandler"/>
        <ref bean="clockProbeOSA540xEventHandler"/>
        <ref bean="pTPClockProbeHisOSA540xEventHandler"/>
        <ref bean="clockProbeHisOSA540xEventHandler"/>
        <ref bean="f3TimeClockRefEventHandler"/>
        <ref bean="dynamicRemoteSlaveOSA540xEventHandler"/>
        <ref bean="irigOpticalOutputPortEventHandler"/>
        <ref bean="irigRelayOutputPortEventHandler"/>
        <ref bean="irigRS422OutputPortEventHandler"/>
        <ref bean="irigDcls1PpsPortEventHandler"/>
        <ref bean="irigAmClkPortEventHandler"/>
        <ref bean="softwareActivationEventHandler"/>
        <ref bean="stlModuleEventHandler"/>
    </util:list>

    <bean class="com.adva.nlms.mediation.config.osa540X.OSA540xNeVersionsUpdateProcess" />
    <bean class="com.adva.nlms.mediation.config.osa540X.capabilities.Osa540xInternalNECapabilities"/>
    <bean class="com.adva.nlms.mediation.config.osa540X.capabilities.Osa540xNECapabilities"/>

</beans>