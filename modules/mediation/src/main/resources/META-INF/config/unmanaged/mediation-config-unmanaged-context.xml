<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~  Copyright 2023 Adtran Networks SE. All rights reserved.
  ~
  ~  Owner: msteiner
  -->

<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
        http://www.springframework.org/schema/context
        http://www.springframework.org/schema/context/spring-context-3.0.xsd">
    <context:annotation-config/>

    <bean class="com.adva.nlms.mediation.config.unmanaged.dwdm.UnmanagedPortDao"/>
    <bean class="com.adva.nlms.mediation.config.unmanaged.dwdm.UnmanagedCrossConnectionDaoImpl"/>

    <bean class="com.adva.nlms.mediation.config.unmanaged.dwdm.UnmanagedCrossConnectionsRestService"/>
    <bean class="com.adva.nlms.mediation.config.unmanaged.dwdm.UnmanagedPortRestService"/>
    <bean class="com.adva.nlms.mediation.config.unmanaged.dwdm.HandoverPortRestService"/>
    <bean class="com.adva.nlms.mediation.config.unmanaged.dwdm.UnmanagedPortService"/>
    <bean class="com.adva.nlms.mediation.config.unmanaged.dwdm.HandoverPortService"/>

    <bean class="com.adva.nlms.mediation.config.unmanaged.dwdm.ODSUnmanagedTerminationPointDaoImpl"/>
    <bean class="com.adva.nlms.mediation.config.unmanaged.dwdm.ODUTerminationPointDaoImpl"/>
    <bean class="com.adva.nlms.mediation.config.unmanaged.dwdm.UnmanagedTerminationPointServiceImpl"/>
    <bean class="com.adva.nlms.mediation.config.unmanaged.dwdm.UnmanagedTerminationPointRestService"/>
    <bean class="    com.adva.nlms.mediation.config.unmanaged.dwdm.NotificationHelper"/>

    <bean class="com.adva.nlms.mediation.mltopologymodel.dbconsistency.checkers.MLUNoTopologyConsistencyChecker"/>
    <bean class="com.adva.nlms.mediation.mltopologymodel.dbconsistency.checkers.MLVirtualOtnConsistencyChecker"/>
    <!-- mo facade -->
    <bean id="serviceManagerMOFacadeUnmanaged"
          class="com.adva.nlms.mediation.config.unmanaged.sm.impl.ServiceManagerMOFacadeUnmanaged"/>
    <bean class="com.adva.nlms.mediation.config.unmanaged.capabilities.UnmanagedNetworkElementCapabilities"/>

</beans>