<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright 2023 Adtran Networks SE. All rights reserved.
  ~
  ~ Owner: benjamint
  -->
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:util="http://www.springframework.org/schema/util"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
       http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util.xsd">
    <context:annotation-config/>
    <util:list id="fspTxxEventHandlers" value-type="com.adva.nlms.mediation.config.entity.IInvPostProcessEventHandler">
        <ref bean="physicalEntityEventHandler"/>
        <ref bean="shelfF3EventHandler"/>
        <ref bean="portF3NetEventHelper"/>
        <ref bean="portF3AccEventHandler"/>
        <ref bean="nteF3EventHandler"/>
        <ref bean="esaProbeScheduleGroupF3EventHandler"/>
        <ref bean="esaProbeF3EventHandler"/>
        <ref bean="esaProbeCosConfigF3EventHandler"/>
        <ref bean="esaProbeMultiMepF3EventHandler"/>
        <ref bean="esaReflectorEventHandler"/>
        <ref bean="flowF3EventHandler"/>
        <ref bean="vcgF3EventHandlerFSPTxx04"/>
        <ref bean="e3T3EventHandlerFSPTxx04"/>
        <ref bean="lagF3EventHandler"/>
        <ref bean="f3SyncRefEventHandlerFSP150CM"/>
        <ref bean="netPortQOSShaperEventHandler"/>
        <ref bean="ifPortEventHandler"/>
        <ref bean="systemEventHandler"/>
        <ref bean="softwareActivationEventHandler"/>
        <ref bean="f3DatabaseSyncTrapEventHandler"/>
        <ref bean="accPrioMapEventHandler"/>
        <ref bean="netPrioMapEventHandler"/>
        <ref bean="maNetEventHandler"/>
        <ref bean="maCompEventHandler"/>
        <ref bean="mepEventHandler"/>
        <ref bean="mdEventHandler"/>
        <ref bean="mepListEventHandler"/>
        <ref bean="f3SyncEventHandler"/>
        <ref bean="accPortQOSShaperEventHandler"/>
        <ref bean="qosFlowPolicerF3EventHandler"/>
        <ref bean="lagPortF3EventHandler"/>
        <ref bean="e1T1EventHandlerFSPTxx04"/>
        <!-- System Feature -->
        <ref bean="f3SystemFeatureEventHandler"/>
    </util:list>

    <bean class="com.adva.nlms.mediation.config.fspTxx04.capabilities.TXX04InternalNECapabilities" />
    <bean class="com.adva.nlms.mediation.config.fspTxx04.capabilities.TXX04NECapabilities" />
</beans>