<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:util="http://www.springframework.org/schema/util"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
       http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util.xsd">
    <context:annotation-config/>
    <bean class="com.adva.nlms.mediation.config.osa5548c.entity.module.ModuleOSAF3DAO"/>
    <bean class="com.adva.nlms.mediation.config.osa5548c.entity.port.PortOSAF3DAO"/>
    <util:list id="osa5548cEventHandlers" value-type="com.adva.nlms.mediation.config.entity.IPostProcessEventHandler">
        <ref bean="physicalEntityEventHandler"/>
        <ref bean="f3GPSReceiverPortOSAEventHandler"/>
        <ref bean="soocEventHandler"/>
        <ref bean="systemEventHandler"/>
        <ref bean="tcEventHandler"/>
        <ref bean="masterClockInterfaceEventHandler"/>
        <ref bean="masterClockEventHandler"/>
        <ref bean="f3SyncEventHandler"/>
        <ref bean="f3SyncRefEventHandler"/>
        <ref bean="f3TimeClockEventHandler"/>
        <ref bean="f3TimeClockRefEventHandler"/>
        <ref bean="ptpClockEventHandler"/>
        <ref bean="portClockEventHandler"/>
        <ref bean="entityPhysicalEventHandler"/>
        <ref bean="redundancyGroupEventHandler"/>
        <ref bean="f3DatabaseSyncTrapOSAEventHandler"/>
        <ref bean="dynamicRemoteSlaveOSAEventHandler"/>
        <ref bean="l2dynamicRemoteSlaveEventHandler"/>
        <ref bean="ifPortEventHandler"/>
        <ref bean="softwareActivationEventHandler"/>
        <!--<ref bean="portF3AccEventHandler"/>-->
    </util:list>

    <bean class="com.adva.nlms.mediation.config.osa5548c.capabilities.OSA5548cInternalNECapabilities"/>
    <bean class="com.adva.nlms.mediation.config.osa5548c.capabilities.OSA5548cNECapabilities"/>
</beans>
