<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

    <bean class="org.springframework.beans.factory.config.MethodInvokingFactoryBean">
        <property name="targetObject" ref="dbImplToScanMappingManager"/>
        <property name="targetMethod" value="registerMapping" />
        <property name="arguments" >
            <list>
                <list value-type="com.adva.nlms.mediation.config.entity.mapping.DBImplToScanMapping">
                    <bean class="com.adva.nlms.mediation.config.osa5548c.entity.port.PortOSAF3DBImplScanMapping"/>
                </list>
                <list value-type="com.adva.nlms.common.config.netypes.NEType">
                    <value>OSA5548C_SSU60</value>
                    <value>OSA5548C_SSU200</value>
                    <value>OSA5548C_TSG60</value>
                    <value>OSA5548C_TSG200</value>
                    <value>OSA5335_PTPGM</value>
                </list>
            </list>
        </property>
    </bean>
    <bean class="org.springframework.beans.factory.config.MethodInvokingFactoryBean">
        <property name="targetObject" ref="dbImplToScanMappingManager"/>
        <property name="targetMethod" value="registerMapping" />
        <property name="arguments" >
            <list>
                <list value-type="com.adva.nlms.mediation.config.entity.mapping.DBImplToScanMapping">
                    <bean class="com.adva.nlms.mediation.config.f3.entity.port.gpsreceiver.F3GPSReceiverPortDBImplScanMapping"/>
                </list>
                <list value-type="com.adva.nlms.common.config.netypes.NEType">
                    <value>OSA5548C_SSU60</value>
                    <value>OSA5548C_SSU200</value>
                    <value>OSA5548C_TSG60</value>
                    <value>OSA5548C_TSG200</value>
                    <value>OSA5335_PTPGM</value>
                </list>
            </list>
        </property>
    </bean>
</beans>