<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:jms="http://www.springframework.org/schema/jms"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
        http://www.springframework.org/schema/context
        http://www.springframework.org/schema/context/spring-context-3.0.xsd
        http://www.springframework.org/schema/jms
	    http://www.springframework.org/schema/jms/spring-jms.xsd">

    <context:annotation-config/>

    <bean class="org.springframework.beans.factory.config.PropertyPlaceholderConfigurer">
        <property name="locations">
            <list>
                <value>file:fnm.properties</value>
                <value>file:activemq/conf/jms.properties</value>
            </list>
        </property>
        <property name="ignoreUnresolvablePlaceholders" value="true"/>
    </bean>

    <bean class="com.adva.nlms.mediation.config.entity.module.ModulePagingUtils" lazy-init="true"/>

    <bean class="com.adva.nlms.mediation.reload.modules.SendUpdateForIntraNe" lazy-init="true"/>
    <bean class="com.adva.nlms.mediation.reload.modules.SendUpdateForWdmModule" lazy-init="true"/>
    <bean class="com.adva.nlms.mediation.reload.shelves.UpdateShelvesWhenNeeded" lazy-init="true"/>
    <bean class="com.adva.nlms.mediation.reload.modules.SendUpdateForEthModule" lazy-init="true"/>
    <bean class="com.adva.nlms.mediation.reload.modules.SendUpdateForHatteras" lazy-init="true"/>

    <bean id="wdmModuleRetrieval" class="com.adva.nlms.mediation.reload.modules.WdmModuleRetrieval" lazy-init="true"/>
    <bean id="ethModuleRetrieval" class="com.adva.nlms.mediation.reload.modules.EthModuleRetrieval" lazy-init="true"/>
    <bean id="hatterasModuleRetrieval" class="com.adva.nlms.mediation.reload.modules.HatterasModuleRetrieval" lazy-init="true"/>
    <bean id="legacyWdmModuleRetrieval" class="com.adva.nlms.mediation.reload.modules.LegacyWdmModuleRetrieval" lazy-init="true"/>
    <bean id="f8ModuleRetrieval" class="com.adva.nlms.mediation.reload.modules.F8ModuleRetrieval" lazy-init="true"/>


    <bean id="reloadService" class="com.adva.nlms.mediation.reload.ReloadPagesAfterEventUpdateService" lazy-init="true"/>

    <bean id="reloadEditorDispatcher" class="com.adva.nlms.mediation.reload.AutomaticEditorReload" />
    <bean id="cryptoMessageForwarder" class="com.adva.nlms.mediation.reload.crypto.CryptoMessageForwarder" />

    <jms:listener-container connection-factory="jmsConnectionFactory" acknowledge="auto" destination-type="topic" prefetch="1000" >
        <jms:listener destination="topic.eventUpdate" ref="reloadEditorDispatcher" method="onMessage"/>
        <jms:listener destination="topic.cryptoUpdate" ref="cryptoMessageForwarder" method="onMessage"/>
    </jms:listener-container>

    <bean id="reloadTaskExecutor" class="org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor">
        <property name="corePoolSize" value="10"/>
        <property name="maxPoolSize" value="10"/>
        <property name="daemon" value="true"/>
        <property name="keepAliveSeconds" value="3"/>
        <property name="threadNamePrefix" value="RLD-"/>
    </bean>


    <bean id="moduleReloader" class="com.adva.nlms.mediation.reload.ModuleReloader" lazy-init="true">
        <constructor-arg ref="mdMessageSender" />
        <constructor-arg ref="managedObjectDAO"/>
        <constructor-arg>
            <list>
                <ref bean="wdmModuleRetrieval"/>
                <ref bean="ethModuleRetrieval"/>
                <ref bean="hatterasModuleRetrieval"/>
                <ref bean="legacyWdmModuleRetrieval"/>
                <ref bean="f8ModuleRetrieval"/>
            </list>
        </constructor-arg>
    </bean>

    <bean id="shelfReloader" class="com.adva.nlms.mediation.reload.ShelfReloader" lazy-init="true">
        <constructor-arg ref="mdMessageSender" />
        <constructor-arg ref="managedObjectDAO"/>
        <constructor-arg>
            <list>
                <ref bean="shelfRetrieval"/>
            </list>
        </constructor-arg>
    </bean>

    <bean id="editorReloadExecutor" class="org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor">
        <property name="maxPoolSize" value="6"/>
        <property name="corePoolSize" value="1"/>
        <property name="threadNamePrefix" value="RLD_INF-"/>
    </bean>

    <bean id="editorReloadQueue" class="java.util.concurrent.LinkedBlockingQueue" />

    <bean id="reloadManager" class="com.adva.nlms.mediation.reload.EditorReloadManager" lazy-init="true">
        <constructor-arg name="editorReloadExecutor" type="org.springframework.core.task.TaskExecutor" ref="editorReloadExecutor" index="0"/>
        <constructor-arg  ref="registeredNetworkElementsHdlr" index="1"/>
        <constructor-arg name="editorReloadQueue" type="java.util.concurrent.BlockingQueue" ref="editorReloadQueue" index="3"/>
    </bean>

    <bean class="com.adva.nlms.mediation.config.moobservers.ModuleUpdateObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
        <constructor-arg ref="reloadManager"/>
    </bean>

    <bean class="com.adva.nlms.mediation.config.moobservers.ModuleCreationObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
        <constructor-arg ref="reloadManager"/>
    </bean>

    <bean class="com.adva.nlms.mediation.config.moobservers.ModuleDeletionObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
        <constructor-arg ref="reloadManager"/>
    </bean>

    <bean class="com.adva.nlms.mediation.config.moobservers.FanCreationObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
        <constructor-arg ref="reloadManager"/>
    </bean>

    <bean class="com.adva.nlms.mediation.config.moobservers.FanUpdateObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
        <constructor-arg ref="reloadManager"/>
    </bean>

    <bean class="com.adva.nlms.mediation.config.moobservers.FanDeletionObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
        <constructor-arg ref="reloadManager"/>
    </bean>

    <bean class="com.adva.nlms.mediation.config.moobservers.PowerSupplyCreationObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
        <constructor-arg ref="reloadManager"/>
    </bean>

    <bean class="com.adva.nlms.mediation.config.moobservers.PowerSupplyUpdateObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
        <constructor-arg ref="reloadManager"/>
    </bean>

    <bean class="com.adva.nlms.mediation.config.moobservers.PowerSupplyDeletionObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
        <constructor-arg ref="reloadManager"/>
    </bean>

    <bean class="com.adva.nlms.mediation.config.moobservers.ShelfCreationObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
        <constructor-arg ref="reloadManager"/>
    </bean>


    <bean class="com.adva.nlms.mediation.config.moobservers.ShelfDeletionObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
        <constructor-arg ref="reloadManager"/>
    </bean>

    <bean class="com.adva.nlms.mediation.config.entity.shelf.ShelfDTOMapperImpl" />

    <bean id="shelfRetrieval" class="com.adva.nlms.mediation.reload.shelves.ShelfRetrieval" lazy-init="true"/>

</beans>