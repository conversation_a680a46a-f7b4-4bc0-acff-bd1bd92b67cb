<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:jms="http://www.springframework.org/schema/jms"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
        http://www.springframework.org/schema/context
        http://www.springframework.org/schema/context/spring-context-3.0.xsd
        http://www.springframework.org/schema/jms
	    http://www.springframework.org/schema/jms/spring-jms.xsd">

    <context:annotation-config/>
<!--    <bean class="com.adva.nlms.mediation.report.performance.location.LocationPerformanceReportBuilder">-->
<!--        <property name="performanceManagerHelper" value="#{performancePrivate.PerformanceManagerHelper}"/>-->
<!--    </bean>-->
<!--    <bean class="com.adva.nlms.mediation.report.performance.PerformanceReportThresholdChecker"/>-->
<!--    <bean class="com.adva.nlms.mediation.performance.template.LocationPerformanceReportDAOHelper"/>-->
<!--    <bean class="com.adva.nlms.mediation.report.performance.location.LocationPerformanceRecordReportDAO"/>-->
<!--    <bean class="com.adva.nlms.mediation.report.performance.connection.ServicePerformanceRecordReportDAO"/>-->
<!--    <bean class="com.adva.nlms.mediation.report.performance.location.hierarchy.EntityHierarchyDAO"/>-->
<!--    <bean class="com.adva.nlms.mediation.report.performance.location.LocationPerformanceReportSizeEstimator" id="locationPerformanceReportSizeEstimator"/>-->
<!--    <bean class="com.adva.nlms.mediation.report.performance.connection.ServicePerformanceReportSizeEstimator" id="servicePerformanceReportSizeEstimator"/>-->
<!--    <bean class="com.adva.nlms.mediation.report.performance.spanloss.LinkLossReportBuilder"/>-->
<!--    <bean class="com.adva.nlms.mediation.report.performance.spanloss.ScheduledLinkLossReportBuilder"/>-->
</beans>