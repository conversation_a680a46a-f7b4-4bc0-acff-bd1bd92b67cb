<?xml version="1.0" encoding="UTF-8"?>
<!--<beans default-lazy-init="true" xmlns="http://www.springframework.org/schema/beans"-->
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
        http://www.springframework.org/schema/context
        http://www.springframework.org/schema/context/spring-context-3.0.xsd">
    <context:annotation-config/>

    <bean id="performanceManagerDAO" class="com.adva.nlms.mediation.performance.manager.PerformanceManagerDAO"/>
    <bean id="pmRecordDAO" class="com.adva.nlms.mediation.performance.PmRecordDAO" lazy-init="true" factory-method="getInstance"/>
    <bean id="f8BinDAO" class="com.adva.nlms.mediation.performance.f8.F8BinDAO" lazy-init="true"/>
    <bean id="pmRecordDAOProxy" class="org.springframework.aop.framework.ProxyFactoryBean">
        <property name="targetSource">
            <bean class="org.springframework.aop.target.LazyInitTargetSource">
                <property name="targetBeanName" value="pmRecordDAO"/>
            </bean>
        </property>
    </bean>
    <!--<bean id="PmSerieDAO" class="com.adva.nlms.mediation.performance.PmSerieDAO" lazy-init="true"/>-->
    <!--<bean id="overloadProtection" class="com.adva.nlms.mediation.performance.OverloadProtection"/>-->
    <bean id="PerformanceStructureFactory" class="com.adva.nlms.common.performance.structure.PerformanceStructureFactory" lazy-init="true"/>
    <bean id="PerformanceStructureFactoryProxy" class="org.springframework.aop.framework.ProxyFactoryBean" lazy-init="true">
        <property name="targetSource">
            <bean class="org.springframework.aop.target.LazyInitTargetSource">
                <property name="targetBeanName" value="PerformanceStructureFactory"/>
            </bean>
        </property>
    </bean>
    <bean id="PerformanceManagerHelper" class="com.adva.nlms.common.performance.structure.PerformanceManagerHelper" lazy-init="true">
        <property name="structureFactory" ref="PerformanceStructureFactoryProxy"/>
    </bean>
    <bean id="OidProviderDiag" class="com.adva.nlms.mediation.performance.diagnostics.OidProviderDiag">
    </bean>
    <bean id="OIDProviderFactory" class="com.adva.nlms.mediation.performance.oidprovider.OIDProviderFactory">
        <property name="diag" ref="OidProviderDiag"/>
    </bean>
    <bean id="PerformanceManagerFactory" class="com.adva.nlms.mediation.performance.manager.PerformanceManagerFactory">
        <property name="pmSerieDAO" ref="PmSerieDAO"/>
        <property name="collectionFilterHelper" ref="CollectionFilterHelper"/>
        <property name="managerHelper" ref="PerformanceManagerHelper"/>
        <property name="entityTypeProviderFactory" ref="EntityTypeProviderFactory"/>
    </bean>
    <bean id="PmSerieDAO" class="com.adva.nlms.mediation.performance.PmSerieDAO" lazy-init="true"/>
    <bean id="CollectionFilterHelper" class="com.adva.nlms.mediation.performance.template.CollectionFilterHelper"/>
    <bean id="EntityTypeProviderFactory" class="com.adva.nlms.mediation.performance.entitytypeprovider.EntityTypeProviderFactory"/>
    <bean id="PmMappingDAO" class="com.adva.nlms.mediation.performance.PmMappingDAO"/>
    <bean id="PersistentPerformanceTemplateDAO" class="com.adva.nlms.mediation.performance.template.PersistentPerformanceTemplateDAO"/>
    <bean id="MappingCalculator" class="com.adva.nlms.mediation.performance.template.MappingCalculator">
        <property name="performanceManagerHelper" ref="PerformanceManagerHelper"/>
    </bean>
    <bean class="com.adva.nlms.mediation.performance.PmSubnetStatistic"/>
    <bean id="CSVManagerDAO" class="com.adva.nlms.mediation.performance.dao.CSVManagerDAO" />
    <bean id ="PmTopologyDAO" class="com.adva.nlms.mediation.performance.dao.PmTopologyDAO" />
    <bean id="dtagFB" class="com.adva.nlms.mediation.performance.csv.dtag.mapping.DtagMappingFactoryBean"/>
    <bean id="DtagMapping" class="com.adva.nlms.mediation.performance.csv.dtag.mapping.DtagMapping" factory-bean="dtagFB" factory-method="parseMapping"/>
    <bean id="CsvLoggerFactory" class="com.adva.nlms.mediation.performance.csv.dtag.CsvLoggerFactory"/>
    <bean id="CsvFileManagerDtag" class="com.adva.nlms.mediation.performance.csv.dtag.CsvFileManagerDtag">
        <property name="loggerFactory" ref="CsvLoggerFactory"/>
    </bean>

    <bean id="CSVFileManagerAdva" class="com.adva.nlms.mediation.performance.csv.CSVFileManagerAdva"/>
    <bean id="CSVFileManagerBt" class="com.adva.nlms.mediation.performance.csv.CSVFileManagerBt"/>
    <bean id="PerformanceTestBeanDAO" class="com.adva.nlms.mediation.performance.tfw.PerformanceTestBeanDAO"/>
    <bean id="SpanLossRecordDAO" class="com.adva.nlms.mediation.performance.spanloss.SpanLossRecordDAO"/>
    <bean id="SpanLossCalculation" class="com.adva.nlms.mediation.performance.spanloss.SpanLossCalculation"/>

    <!--<bean id="MyBeanProxy" class="org.springframework.aop.framework.ProxyFactoryBean">-->
        <!--<property name="targetSource">-->
            <!--<bean class="org.springframework.aop.target.LazyInitTargetSource">-->
                <!--<property name="targetBeanName" value="MyBean"/>-->
            <!--</bean>-->
        <!--</property>-->
    <!--</bean>-->
    <!--<bean id="MyBean" class="com.adva.nlms.mediation.performance.MyBean" lazy-init="true"></bean>-->

</beans>