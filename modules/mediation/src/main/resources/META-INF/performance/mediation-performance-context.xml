<?xml version="1.0" encoding="UTF-8"?>
<!--<beans default-lazy-init="true" xmlns="http://www.springframework.org/schema/beans"-->
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
        http://www.springframework.org/schema/context
        http://www.springframework.org/schema/context/spring-context-3.0.xsd">
    <context:annotation-config/>
    <!-- ant build checks for string to add this file in jar com.adva.nlms.mediation.event.EventDataAccessHdlrImpl, do not remove this line so as this file is included in jar, unless some beans are added-->
    <bean class="com.adva.nlms.mediation.performance.PerformancePollingShortCommand">
        <qualifier type="com.adva.nlms.mediation.polling.NePolling"/>
    </bean>
    <bean class="com.adva.nlms.mediation.performance.PerformancePollingLongCommand">
        <qualifier type="com.adva.nlms.mediation.polling.NePolling"/>
    </bean>
    <bean class="com.adva.nlms.mediation.SpringAppContext"/>
    <bean id="TemplateOverrideChecker" class="com.adva.nlms.mediation.performance.template.TemplateOverrideChecker">
        <constructor-arg index="0" ref="com.adva.nlms.mediation.sm.ServiceManagerFacadeImpl"/>
        <constructor-arg index="1" ref="PerformanceCtrlDAO"/>
        <constructor-arg index="2" ref="com.adva.nlms.pd.inventory.mofacade.modto.PDProvisioningFacade"/>
        <constructor-arg index="3" ref="com.adva.nlms.mediation.sm.helper.F8ServiceHelperImpl"/>
    </bean>
    <bean id="PerformanceCtrlDAO" class="com.adva.nlms.mediation.performance.PerformanceCtrlDAO" init-method="init"/>
    <bean class="com.adva.nlms.mediation.performance.template.TemplatesPerPollingType">
        <constructor-arg index="0" value="#{performancePrivate.PersistentPerformanceTemplateDAO}"/>
    </bean>
    <bean class="com.adva.nlms.mediation.performance.CSVFileTransferRestHdlr">
        <constructor-arg index="0" ref="com.adva.nlms.mediation.housekeeping.FTPPreferencesDAOImpl"/>
    </bean>

    <bean id="TemplateAssignmentProgress" class="com.adva.nlms.mediation.performance.template.TemplateAssignmentProgress">
        <constructor-arg index="0" ref="mdMessageSender"/>
    </bean>
    <bean id="ServicePMFinder" class="com.adva.nlms.mediation.performance.template.ServicePMFinder">
        <property name="performanceManagerFactory" value="#{performancePrivate.PerformanceManagerFactory}"/>
        <property name="serviceManagerFacade" ref="com.adva.nlms.mediation.sm.ServiceManagerFacadeImpl"/>
        <property name="pdServiceDataController" ref="pdServiceDataControllerImpl"/>
    </bean>
    <bean id="DboConCache" class="com.adva.nlms.mediation.performance.template.DboConCache">
        <constructor-arg index="0" ref="ServicePMFinder"/>
    </bean>
    <bean id="PerformanceManagerFinder" class="com.adva.nlms.mediation.performance.template.PerformanceManagerFinder">
        <property name="servicePerformanceManagerFinder" ref="ServicePMFinder"/>
        <property name="subnetHdlrLocal" value="#{subnetHdlrImpl}"/>
        <property name="provisioningFacadeAPI" ref="com.adva.nlms.pd.inventory.mofacade.modto.PDProvisioningFacade"/>
        <property name="f8ServiceHelper" ref="com.adva.nlms.mediation.sm.helper.F8ServiceHelperImpl"/>
        <property name="pdl3IPVPNServiceApi" ref="pdl3IPVPNServiceImpl"/>
    </bean>
    <bean id="TemplateManagerImpl" class="com.adva.nlms.mediation.performance.template.TemplateManagerImpl">
        <property name="persistentPerformanceTemplateDAO" value="#{performancePrivate.PersistentPerformanceTemplateDAO}"/>
        <property name="performanceManagerHelper" value="#{performancePrivate.PerformanceManagerHelper}"/>
        <!--<property name="templateAssignmentManager" ref="TemplateAssignmentManager"/>-->
<!--        <property name="structureFactory" value="#{performancePrivate.PerformanceStructureFactory}"/>-->
        <property name="serviceManagerFacade" ref="com.adva.nlms.mediation.sm.ServiceManagerFacadeImpl"/>
        <property name="provisioningFacadeAPI" ref="com.adva.nlms.pd.inventory.mofacade.modto.PDProvisioningFacade"/>
        <property name="pdServiceDataController" ref="pdServiceDataControllerImpl"/>
        <property name="pdl3IPVPNServiceApi" ref="pdl3IPVPNServiceImpl"/>
    </bean>

    <bean id="AssignMapping" class="com.adva.nlms.mediation.performance.template.AssignMapping">
        <constructor-arg index="0" value="#{performancePrivate.PmSerieDAO}"/>
        <constructor-arg index="1" value="#{performancePrivate.PersistentPerformanceTemplateDAO}"/>
        <constructor-arg index="2" ref="TemplateAssignmentProgress"/>
    </bean>
    <bean id="AssignPmTemplateService" class="com.adva.nlms.mediation.performance.template.AssignPmTemplateService">
        <constructor-arg index="0" ref="com.adva.nlms.mediation.sm.ServiceManagerFacadeImpl"/>
        <constructor-arg index="1" ref="ServicePMFinder"/>
        <constructor-arg index="2" ref="AssignMapping"/>
    </bean>
    <bean id="AssignPmTemplatePDService" class="com.adva.nlms.mediation.performance.template.AssignPmTemplatePDService">
        <constructor-arg index="0" ref="com.adva.nlms.mediation.sm.ServiceManagerFacadeImpl"/>
        <constructor-arg index="1" ref="ServicePMFinder"/>
        <constructor-arg index="2" ref="AssignMapping"/>
        <constructor-arg index="3" ref="com.adva.nlms.pd.inventory.mofacade.modto.PDProvisioningFacade"/>
        <constructor-arg index="4" ref="pdServiceDataControllerImpl"/>
    </bean>
    <bean id="AssignPmTemplateIpvpnService" class="com.adva.nlms.mediation.performance.template.AssignPmTemplateIpvpnService">
        <constructor-arg index="0" ref="com.adva.nlms.mediation.sm.ServiceManagerFacadeImpl"/>
        <constructor-arg index="1" ref="ServicePMFinder"/>
        <constructor-arg index="2" ref="AssignMapping"/>
        <constructor-arg index="3" ref="pdl3IPVPNServiceImpl"/>
    </bean>
    <bean id="AssignPmTemplateF8Service" class="com.adva.nlms.mediation.performance.template.AssignPmTemplateF8Service">
        <constructor-arg index="0" ref="AssignMapping"/>
        <constructor-arg index="1" ref="com.adva.nlms.mediation.sm.ServiceManagerFacadeImpl"/>
        <constructor-arg index="2" ref="ServicePMFinder"/>
        <constructor-arg index="3" ref="com.adva.nlms.mediation.sm.helper.F8ServiceHelperImpl"/>
    </bean>
    <bean id="TemplateAssignmentManager" class="com.adva.nlms.mediation.performance.template.TemplateAssignmentManager">
        <property name="templateAssignmentHelper" ref="TemplateOverrideChecker"/>
        <property name="templateAssignmentProgress" ref="TemplateAssignmentProgress"/>
        <property name="performanceManagerFinder" ref="PerformanceManagerFinder"/>
        <property name="servicePerformanceManagerFinder" ref="ServicePMFinder"/>
        <property name="templateManager" ref="TemplateManagerImpl"/>
        <property name="iTemplateManager" ref="TemplateManagerImpl" />
        <property name="serviceManagerFacade" ref="com.adva.nlms.mediation.sm.ServiceManagerFacadeImpl"/>
        <property name="pollingCtrl" ref="com.adva.nlms.mediation.polling.PollingCtrlImpl"/>
        <property name="assignMapping" ref="AssignMapping"/>
        <property name="templateService" ref="AssignPmTemplateService"/>
        <property name="templatePDService" ref="AssignPmTemplatePDService"/>
        <property name="templateF8Service" ref="AssignPmTemplateF8Service"/>
        <property name="provisioningFacadeAPI" ref="com.adva.nlms.pd.inventory.mofacade.modto.PDProvisioningFacade"/>
        <property name="f8ServiceHelper" ref="com.adva.nlms.mediation.sm.helper.F8ServiceHelperImpl"/>
        <property name="templateIpvpnService" ref="AssignPmTemplateIpvpnService"/>
        <property name="pdl3IPVPNServiceApi" ref="pdl3IPVPNServiceImpl"/>
        <property name="imlServicePMTemplate" ref="mlServicePMTemplateHdl"/>
    </bean>
    <bean id="TemplateImporter" class="com.adva.nlms.mediation.performance.template.TemplateImporter">
        <property name="structureFactory" value="#{performancePrivate.PerformanceStructureFactory}"/>
        <property name="persistentPerformanceTemplateDAO" value="#{performancePrivate.PersistentPerformanceTemplateDAO}"/>
        <property name="templateManager" ref="TemplateManagerImpl"/>
    </bean>
    <bean id="jsonRecordReader" class="com.adva.nlms.mediation.performance.json.RecordReader">
        <constructor-arg index="0" value="#{performancePrivate.CSVManagerDAO}"/>
        <constructor-arg index="1" value="#{performancePrivate.PerformanceManagerHelper}"/>
        <constructor-arg index="2" ref="pmCsvNameAttributeDescriptionDecorator"/>
    </bean>
    <bean id="recordReaderFacade" class="com.adva.nlms.mediation.performance.json.RecordReaderFacadeImpl">
        <property name="managerFinder" ref="PerformanceManagerFinder"/>
        <property name="recordReaderJson" ref="jsonRecordReader"/>
    </bean>
    <bean id="selectedPMLayersProvider" class="com.adva.nlms.mediation.performance.layer.SelectedPMLayersProviderImpl">
        <constructor-arg ref="TemplateManagerImpl"/>
        <constructor-arg value="#{performancePrivate.PerformanceManagerHelper}"/>
    </bean>

    <bean id="MappingFactory" class="com.adva.nlms.mediation.performance.template.MappingFactory">
        <property name="templateManager" ref="TemplateManagerImpl"/>
        <property name="mappingCalculator" value="#{performancePrivate.MappingCalculator}"/>
        <property name="mappingDAO" value="#{performancePrivate.PmMappingDAO}"/>
        <property name="collectionFilterHelper" value="#{performancePrivate.CollectionFilterHelper}"/>
    </bean>
    <bean id="pmChangeNotifier" class="com.adva.nlms.mediation.performance.template.ServiceChangeNotifier">
        <property name="templateAssignmentManager" ref="TemplateAssignmentManager"/>
    </bean>
    <!--<bean id="test3Mock" class="org.mockito.Mockito" factory-method="mock">-->
        <!--<constructor-arg value="com.adva.nlms.mediation.performance.Test3" />-->
    <!--</bean>-->

    <bean id="OldPMStructureFillingStrategy" class="com.adva.nlms.mediation.performance.database.OldPMStructureFillingStrategy">
        <property name="performanceManagerHelper" value="#{performancePrivate.PerformanceManagerHelper}"/>
        <property name="networkElementHdlr" value="#{networkElementHdlr}"/>
    </bean>


    <bean class="com.adva.nlms.mediation.performance.manager.EthernetF3PortMediaTypeObserver">
      <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
  </bean>

    <bean id="OverloadProtection" class="com.adva.nlms.mediation.performance.OverloadProtection">
        <property name="eventCtrl" ref="com.adva.nlms.mediation.event.EventCtrlImpl"/>
        <property name="statisticsCollector" ref="PMStatisticsCollector"/>
    </bean>

    <bean id="F8OverloadProtector" class="com.adva.nlms.mediation.performance.f8.F8OverloadProtector">
        <constructor-arg ref="OverloadProtection"/>
        <constructor-arg ref="PMStatisticsCollector"/>
    </bean>

    <bean id="PerformanceManagerHdlr" class="com.adva.nlms.mediation.performance.snmp.PMCollector">
        <property name="performanceManagerDAO" value="#{performancePrivate.performanceManagerDAO}"/>
        <property name="overloadProtection" ref="OverloadProtection"/>
        <property name="pmStatisticsCollector" ref="PMStatisticsCollector"/>
        <property name="mappingFactory" ref="MappingFactory"/>
        <property name="performanceManagerHelper" value="#{performancePrivate.PerformanceManagerHelper}"/>
        <property name="oidProviderFactory" value="#{performancePrivate.OIDProviderFactory}"/>
        <property name="pmCsvNameAttributeDescriptionDecorator" ref="pmCsvNameAttributeDescriptionDecorator"/>
    </bean>
    <!--<bean id="ConfigurationParameters" class="com.adva.nlms.mediation.performance.ConfigurationParameters"/>-->
    <bean id="BufferedRecordSaver" class="com.adva.nlms.mediation.performance.database.BufferedRecordSaver">
        <property name="recordSaver" ref="RecordSaverNoCompressing"/>
    </bean>
    <bean id="RecordFactory" class="com.adva.nlms.mediation.performance.database.RecordFactory">
        <constructor-arg ref="MappingFactory"/>
    </bean>
    <bean id="RecordSaverNoCompressing" class="com.adva.nlms.mediation.performance.database.RecordSaverNoCompressing">
        <property name="pmSerieDAO" value="#{performancePrivate.PmSerieDAO}"/>
        <property name="mappingFactory" ref="MappingFactory"/>
        <property name="recordFactory" ref="RecordFactory"/>
    </bean>
    <bean class="com.adva.nlms.mediation.performance.database.OldRecordsRemover">
        <constructor-arg name="pmRecordDAO" value="#{performancePrivate.pmRecordDAO}" />
        <constructor-arg name="pmSerieDAO" value="#{performancePrivate.PmSerieDAO}" />
    </bean>
    <bean id="PerformanceInfo" class="com.adva.nlms.mediation.performance.PerformanceInfo">
    </bean>
    <bean id="PMStatisticsCollector" class="com.adva.nlms.mediation.performance.statistics.PMStatisticsCollector">
        <property name="performanceMBean" ref="PerformanceInfo"/>
        <property name="performancePollingStarterQueue" value="#{performanceQueue}"/>
    </bean>
    <!--<bean class="com.adva.nlms.mediation.performance.PmSubnetStatistic"/>-->
    <bean id="PmWatchdog" class="com.adva.nlms.mediation.performance.PmWatchdog">
        <property name="performanceInfo" ref="PerformanceInfo"/>
        <property name="eventProcFacade" ref="eventProcFacadeImpl"/>
        <property name="configCtrl" value="#{configCtrl}"/>
        <property name="messageManager" value="#{messageManager}"/>
    </bean>

    <bean class="com.adva.nlms.mediation.performance.manager.Xg210MibvariantObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>

    <bean id="CsvManagerDtag" class="com.adva.nlms.mediation.performance.csv.dtag.CsvManagerDtag">
        <property name="sftpManagerDtag" ref="com.adva.nlms.mediation.performance.csv.dtag.SFTPManagerDtag"/>
        <property name="mapping" value="#{performancePrivate.DtagMapping}"/>
        <property name="fileManager" value="#{performancePrivate.CsvFileManagerDtag}"/>
        <property name="serverCtrl" ref="com.adva.nlms.mediation.server.ServerCtrlImpl"/>
    </bean>
    <bean id="CSVManager" class="com.adva.nlms.mediation.performance.csv.CSVManager" abstract="true">
        <property name="performanceManagerHelper" value="#{performancePrivate.PerformanceManagerHelper}"/>
        <property name="csvManagerDAO" value="#{performancePrivate.CSVManagerDAO}"/>
        <property name="pmTopologyDAO" value="#{performancePrivate.PmTopologyDAO}"/>
        <property name="performanceTemplateService" ref="TemplateManagerImpl"/>
    </bean>
    <bean id="CsvManagerBt" class="com.adva.nlms.mediation.performance.csv.CsvManagerBt" parent="CSVManager">
        <property name="mappingFactory" ref="MappingFactory"/>
        <property name="fileManager" value="#{performancePrivate.CSVFileManagerBt}"/>
    </bean>
    <bean id="CsvManagerAdva" class="com.adva.nlms.mediation.performance.csv.CsvManagerAdva" parent="CSVManager">
        <property name="fileManager" value="#{performancePrivate.CSVFileManagerAdva}"/>
        <property name="sftpManager" ref="com.adva.nlms.mediation.performance.csv.SFTPManagerTelmex"/>
        <property name="pdServiceDataController" ref="pdServiceDataControllerImpl"/>
        <property name="pdl3IPVPNServiceApi" ref="pdl3IPVPNServiceImpl"/>
    </bean>
    <bean class="com.adva.nlms.mediation.performance.csv.SFTPManagerTelmex"/>
    <bean class="com.adva.nlms.mediation.performance.csv.dtag.SFTPManagerDtag"/>
    <bean id="SFTPManagerForInventoryReports" class="com.adva.nlms.mediation.performance.csv.SFTPManagerForInventoryReports"/>
    <bean class="com.adva.nlms.mediation.performance.csv.SFTPManagerForSyncReports"/>

    <bean class="com.adva.nlms.mediation.performance.tfw.PerformanceTestBean">
        <property name="typeDescriptions" value="#{performancePrivate.PerformanceManagerHelper}"/>
        <property name="csvManagerDAO" value="#{performancePrivate.CSVManagerDAO}"/>
        <property name="registeredTreeNodesHdlr" value="#{registeredTreeNodesHdlr}"/>
        <property name="oidProviderFactory" value="#{performancePrivate.OIDProviderFactory}"/>
        <property name="registeredNetworkElementsHdlr" value="#{registeredNetworkElementsHdlr}"/>
        <property name="performanceTestBeanDAO" value="#{performancePrivate.PerformanceTestBeanDAO}"/>
    </bean>

    <bean id="SpanLossSnmpWorker" class="com.adva.nlms.mediation.performance.spanloss.snmp.SpanLossSnmpWorker">
        <property name="registeredNetworkElements" value="#{registeredNetworkElementsHdlr}"/>
    </bean>

    <bean id="spanLossRestWorker" class="com.adva.nlms.mediation.performance.spanloss.rest.SpanLossRestWorker">
        <constructor-arg index="0" value="#{networkElementHdlr}"/>
    </bean>

    <bean id="pmDataProvider" class="com.adva.nlms.mediation.performance.spanloss.PmDataProvider">
        <constructor-arg name="snmpWorker" ref="SpanLossSnmpWorker"/>
        <constructor-arg name="restWorker" ref="spanLossRestWorker"/>
    </bean>
    <bean id="SpanLossManager" class="com.adva.nlms.mediation.performance.spanloss.SpanLossManager">
        <property name="linkLossEntitiesProvider" ref="com.adva.nlms.mediation.topology.linkloss.LinkLossEntitiesProvider"/>
        <property name="pmDataProvider" ref="pmDataProvider"/>
        <property name="lineDao" value="#{lineDao}"/>
        <property name="spanLossDao" value="#{performancePrivate.SpanLossRecordDAO}"/>
        <property name="calculation" value="#{performancePrivate.SpanLossCalculation}"/>
        <property name="networkElementHdlr" value="#{networkElementHdlr}"/>
    </bean>

    <bean class="com.adva.nlms.mediation.performance.spanloss.SpanLossCollectionPollingCommand">
        <qualifier type="com.adva.nlms.mediation.polling.DomainPolling"/>
        <property name="spanLossManager" ref="SpanLossManager"/>
        <property name="configCtrl" value="#{configCtrl}"/>
        <property name="lineDao" value="#{lineDao}"/>
        <property name="pollingManagerDAO" ref="com.adva.nlms.mediation.polling.db.PollingManagerDAO"/>
    </bean>

    <bean id="lineDeletionSpanLossObserver" class="com.adva.nlms.mediation.performance.spanloss.LineDeletionSpanLossObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
        <property name="spanLossRecordDAO" value="#{performancePrivate.SpanLossRecordDAO}"/>
        <property name="messageManager" value="#{messageManager}"/>
    </bean>

    <bean id="lineUpdateSpanLossObserver" class="com.adva.nlms.mediation.performance.spanloss.LineUpdateSpanLossObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
        <constructor-arg name="messageManager" ref="messageManager" />
        <constructor-arg name="spanLossManager" ref="SpanLossManager" />
        <constructor-arg name="lineDao" ref="lineDao" />
        <constructor-arg name="thresholdAlarmHdlr" ref="thresholdAlarmHdlr" />
    </bean>

    <bean id="PmIndexUpdater" class="com.adva.nlms.mediation.performance.entitytypeprovider.f7.PmIndexUpdaterImpl"/>

    <bean class="com.adva.nlms.mediation.performance.pmfiles.PerformanceMerge">
        <property name="structureFactory" value="#{performancePrivate.PerformanceStructureFactory}"/>
        <property name="performanceManagerHelper" value="#{performancePrivate.PerformanceManagerHelper}"/>
    </bean>

    <bean id="ctxFactory" class="com.adva.nlms.mediation.CurrentContext"/>
    <bean id="currentCtx" class="org.springframework.context.ApplicationContext" factory-bean="ctxFactory" factory-method="getCurrent"/>

    <bean id="performancePrivate"
          class="org.springframework.context.support.ClassPathXmlApplicationContext">
        <constructor-arg>
            <list>
                <value>META-INF/performance/mediation-performance-private-context.xml</value>
            </list>
        </constructor-arg>
        <constructor-arg ref="currentCtx"/>
    </bean>
    <bean id="performancePrivateProxy" class="org.springframework.aop.framework.ProxyFactoryBean">
        <property name="targetSource">
            <bean class="org.springframework.aop.target.LazyInitTargetSource">
                <property name="targetBeanName" value="performancePrivate"/>
            </bean>
        </property>
    </bean>

    <!--<bean id="PmSftpAlarmCleaner" class="com.adva.nlms.mediation.performance.csv.SFTPManagerForInventoryReports"/>-->
    <bean id="pmCsvExport" class="com.adva.nlms.mediation.performance.csv.PmCsvExport">
        <property name="serverCtrl" ref="com.adva.nlms.mediation.server.ServerCtrlImpl"/>
        <property name="performanceMBean" ref="PerformanceInfo"/>
        <property name="csvManagerBt" ref="CsvManagerBt"/>
        <property name="csvManagerDtag" ref="CsvManagerDtag"/>
        <property name="csvManagerAdva" ref="CsvManagerAdva"/>
        <property name="subnetHdlr" ref="subnetHdlrImpl"/>
    </bean>
    <bean id="dbRecordReader" class="com.adva.nlms.mediation.performance.database.PmRecordReader">
        <constructor-arg index="0" value="#{performancePrivate.PerformanceStructureFactory}"/>
        <constructor-arg index="1" ref="OldPMStructureFillingStrategy"/>
        <constructor-arg index="2" value="#{performancePrivate.PerformanceManagerHelper}"/>
    </bean>
    <bean class="com.adva.nlms.mediation.performance.database.PmRecordManager">
        <constructor-arg index="0" value="#{performancePrivate.pmRecordDAO}"/>
        <constructor-arg index="1" value="#{performancePrivate.performanceManagerDAO}"/>
    </bean>
    <bean id="TemplatesController" class="com.adva.nlms.mediation.performance.template.TemplatesController">
        <constructor-arg index="0" ref="TemplateManagerImpl"/>
        <constructor-arg index="1" ref="TemplateAssignmentManager"/>
        <constructor-arg index="2" value="#{systemEventLoggingImpl}"/>
    </bean>
    <bean id="PerformanDataStructureReader" class="com.adva.nlms.mediation.performance.PerformanDataStructureReader">
        <constructor-arg index="0" value="#{networkElementHdlr}"/>
        <constructor-arg index="1" ref="TemplateManagerImpl"/>
        <constructor-arg index="2" ref="PerformanceManagerFinder"/>
        <constructor-arg index="3" value="#{performancePrivate.PerformanceManagerFactory}"/>
        <constructor-arg index="4" value="#{performancePrivate.PerformanceManagerHelper}"/>
        <constructor-arg index="5" value="#{performancePrivate.performanceManagerDAO}"/>
        <constructor-arg index="6" ref="networkView"/>
        <constructor-arg index="7" ref="sessionHdlr"/>
    </bean>
    <bean class="com.adva.nlms.mediation.performance.PerformanceCtrlImpl">
        <property name="configCtrl" value="#{configCtrl}"/>
        <property name="overloadProtection" ref="OverloadProtection"/>
        <property name="neHdlr" value="#{networkElementHdlr}"/>
        <property name="templateManager" ref="TemplateManagerImpl"/>
        <property name="statisticsCollector" ref="PMStatisticsCollector"/>
        <property name="managerFinder" ref="PerformanceManagerFinder"/>
        <property name="templateAssignmentManager" ref="TemplateAssignmentManager"/>
        <property name="mappingFactory" ref="MappingFactory"/>
        <property name="templateImporter" ref="TemplateImporter"/>
        <property name="pmSerieDAO" value="#{performancePrivate.PmSerieDAO}"/>
        <property name="watchdog" ref="PmWatchdog"/>
        <property name="csvExport" value="#{pmCsvExport}"/>
        <property name="templatesController" ref="TemplatesController"/>
        <property name="performanceMBean" ref="PerformanceInfo"/>
        <property name="recordReader" ref="dbRecordReader"/>
        <property name="recordReaderJson" ref="jsonRecordReader"/>
        <property name="performanDataStructureReader" ref="PerformanDataStructureReader"/>
        <property name="pmRecordManager" ref="com.adva.nlms.mediation.performance.database.PmRecordManager"/>
        <property name="pmOldRecordsRemover" ref="com.adva.nlms.mediation.performance.database.OldRecordsRemover"/>
    </bean>
    <bean class="com.adva.nlms.mediation.performance.template.PerformanceTemplateConfiguration" lazy-init="true"/>
    <bean id="esaDynamicAssociation" class="com.adva.nlms.mediation.performance.DynamicAssociationOfPm">
        <property name="performanceManagerFactory" value="#{performancePrivate.PerformanceManagerFactory}"/>
        <property name="networkElementHdlr" value="#{networkElementHdlr}"/>
        <property name="templateManager" ref="TemplateManagerImpl"/>
    </bean>

    <bean id="pmCsvNameAttributeDescriptionDecorator" class="com.adva.nlms.mediation.performance.PMCsvNameAttributeDescriptionDecorator"/>
    <bean id="performanceTemplateUsagePageHdlr" class="com.adva.nlms.mediation.performance.template.SubnetPMTemplateUsagePageHdlr">
        <constructor-arg value="#{performancePrivate.PersistentPerformanceTemplateDAO}"/>
    </bean>
</beans>