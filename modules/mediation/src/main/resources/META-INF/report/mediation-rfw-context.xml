<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:jms="http://www.springframework.org/schema/jms"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
        http://www.springframework.org/schema/context
        http://www.springframework.org/schema/context/spring-context-3.0.xsd
        http://www.springframework.org/schema/jms
	    http://www.springframework.org/schema/jms/spring-jms.xsd">

    <context:annotation-config/>

<!--    &lt;!&ndash;Spring Beans&ndash;&gt;-->
<!--    <bean class="com.adva.nlms.mediation.report.database.FileDataBase"/>-->
<!--    <bean class="com.adva.nlms.mediation.report.database.ReportDAO"/>-->
<!--    <bean class="com.adva.nlms.mediation.report.database.ReportSecurityUpdater"/>-->
    <bean class="com.adva.nlms.common.report.ReportProvider"/>
<!--    <bean class="com.adva.nlms.mediation.report.resources.ReportCtrlResource"/>-->
<!--    <bean class="com.adva.nlms.mediation.report.ReportHelper"/>-->
<!--    <bean class="com.adva.nlms.mediation.report.ReportFactory"/>-->
<!--    <bean class="com.adva.nlms.mediation.report.ReportMapperImpl"/>-->
<!--    <bean class="com.adva.nlms.mediation.report.paging.ReportPagingDataAccessHdlrImpl"/>-->
<!--    <bean class="com.adva.nlms.mediation.report.paging.ReportPageHdlrImpl">-->
<!--        <constructor-arg type="com.adva.nlms.common.paging.PageArea" value="REPORTS"/>-->
<!--    </bean>-->
<!--    <bean class="com.adva.nlms.mediation.report.decorator.ExtendedReportPropertyDecorator_150MX"/>-->
<!--    <bean class="com.adva.nlms.mediation.report.decorator.ExtendedReportPropertyDecorator_R7"/>-->
<!--    <bean class="com.adva.nlms.mediation.report.decorator.DefaultExtendedReportPropertyDecorator"/>-->
<!--    <bean class="com.adva.nlms.mediation.report.inventory.F7HierarchyReportBuilder"/>-->
    <bean class="com.adva.nlms.mediation.config.util.custom.ReportFileImplHandler"/>

<!--    <import resource="../performance/report/mediation-rfw-performance-context.xml"/>-->

<!--    &lt;!&ndash;JMS notification&ndash;&gt;-->
<!--    <bean id="reportTopologyConsumer" class="com.adva.nlms.mediation.report.jms.ReportJMSConsumer"/>-->
<!--    <bean id="jmsTopologyHandler" class="com.adva.nlms.mediation.report.jms.ReportJMSTopologyHandler"/>-->
<!--    <bean id="jmsSecurityHandler" class="com.adva.nlms.mediation.report.jms.ReportJMSSecurityHandler"/>-->

<!--    <jms:listener-container connection-factory="jmsConnectionFactory" acknowledge="auto" destination-type="topic">-->
<!--        <jms:listener destination="topic.topologyChange" ref="reportTopologyConsumer" method="onMessage"/>-->
<!--        <jms:listener destination="topic.securityUpdate" ref="reportTopologyConsumer" method="onMessage"/>-->
<!--    </jms:listener-container>-->

<!--    &lt;!&ndash; RFW pollings definition &ndash;&gt;-->
<!--    &lt;!&ndash; for domain &ndash;&gt;-->
<!--    <bean class="com.adva.nlms.mediation.report.inventory.service.ServiceInventoryReportGenerationPollingCommand">-->
<!--        <qualifier type="com.adva.nlms.mediation.polling.DomainPolling"/>-->
<!--    </bean>-->
<!--    <bean class="com.adva.nlms.mediation.report.inventory.InventoryReportGenerationPollingCommand">-->
<!--        <qualifier type="com.adva.nlms.mediation.polling.DomainPolling"/>-->
<!--    </bean>-->
<!--    <bean class="com.adva.nlms.mediation.report.inventory.F7HierarchyReportGenerationPollingCommand">-->
<!--        <qualifier type="com.adva.nlms.mediation.polling.DomainPolling"/>-->
<!--    </bean>-->
<!--    <bean class="com.adva.nlms.mediation.report.topology.TopologyReportGenerationPollingCommand">-->
<!--        <qualifier type="com.adva.nlms.mediation.polling.DomainPolling"/>-->
<!--    </bean>-->
<!--    <bean class="com.adva.nlms.mediation.report.synctopology.SyncTopologyReportGenerationPollingCommand">-->
<!--        <qualifier type="com.adva.nlms.mediation.polling.DomainPolling"/>-->
<!--    </bean>-->
<!--    <bean class="com.adva.nlms.mediation.report.remoteslaves.RemoteSlavesReportGenerationPollingCommand">-->
<!--        <qualifier type="com.adva.nlms.mediation.polling.DomainPolling"/>-->
<!--    </bean>-->
<!--    <bean class="com.adva.nlms.mediation.report.performance.spanloss.LinkLossReportGenerationPollingCommand">-->
<!--        <qualifier type="com.adva.nlms.mediation.polling.DomainPolling"/>-->
<!--    </bean>-->
<!--    &lt;!&ndash; for NE &ndash;&gt;-->
<!--    <bean class="com.adva.nlms.mediation.report.inventory.InventoryReportGenerationPollingCommand">-->
<!--        <qualifier type="com.adva.nlms.mediation.polling.NePolling"/>-->
<!--    </bean>-->
<!--    <bean class="com.adva.nlms.mediation.report.inventory.F7HierarchyReportGenerationPollingCommand">-->
<!--        <qualifier type="com.adva.nlms.mediation.polling.NePolling"/>-->
<!--    </bean>-->
<!--    <bean class="com.adva.nlms.mediation.report.syncperformance.SyncPerformanceReportGenerationPollingCommand">-->
<!--        <qualifier type="com.adva.nlms.mediation.polling.DomainPolling"/>-->
<!--    </bean>-->
<!--    <bean class="com.adva.nlms.mediation.report.topology.TopologyReportBuilder"/>-->
    <bean class="com.adva.nlms.mediation.event.archiving.AlarmArchivingCommand">
        <qualifier type="com.adva.nlms.mediation.polling.DomainPolling"/>
    </bean>
</beans>