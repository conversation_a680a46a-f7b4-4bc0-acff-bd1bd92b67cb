#
#  Copyright 2011 ADVA AG Optical Networking. All rights reserved.
#
#  Owner: tomaszm
#
#  $Id: $
#
#
# This file contains network element instance creators which will be loaded automatically by NMS.
# All of specified classes can be ommited in existing "static" definition in
# com.adva.nlms.mediation.config.NetworkElementFactoryImpl.registerNeInstanceCreators()
#
#
#
com.adva.nlms.mediation.config.fsp1XX.NetworkElementInstanceCreatorFSPGE1XXImpl
com.adva.nlms.mediation.config.fsp1XX.NetworkElementInstanceCreatorFSPGE102ProHImpl
com.adva.nlms.mediation.config.fsp150egx.nefactory.NetworkElementInstanceCreatorFSP150EGXImpl
com.adva.nlms.mediation.config.fspTxx04.nefactory.NetworkElementInstanceCreatorFSPTxx04
com.adva.nlms.mediation.config.osa5331.NetworkElementInstanceCreatorOSA5331
com.adva.nlms.mediation.config.symmetricom.nefactory.NetworkElementInstanceCreatorSymmetricomImpl
com.adva.nlms.mediation.config.fsp210.NetworkElementInstanceCreatorFSPXG210Impl
com.adva.nlms.mediation.config.fsp_xg1xx.NetworkElementInstanceCreatorFSPXG1XXImpl
