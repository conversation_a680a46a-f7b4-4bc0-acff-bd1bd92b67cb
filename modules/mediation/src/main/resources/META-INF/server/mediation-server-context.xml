<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
        http://www.springframework.org/schema/context
        http://www.springframework.org/schema/context/spring-context-3.0.xsd">
    <context:annotation-config/>

    <!--<bean id="mbeanServer" class="org.springframework.jmx.support.MBeanServerFactoryBean"/>-->
    <!--<bean id="exporter" class="org.springframework.jmx.export.MBeanExporter">-->
        <!--<property name="beans">-->
            <!--<map>-->
                <!--<entry key="bean:name=jmsMonitor1" value-ref="jmsMonitor"/>-->
                <!--<entry key="bean:name=server.JmsMonitorOverview" value-ref="jmsMonitorOverview"/>-->
                <!--<entry key="bean:name=server.ServerMonitorValues" value-ref="serverMonitorValues"/>-->
            <!--</map>-->
        <!--</property>-->
        <!--<property name="server" ref="mbeanServer"/>-->
    <!--</bean>-->
    <bean class="com.adva.nlms.mediation.server.ServerInterfaceInfo"/>
    <bean id="jmsMonitorOverview" class="com.adva.nlms.mediation.server.monitoring.JmsMonitorOverview"/>
    <bean id="jmsMonitor" class="com.adva.nlms.mediation.server.monitoring.JmsMonitor"/>
    <bean class="com.adva.nlms.mediation.server.ServerMonitor" />
    <bean class="com.adva.nlms.mediation.server.ServerMonitorOverview"/>
    <bean class="com.adva.nlms.mediation.server.ServerMonitorDetails"/>
    <bean id="serverMonitorValues" class="com.adva.nlms.mediation.server.ServerMonitorValues"/>
    <bean class="com.adva.nlms.mediation.monitoring.broker.BrokerStatistics" init-method="init"/>

    <!-- pollings  -->
    <bean class="com.adva.nlms.mediation.server.backup.DBBackupPollingCommand">
        <qualifier type="com.adva.nlms.mediation.polling.DomainPolling"/>
    </bean>

    <bean class="com.adva.nlms.mediation.server.FormModelProviderRestResource"/>

    <bean class="com.adva.nlms.mediation.server.backup.DBBackupEventObserver" id="DBBackupEventObserver"/>
</beans>