<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:jms="http://www.springframework.org/schema/jms"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
        http://www.springframework.org/schema/context
        http://www.springframework.org/schema/context/spring-context-3.0.xsd
        http://www.springframework.org/schema/jms
	    http://www.springframework.org/schema/jms/spring-jms.xsd">

    <context:annotation-config/>

    <!--Property reader-->
    <bean class="org.springframework.beans.factory.config.PropertyPlaceholderConfigurer">
        <property name="locations">
            <list>
                <value>file:fnm.properties</value>
                <value>file:activemq/conf/jms.properties</value>
            </list>
        </property>
        <property name="ignoreUnresolvablePlaceholders" value="true"/>
    </bean>

    <!-- Each NMS area has its dedicated consumer. Each parcticular consumer will read messages from dedicated topic-->
    <bean id="internalNotificationJMSConsumerSM"
          class="com.adva.nlms.inf.impl.InternalNotificationJMSConsumer">
        <constructor-arg
                value="#{ T(com.adva.nlms.mediation.monitoring.metrics.GlobalMetricRegistry).getGlobalMetricRegistry()}"/>
        <constructor-arg ref="bestEfforConsumpition"/>
    </bean>
    <bean id="internalNotificationJMSConsumerMO"
          class="com.adva.nlms.inf.impl.InternalNotificationJMSConsumer">
        <constructor-arg
                value="#{ T(com.adva.nlms.mediation.monitoring.metrics.GlobalMetricRegistry).getGlobalMetricRegistry()}"/>
        <constructor-arg ref="bestEfforConsumpition"/>
    </bean>
    <bean id="internalNotificationJMSConsumerMTOSI"
          class="com.adva.nlms.inf.impl.InternalNotificationJMSConsumer">
        <constructor-arg
                value="#{ T(com.adva.nlms.mediation.monitoring.metrics.GlobalMetricRegistry).getGlobalMetricRegistry()}"/>
        <constructor-arg ref="bestEfforConsumpition"/>
    </bean>
    <bean id="internalNotificationJMSConsumerPM"
          class="com.adva.nlms.inf.impl.InternalNotificationJMSConsumer">
        <constructor-arg
                value="#{ T(com.adva.nlms.mediation.monitoring.metrics.GlobalMetricRegistry).getGlobalMetricRegistry()}"/>
        <constructor-arg ref="bestEfforConsumpition"/>
    </bean>

    <jms:listener-container connection-factory="infJmsConsumptionPooledConnectionFactory" acknowledge="auto"
                            concurrency="5"
                            destination-type="queue" task-executor="commonINFMessagesExecutor">

        <jms:listener
                destination="#{T(com.adva.nlms.inf.api.DestinationArea).MTOSI.getDestinationName()}"
                ref="internalNotificationJMSConsumerMTOSI"/>
        <jms:listener
                destination="#{T(com.adva.nlms.inf.api.DestinationArea).MO.getDestinationName()}"
                ref="internalNotificationJMSConsumerMO"/>
        <jms:listener
                destination="#{T(com.adva.nlms.inf.api.DestinationArea).PM.getDestinationName()}"
                ref="internalNotificationJMSConsumerPM"/>
    </jms:listener-container>

    <bean id="commonOrderedInternalNotificationJMSConsumer"
          class="com.adva.nlms.inf.impl.InternalNotificationJMSConsumer">
        <constructor-arg
                value="#{ T(com.adva.nlms.mediation.monitoring.metrics.GlobalMetricRegistry).getGlobalMetricRegistry()}"/>
    </bean>

    <jms:listener-container connection-factory="infJmsConsumptionPooledConnectionFactory" acknowledge="auto" concurrency="1"
                            destination-type="queue" task-executor="singleThreadedCommonINFMessagesExecutor">

        <jms:listener
                destination="#{T(com.adva.nlms.inf.api.DestinationArea).OTHER.getDestinationName()}"
                ref="commonOrderedInternalNotificationJMSConsumer"/>

    </jms:listener-container>

    <jms:listener-container connection-factory="infJmsConsumptionPooledConnectionFactory" acknowledge="auto"
                            concurrency="5"
                            destination-type="queue" task-executor="smINFMessagesExecutor">
        <jms:listener
                destination="#{T(com.adva.nlms.inf.api.DestinationArea).SM.getDestinationName()}"
                ref="internalNotificationJMSConsumerSM"/>
    </jms:listener-container>
</beans>
