<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
        http://www.springframework.org/schema/context
        http://www.springframework.org/schema/context/spring-context-3.0.xsd">

    <context:annotation-config/>

    <!-- com.adva.nlms.mediation -->

    <bean class="org.springframework.beans.factory.config.PropertyPlaceholderConfigurer">
        <property name="locations">
            <list>
                <value>file:fnm.properties</value>
                <value>file:activemq/conf/jms.properties</value>
            </list>
        </property>
        <property name="ignoreUnresolvablePlaceholders" value="true" />
    </bean>

    <bean id="jmsConnectionFactory" class="com.adva.nlms.mediation.security.activemq.EncConnectionFactoryImpl">
        <property name="brokerURL" value="${jms.transportProtocol}://${jms.url}:${jms.port}?wireFormat.maxInactivityDuration=60000&amp;wireFormat.maxInactivityDurationInitalDelay=60000${jms.additional.args}"/>
    </bean>

    <bean id="infConnectionFactory" class="org.apache.activemq.pool.PooledConnectionFactory" destroy-method="stop">
        <property name="maxConnections" value="${infpool.maxConnections}"/>
        <property name="maximumActiveSessionPerConnection" value="${infpool.maximumActive}"/>
        <property name="connectionFactory" ref="jmsConnectionFactory"/>
        <property name="idleTimeout" value="${infpool.idleTimeout}"/>
    </bean>

    <bean id="infJmsTemplate" class="org.springframework.jms.core.JmsTemplate">
        <qualifier value = "persisted" />
        <property name="connectionFactory" ref="infConnectionFactory"/>
        <property name="explicitQosEnabled" value="true"/>
        <property name="deliveryMode" value="2"/>
        <property name="timeToLive" value="172800000"/>
    </bean>

    <bean id="nonPersistedInfJmsTemplate" class="org.springframework.jms.core.JmsTemplate" parent="infJmsTemplate">
        <qualifier value = "nonPersisted" />
        <property name="deliveryMode" value="1"/>
    </bean>

</beans>