<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
        http://www.springframework.org/schema/context
        http://www.springframework.org/schema/context/spring-context-3.0.xsd">

    <context:annotation-config/>

    <!-- no JMS solution configuration -->
    <!--<bean id="incomingNotificationQueue"-->
    <!--depends-on="fnmproperties"-->
    <!--class="${monotifications.incomingNotificationQueueClass?java.util.concurrent.LinkedBlockingQueue}">-->
    <!--<constructor-arg type="int" value="${monotifications.incomingNotificationQueueCapacity:10000}"/>-->
    <!--</bean>-->
    <!--<bean class="com.adva.nlms.mediation.messaging.inf.impl.QueueBasedIncomingEventProcessor">-->
    <!--<constructor-arg type="org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor">-->
    <!--<bean id="incomingEventExecutorService"-->
    <!--class="org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor">-->

    <!--<property name="maxPoolSize" value="1"/>-->
    <!--<property name="corePoolSize" value="1"/>-->
    <!--<property name="keepAliveSeconds" value="0"/>-->
    <!--<property name="threadNamePrefix" value="IEP-"/>-->
    <!--</bean>-->
    <!--</constructor-arg>-->
    <!--</bean>-->
    <!--<bean class="com.adva.nlms.mediation.messaging.inf.impl.controllers.PollingManagerBasedActionRunner"/>-->
    <!-- END no JMS solution configuration -->

    <!-- JMS solution -->

    <!--<constructor-arg index="0" ref="infJmsTemplate"/>-->
    <!--<constructor-arg index="1" value="#{ T(com.adva.nlms.mediation.monitoring.metrics.GlobalMetricRegistry).getGlobalMetricRegistry()}"/>-->
    <!--</bean>-->
    <!-- END JMS solution -->

    <!--TODO extract when *Notification will be in separate modules-->
    <bean id="registeredObserversContainer" class="com.adva.nlms.inf.impl.RegisteredObserversContainer">
        <constructor-arg>
            <list value-type="java.lang.Class">
                <value>com.adva.nlms.inf.mo.api.notification.MONotification</value>
                <value>com.adva.nlms.inf.mo.api.notification.MOCreationNotification</value>
                <value>com.adva.nlms.inf.mo.api.notification.MODeletionNotification</value>
                <value>com.adva.nlms.inf.mo.api.notification.MOUpdateNotification</value>
                <value>com.adva.sm.tag.api.notification.TagNotification</value>
                <value>com.adva.sm.tag.api.notification.TagCreationNotification</value>
                <value>com.adva.sm.tag.api.notification.TagDeletionNotification</value>
                <value>com.adva.sm.tag.api.notification.TagUpdateNotification</value>
                <value>com.adva.nlms.mediation.messaging.inf.NENotification</value>
                <value>com.adva.nlms.mediation.messaging.inf.NEUpdateNotification</value>
                <value>com.adva.nlms.mediation.messaging.inf.NECreationNotification</value>
                <value>com.adva.nlms.mediation.messaging.inf.NEDeletionNotification</value>
                <value>com.adva.nlms.mediation.messaging.inf.TopologyCreationNotification</value>
                <value>com.adva.nlms.mediation.messaging.inf.TopologyDeletionNotification</value>
                <value>com.adva.nlms.mediation.messaging.inf.TopologyUpdateNotification</value>
                <value>com.adva.nlms.mediation.messaging.inf.TopologyNotification</value>
                <value>com.adva.nlms.mediation.messaging.inf.MLTopoNotification</value>
                <value>com.adva.nlms.mediation.messaging.inf.MLTopoCreationNotification</value>
                <value>com.adva.nlms.mediation.messaging.inf.MLTopoDeletionNotification</value>
                <value>com.adva.nlms.mediation.messaging.inf.MLTopoUpdateNotification</value>
                <value>com.adva.nlms.mediation.messaging.inf.NrimLogicalLinkNotification</value>
                <value>com.adva.nlms.mediation.messaging.inf.NrimLogicalLinkCreationNotification</value>
                <value>com.adva.nlms.mediation.messaging.inf.NrimLogicalLinkUpdateNotification</value>
                <value>com.adva.nlms.mediation.messaging.inf.NrimLogicalLinkDeletionNotification</value>
                <value>com.adva.nlms.mediation.messaging.inf.PDTopoNotification</value>
                <value>com.adva.nlms.mediation.messaging.inf.PDTopoCreationNotification</value>
                <value>com.adva.nlms.mediation.messaging.inf.PDTopoDeletionNotification</value>
                <value>com.adva.nlms.mediation.messaging.inf.PDTopoUpdateNotification</value>
                <value>com.adva.nlms.mediation.messaging.inf.PDServiceIntentNotification</value>
                <value>com.adva.nlms.mediation.messaging.inf.PDServiceIntentCreationNotification</value>
                <value>com.adva.nlms.mediation.messaging.inf.PDServiceIntentDeletionNotification</value>
                <value>com.adva.nlms.mediation.messaging.inf.PDServiceIntentUpdateNotification</value>
                <value>com.adva.nlms.mediation.messaging.inf.SubnetNotification</value>
                <value>com.adva.nlms.mediation.messaging.inf.SubnetCreationNotification</value>
                <value>com.adva.nlms.mediation.messaging.inf.SubnetDeletionNotification</value>
                <value>com.adva.nlms.mediation.messaging.inf.SubnetUpdateNotification</value>
                <value>com.adva.nlms.mediation.messaging.inf.SNMPPropertiesUpdateNotification</value>
                <value>com.adva.nlms.mediation.messaging.inf.SNMPPropertiesDeletionNotification</value>
                <value>com.adva.nlms.mediation.messaging.inf.SNMPPropertiesCreationNotification</value>
                <value>com.adva.nlms.mediation.messaging.inf.HTTPPropertiesDeletionNotification</value>
                <value>com.adva.nlms.mediation.messaging.inf.HTTPPropertiesUpdateNotification</value>
                <value>com.adva.nlms.mediation.messaging.inf.HTTPPropertiesCreationNotification</value>
            </list>
        </constructor-arg>
    </bean>

    <bean id="mlTopoCreationNotificationHandler"
          class="com.adva.nlms.mediation.messaging.inf.impl.MLTopoCreationNotificationHandler"
          lazy-init="true"/>
    <bean id="mlTopoDeletionNotificationHandler"
          class="com.adva.nlms.mediation.messaging.inf.impl.MLTopoDeletionNotificationHandler" lazy-init="true"/>
    <bean id="mlTopoUpdateNotificationHandler"
          class="com.adva.nlms.mediation.messaging.inf.impl.MLTopoUpdateNotificationHandler" lazy-init="true"/>

    <bean id="nrimLogicalLinkCreationNotificationHandler"
          class="com.adva.nlms.mediation.messaging.inf.impl.NrimLogicalLinkCreationNotificationHandler" lazy-init="true"/>
    <bean id="nrimLogicalLinkDeletionNotificationHandler"
          class="com.adva.nlms.mediation.messaging.inf.impl.NrimLogicalLinkDeletionNotificationHandler" lazy-init="true"/>
    <bean id="nrimLogicalLinkUpdateNotificationHandler"
          class="com.adva.nlms.mediation.messaging.inf.impl.NrimLogicalLinkUpdateNotificationHandler" lazy-init="true"/>

    <bean id="pdTopoCreationNotificationHandler"
          class="com.adva.nlms.mediation.messaging.inf.impl.PDTopoCreationNotificationHandler"
          lazy-init="true"/>
    <bean id="pdTopoDeletionNotificationHandler"
          class="com.adva.nlms.mediation.messaging.inf.impl.PDTopoDeletionNotificationHandler" lazy-init="true"/>
    <bean id="pdTopoUpdateNotificationHandler"
          class="com.adva.nlms.mediation.messaging.inf.impl.PDTopoUpdateNotificationHandler" lazy-init="true"/>

    <bean id="neUpdateNotificationHandler"
          class="com.adva.nlms.mediation.messaging.inf.impl.NEUpdateNotificationHandler" lazy-init="true"/>
    <bean id="neCreationNotificationHandler"
          class="com.adva.nlms.mediation.messaging.inf.impl.NECreationNotificationHandler" lazy-init="true"/>
    <bean id="neDeletionNotificationHandler"
          class="com.adva.nlms.mediation.messaging.inf.impl.NEDeletionNotificationHandler" lazy-init="true"/>

    <bean id="snmpPropertiesUpdateNotificationHandler"
          class="com.adva.nlms.mediation.messaging.inf.impl.SNMPPropertiesUpdateNotificationHandler" lazy-init="true">
    </bean>
    <bean id="snmpPropertiesCreationNotificationHandler"
          class="com.adva.nlms.mediation.messaging.inf.impl.SNMPPropertiesCreationNotificationHandler"
          lazy-init="true"/>
    <bean id="snmpPropertiesDeletionNotificationHandler"
          class="com.adva.nlms.mediation.messaging.inf.impl.SNMPPropertiesDeletionNotificationHandler"
          lazy-init="true"/>

    <bean id="httpPropertiesUpdateNotificationHandler"
          class="com.adva.nlms.mediation.messaging.inf.impl.HTTPPropertiesUpdateNotificationHandler" lazy-init="true"/>
    <bean id="httpPropertiesCreationNotificationHandler"
          class="com.adva.nlms.mediation.messaging.inf.impl.HTTPPropertiesCreationNotificationHandler"
          lazy-init="true"/>
    <bean id="httpPropertiesDeletionNotificationHandler"
          class="com.adva.nlms.mediation.messaging.inf.impl.HTTPPropertiesDeletionNotificationHandler"
          lazy-init="true"/>

    <bean id="subnetCreationNotificationHandler"
          class="com.adva.nlms.mediation.messaging.inf.impl.SubnetCreationNotificationHandler" lazy-init="true"/>
    <bean id="subnetDeletionNotificationHandler"
          class="com.adva.nlms.mediation.messaging.inf.impl.SubnetDeletionNotificationHandler" lazy-init="true"/>
    <bean id="subnetUpdateNotificationHandler"
          class="com.adva.nlms.mediation.messaging.inf.impl.SubnetUpdateNotificationHandler" lazy-init="true"/>

    <bean id="topologyCreationNotificationHandler"
          class="com.adva.nlms.mediation.messaging.inf.impl.TopologyCreationNotificationHandler" lazy-init="true"/>
    <bean id="topologyDeletionNotificationHandler"
          class="com.adva.nlms.mediation.messaging.inf.impl.TopologyDeletionNotificationHandler" lazy-init="true"/>
    <bean id="topologyUpdateNotificationHandler"
          class="com.adva.nlms.mediation.messaging.inf.impl.TopologyUpdateNotificationHandler" lazy-init="true"/>

    <bean id="pdServiceIntentCreationNotificationHandler"
          class="com.adva.nlms.mediation.messaging.inf.impl.PDServiceIntentCreationNotificationHandler" lazy-init="true"/>
    <bean id="pdServiceIntentDeletionNotificationHandler"
          class="com.adva.nlms.mediation.messaging.inf.impl.PDServiceIntentDeletionNotificationHandler" lazy-init="true"/>
    <bean id="pdServiceIntentUpdateNotificationHandler"
          class="com.adva.nlms.mediation.messaging.inf.impl.PDServiceIntentUpdateNotificationHandler" lazy-init="true"/>
    <bean id="tagCreationNotificationHandler"
          class="com.adva.sm.tag.api.notification.TagCreationNotificationHandler" lazy-init="true"/>
    <bean id="tagDeletionNotificationHandler"
          class="com.adva.sm.tag.api.notification.TagDeletionNotificationHandler" lazy-init="true"/>
    <bean id="tagUpdateNotificationHandler"
          class="com.adva.sm.tag.api.notification.TagUpdateNotificationHandler" lazy-init="true"/>

    <!-- JMX initialization -->
    <bean id="exporter" class="org.springframework.jmx.export.MBeanExporter" lazy-init="false">
        <property name="beans">
            <map>
                <entry key="com.adva.nlms.mediation:type=MONotificationManager"
                       value-ref="internalNotificationManager"/>
            </map>
        </property>
    </bean>
    <!-- END JMX initialization -->

</beans>
