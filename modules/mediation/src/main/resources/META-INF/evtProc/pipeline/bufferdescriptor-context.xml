<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
        http://www.springframework.org/schema/context
        http://www.springframework.org/schema/context/spring-context-3.0.xsd">
    <context:annotation-config/>

    <!--
    <bean id="eventProcessingBufferDescriptor" abstract="true"  class="com.adva.nlms.mediation.evtProc.pipeline.EventProcessingBufferDescriptor"/>
    -->

    <!-- No Spring Bean: DecorationBufferDescriptor
    <bean id="decorationThreadExecutor" class="com.adva.nlms.mediation.common.concurrent.AdvaExecutors" scope="prototype" factory-method="newFixedThreadPool">
        <constructor-arg name="nThreads" value="1"/>
        <constructor-arg name="threadFactory" ref="decorationThreadFactory"/>
        <constructor-arg name="registerInManager" value="true"/>
    </bean>

    <bean name="decorationThreadFactory" class="com.adva.nlms.mediation.common.concurrent.NamedThreadFactory" scope="prototype">
        <constructor-arg name="customPrefix" value="#{T(com.adva.nlms.mediation.evtProc.bufferdescriptor.EventProcessingThreadNames).EventDecorator.name()}"/>
    </bean>

    <bean name="decorationQueue" class="java.util.concurrent.ArrayBlockingQueue">
        <constructor-arg index="0" type="int" value="#{T(com.adva.nlms.common.property.FNMPropertyFactory).getPropertyAsInt(T(com.adva.nlms.common.property.FNMPropertyFactory).MAX_EVENT_QUEUE_SIZE, 500)}"/>
    </bean>

    <bean id="decorationExecutorService" class="com.adva.nlms.mediation.common.concurrent.AdvaExecutors" scope="prototype" factory-method="newFixedThreadPool">
        <constructor-arg name="nThreads" value="5"/>
        <constructor-arg name="threadFactory" ref="decorationThreadFactory"/>
        <constructor-arg name="registerInManager" value="true"/>
    </bean>

    <bean id="decorationBufferDescriptor"  class="com.adva.nlms.mediation.evtProc.bufferdescriptor.DecorationBufferDescriptor">
        <!++Override the value of the abstract based class if necessary++>
        <property name="allowedThreadNumber" value="5"/>
        <property name="buffer" ref="decorationQueue"/>
        <property name="bufferName" value="#{T(com.adva.nlms.mediation.evtProc.bufferdescriptor.EventProcessingQueueNames).DECORATOR.name()}"/>
        <property name="allowedProcessedTaskNumber" value="5"/>
        <property name="threadPoolExecutor" ref="decorationExecutorService"/>
        <property name="idleTime" value="1"/>
    </bean>
    -->

    <!-- No Spring Bean: MOEventProcessingBufferDescriptor
    <bean name="moEventProcessingThreadFactory" class="com.adva.nlms.mediation.common.concurrent.NamedThreadFactory" scope="prototype">
        <constructor-arg name="customPrefix" value="#{T(com.adva.nlms.mediation.evtProc.bufferdescriptor.EventProcessingThreadNames).EventMoSync.name()}"/>
    </bean>

    <bean id="moEventProcessingThreadExecutor" class="com.adva.nlms.mediation.common.concurrent.AdvaExecutors" scope="prototype" factory-method="newFixedThreadPool">
        <constructor-arg name="nThreads" value="1"/>
        <constructor-arg name="threadFactory" ref="moEventProcessingThreadFactory"/>
        <constructor-arg name="registerInManager" value="true"/>
    </bean>

    <bean name="moEventQueue" class="java.util.concurrent.LinkedBlockingQueue"/>

    <bean id="moBufferDescriptor"  class="com.adva.nlms.mediation.evtProc.bufferdescriptor.MOEventProcessingBufferDescriptor">
        <!++Override the value of the abstract based class if necessary++>
        <property name="allowedThreadNumber" value="1"/>
        <property name="buffer" ref="moEventQueue"/>
        <property name="bufferName" value="#{T(com.adva.nlms.mediation.evtProc.bufferdescriptor.EventProcessingQueueNames).MO_SYNC.name()}"/>
        <property name="allowedProcessedTaskNumber" value="5"/>
        <property name="threadPoolExecutor" ref="moEventProcessingThreadExecutor"/>
        <property name="idleTime" value="1"/>
    </bean>
    -->



    <!-- No Spring Bean: DBConsumerBufferDescriptor
    <bean name="eventForDBConsumerThreadFactory" class="com.adva.nlms.mediation.common.concurrent.NamedThreadFactory" scope="prototype">
        <constructor-arg name="customPrefix" value="#{T(com.adva.nlms.mediation.evtProc.bufferdescriptor.EventProcessingThreadNames).DBConsumer.name()}"/>
    </bean>

    <bean id="eventForDBConsumerThreadExecutor" class="com.adva.nlms.mediation.common.concurrent.AdvaExecutors" scope="prototype" factory-method="newFixedThreadPool">
        <constructor-arg name="nThreads" value="1"/>
        <constructor-arg name="threadFactory" ref="eventForDBConsumerThreadFactory"/>
        <constructor-arg name="registerInManager" value="true"/>
    </bean>

    <bean name="eventForDBConsumerQueue" class="java.util.concurrent.ArrayBlockingQueue">
        <constructor-arg index="0" type="int" value="#{T(com.adva.nlms.common.property.FNMPropertyFactory).getPropertyAsInt(T(com.adva.nlms.common.property.FNMPropertyFactory).MAX_EVENT_QUEUE_SIZE_DB, 200* T(com.adva.nlms.common.property.FNMPropertyFactory).getPropertyAsInt(T(com.adva.nlms.common.property.FNMPropertyFactory).MAX_EVENT_QUEUE_SIZE,500))}"/>
    </bean>


    <bean id="dbBufferDescriptor"  class="com.adva.nlms.mediation.evtProc.bufferdescriptor.DBConsumerBufferDescriptor">
        <!++Override the value of the abstract based class if necessary++>
        <property name="allowedThreadNumber" value="1"/>
        <property name="buffer" ref="eventForDBConsumerQueue"/>
        <property name="bufferName" value="#{T(com.adva.nlms.mediation.evtProc.bufferdescriptor.EventProcessingQueueNames).EVENT_FOR_DB.name()}"/>
        <property name="allowedProcessedTaskNumber" value="#{T(com.adva.nlms.common.property.FNMPropertyFactory).getPropertyAsInt(T(com.adva.nlms.common.property.FNMPropertyFactory).MAX_EVENT_TRA_SIZE_DB, 50)}"/>
        <property name="threadPoolExecutor" ref="eventForDBConsumerThreadExecutor"/>
        <property name="idleTime" value="60"/>
    </bean>
    -->

    <!-- No Spring Bean: EventDistributerBufferDescriptor
    <bean name="eventDistributorThreadFactory" class="com.adva.nlms.mediation.common.concurrent.NamedThreadFactory" scope="prototype">
        <constructor-arg name="customPrefix" value="#{T(com.adva.nlms.mediation.evtProc.bufferdescriptor.EventProcessingThreadNames).EventDistributor.name()}"/>
    </bean>

    <bean id="eventDistributorThreadExecutor" class="com.adva.nlms.mediation.common.concurrent.AdvaExecutors" scope="prototype" factory-method="newFixedThreadPool">
        <constructor-arg name="nThreads" value="1"/>
        <constructor-arg name="threadFactory" ref="eventDistributorThreadFactory"/>
        <constructor-arg name="registerInManager" value="true"/>
    </bean>

    <bean name="eventDistributorQueue" class="java.util.concurrent.ArrayBlockingQueue">
        <constructor-arg index="0" type="int" value="#{T(com.adva.nlms.common.property.FNMPropertyFactory).getPropertyAsInt(T(com.adva.nlms.common.property.FNMPropertyFactory).MAX_EVENT_QUEUE_SIZE, 500)}"/>
    </bean>

    <bean id="distributorBufferDescriptor"  class="com.adva.nlms.mediation.evtProc.bufferdescriptor.EventDistributorBufferDescriptor">
        <!++Override the value of the abstract based class if necessary++>
        <property name="allowedThreadNumber" value="1"/>
        <property name="buffer" ref="eventDistributorQueue"/>
        <property name="bufferName" value="#{T(com.adva.nlms.mediation.evtProc.bufferdescriptor.EventProcessingQueueNames).EVENT_DISTRIBUTOR.name()}"/>
        <property name="allowedProcessedTaskNumber" value="59"/>
        <property name="threadPoolExecutor" ref="eventDistributorThreadExecutor"/>
        <property name="idleTime" value="60"/>
    </bean>
    -->

</beans>