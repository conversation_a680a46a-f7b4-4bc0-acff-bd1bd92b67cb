<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~  Copyright 2023 Adtran Networks SE. All rights reserved.
  ~
  ~  Owner: rafalr
  -->

<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
       http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd">

    <context:annotation-config/>

    <bean id="snmpProfileNetworkElementDeleteObserver" class="com.adva.nlms.mediation.profile.snmp.SnmpProfileNetworkElementDeleteObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
        <constructor-arg ref="assignedSnmpProfileService"/>
    </bean>

    <bean class="com.adva.nlms.mediation.profile.snmp.SnmpProfileRelatedChangeHdlr"/>
    <bean id="assignedSmnpProfileStateListener" class="com.adva.nlms.mediation.profile.snmp.AssignedSmnpProfileStateListener"/>
    <bean id="assignedSnmpProfileHdlr" class="com.adva.nlms.mediation.profile.snmp.AssignedSnmpProfileHdlr"/>
    <bean id="snmpProfileEventsThreadPool" class="com.adva.nlms.mediation.profile.snmp.SnmpProfileEventsThreadPool"/>

</beans>