<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
        http://www.springframework.org/schema/context
        http://www.springframework.org/schema/context/spring-context-3.0.xsd">

    <context:annotation-config/>

    <import resource="messaging/inf/mediation-messaging-inf-consumers-context.xml"/>
    <import resource="messaging/inf/mediation-messaging-inf-publishers-context.xml"/>

    <import resource="topics-context.xml"/>

    <!--Property reader-->
    <bean class="org.springframework.beans.factory.config.PropertyPlaceholderConfigurer">
        <property name="locations">
            <list>
                <value>file:fnm.properties</value>
                <value>file:activemq/conf/jms.properties</value>
            </list>
        </property>
        <property name="ignoreUnresolvablePlaceholders" value="true" />
    </bean>

    <!-- spring -jms communication setup -->
    <bean id="jmsConnectionFactory" class="com.adva.nlms.mediation.security.activemq.EncConnectionFactoryImpl">
        <property name="brokerURL" value="${jms.transportProtocol}://${jms.url}:${jms.port}?wireFormat.maxInactivityDuration=60000&amp;wireFormat.maxInactivityDurationInitalDelay=60000&amp;jms.useAsyncSend=true${jms.additional.args}"/>
        <property name="useCompression" value="${jms.useCompression}"/>
    </bean>

    <!-- spring -jms communication setup -->
    <bean id="jmsConnectionFactoryNoPrefetch" class="com.adva.nlms.mediation.security.activemq.EncConnectionFactoryImpl">
        <property name="brokerURL" value="${jms.transportProtocol}://${jms.url}:${jms.port}?wireFormat.maxInactivityDuration=60000&amp;wireFormat.maxInactivityDurationInitalDelay=60000&amp;jms.useAsyncSend=true${jms.additional.args}"/>
        <property name="useCompression" value="false"/>
        <property name="prefetchPolicy" ref="prefetchPolicy"/>
    </bean>

    <bean id="prefetchPolicy" class="org.apache.activemq.ActiveMQPrefetchPolicy">
        <property name="queuePrefetch" value="${infpool.prefetchSize}" />
    </bean>

    <bean id="jmsInternalConnectionFactory" class="com.adva.nlms.mediation.security.activemq.EncConnectionFactoryImpl">
        <property name="brokerURL" value="${jms.transportProtocol}://${jms.url}:${jms.port}?wireFormat.maxInactivityDuration=60000&amp;wireFormat.maxInactivityDurationInitalDelay=60000${jms.additional.args}"/>
        <property name="redeliveryPolicy" ref="redeliveryPolicy"/>
    </bean>

    <bean id="pooledJmsConnectionFactory" class="org.apache.activemq.pool.PooledConnectionFactory" destroy-method="stop">
        <property name="maxConnections" value="${pool.maxConnections}"/>
        <property name="maximumActiveSessionPerConnection" value="${pool.maximumActive}"/>
        <property name="connectionFactory" ref="jmsConnectionFactory"/>
        <property name="idleTimeout" value="${pool.idleTimeout}"/>
    </bean>

    <bean id="internalMessageConnectionFactory" class="org.apache.activemq.pool.PooledConnectionFactory" destroy-method="stop">
        <property name="maxConnections" value="${infpool.maxConnections}"/>
        <property name="maximumActiveSessionPerConnection" value="${infpool.maximumActive}"/>
        <property name="connectionFactory" ref="jmsInternalConnectionFactory"/>
        <property name="idleTimeout" value="${infpool.idleTimeout}"/>
    </bean>

    <bean id="jmsTemplate" class="org.springframework.jms.core.JmsTemplate">
        <property name="connectionFactory" ref="pooledJmsConnectionFactory"/>
        <property name="explicitQosEnabled" value="true"/>
        <property name="deliveryMode" value="1"/>
    </bean>

    <bean id="internalMessageJmsTemplate" class="org.springframework.jms.core.JmsTemplate">
        <qualifier value = "internalMessagePersisted" />
        <property name="connectionFactory" ref="internalMessageConnectionFactory"/>
        <property name="explicitQosEnabled" value="true"/>
        <property name="deliveryMode" value="2"/>
        <property name="timeToLive" value="172800000"/>
    </bean>

    <bean id="nonPersistedInternalMessageJmsTemplate" class="org.springframework.jms.core.JmsTemplate" parent="internalMessageJmsTemplate">
        <qualifier value = "internalMessageNonPersisted" />
        <property name="deliveryMode" value="1"/>
    </bean>

    <bean id="redeliveryPolicy" class="org.apache.activemq.RedeliveryPolicy">
        <property name="backOffMultiplier" value="3"/>
        <property name="initialRedeliveryDelay" value="1000"/>
        <property name="maximumRedeliveries" value="3"/>
        <property name="queue" value="*"/>
        <property name="redeliveryDelay" value="1000"/>
        <property name="useExponentialBackOff" value="true"/>
    </bean>

    <bean id="fnmPropertyFactory" class="com.adva.nlms.common.property.FNMPropertyFactory" factory-method="getInstance"/>
    <!-- Access point for server side communications -->
    <bean id="messagingService" class="com.adva.nlms.mediation.messaging.MessagingServices"/>

    <bean id="messageSender" class="com.adva.nlms.mediation.messaging.MessageSender">
        <property name="destinations">
            <map key-type="com.adva.nlms.common.messages.MessageType" value-type="org.apache.activemq.command.ActiveMQTopic">
                <entry key="EventUpdate" value-ref="eventUpdate"/>
                <entry key="ObjectEventState" value-ref="objectEventState"/>
                <entry key="ShutdownType" value-ref="general"/>
                <entry key="TopologyChange" value-ref="topologyChange"/>
                <entry key="GeneralChange" value-ref="generalChange"/>
                <entry key="SyncTopologyChange"  value-ref="syncTopologyChange"/>
                <entry key="ServerActivity" value-ref="serverActivity"/>
                <entry key="ProgressStatus" value-ref="progressStatus"/>
                <entry key="PmTemplateAssignmentStatus" value-ref="pmTemplateAssignmentStatus"/>
                <entry key="MessageProperties" value-ref="messageProperties"/>
                <entry key="UpdateStatus" value-ref="updateStatus"/>
                <entry key="ReportUpdate" value-ref="reportUpdate"/>
                <entry key="SecurityUpdate" value-ref="securityUpdate"/>
                <entry key="CryptoUpdate" value-ref="cryptoUpdate"/>
                <entry key="PollingPropertiesChange" value-ref="pollingPropertiesChange"/>
                <entry key="HaApplyConfigurationStatus" value-ref="haApplyConfigurationStatus"/>
                <entry key="PCAStatus" value-ref="pcaStatus"/>
                <entry key="CUAStatus" value-ref="cuaStatus"/>
                <entry key="TCAMonitorEvent" value-ref="general"/>
                <entry key="BulkTransferStatusPerNE" value-ref="general"/>
                <entry key="EthernetConfigFileRequestStatus" value-ref="general"/>
                <entry key="RetrieveConfigFileStatus" value-ref="general"/>
                <entry key="RapidMonitoringStatus" value-ref="general"/>
                <entry key="SecurityApprovalStatus" value-ref="approvalStatus"/>
                <entry key="PrivilegeChange" value-ref="approvalStatus"/>
                <entry key="ESAUpdate" value-ref="general"/>
                <entry key="TemplateUpdate" value-ref="general"/>
                <entry key="UserNotification" value-ref="userNotification"/>
                <entry key="BackupInfoUpdate" value-ref="general"/>
                <entry key="PollingDelayStatusUpdate" value-ref="general"/>
                <entry key="PowerLevelUpdate" value-ref="general"/>
                <entry key="LicensingServersStatusesDto" value-ref="general"/>
                <entry key="BrokerAlive" value-ref="general"/>
                <entry key="AlmSyncInfo" value-ref="general"/>
                <entry key="MultiServerSync" value-ref="general"/>
            </map>
        </property>
    </bean>

</beans>
