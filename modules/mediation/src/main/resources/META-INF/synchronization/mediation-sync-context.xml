<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
        http://www.springframework.org/schema/context
        http://www.springframework.org/schema/context/spring-context-3.0.xsd">
    <context:annotation-config/>
    <bean id="rcaWorker" class="com.adva.nlms.mediation.synchronization.gnss.rca.RcaWorker"/>
    <bean id="AlarmsFetcher" class="com.adva.nlms.mediation.synchronization.gnss.rca.handlers.AlarmsFetcherImpl"/>
    <bean id="neIpsFetcher" class="com.adva.nlms.mediation.synchronization.gnss.rca.handlers.NeIpsFetcherImpl"/>
    <bean id="publisher" class="com.adva.nlms.mediation.synchronization.gnss.rca.handlers.GnssRcaServiceImpl"/>
    <bean id="mdMessageSender" class="com.adva.nlms.mediation.server.MDMessageSender"/>
    <bean id="rcaCtrl" class="com.adva.nlms.mediation.synchronization.gnss.rca.RcaCtrl"/>
    <bean id="syncCtrlImpl" class="com.adva.nlms.mediation.synchronization.SyncCtrlImpl">
        <constructor-arg index="0" ref="blocklistHdlr"/>
        <constructor-arg index="1" ref="ntpSchedulerImpl"/>
        <constructor-arg index="2" ref="ntpSequenceImpl"/>
    </bean>
    <bean id="syncPropertiesDAO" class="com.adva.nlms.mediation.synchronization.model.SyncPropertiesDAO"/>
    <bean class="com.adva.nlms.mediation.synchronization.SyncHandler"/>
    <bean class="com.adva.nlms.mediation.synchronization.SyncTestHandler"/>
    <bean class="com.adva.nlms.mediation.synchronization.RemoteSlavesHandler"/>
    <bean class="com.adva.nlms.mediation.synchronization.topology.SyncTopologyProvider"/>
    <bean class="com.adva.nlms.mediation.synchronization.topology.SyncTopologyGraphModelBuilder"/>
    <bean class="com.adva.nlms.mediation.synchronization.UserRestrictionProvider"/>
    <bean class="com.adva.nlms.mediation.synchronization.paging.SyncTestPageHdlrImpl"/>
    <bean class="com.adva.nlms.mediation.synchronization.paging.RoutePageHdlrImpl"/>
    <bean class="com.adva.nlms.mediation.synchronization.paging.SyncTopologyPageHdlrImpl"/>
    <bean class="com.adva.nlms.mediation.synchronization.paging.RemoteSlavePageHdlrImpl"/>
    <bean class="com.adva.nlms.mediation.synchronization.paging.RemoteSlaveDBDataProvider"/>
    <bean class="com.adva.nlms.mediation.synchronization.paging.RemoteSlaveCapacityDBDataProvider"/>
    <bean class="com.adva.nlms.mediation.synchronization.paging.RemoteSlaveCapacityPageHdlrImpl"/>
    <bean class="com.adva.nlms.mediation.synchronization.paging.RemoteSlavesConnectivityPageHdlrImpl"/>
    <bean class="com.adva.nlms.mediation.synchronization.paging.GnssReceiverPageHdlrImpl"/>
    <bean class="com.adva.nlms.mediation.synchronization.paging.BitsPortsPageHdlrImpl"/>
    <bean class="com.adva.nlms.mediation.synchronization.paging.NtpDynamicClientPageHdlrImpl"/>
    <bean class="com.adva.nlms.mediation.synchronization.paging.NtpTrackedClientPageHdlrImpl"/>
    <bean class="com.adva.nlms.mediation.synchronization.SyncManagerResource"/>
    <bean class="com.adva.nlms.mediation.synchronization.gnss.rca.controller.RcaCorrelationUpdateResource"/>
    <bean class="com.adva.nlms.mediation.synchronization.gnss.rca.controller.OnDemandRcaResource"/>
    <bean class="com.adva.nlms.mediation.synchronization.gnss.rca.controller.RcaDefaultSettingsResource"/>
    <bean class="com.adva.nlms.mediation.synchronization.gnss.rca.controller.RcaSettingsResource"/>
    <bean class="com.adva.nlms.mediation.synchronization.SyncGnssRestResource"/>
    <bean class="com.adva.nlms.mediation.synchronization.discovery.SyncRediscoveryHandler"/>
    <bean class="com.adva.nlms.mediation.synchronization.discovery.ncd.SubnetworkToNCDHandler"/>
    <bean class="com.adva.nlms.mediation.synchronization.subnetobserver.SubnetObserver" id="SubnetObserver"/>
    <bean class="com.adva.nlms.mediation.synchronization.mofacade.SubnetNotificationHdlr" init-method="init" />
    <bean class="com.adva.nlms.mediation.synchronization.gnss.GnssAssuranceRestClient">
        <constructor-arg index="2" ref="clusterAccessParams"/>
    </bean>
    <bean class="com.adva.nlms.mediation.synchronization.tpa.TpaAssuranceRestClient">
        <constructor-arg index="2" ref="clusterAccessParams"/>
    </bean>

    <bean id="gnssFirewallCtrl" class="com.adva.nlms.mediation.synchronization.gnss.firewall.GnssFirewallCtrl"/>

    <bean class="com.adva.nlms.mediation.synchronization.gnss.blocklist.GnssAssuranceBlocklistRestClient">
        <constructor-arg index="0" ref="blocklistCtrl"/>
    </bean>
    <bean id="syncDeviceUtil" class="com.adva.nlms.common.synchronization.util.SyncDeviceUtil"/>
    <bean id="blocklistCtrl" class="com.adva.nlms.mediation.synchronization.gnss.blocklist.BlocklistCtrl">
        <constructor-arg index="0" ref="blocklistHdlr"/>
        <constructor-arg index="1" ref="networkElementDAO"/>
        <constructor-arg index="2" ref="syncDeviceUtil"/>
        <constructor-arg index="3" ref="eventDBChangeHdlr"/>
    </bean>
    <bean id="subnetDAO" class="com.adva.nlms.mediation.topology.SubnetDAO"/>
    <bean id="blocklistHdlr" class="com.adva.nlms.mediation.synchronization.gnss.blocklist.BlocklistHdlr">
        <constructor-arg index="0" ref="subnetDAO"/>
        <constructor-arg index="1" ref="registeredNetworkElementsHdlr"/>
        <constructor-arg index="2" ref="messageManager"/>
    </bean>

    <bean id="clusterAccessRestParamsCtrl" class="com.adva.nlms.common.rest.clusteraccess.ClusterAccessRestParamsCtrl"/>
    <bean class="com.adva.nlms.mediation.synchronization.discovery.gnss.GnssAssuranceHdlr">
        <constructor-arg index="0" ref="clusterAccessRestParamsCtrl"/>
    </bean>
    <bean class="com.adva.nlms.mediation.synchronization.discovery.tpa.TpaAssuranceHdlr"/>
    <bean class="com.adva.nlms.mediation.synchronization.tpa.QMMaskFormModelProvider"/>

    <bean id="sjProbeCreationObserver"  class="com.adva.nlms.mediation.synchronization.moobserver.SJProbeCreationObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="sjProbeDeletionObserver" class="com.adva.nlms.mediation.synchronization.moobserver.SJProbeDeletionObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="sjProbeUpdateObserver" class="com.adva.nlms.mediation.synchronization.moobserver.SJProbeUpdateObserver">
      <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="mtieResultDeletionObserver" class="com.adva.nlms.mediation.synchronization.moobserver.MTIEResultDeletionObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="f3SyncRefDeletionObserver" class="com.adva.nlms.mediation.synchronization.moobserver.F3SyncRefDeletionObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="f3SyncRefCreationObserver" class="com.adva.nlms.mediation.synchronization.moobserver.F3SyncRefCreationObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="f3SyncRefUpdateObserver" class="com.adva.nlms.mediation.synchronization.moobserver.F3SyncRefUpdateObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="f3SyncCreationObserver" class="com.adva.nlms.mediation.synchronization.moobserver.F3SyncCreationObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="f3SyncUpdateObserver" class="com.adva.nlms.mediation.synchronization.moobserver.F3SyncUpdateObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="f3SyncDeletionObserver" class="com.adva.nlms.mediation.synchronization.moobserver.F3SyncDeletionObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="F3EthPortUpdateObserver" class="com.adva.nlms.mediation.synchronization.moobserver.F3EthPortUpdateObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="f3TimeClockUpdateObserver" class="com.adva.nlms.mediation.synchronization.moobserver.F3TimeClockUpdateObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="boundaryClockCreationObserver" class="com.adva.nlms.mediation.synchronization.moobserver.BoundaryClockCreationObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="boundaryClockDeletionObserver" class="com.adva.nlms.mediation.synchronization.moobserver.BoundaryClockDeletionObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="boundaryClockUpdateObserver" class="com.adva.nlms.mediation.synchronization.moobserver.BoundaryClockUpdateObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>

    <bean id="masterClockInterfaceDeletionObserver" class="com.adva.nlms.mediation.synchronization.moobserver.MasterClockInterfaceDeletionObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="masterClockInterfaceCreationObserver" class="com.adva.nlms.mediation.synchronization.moobserver.MasterClockInterfaceCreationObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="masterClockInterfaceUpdateObserver" class="com.adva.nlms.mediation.synchronization.moobserver.MasterClockInterfaceUpdateObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>

    <bean id="masterClockCreationObserver" class="com.adva.nlms.mediation.synchronization.moobserver.MasterClockCreationObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="masterClockUpdateObserver" class="com.adva.nlms.mediation.synchronization.moobserver.MasterClockUpdateObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="masterClockDeletionObserver" class="com.adva.nlms.mediation.synchronization.moobserver.MasterClockDeletionObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>

    <bean id="f3TimeClockRefCreationObserver" class="com.adva.nlms.mediation.synchronization.moobserver.F3TimeClockRefCreationObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="f3TimeClockRefDeletionObserver" class="com.adva.nlms.mediation.synchronization.moobserver.F3TimeClockRefDeletionObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="f3TimeClockRefUpdateObserver" class="com.adva.nlms.mediation.synchronization.moobserver.F3TimeClockRefUpdateObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>

    <bean id="NEUpdateObserver" class="com.adva.nlms.mediation.synchronization.moobserver.NEUpdateObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>

    <bean id="neCreationObserver" class="com.adva.nlms.mediation.gistransfer.NetworkElementALMCreationObserver">
        <constructor-arg index="0" ref="fbrPlantAlmHdlrChooser"/>
        <constructor-arg index="1" ref="managedObjectDAO"/>
        <constructor-arg index="2" ref="httpPropertiesService"/>
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>

    <bean id="neUpdateObserver" class="com.adva.nlms.mediation.gistransfer.NetworkElementALMUpdateObserver">
        <constructor-arg index="0" ref="fbrPlantAlmHdlrChooser"/>
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>

    <bean id="neDeletionObserver" class="com.adva.nlms.mediation.gistransfer.NetworkElementALMDeletionObserver">
        <constructor-arg index="0" ref="fbrPlantAlmHdlrChooser"/>
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>

    <bean id="sNMPPropertiesUpdateObserver" class="com.adva.nlms.mediation.synchronization.moobserver.SNMPPropertiesUpdateObserver">
        <constructor-arg index="0" ref="fbrPlantAlmHdlrChooser"/>
        <constructor-arg index="1" ref="networkElementDAO"/>
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>

    <bean id="httpPropertiesCreationObserver" class="com.adva.nlms.mediation.synchronization.moobserver.HTTPPropertiesCreationObserver">
        <constructor-arg index="0" ref="fbrPlantAlmHdlrChooser"/>
        <constructor-arg index="1" ref="networkElementDAO"/>
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>

    <bean id="httpPropertiesUpdateObserver" class="com.adva.nlms.mediation.synchronization.moobserver.HTTPPropertiesUpdateObserver">
        <constructor-arg index="0" ref="fbrPlantAlmHdlrChooser"/>
        <constructor-arg index="1" ref="networkElementDAO"/>
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>

    <bean id="gpsReceiverUpdateObserver" class="com.adva.nlms.mediation.synchronization.moobserver.GPSReceiverUpdateObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="gpsReceiverCreationObserver" class="com.adva.nlms.mediation.synchronization.moobserver.GPSReceiverCreationObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="gpsReceiverDeletionObserver" class="com.adva.nlms.mediation.synchronization.moobserver.GPSReceiverDeletionObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>

    <bean id="sOOCUpdatedObserver" class="com.adva.nlms.mediation.synchronization.moobserver.SOOCUpdatedObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>

    <bean id="ptpClockCreationObserver" class="com.adva.nlms.mediation.synchronization.moobserver.PTPClockCreationObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>

    <bean id="ptpClockDeletionObserver" class="com.adva.nlms.mediation.synchronization.moobserver.PTPClockDeletionObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>

    <bean id="ptpClockUpdateObserver" class="com.adva.nlms.mediation.synchronization.moobserver.PTPClockUpdateObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>

    <bean id="redundancyGroupCreationObserver" class="com.adva.nlms.mediation.synchronization.moobserver.RedundancyGroupCreationObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>

    <bean id="redundancyGroupDeletionObserver" class="com.adva.nlms.mediation.synchronization.moobserver.RedundancyGroupDeletionObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>

    <bean id="redundancyGroupUpdateObserver" class="com.adva.nlms.mediation.synchronization.moobserver.RedundancyGroupUpdateObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>

    <bean id="ptpPortCreationObserver" class="com.adva.nlms.mediation.synchronization.moobserver.PTPPortCreationObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>

    <bean id="ptpPortDeletionObserver" class="com.adva.nlms.mediation.synchronization.moobserver.PTPPortDeletionObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="ptpPortUpdateObserver" class="com.adva.nlms.mediation.synchronization.moobserver.PTPPortUpdateObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>

    <bean id="l3PTPPortCreationObserver" class="com.adva.nlms.mediation.synchronization.moobserver.L3PTPPortCreationObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="l3PTPPortDeletionObserver" class="com.adva.nlms.mediation.synchronization.moobserver.L3PTPPortDeletionObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="l3PTPPortUpdateObserver" class="com.adva.nlms.mediation.synchronization.moobserver.L3PTPPortUpdateObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>

    <bean id="SOOCDeletedObserver" class="com.adva.nlms.mediation.synchronization.moobserver.SOOCDeletedObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="SOOCCreatedObserver" class="com.adva.nlms.mediation.synchronization.moobserver.SOOCCreatedObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="OCSlaveCreationObserver" class="com.adva.nlms.mediation.synchronization.moobserver.OCSlaveCreationObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="OCSlaveUpdateObserver" class="com.adva.nlms.mediation.synchronization.moobserver.OCSlaveUpdateObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="OCSlaveDeletionObserver" class="com.adva.nlms.mediation.synchronization.moobserver.OCSlaveDeletionObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="OCSlavePortCreationObserver" class="com.adva.nlms.mediation.synchronization.moobserver.OCSlavePortCreationObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="OCSlavePortDeletionObserver" class="com.adva.nlms.mediation.synchronization.moobserver.OCSlavePortDeletionObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="OCSlavePortUpdateObserver" class="com.adva.nlms.mediation.synchronization.moobserver.OCSlavePortUpdateObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="TCCreatedObserver" class="com.adva.nlms.mediation.synchronization.moobserver.TCCreatedObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="TCUpdatedObserver" class="com.adva.nlms.mediation.synchronization.moobserver.TCUpdatedObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="TCDeletedObserver" class="com.adva.nlms.mediation.synchronization.moobserver.TCDeletedObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="RemoteSlaveCreationObserver" class="com.adva.nlms.mediation.synchronization.moobserver.RemoteSlaveCreationObserver">
        <!--<constructor-arg index="0" ref="registeredNetworkElementsHdlr"></constructor-arg>-->
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="RemoteSlaveDeletionObserver" class="com.adva.nlms.mediation.synchronization.moobserver.RemoteSlaveDeletionObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="RemoteSlaveUpdateObserver" class="com.adva.nlms.mediation.synchronization.moobserver.RemoteSlaveUpdateObserver">
        <!--<constructor-arg index="0" ref="registeredNetworkElementsHdlr"></constructor-arg>-->
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="L2DynamicRemoteSlaveCreationObserver" class="com.adva.nlms.mediation.synchronization.moobserver.L2RemoteSlaveCreationObserver">
        <!--<constructor-arg index="0" ref="registeredNetworkElementsHdlr"></constructor-arg>-->
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="L2DynamicRemoteSlaveDeletionObserver" class="com.adva.nlms.mediation.synchronization.moobserver.L2RemoteSlaveDeletionObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean class="com.adva.nlms.mediation.synchronization.moobserver.SyncNeDiscoveryObserver" id="syncNeDiscoveredObserver">
        <constructor-arg index="0" ref="registeredNetworkElementsHdlr"></constructor-arg>
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>


    <bean class="com.adva.nlms.mediation.synchronization.formmodel.SyncFormModelContainer"/>

    <bean id="MasterVirtualPortCreationObserver" class="com.adva.nlms.mediation.synchronization.moobserver.MasterVirtualPortCreationObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="MasterVirtualPortDeletionObserver" class="com.adva.nlms.mediation.synchronization.moobserver.MasterVirtualPortDeletionObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>

    <bean id="portBitsF3UpdateObserver" class="com.adva.nlms.mediation.synchronization.moobserver.PortBitsF3UpdateObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="bitsInEcUpdateObserver" class="com.adva.nlms.mediation.synchronization.moobserver.BitsInEcUpdateObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="bitsOutEcUpdateObserver" class="com.adva.nlms.mediation.synchronization.moobserver.BitsOutEcUpdateObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="gps10MHzPortCreationObserver" class="com.adva.nlms.mediation.synchronization.moobserver.Gps10MHzPortCreationObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="gps10MHzPortDeletionObserver" class="com.adva.nlms.mediation.synchronization.moobserver.Gps10MHzPortDeletionObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="gps10MHzPortUpdateObserver" class="com.adva.nlms.mediation.synchronization.moobserver.Gps10MHzPortUpdateObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="f3PulsePerSecondPortCreationObserver" class="com.adva.nlms.mediation.synchronization.moobserver.F3PulsePerSecondPortCreationObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="f3PulsePerSecondPortDeletionObserver" class="com.adva.nlms.mediation.synchronization.moobserver.F3PulsePerSecondPortDeletionObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="f3PulsePerSecondPortUpdateObserver" class="com.adva.nlms.mediation.synchronization.moobserver.F3PulsePerSecondPortUpdateObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="f3TimeOfDayPortUpdateObserver" class="com.adva.nlms.mediation.synchronization.moobserver.F3TimeOfDayPortUpdateObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>

    <bean id="bits16PortCardCreationObserver" class="com.adva.nlms.mediation.synchronization.moobserver.Bits16PortCardCreationObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="bits16PortCardDeletionObserver" class="com.adva.nlms.mediation.synchronization.moobserver.Bits16PortCardDeletionObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="bits16PortCardUpdateObserver" class="com.adva.nlms.mediation.synchronization.moobserver.Bits16PortCardUpdateObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="clk16PortCardCreationObserver" class="com.adva.nlms.mediation.synchronization.moobserver.Clk16PortCardCreationObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="clk16PortCardDeletionObserver" class="com.adva.nlms.mediation.synchronization.moobserver.Clk16PortCardDeletionObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="clk16PortCardUpdateObserver" class="com.adva.nlms.mediation.synchronization.moobserver.Clk16PortCardUpdateObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="ge4PortCardCreationObserver" class="com.adva.nlms.mediation.synchronization.moobserver.GE4PortCardCreationObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="ge4PortCardDeletionObserver" class="com.adva.nlms.mediation.synchronization.moobserver.GE4PortCardDeletionObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="ge4PortCardUpdateObserver" class="com.adva.nlms.mediation.synchronization.moobserver.GE4PortCardUpdateObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="pps16PortCardCreationObserver" class="com.adva.nlms.mediation.synchronization.moobserver.Pps16PortCardCreationObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="pps16PortCardDeletionObserver" class="com.adva.nlms.mediation.synchronization.moobserver.Pps16PortCardDeletionObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="pps16PortCardUpdateObserver" class="com.adva.nlms.mediation.synchronization.moobserver.Pps16PortCardUpdateObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="todPps16PortCardCreationObserver" class="com.adva.nlms.mediation.synchronization.moobserver.TodPps16PortCardCreationObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="todPps16PortCardDeletionObserver" class="com.adva.nlms.mediation.synchronization.moobserver.TodPps16PortCardDeletionObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="todPps16PortCardUpdateObserver" class="com.adva.nlms.mediation.synchronization.moobserver.TodPps16PortCardUpdateObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="ptpFlowPointCreationObserver" class="com.adva.nlms.mediation.synchronization.moobserver.PTPFlowPointCreationObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="ptpFlowPointDeletionObserver" class="com.adva.nlms.mediation.synchronization.moobserver.PTPFlowPointDeletionObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="ptpFlowPointUpdateObserver" class="com.adva.nlms.mediation.synchronization.moobserver.PTPFlowPointUpdateObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="portBits8GroupCreationObserver" class="com.adva.nlms.mediation.synchronization.moobserver.PortBits8GroupCreationObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="portBits8GroupDeletionObserver" class="com.adva.nlms.mediation.synchronization.moobserver.PortBits8GroupDeletionObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="portBits8GroupUpdateObserver" class="com.adva.nlms.mediation.synchronization.moobserver.PortBits8GroupUpdateObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="portClk16GroupUpdateObserver" class="com.adva.nlms.mediation.synchronization.moobserver.PortClk16GroupUpdateObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="portTodPps16GroupCreationObserver" class="com.adva.nlms.mediation.synchronization.moobserver.PortTodPps16GroupCreationObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="portTodPps16GroupDeletionObserver" class="com.adva.nlms.mediation.synchronization.moobserver.PortTodPps16GroupDeletionObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="portTodPps16GroupUpdateObserver" class="com.adva.nlms.mediation.synchronization.moobserver.PortTodPps16GroupUpdateObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="portPps16GroupCreationObserver" class="com.adva.nlms.mediation.synchronization.moobserver.PortPps16GroupCreationObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="portPps16GroupDeletionObserver" class="com.adva.nlms.mediation.synchronization.moobserver.PortPps16GroupDeletionObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="portPps16GroupUpdateObserver" class="com.adva.nlms.mediation.synchronization.moobserver.PortPps16GroupUpdateObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="nteF3UpdateObserver" class="com.adva.nlms.mediation.synchronization.moobserver.NteF3UpdateObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="csmF3UpdateObserver" class="com.adva.nlms.mediation.synchronization.moobserver.CsmF3UpdateObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="auxF3UpdateObserver" class="com.adva.nlms.mediation.synchronization.moobserver.AuxF3UpdateObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="syncEPGUpdateObserver" class="com.adva.nlms.mediation.synchronization.moobserver.SyncEPGUpdateObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="timeClockPGUpdateObserver" class="com.adva.nlms.mediation.synchronization.moobserver.TimeClockPGUpdateObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="ptpMCIPGUpdateObserver" class="com.adva.nlms.mediation.synchronization.moobserver.PtpMCIPGUpdateObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="ptpMCIPGCreationObserver" class="com.adva.nlms.mediation.synchronization.moobserver.PtpMCIPGCreationObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="ptpMCIPGDeletionObserver" class="com.adva.nlms.mediation.synchronization.moobserver.PtpMCIPGDeletionObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <!-- XG 210 Device Cards -->
    <bean id="ethernetXG1XCCCardCreationObserver" class="com.adva.nlms.mediation.synchronization.moobserver.EthernetXG1XCCCardCreationObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="ethernetXG1XCCCardDeletionObserver" class="com.adva.nlms.mediation.synchronization.moobserver.EthernetXG1XCCCardDeletionObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="ethernetXG1SCCCardCreationObserver" class="com.adva.nlms.mediation.synchronization.moobserver.EthernetXG1SCCCardCreationObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="ethernetXG1SCCCardDeletionObserver" class="com.adva.nlms.mediation.synchronization.moobserver.EthernetXG1SCCCardDeletionObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="ethernetGE8ECCCardCreationObserver" class="com.adva.nlms.mediation.synchronization.moobserver.EthernetGE8ECCCardCreationObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="ethernetGE8ECCCardDeletionObserver" class="com.adva.nlms.mediation.synchronization.moobserver.EthernetGE8ECCCardDeletionObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="ethernetGE8SCCCardCreationObserver" class="com.adva.nlms.mediation.synchronization.moobserver.EthernetGE8SCCCardCreationObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="ethernetGE8SCCCardDeletionObserver" class="com.adva.nlms.mediation.synchronization.moobserver.EthernetGE8SCCCardDeletionObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="ethernetGE8SCryptoCCCardCreationObserver" class="com.adva.nlms.mediation.synchronization.moobserver.EthernetGE8SCryptoCCCardCreationObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="ethernetGE8SCryptoCCCardDeletionObserver" class="com.adva.nlms.mediation.synchronization.moobserver.EthernetGE8SCryptoCCCardDeletionObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>

    <bean id="mbGNSSLCCreationObserver" class="com.adva.nlms.mediation.synchronization.moobserver.MbGNSSLCCreationObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="mbGNSSLCDeletionObserver" class="com.adva.nlms.mediation.synchronization.moobserver.MbGNSSLCDeletionObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="mbGNSSLCUpdateObserver" class="com.adva.nlms.mediation.synchronization.moobserver.MbGNSSLCUpdateObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="stlLCCreationObserver" class="com.adva.nlms.mediation.synchronization.moobserver.StlLCCreationObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="stlLCDeletionObserver" class="com.adva.nlms.mediation.synchronization.moobserver.StlLCDeletionObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="stlLCUpdateObserver" class="com.adva.nlms.mediation.synchronization.moobserver.StlLCUpdateObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="osa33xxCardCreationObserver" class="com.adva.nlms.mediation.synchronization.moobserver.OSA33xxCardCreationObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="osa33xxCardDeletionObserver" class="com.adva.nlms.mediation.synchronization.moobserver.OSA33xxCardDeletionObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="osa33xxCardUpdateObserver" class="com.adva.nlms.mediation.synchronization.moobserver.OSA33xxCardUpdateObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="compositeClockCardCreationObserver" class="com.adva.nlms.mediation.synchronization.moobserver.CompositeClockCardCreationObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="compositeClockCardDeletionObserver" class="com.adva.nlms.mediation.synchronization.moobserver.CompositeClockCardDeletionObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="compositeClockCardUpdateObserver" class="com.adva.nlms.mediation.synchronization.moobserver.CompositeClockCardUpdateObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>

    <bean id="irigCardCreationObserver" class="com.adva.nlms.mediation.synchronization.moobserver.IrigCardCreationObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="irigCardDeletionObserver" class="com.adva.nlms.mediation.synchronization.moobserver.IrigCardDeletionObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="irigCardUpdateObserver" class="com.adva.nlms.mediation.synchronization.moobserver.IrigCardUpdateObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="clkx4lpnCardCreationObserver" class="com.adva.nlms.mediation.synchronization.moobserver.Clkx4lpnCardCreationObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="clkx4lpnCardDeletionObserver" class="com.adva.nlms.mediation.synchronization.moobserver.Clkx4lpnCardDeletionObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="clkx4lpnCardUpdateObserver" class="com.adva.nlms.mediation.synchronization.moobserver.Clkx4lpnCardUpdateObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="avsCardCreationObserver" class="com.adva.nlms.mediation.synchronization.moobserver.AvsCardCreationObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="avsCardDeletionObserver" class="com.adva.nlms.mediation.synchronization.moobserver.AvsCardDeletionObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="avsCardUpdateObserver" class="com.adva.nlms.mediation.synchronization.moobserver.AvsCardUpdateObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="bitsx16ProtectedCardCreationObserver" class="com.adva.nlms.mediation.synchronization.moobserver.Bitsx16ProtectedCardCreationObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="bitsx16ProtectedCardDeletionObserver" class="com.adva.nlms.mediation.synchronization.moobserver.Bitsx16ProtectedCardDeletionObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="bitsx16ProtectedCardUpdateObserver" class="com.adva.nlms.mediation.synchronization.moobserver.Bitsx16ProtectedCardUpdateObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="clk4OutputPortGroupCreationObserver" class="com.adva.nlms.mediation.synchronization.moobserver.Clk4OutputPortGroupCreationObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="clk4OutputPortGroupDeletionObserver" class="com.adva.nlms.mediation.synchronization.moobserver.Clk4OutputPortGroupDeletionObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="clk4OutputPortGroupUpdateObserver" class="com.adva.nlms.mediation.synchronization.moobserver.Clk4OutputPortGroupUpdateObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="avsOutputPortCreationObserver" class="com.adva.nlms.mediation.synchronization.moobserver.AvsOutputPortCreationObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="avsOutputPortDeletionObserver" class="com.adva.nlms.mediation.synchronization.moobserver.AvsOutputPortDeletionObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="avsOutputPortUpdateObserver" class="com.adva.nlms.mediation.synchronization.moobserver.AvsOutputPortUpdateObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="stlModuleCreationObserver" class="com.adva.nlms.mediation.synchronization.moobserver.StlModuleCreationObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="stlModuleDeletionObserver" class="com.adva.nlms.mediation.synchronization.moobserver.StlModuleDeletionObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="stlModuleUpdateObserver" class="com.adva.nlms.mediation.synchronization.moobserver.StlModuleUpdateObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>

    <bean id="irigOutputPortsCreationObserver" class="com.adva.nlms.mediation.synchronization.moobserver.IrigOutputPortsCreationObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="irigOutputPortsDeletionObserver" class="com.adva.nlms.mediation.synchronization.moobserver.IrigOutputPortsDeletionObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="irigOutputPortsUpdateObserver" class="com.adva.nlms.mediation.synchronization.moobserver.IrigOutputPortsUpdateObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>

    <bean id="compositeClockGroupCreationObserver" class="com.adva.nlms.mediation.synchronization.moobserver.CompositeClockPortsCreationObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="compositeClockGroupDeletionObserver" class="com.adva.nlms.mediation.synchronization.moobserver.CompositeClockPortsDeletionObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="compositeClockGroupUpdateObserver" class="com.adva.nlms.mediation.synchronization.moobserver.CompositeClockPortsUpdateObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>

    <bean id="f3PrcUpdateObserver" class="com.adva.nlms.mediation.synchronization.moobserver.F3PrcUpdateObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>


<!--    F4 Device MO Observers-->
    <bean id="F4EthPortUpdateObserver" class="com.adva.nlms.mediation.synchronization.moobserver.F4EthPortUpdateObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>

    <bean id="f3SystemClockUpdateObserver" class="com.adva.nlms.mediation.synchronization.moobserver.F3SystemClockUpdateObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>

</beans>