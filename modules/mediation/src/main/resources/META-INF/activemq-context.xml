<!--
    Licensed to the Apache Software Foundation (ASF) under one or more
    contributor license agreements.  See the NOTICE file distributed with
    this work for additional information regarding copyright ownership.
    The ASF licenses this file to You under the Apache License, Version 2.0
    (the "License"); you may not use this file except in compliance with
    the License.  You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.
-->
<beans  xmlns="http://www.springframework.org/schema/beans"
        xmlns:amq="http://activemq.apache.org/schema/core"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
        http://activemq.apache.org/schema/core
        http://activemq.apache.org/schema/core/activemq-core.xsd">

    <!-- Allows us to use system properties as variables in this configuration file -->
    <bean class="org.springframework.beans.factory.config.PropertyPlaceholderConfigurer">
        <property name="locations">
            <list>
                <value>file:fnm.properties</value>
            </list>
        </property>
    </bean>

    <broker xmlns="http://activemq.apache.org/schema/core" brokerName="${jms.brokerName}"
            dataDirectory="${activemq.data}"
            persistent="${activemq.persistent}"
            useJmx="${activemq.useJMX}" advisorySupport="true" dedicatedTaskRunner="false">

        <destinationPolicy>
            <policyMap>
                <policyEntries>
                    <policyEntry topic=">" producerFlowControl="false" memoryLimit="128mb" prioritizedMessages="false" expireMessagesPeriod="7000"
                                 optimizedDispatch="true" lazyDispatch="false">
                        <pendingSubscriberPolicy>
                            <vmCursor/>
                        </pendingSubscriberPolicy>
                        <subscriptionRecoveryPolicy>
                            <timedSubscriptionRecoveryPolicy recoverDuration="360000" />
                        </subscriptionRecoveryPolicy>
                    </policyEntry>
                    <policyEntry queue=">" producerFlowControl="false" memoryLimit="128mb" prioritizedMessages="false" expireMessagesPeriod="7000"
                                 optimizedDispatch="true" lazyDispatch="false">
                        <pendingQueuePolicy>
                            <vmQueueCursor/>
                        </pendingQueuePolicy>
                        <subscriptionRecoveryPolicy>
                            <timedSubscriptionRecoveryPolicy recoverDuration="360000" />
                        </subscriptionRecoveryPolicy>
                    </policyEntry>
                </policyEntries>
            </policyMap>
        </destinationPolicy>

        <systemUsage>
            <systemUsage>
                <memoryUsage>
                    <memoryUsage limit="200 mb"/>
                </memoryUsage>
                <storeUsage>
                    <storeUsage limit="2 gb"/>
                </storeUsage>
                <tempUsage>
                    <tempUsage limit="5000 mb"/>
                </tempUsage>
            </systemUsage>
        </systemUsage>

        <transportConnectors>
            <transportConnector name="openwire" uri="${jms.transportProtocol}://${jms.url}:${jms.port}?${jms.additional.args}"/>
        </transportConnectors>

    </broker>
</beans>
