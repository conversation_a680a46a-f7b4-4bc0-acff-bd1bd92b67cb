<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright 2023 Adtran Networks SE. All rights reserved.
  ~
  ~ Owner: benjamint
  -->

<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:util="http://www.springframework.org/schema/util"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
        http://www.springframework.org/schema/context
        http://www.springframework.org/schema/context/spring-context-3.0.xsd
        http://www.springframework.org/schema/util
        http://www.springframework.org/schema/util/spring-util.xsd">

    <context:annotation-config/>
    <bean class="com.adva.eod.mediator.ni.rest.server.NiRestNIMediatorConfiguration"/>

    <import resource="classpath*:META-INF/com/adva/nlms/mediation/common/persistence/persistence-common-config-context.xml"/>

    <import resource="classpath*:META-INF/com/adva/enc/profile/snmp/impl/config/profile-snmp-config.xml" />

    <bean class="com.adva.nlms.mediation.LauncherBasedInfrastructureStartup" />

    <bean id="configNotificationOriginDeterminer" class="com.adva.nlms.mediation.config.ConfigNotificationOriginDeterminer">
        <constructor-arg ref="registeredNetworkElementsHdlr" />
    </bean>

    <!--<bean class="com.adva.nlms.mediation.cluster.ClusterManager" />-->

    <bean id="dependencyFactory" class="com.adva.nlms.mediation.server.spring.DependencyFactory"/>
    <bean class="com.adva.nlms.mediation.config.factory.ArtifactFactoryImpl" factory-method="getInstance"/>
    <bean class="com.adva.nlms.mediation.event.EventCtrlImpl"/>
    <bean id="approvalProxySpringConfiguration" class="com.adva.nlms.approvalproxy.configuration.ApprovalProxySpringConfiguration"/>
    <bean id="ApprovalNotificationConsumerConfig" class="com.adva.nlms.approvalproxy.ApprovalNotificationConsumerConfig"/>

    <bean class="com.adva.nlms.mediation.sound.SoundRestHdlr" />
    <bean class="com.adva.nlms.mediation.redundancy.HaClusterStatusMonitor">
        <constructor-arg ref="mdMessageSender"/>
        <constructor-arg ref="serverStateImpl"/>
        <constructor-arg ref="haController"/>
    </bean>
    <bean class="com.adva.nlms.mediation.redundancy.AsyncDBAlarmRaising"/>
    <bean class="com.adva.nlms.mediation.server.ServerCtrlImpl"/>
    <bean id="jmxMonitor" class="com.adva.nlms.mediation.server.JMXMonitor"/>
    <bean class="com.adva.nlms.mediation.polling.PollingCtrlImpl"/>
    <bean class="com.adva.nlms.mediation.config.MapViewConfigurationRestCtrl" />
    <bean class="com.adva.nlms.mediation.config.CustomFieldsConfigurationCtrl">
    </bean>
    <bean class="com.adva.nlms.mediation.config.TopologyConfigurationRestCtrl"/>
    <bean id="configCtrl" name="configCtrl, domainPollingManagerOwner" class="com.adva.nlms.mediation.config.ConfigCtrlImpl"/>
    <bean class="com.adva.nlms.mediation.config.GlobalPollingManagerProvider"/>
    <bean id="protectionStatusChangeNotifier"  class="com.adva.nlms.mediation.config.protection.ProtectionStatusChangeNotifier"/>
    <bean id="configDAO" class="com.adva.nlms.mediation.config.ConfigDAO"/>
    <bean class="com.adva.nlms.mediation.config.ConfigCtrlPollingPerformer">
        <!--<property name="pmCsvExport" value="#{performanceContext.pmCsvExport}"/>-->
        <property name="pmCsvExport" value="#{pmCsvExport}"/>
    </bean>

    <bean class="com.adva.nlms.mediation.event.EventSeverityHandler"/>
    <bean class="com.adva.nlms.mediation.event.EventSeverityDAO"/>
    <bean class="com.adva.nlms.mediation.event.EventCtrlRestHdlr"/>
    <bean class="com.adva.nlms.mediation.event.EventNbiCtrlImpl"/>
    <bean class="com.adva.nlms.mediation.topology.NetworkDiscoveryTool"/>
    <bean id="subnetHdlrImpl" class="com.adva.nlms.mediation.topology.SubnetHdlrImpl"/>
    <bean id="subnetReadOnlyRepositoryImpl" class="com.adva.nlms.mediation.topology.SubnetReadOnlyRepositoryImpl"/>
    <bean class="com.adva.nlms.mediation.topology.SubnetController"/>
    <bean id="topLevelSubnetHdlr" class="com.adva.nlms.mediation.topology.TopLevelSubnetHdlr"/>
    <bean id="networkElementImporter" class="com.adva.nlms.mediation.topology.consolidation.NetworkElementImporterImpl"/>
    <bean id="imageDBImplHandler" class="com.adva.nlms.mediation.config.util.custom.ImageDBImplHandler"/>
    <bean class="com.adva.nlms.mediation.importExport.TopologyFacade"/>
    <bean id = "evtProcCtrlImpl" class="com.adva.nlms.mediation.evtProc.EvtProcCtrlImpl"/>
    <bean id="serviceManagerCtrlImpl" class="com.adva.nlms.mediation.sm.ServiceManagerCtrlImpl"/>
    <bean id="serviceManagerRestResource" class="com.adva.nlms.mediation.sm.ServiceManagerRestResource"/>
    <bean id="serviceManagerEnableDisableCPCtrl" class="com.adva.nlms.mediation.sm.ServiceManagerEnableDisableCPCtrlImpl"/>
    <bean id="connectivityServiceCtrlImpl" class="com.adva.nlms.mediation.sm.ConnectivityServiceCtrlImpl"/>
    <bean id="connectivityServiceHelper" class="com.adva.nlms.mediation.sm.ConnectivityServiceHelper"/>
    <bean id="connectivityServiceDeleteHelper" class="com.adva.nlms.mediation.sm.ConnectivityServiceDeleteHelper"/>
    <bean id="connectivityServiceRestClient" class="com.adva.nlms.mediation.sm.ConnectivityServiceRestClient"/>
    <bean id="serviceMessageManager" class="com.adva.nlms.mediation.sm.ServiceMessageManager"/>
    <bean id="serviceGrapLayer" class="com.adva.nlms.mediation.sm.ServiceGraphLayer"/>
    <bean class="com.adva.nlms.mediation.sm.bandwidthrestriction.BandwidthRestrictionDAO"/>
    <bean class="com.adva.nlms.mediation.sm.capability.CapabilityBrokerRestResource"/>
    <bean class="com.adva.nlms.mediation.sm.ethcrypto.SMEthernetCryptoManagerImpl"/>
    <bean class="com.adva.nlms.mediation.sm.ethcrypto.MonitoringFormModelProvider"/>
    <bean class="com.adva.nlms.mediation.sm.ethcrypto.KeyExchangeFormModelProvider"/>
    <bean class="com.adva.nlms.mediation.sm.ethcrypto.KeyExchangeReadOnlyModelProvider"/>
    <bean class="com.adva.nlms.mediation.config.f3.connectguard.EthEncryptionSettingsImpl"/>
    <bean class="com.adva.nlms.mediation.config.driver.rest.DriverRestFacade"/>
    <!--<bean class="com.adva.nlms.mediation.config.driver.remote.RemoteDriverRegistrationService"/>-->
    <bean class="com.adva.nlms.mediation.monitoring.MonitoringCtrlImpl"/>
    <bean class="com.adva.nlms.mediation.monitoring.overview.MonitorOverviewHandlerImpl"/>
    <bean class="com.adva.nlms.mediation.monitoring.syslogging.SysLogger"/>
    <bean class="com.adva.nlms.mediation.ethNEConfig.EthNEConfigCtrlImpl"/>
    <bean class="com.adva.nlms.mediation.guidata.UserDataCtrlImpl"/>
    <bean class="com.adva.nlms.mediation.guidata.PDUserSessionDataHandler"/>
    <bean class="com.adva.nlms.mediation.smapp.nbi.rest.ServiceProvisioningEndpoint"/>
    <bean class="com.adva.nlms.mediation.smapp.nbi.rest.ServiceDraftEndpoint"/>
    <bean class="com.adva.nlms.mediation.smapp.nbi.rest.SatActivationHandler" />
    <bean class="com.adva.nlms.mediation.smapp.nbi.rest.SatResultsHandler" />
    <bean class="com.adva.nlms.mediation.smapp.nbi.rest.CfmMonitoringHandler" />
    <bean id="securityCtrl" class="com.adva.nlms.mediation.security.SecurityCtrlImpl"/>
    <bean id="securityRestResource" class="com.adva.nlms.mediation.security.SecurityRestResource"/>
    <bean id="roleActionRestResource" class="com.adva.nlms.mediation.security.RoleActionRestResource"/>
    <bean class="com.adva.nlms.mediation.security.api.authorization.PermissionChecker"/>
    <bean id="authorizationAspect" class="com.adva.nlms.infrastucture.security.permission.api.AuthorizationAspect" factory-method="aspectOf"/>
    <bean id="withLimitsAspect" class="com.adva.nlms.mediation.infrastructure.security.sabotage.impl.WithLimitsAspect" factory-method="aspectOf"/>

    <bean class="com.adva.nlms.mediation.mtosi.MtosiCtrlImpl"/>
    <bean class="com.adva.nlms.mediation.config.mtosi.MtosiImplFactory" factory-method="getInstance"/>

    <bean id="mdMessageSender" class="com.adva.nlms.mediation.server.MDMessageSender"/>
    <bean id="messagePropertiesSender" class="com.adva.nlms.mediation.server.kafka.messages.MessagePropertiesSenderAdapter"/>
    <bean class="com.adva.nlms.mediation.server.MultiServerConnectorImpl"/>

    <bean id="networkTopologyProvider" class="com.adva.nlms.mediation.topology.NetworkTopologyProvider" />
    <bean id="moduleSpringConfiguration"  class="com.adva.nlms.mediation.infrastructure.server_modules.impl.ModuleSpringConfiguration"/>
    <bean id="ethernetCustomerGroupTopologyProvider" class="com.adva.nlms.mediation.topology.ethernet.customer.EthernetCustomerGroupTopologyProvider" />

    <bean class="com.adva.nlms.mediation.topology.NetworkTopologyGraphModelBuilder" />


    <bean class="com.adva.nlms.mediation.manual.NmsManualResource" />

    <bean id="registeredTreeNodesHdlr" class="com.adva.nlms.mediation.topology.RegisteredTreeNodesHdlr" factory-method="getInstance"/>


    <bean id="networkElementHdlr" class="com.adva.nlms.mediation.config.NetworkElementHdlrImpl"/>
    <bean id="networkElementRestResource"  class="com.adva.nlms.mediation.config.NetworkElementRestResource"/>
    <bean id="linePropertiesEditorHdlr" class="com.adva.nlms.mediation.topology.lineproperties.LinePropertiesEditorHdlrImpl"/>
    <bean id="lineDetailsHdlr" class="com.adva.nlms.mediation.topology.lineproperties.LineDetailsHdlrImpl"/>

    <bean class="com.adva.nlms.mediation.topology.lineproperties.vlan.VLanForLinkHdlr"/>

    <bean id="networkElementInventoryResource" class="com.adva.nlms.mediation.config.NetworkElementInventoryResource"/>
    <bean id="topologyNodeHdlr" class="com.adva.nlms.mediation.topology.TopologyNodeHdlrImpl"/>
    <bean id="serviceVisibilityHandler" class="com.adva.nlms.mediation.topology.ServiceVisibilityHandlerImpl"/>
    <bean class="com.adva.nlms.mediation.topology.SubnetworkPlannerExportService"/>
    <bean id="topologyNodeRestResource" class="com.adva.nlms.mediation.topology.TopologyNodeRestResource"/>
    <bean id="topologyManager" class="com.adva.nlms.mediation.topology.TopologyManagerImpl"/>
    <bean id="topologyEodManager" class="com.adva.nlms.mediation.topology.eod.TopologyEodManagerImpl"/>
    <bean id="topologyCoreManager" class="com.adva.nlms.mediation.topology.core.TopologyCoreManagerImpl"/>
    <bean id="topologyControllerCoreConfig" class="com.adva.topology.manager.adapters.rest.server.TopologyControllerConfig"/>
    <bean id="serviceIntentManager" class="com.adva.nlms.mediation.sm.serviceintent.eod.ServiceIntentManager"/>
    <bean id="cpcManagerRestServerConfig" class="com.adva.cpc.manager.adapters.rest.server.CpcManagerRestServerConfig"/>
    <bean id="cpcManagerConfig" class="com.adva.nlms.mediation.ext.cpc.CpcManagerConfig"/>
    <bean class="com.adva.nlms.mediation.topology.TopologyController"/>
    <bean class="com.adva.nlms.mediation.topology.eod.TopologyEodController"/>
    <bean class="com.adva.nlms.mediation.topology.eod.NetworkElementHandler"/>
    <bean class="com.adva.nlms.mediation.sm.serviceintent.eod.ServiceIntentController"/>
    <bean id="ethernetRingRestResource" class="com.adva.nlms.mediation.topology.ethernet.ring.api.rest.EthernetRingRestResource"/>
    <bean class="com.adva.nlms.mediation.topology.ethernet.ring.business.EthernetRingControllerImpl"/>
    <bean class="com.adva.nlms.mediation.topology.ethernet.ring.business.associatedobject.EthernetRingAssociatedObjectsOperation"/>
    <bean class="com.adva.nlms.mediation.topology.ethernet.ring.business.associatedobject.EthernetRingAssociatedObjectControllerImpl"/>
    <bean class="com.adva.nlms.mediation.topology.ethernet.ring.business.DefaultBandwidthCalculator"/>
    <bean class="com.adva.nlms.mediation.topology.ethernet.ring.business.BandwidthHelper"/>
    <bean class="com.adva.nlms.mediation.topology.ethernet.ring.business.EthernetRingDataHelperImpl"/>
    <bean id="ethernetRingGroupRestResource" class="com.adva.nlms.mediation.topology.ethernet.ring.api.rest.EthernetRingGroupRestResource"/>
    <bean class="com.adva.nlms.mediation.topology.ethernet.ring.business.EthernetRingGroupControllerImpl"/>
    <bean class="com.adva.nlms.mediation.topology.TopologyDiscoveryManagerImpl"/>
    <bean id="lineHdlr" class="com.adva.nlms.mediation.topology.LineHdlrImpl"/>
    <bean class="com.adva.nlms.mediation.topology.HasLinkLightCheck" id="HasLinkLightCheck"/>
    <bean class="com.adva.nlms.mediation.topology.HasLinkLightCheckAdvanced" id="HasLinkLightCheckAdvanced"/>
    <bean class="com.adva.nlms.mediation.topology.ExtLayersHdlr" init-method="init" />
    <bean id="lineDao" class="com.adva.nlms.mediation.topology.LineDao"/>
    <bean class="com.adva.nlms.mediation.topology.LinesRepositoryImpl" />
    <bean class="com.adva.nlms.mediation.smapp.crm.LinkResourcesImpl"/>
    <bean id="graphNodeLocationDao" class="com.adva.nlms.mediation.topology.GraphNodeLocation.GraphNodeLocationDao"/>
    <bean class="com.adva.nlms.mediation.topology.VirtualOtnNodeHdlr" init-method="init" />
    <bean class="com.adva.nlms.mediation.topology.VirtualOtnNodePageHdlrImpl" />

    <bean class="com.adva.nlms.mediation.common.paging.PagingRestHandler" />

    <bean class="com.adva.nlms.mediation.topology.ProtLineHelperImpl"/>
    <bean class="com.adva.nlms.mediation.topology.LineRestHdlr" />
    <bean class="com.adva.nlms.mediation.topology.LinkTrafficRestHdlr" />
    <bean class="com.adva.nlms.mediation.topology.LinkValidator" />
    <bean class="com.adva.nlms.mediation.sm.bandwidthrestriction.BandwidthRestrictionValidator" />
    <bean class="com.adva.nlms.mediation.sm.bandwidthrestriction.BandwidthRestrictionHelper" />
    <bean class="com.adva.nlms.mediation.sm.bandwidthrestriction.BandwidthRestrictionCtrlImpl" />
    <bean class="com.adva.nlms.mediation.da.DataAccessUnmanagedPortService" />
    <bean class="com.adva.nlms.mediation.da.DataAccessCrossConnectService" />
    <bean class="com.adva.nlms.mediation.da.DataAccessHandoverPortService" />
    <bean class="com.adva.nlms.mediation.sm.ethcrypto.KeyExchangeService" />
    <bean class="com.adva.nlms.mediation.sm.ethcrypto.SecureFlowService" />
    <bean class="com.adva.nlms.mediation.sm.ethcrypto.SecureFlowDownMepService" />

    <bean id="snmpFacade" class="com.adva.nlms.mediation.ne_comm.SNMPFacadeImpl" />
    <bean id="discoveryFacade" class="com.adva.nlms.mediation.config.discovery.DiscoveryFacadeImpl" />
    <bean class="com.adva.nlms.mediation.ne_comm.discovery.NetworkElementDiscoveryRestController"/>
    <bean class="com.adva.nlms.mediation.ApplicationContextProvider"/>
    <bean id="statusNotificationBroker" class="com.adva.nlms.mediation.server.StatusNotificationBrokerImpl">

    </bean>
    <bean id="logHeaderLookupImpl" class="com.adva.nlms.mediation.monitoring.logging.MonitoringLogHeader" lazy-init="true"/>
    <bean class="com.adva.nlms.mediation.monitoring.server.ServerMonitoringRestHdlr"/>
    <bean class="com.adva.nlms.mediation.monitoring.server.FiberMonitor"/>
    <bean class="com.adva.nlms.mediation.monitoring.server.DcnMonitor"/>
    <bean class="com.adva.nlms.mediation.monitoring.server.ServiceMonitor"/>
    <bean class="com.adva.nlms.mediation.monitoring.server.DeviceMonitor"/>
    <bean class="com.adva.nlms.mediation.monitoring.server.AlarmMonitor"/>
    <bean class="com.adva.nlms.mediation.monitoring.rest.MBeanRESTHandler"/>
    <bean class="com.adva.nlms.mediation.importExport.ImportExportRestHdlr"/>
    <bean class="com.adva.nlms.mediation.importExport.ImportExportJsonFile"/>
    <bean class="com.adva.nlms.mediation.importExport.ImportExportTopologyHdlr"/>
    <bean class="com.adva.nlms.mediation.importExport.sm.ImportExportServiceHandler"/>
    <bean class="com.adva.nlms.mediation.importExport.sm.ImportExportServiceTreeHdlr"/>

    <!-- dao -->
    <bean class="com.adva.nlms.mediation.topology.TopologyNodeDAO"/>

    <!-- utils -->
    <bean class="com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils"/>
    <bean class="com.adva.nlms.mediation.mtosi.v2.utils.ServiceUtils"/>
    <bean class="com.adva.nlms.mediation.pca.PCAManagerImpl"/>
    <bean class="com.adva.nlms.mediation.pca.SetPasswordOnNEsScheduledPollingCommand">
        <qualifier type="com.adva.nlms.mediation.polling.DomainPolling"/>
    </bean>

    <bean class="com.adva.nlms.mediation.housekeeping.serverproperties.DisplaySettingsRestResource"/>
    <bean id="dbObjectFactoryF3" class="com.adva.nlms.mediation.config.f3.entity.factory.impl.DBObjectFactoryF3Impl"/>
    <bean id="dbObjectFactoryEGM" class="com.adva.nlms.mediation.config.ovn.DBObjectFactoryEGMImpl"/>
    <bean id="dbObjectFactoryFSP150CM" class="com.adva.nlms.mediation.config.fsp150cm.entity.factory.impl.DBObjectFactoryFSP150CM"/>
    <bean id="dbObjectFactory" class="com.adva.nlms.mediation.config.entity.factory.NEDBObjectFactory"/>

    <bean class="com.adva.nlms.mediation.topology.formmodel.NetworkElementFormModelContainer"/>
    <bean class="com.adva.nlms.mediation.fam.FAMCtrlImpl" id="famCtrl"/>

    <bean class="com.adva.nlms.mediation.config.userlabels.LabelManager" />
    <!-- context imports -->
    <import resource="mediation-properties-context.xml"/>
    <import resource="server/mediation-server-context.xml"/>
    <import resource="mediation-messaging-context.xml"/>
    <import resource="redundancy/mediation-redundancy-context.xml"/>
    <import resource="mediation-config-model-context.xml"/>
    <import resource="polling/mediation-polling-context.xml"/>
    <import resource="security/mediation-security-context.xml"/>
    <import resource="sm/mediation-sm-context.xml"/>
    <import resource="event/mediation-event-context.xml"/>
    <import resource="config/mediation-mo-context.xml"/>
    <import resource="evtProc/mediation-evtproc-context.xml"/>
    <import resource="performance/mediation-performance-context.xml"/>
    <import resource="housekeeping/nebackup/mediation-nebackup-context.xml"/>
    <import resource="mediation-ftp-context.xml"/>
    <import resource="ne_comm/mediation-necomm-context.xml"/>
    <import resource="report/mediation-rfw-context.xml"/>
    <import resource="clientUpdate/mediation-clientupdate-context.xml"/>
    <import resource="synchronization/mediation-sync-context.xml"/>
    <import resource="classpath*:META-INF/mtosi/common/mtosisupport/mediation-mtosi-context.xml"/>
    <import resource="search/mediation-search-context.xml"/>

    <import resource="housekeeping/swupgrade/mediation-swupgrade-context.xml"/>
    <import resource="neResources/mediation-neresources-context.xml"/>
    <import resource="ethNEConfig/mediation-ethneconfig-context.xml"/>
    <import resource="fam/mediation-fam-context.xml"/>

    <bean id="fiberDirectorConfiguration" class="com.adva.apps.efd.fiberdirector.FiberDirectorConfiguration"/>
    <bean id="coreConfiguration" class="com.adva.apps.efd.core.CoreConfiguration"/>
    <bean id="cuaConfiguration" class="com.adva.nlms.mediation.cua.CUAConfiguration"/>

    <import resource="classpath*:META-INF/device-inventory-context.xml"/>

    <import resource="classpath*:META-INF/tfw-context.xml" />
    <import resource="thresholdCrossingAlert/tca-context.xml"/>
    <import resource="unsupne/unsupported-ne-context.xml"/>
    <import resource="mltopologymodel/mediation-mltopology-context.xml"/>
    <import resource="classpath*:META-INF/mediation-pdtopology-context.xml"/>
    <bean id="pdl3IpvpnServiceConfiguration" class="com.adva.packet_layer3.ipvpnsm.ipvpnservice.PDL3IPVPNServiceConfiguration"/>
    <bean id="pdl3IpvpnServiceRestConfiguration" class="com.adva.packet_layer3.ipvpnsm.adapters.rest.PDL3IPVPNServiceRestConfiguration"/>
    <bean id="pdl3IpvpnServiceDatabaseConfiguration" class="com.adva.packet_layer3.ipvpnsm.adapters.persistence.adapter.PDL3IPVPNServiceDatabaseConfiguration"/>
    <bean id="pdl3IpvpnServiceMediationConfiguration" class="com.adva.packet_layer3.ipvpnsm.adapters.mediation.adapter.PDL3IPVPNServiceMediationConfiguration"/>
    <import resource="smapp/mediation-smapp-context.xml"/>
    <!-- MO notification manager -->
    <import resource="messaging/inf/impl/notification-manager-context.xml"/>
    <import resource="notifications/mediation-internal-message-context.xml"/>
    <import resource="gistransfer/mediation-gistransfer-context.xml"/>
    <import resource="properties/mediation-properties-context.xml"/>



    <bean class="com.adva.nlms.mediation.polling.metric.PollingAvgDelayNotifierTimer" scope="singleton">
        <constructor-arg name="mdMessageSender" ref="mdMessageSender"/>
    </bean>

    <bean class="com.adva.nlms.mediation.config.fsp_r7.topology.discovery.LinkMismatchAlarmsHelper">
        <constructor-arg name="eventProcFacade" ref="eventProcFacadeImpl"/>
        <constructor-arg name="lineDao" ref="lineDao"/>
    </bean>

    <!-- Rest Common RestExceptionMapper -->
    <bean class="com.adva.nlms.mediation.common.rest.providers.MDRestExceptionMapper" scope="singleton"/>
    <bean class="com.adva.nlms.mediation.common.rest.providers.MDSdnRestExceptionMapper" scope="singleton"/>
    <bean class="com.adva.nlms.mediation.common.rest.providers.MDRestAuthenicationFilterExceptionMapper" scope="singleton"/>
    <bean class="com.adva.nlms.mediation.common.rest.providers.AsyncRestApiExceptionMapper" scope="singleton"/>
    <bean class="com.adva.nlms.mediation.common.rest.providers.EthernetServiceProvExceptionMapper" scope="singleton" />

    <bean class="com.adva.nlms.mediation.common.persistence.EntityManagerProviderImpl" id="entityManagerProvider"/>
    <bean class="com.adva.nlms.mediation.common.persistence.PersistenceHelperImpl"/>
    <bean class="com.adva.nlms.mediation.common.persistence.MDPersistenceTransactionImpl" id="mdPersistenceTransaction"/>

    <bean lazy-init="true" class="com.adva.nlms.mediation.topology.arc.ArcStateCsvTaskRunner">
        <constructor-arg type="int" name="numberOfMainThreads">
            <value>1</value>
        </constructor-arg>
        <constructor-arg type="int" name="numberOfExecutorThreads">
            <value>4</value>
        </constructor-arg>
        <constructor-arg type="int" name="timeoutInMinutes">
            <value>60</value>
        </constructor-arg>
    </bean>
    <bean scope="prototype" class="com.adva.nlms.mediation.topology.arc.ArcStateCsvTask"/>

    <bean id="lineUpdateObserver" class="com.adva.nlms.mediation.topology.LineUpdateObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>

    <bean id="crmLinkCreateObserver" class="com.adva.nlms.mediation.smapp.crm.link.CrmLinkCreateObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="crmLinkUpdateObserver" class="com.adva.nlms.mediation.smapp.crm.link.CrmLinkUpdateObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean id="crmLinkDeleteObserver" class="com.adva.nlms.mediation.smapp.crm.link.CrmLinkDeleteObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>

    <bean id="changedLineAssignmentEvent" class="com.adva.nlms.mediation.topology.ChangedLineAssignmentEvent"/>

    <bean class="com.adva.nlms.mediation.usernotification.UserNotificationManagerConfiguration" lazy-init="true"/>

    <bean class="com.adva.nlms.mediation.usernotification.UserNotificationDAO"/>

    <import resource="./topology/mediation-topology-context.xml" />

    <import resource="monitoring/metrics/mediation-metrics-export-context.xml" />

    <import resource="classpath*:*-nms-ext.xml" />

    <bean id="keyStore" class="com.adva.nlms.mediation.ssl.KeyStore"/>

    <bean id="sslConfiguration" class="com.adva.nlms.mediation.ssl.SSLConfiguration"/>

    <bean id="sslConfigurationUpdateObserver" class="com.adva.nlms.mediation.ssl.SSLConfigurationUpdateObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>

    <!-- JAVA17_MIGRATION: Moved to ypdb-java17-bridge module which translates the YP JPA 2.x interfaces to JPA 3.x
    <import resource="./yp-server-context.xml"/>
    -->
    <import resource="./yp-server-bridge-context.xml"/>

    <import resource="./reload/mediation-reload-context.xml" />

    <import resource="./ec/mediation-ec-context.xml" />

    <import resource="./f8/mediation-f8-context.xml" />

    <import resource="./f4/mediation-f4-context.xml" />

    <import resource="ni/mediation-ni-context.xml"/>

    <import resource="profile/mediation-profile-snmp-context.xml"/>

    <import resource="bean-provider/bean-context.xml"/>

    <import resource="server-state/bean-context.xml"/>

    <import resource="classpath*:META-INF/power-level-context.xml"/>

    <import resource="classpath*:META-INF/sm-tag-persistence-context.xml"/>

    <import resource="classpath*:META-INF/sm-tag-impl-context.xml"/>

    <import resource="classpath*:META-INF/sm-core-impl-context.xml"/>

    <import resource="classpath*:META-INF/sm-core-adapters-context.xml"/>

    <import resource="classpath*:META-INF/report-context.xml"/>

    <import resource="classpath*:META-INF/security-impl-context.xml"/>

    <import resource="classpath*:META-INF/health-center-impl-context.xml"/>

    <import resource="classpath*:META-INF/streaming-telemetry-impl-context.xml"/>

    <import resource="classpath*:META-INF/thirdparty-ptp-topology-impl-context.xml"/>

    <import resource="classpath*:META-INF/ntp-rest-server-context.xml"/>

    <import resource="classpath*:META-INF/ntp-impl-context.xml"/>

    <import resource="classpath*:META-INF/rest-infra-context.xml"/>

    <import resource="classpath*:META-INF/rest-nbi-impl-context.xml"/>

    <import resource="classpath*:META-INF/cp-ese-context.xml"/>

    <import resource="classpath*:META-INF/sm-migration-impl-context.xml"/>

    <import resource="log4jplugins/bean-context.xml"/>
    <import resource="classpath*:META-INF/com/adva/nlms/db/consolidation/tool/config/db-consolidation-tool-config-context.xml"/>
    <import resource="classpath*:META-INF/com/adva/nlms/inf/config/inf-config-context.xml"/>

    <context:component-scan base-package="com.adva.nlms.mediation.test.mo2csm.api" use-default-filters="false" >
        <context:include-filter type="annotation" expression="org.springframework.stereotype.Component"/>
    </context:component-scan>

    <bean id="capabilitiesRegistry" name="capabilitiesRegistry" class="com.adva.nlms.mediation.common.capabilities.VolatileCapabilitiesRegistry"/>
    <bean id = "capabilitiesDefaultsProvider" class="com.adva.nlms.common.capabilities.CapabilitiesDefaultsProvider"/>
    <bean id = "mediationCapabilitiesDefaultsProvider" class="com.adva.nlms.common.capabilities.CapabilitiesDefaultsProvider">
        <constructor-arg  name="packageToScan" value ="com.adva.nlms.mediation.common.capabilities" />
    </bean>
    <bean class="com.adva.nlms.common.capabilities.CapabilitiesInitializerProvider"/>
    <bean class="com.adva.nlms.mediation.common.capabilities.FNMPropertiesCapabilitiesProvider"/>

    <bean name="networkElementCapabilityHdlr" class="com.adva.nlms.mediation.config.capability.NetworkElementCapabilitiesHdlr"/>

    <!-- NE Version Providers. Define here different implementations and add their ids into the neVersionProvidersMap defined below.
     The unsupportedNetworkElementsHandler will choose the implementation based on the NeTypeId integer, retrieved by the NetworkElementDBIMpl#getNetworkElementType. -->
    <bean id="genericNeVersionProvider" class="com.adva.nlms.mediation.topology.unsupne.GenericNeVersionProvider"/>
    <bean id="osa54CRNeVersionProvider" class="com.adva.nlms.mediation.topology.unsupne.OSA54CRNeVersionProvider"/>


    <bean id="unsupportedNetworkElementsHandler" class="com.adva.nlms.mediation.topology.UnsupportedNetworkElementsHandlerImpl">
        <property name="neVersionProvidersMap">
            <map key-type="java.lang.Integer" value-type="com.adva.nlms.mediation.topology.unsupne.NeVersionProvider">
                <entry>
                    <key>
                        <util:constant static-field="com.adva.nlms.common.config.netypes.NeTypeIds.NETWORK_ELEMENT_TYPE_ANY"/>
                    </key>
                    <ref bean="genericNeVersionProvider"/>
                </entry>
                <entry>
                    <key>
                        <util:constant static-field="com.adva.nlms.common.config.netypes.NeTypeIds.NETWORK_ELEMENT_TYPE_OSA5430"/>
                    </key>
                    <ref bean="osa54CRNeVersionProvider"/>
                </entry>
                <entry>
                    <key>
                        <util:constant static-field="com.adva.nlms.common.config.netypes.NeTypeIds.NETWORK_ELEMENT_TYPE_OSA5440"/>
                    </key>
                    <ref bean="osa54CRNeVersionProvider"/>
                </entry>
            </map>
        </property>
    </bean>
    <bean id="unsupportedNetworkElementDAO" class="com.adva.nlms.mediation.config.UnsupportedNetworkElementDAO"/>
    <bean id="assignedSnmpProfileViewHdlrImpl" class="com.adva.nlms.mediation.profile.snmp.paging.AssignedSnmpProfileViewHdlrImpl"/>
    <bean class="com.adva.nlms.mediation.profile.snmp.paging.AssignedSnmpProfilePagingService"/>
    <bean class="com.adva.nlms.mediation.config.polling.configuration.StatusAndConfigPollingUtilFactory" />
    <bean class="com.adva.nlms.mediation.config.DataManagerImplReflectionFactory" factory-method="getInstance"/>
    <bean class="com.adva.nlms.mediation.config.NetTransactionDomainContextFacade"/>
    <bean class="com.adva.nlms.mediation.config.fsp_r7.F7EntityFacadeFactory"/>
    <bean class="com.adva.nlms.mediation.common.concurrent.MDFixedThreadPoolFacade"/>
    <bean class="com.adva.nlms.mediation.common.util.MODefinitionHelperFacadeFactory"/>
    <bean id="serverRestResource" class="com.adva.nlms.mediation.server.ServerRestResource" />
    <bean class="com.adva.nlms.mediation.server.DBCredentialsUpdater"/>
    <bean class="com.adva.nlms.mediation.server.DatabaseDumpUtil"/>

    <import resource="classpath*:META-INF/polling-context.xml"/>
    <import resource="classpath*:META-INF/planner-export-context.xml"/>

    <bean id="mdRestCustomAuthMechanism" class="com.adva.nlms.mediation.common.rest.authorization.MDRestCustomAuthMechanism"/>
    <bean id="mdRestBasicAuthMechanism" class="com.adva.nlms.mediation.common.rest.authorization.MDRestBasicAuthMechanism"/>
    <bean id="mdRestTokenAuthMechanism" class="com.adva.nlms.mediation.common.rest.authorization.MDRestTokenAuthMechanism"/>
    <bean id="mdRestAuthHandler" class="com.adva.nlms.mediation.common.rest.authorization.MDRestAuthHandler">
        <property name="authMechanisms">
            <list value-type="com.adva.nlms.mediation.common.rest.authorization.MDRestAuthenticationMechanism">
                <ref bean="mdRestCustomAuthMechanism"/>
                <ref bean="mdRestBasicAuthMechanism"/>
                <ref bean="mdRestTokenAuthMechanism"/>
            </list>
        </property>
    </bean>
    <bean id="mdRestAuthenticationFilter" class="com.adva.nlms.mediation.common.rest.MDRestAuthenticationFilter" />

    <bean id="supportedNeTypeVersionSpringConfig" class="com.adva.nlms.mediation.swver.impl.SupportedNeTypeVersionSpringConfig"/>

    <bean id="mdPersistenceConfiguration" class="com.adva.nlms.mediation.common.persistence.MDPersistenceConfiguration"/>

    <bean class="com.adva.nlms.mediation.ec.neComm.rest.ConnectionManagerProvider"/>

    <import resource="layer/apps/mediation-layer-apps-ethsm-context.xml"/>
    <import resource="classpath*:META-INF/com/adva/infrastructure/capability/provider/capability-provider-context.xml" />
    <import resource="classpath*:META-INF/com/adva/eod/capabilitybroker/capability-broker-context.xml" />
    <import resource="classpath*:META-INF/com/adva/eod/capabilitybroker/rest/server/capability-broker-rest-context.xml" />
    <import resource="classpath*:META-INF/com/adva/infrastructure/capabilityprovider/client/capability-provider-rest-client-context.xml" />
    <import resource="classpath*:META-INF/com/adva/eod/po/impl/provisioning-orchestrator-context.xml" />
    <import resource="classpath*:META-INF/com/adva/eod/po/rest/provisioning-orchestrator-rest-context.xml" />
    <import resource="classpath*:META-INF/com/adva/eod/po/kafka/provisioning-orchestrator-kafka-context.xml" />
    <import resource="classpath*:META-INF/com/adva/nlms/opticalrouter/impl/optical-router-driver.xml" />
    <import resource="classpath*:META-INF/com/adva/nlms/opticalrouter/kafka/optical-router-kafka.xml" />
    <import resource="classpath*:META-INF/com/adva/nlms/opticalrouter/rest/server/optical-router-rest-server.xml" />
    <import resource="classpath*:META-INF/com/adva/nlms/opticalrouter/rest/server/optical-router-driver-rest-client.xml" />
    <import resource="classpath*:META-INF/com/adva/nlms/opticalrouter/persistence/optical-router-persistence.xml" />
    <import resource="classpath*:META-INF/com/adva/nlms/txprovisioning/impl/txprovisioning-context.xml"/>
</beans>
