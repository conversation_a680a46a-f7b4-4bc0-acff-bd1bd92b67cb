/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: krzemek
 */
package com.adva.nlms.common.mltopologymodel.pce;

import com.adva.nlms.common.mltopologymodel.intent.implementation.MLServiceIntentBean;
import com.adva.nlms.common.mltopologymodel.pce.exceptions.MLCrossConnectionCreationException;
import org.apache.commons.lang3.ObjectUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public class MLCrossConnectionDTO implements StateMachineBean, MLServiceIntentBean {

    //list of FP

    private List<MLConnPointDTO> connPointDTOList; // list of FP to be provisioned
    private int mlNodeId; //id or IP
    private int customerId;
    private String serviceName;
    private String alias;
    private Integer customIndex;
    private int intentDbId;
    private boolean isSingleNodeService;
    private int existingServiceId;
    private int groupId;
    private String remarks;
    private String alternativeServiceName;

    public MLCrossConnectionDTO() {
    }

    public MLCrossConnectionDTO(MLConnPointDTO connPointDTO1, MLConnPointDTO connPointDTO2) throws Exception {
        if (connPointDTO1.getMlMeId() != connPointDTO2.getMlMeId()) {
            throw new MLCrossConnectionCreationException(
                    "CrossConnection between different devices is not possible neID1= " + connPointDTO1.getMlMeId() +
                            ", neId2= " + connPointDTO2.getMlMeId());
        }
        this.connPointDTOList = Arrays.asList(connPointDTO1, connPointDTO2);
        this.mlNodeId = connPointDTO1.getMlMeId();
    }

    public MLCrossConnectionDTO(List<MLConnPointDTO> mlConnPointDTOS) throws MLCrossConnectionCreationException {
        if (mlConnPointDTOS.size() >1){
            if (mlConnPointDTOS.stream().map(MLConnPointDTO::getMlMeId).collect(Collectors.toSet()).size() != 1) {
                List<String> mlNeIdsWithPorts = mlConnPointDTOS
                        .stream()
                        .map(mlConnPointDTO -> "mlNeId:" + mlConnPointDTO.getMlMeId()
                                + " port: " + mlConnPointDTO.getMlPortLocation() + "\n")
                        .collect(Collectors.toList());
                throw new MLCrossConnectionCreationException
                        ("CrossConnection between different devices is not possible: \n" + mlNeIdsWithPorts);
            }
        }
        this.connPointDTOList = new ArrayList<>(mlConnPointDTOS);
        this.mlNodeId = mlConnPointDTOS.get(0).getMlMeId();
    }

    public List<MLConnPointDTO> getConnPointDTOList() {
        return connPointDTOList;
    }

    public void setConnPointDTOList(List<MLConnPointDTO> connPointDTOList) {
        this.connPointDTOList = connPointDTOList;
    }

    public int getMlNodeId() {
        return mlNodeId;
    }

    public void setMlNodeId(int mlNodeId) {
        this.mlNodeId = mlNodeId;
    }

    public int getCustomerId() {
        return customerId;
    }

    public void setCustomerId(int customerId) {
        this.customerId = customerId;
    }

    public String getServiceName() {
        return serviceName;
    }

    public void setServiceName(String serviceName) {
        this.serviceName = serviceName;
    }

    @Override
    public String toString() {
        return "MLCrossConnectionDTO{" +
                "connPointDTOList=" + connPointDTOList +
                ", mlNodeId=" + mlNodeId +
                ", customerId='" + customerId + '\'' +
                ", serviceName='" + serviceName + '\'' +
                '}';
    }

    @Override
    public void saveIntentDbId(int intentDbId) {
        this.intentDbId = intentDbId;
    }

    @Override
    public int retrieveIntentDbId() {
        return intentDbId;
    }

    public void setAlias(final String alias) { this.alias=alias; }

    public String getAlias(){
        return ObjectUtils.defaultIfNull(alias, "");
    }

    public Integer getCustomIndex() { return customIndex; }

    public void setCustomIndex(Integer customIndex) { this.customIndex = customIndex; }

    public boolean isSingleNodeService() {
        return this.isSingleNodeService;
    }

    public void setSingleNodeService(boolean singleNodeService) {
        this.isSingleNodeService = singleNodeService;
    }

    public int getExistingServiceId() { return existingServiceId; }

    public void setExistingServiceId(int existingServiceId) { this.existingServiceId = existingServiceId; }

    public int getGroupId() { return groupId; }

    public void setGroupId(int groupId) { this.groupId = groupId; }

    public String getRemarks() { return remarks; }

    public void setRemarks(String remarks) { this.remarks = remarks; }

    public String getAlternativeServiceName() { return alternativeServiceName; }

    public void setAlternativeServiceName(String alternativeServiceName) { this.alternativeServiceName = alternativeServiceName; }
}
