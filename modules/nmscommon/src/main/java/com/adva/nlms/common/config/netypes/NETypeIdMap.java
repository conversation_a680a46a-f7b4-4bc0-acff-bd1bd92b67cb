/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: astec
 */

package com.adva.nlms.common.config.netypes;

import java.util.HashMap;
import java.util.Map;

public class NETypeIdMap {
    private final Map<String,Integer> valueByName;

    @SuppressWarnings("java:S138")
    public  NETypeIdMap() {
        valueByName = new HashMap<>();
        valueByName.put("NETWORK_ELEMENT_TYPE_ANY", NeTypeIds.NETWORK_ELEMENT_TYPE_ANY);
        valueByName.put("NETWORK_ELEMENT_TYPE_FSP1500", NeTypeIds.NETWORK_ELEMENT_TYPE_FSP1500);
        valueByName.put("NETWORK_ELEMENT_TYPE_FSP3000C", NeTypeIds.NETWORK_ELEMENT_TYPE_FSP3000C);
        valueByName.put("NETWORK_ELEMENT_TYPE_FSP_R7", NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_R7);
        valueByName.put("NETWORK_ELEMENT_TYPE_OTS1000", NeTypeIds.NETWORK_ELEMENT_TYPE_OTS1000);
        valueByName.put("NETWORK_ELEMENT_TYPE_FSP_150CC", NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150CC);
        valueByName.put("NETWORK_ELEMENT_TYPE_FSP_150CP", NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150CP);
        valueByName.put("NETWORK_ELEMENT_TYPE_FSP_150MX", NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150MX);
        valueByName.put("NETWORK_ELEMENT_TYPE_FSP_150CM", NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150CM);
        valueByName.put("NETWORK_ELEMENT_TYPE_FSP_150CM_CPMR", NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150CM_CPMR);
        valueByName.put("NETWORK_ELEMENT_TYPE_HN4000", NeTypeIds.NETWORK_ELEMENT_TYPE_HN4000);
        valueByName.put("NETWORK_ELEMENT_TYPE_HN400", NeTypeIds.NETWORK_ELEMENT_TYPE_HN400);
        valueByName.put("NETWORK_ELEMENT_TYPE_HN400_STANDALONE", NeTypeIds.NETWORK_ELEMENT_TYPE_HN400_STANDALONE);
        valueByName.put("NETWORK_ELEMENT_TYPE_FSP_GE206", NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE206);
        valueByName.put("NETWORK_ELEMENT_TYPE_FSP_GE206F", NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE206F);
        valueByName.put("NETWORK_ELEMENT_TYPE_FSP_GE206V", NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE206V);
        valueByName.put("NETWORK_ELEMENT_TYPE_FSP_GE201", NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE201);
        valueByName.put("NETWORK_ELEMENT_TYPE_FSP_GE201SE", NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE201SE);
        valueByName.put("NETWORK_ELEMENT_TYPE_FSP_GE112", NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE112);
        valueByName.put("NETWORK_ELEMENT_TYPE_FSP_GE112PRO", NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE112PRO);
        valueByName.put("NETWORK_ELEMENT_TYPE_FSP_GE112PRO_M", NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE112PRO_M);
        valueByName.put("NETWORK_ELEMENT_TYPE_FSP_GE112PRO_H", NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE112PRO_H);
        valueByName.put("NETWORK_ELEMENT_TYPE_FSP_GE104", NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE104);
        valueByName.put("NETWORK_ELEMENT_TYPE_FSP_GE104E", NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE104E);
        valueByName.put("NETWORK_ELEMENT_TYPE_FSP_GE114", NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE114);
        valueByName.put("NETWORK_ELEMENT_TYPE_FSP_GE114S", NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE114S);
        valueByName.put("NETWORK_ELEMENT_TYPE_FSP_GE114SH", NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE114SH);
        valueByName.put("NETWORK_ELEMENT_TYPE_FSP_GE114H", NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE114H);
        valueByName.put("NETWORK_ELEMENT_TYPE_FSP_GE114PH", NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE114PH);
        valueByName.put("NETWORK_ELEMENT_TYPE_FSP_GE114PRO", NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE114PRO);
        valueByName.put("NETWORK_ELEMENT_TYPE_FSP_GO102PRO_S", NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GO102PRO_S);
        valueByName.put("NETWORK_ELEMENT_TYPE_FSP_GO102PRO_SP", NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GO102PRO_SP);
        valueByName.put("NETWORK_ELEMENT_TYPE_FSP_GE102PRO_H", NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE102PRO_H);
        valueByName.put("NETWORK_ELEMENT_TYPE_FSP_GO102PRO_SM", NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GO102PRO_SM);
        valueByName.put("NETWORK_ELEMENT_TYPE_FSP_GE114PRO_C", NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE114PRO_C);
        valueByName.put("NETWORK_ELEMENT_TYPE_FSP_GE114PRO_SH", NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE114PRO_SH);
        valueByName.put("NETWORK_ELEMENT_TYPE_FSP_GE114PRO_CSH", NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE114PRO_CSH);
        valueByName.put("NETWORK_ELEMENT_TYPE_FSP_GE114PRO_HE", NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE114PRO_HE);
        valueByName.put("NETWORK_ELEMENT_TYPE_FSP_GE114G", NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE114G);
        valueByName.put("NETWORK_ELEMENT_TYPE_FSP_GE101PRO", NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE101PRO);
        valueByName.put("NETWORK_ELEMENT_TYPE_SYNC_PROB", NeTypeIds.NETWORK_ELEMENT_TYPE_SYNC_PROB);
        valueByName.put("NETWORK_ELEMENT_TYPE_JUNIPER_MX", NeTypeIds.NETWORK_ELEMENT_TYPE_JUNIPER_MX);
        valueByName.put("NETWORK_ELEMENT_TYPE_SYMMETRICOM", NeTypeIds.NETWORK_ELEMENT_TYPE_SYMMETRICOM);
        valueByName.put("NETWORK_ELEMENT_TYPE_FSP_150EGX", NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150EGX);
        valueByName.put("NETWORK_ELEMENT_TYPE_F3_EFM", NeTypeIds.NETWORK_ELEMENT_TYPE_F3_EFM);
        valueByName.put("NETWORK_ELEMENT_TYPE_FSP_XG210", NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_XG210);
        valueByName.put("NETWORK_ELEMENT_TYPE_FSP_XG210C", NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_XG210C);
        valueByName.put("NETWORK_ELEMENT_TYPE_FSP_XG116", NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_XG116);
        valueByName.put("NETWORK_ELEMENT_TYPE_FSP_XG116H", NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_XG116H);
        valueByName.put("NETWORK_ELEMENT_TYPE_FSP_XG118PROSH", NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_XG118PROSH);
        valueByName.put("NETWORK_ELEMENT_TYPE_FSP_XG118PROACSH", NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_XG118PROACSH);
        valueByName.put("NETWORK_ELEMENT_TYPE_FSP_XG120", NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_XG120);
        valueByName.put("NETWORK_ELEMENT_TYPE_FSP_XG120PROSH", NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_XG120PROSH);
        valueByName.put("NETWORK_ELEMENT_TYPE_UNMANAGED_NETWORK", NeTypeIds.NETWORK_ELEMENT_TYPE_UNMANAGED_NETWORK);
        valueByName.put("NETWORK_ELEMENT_TYPE_T1804", NeTypeIds.NETWORK_ELEMENT_TYPE_T1804);
        valueByName.put("NETWORK_ELEMENT_TYPE_T3204", NeTypeIds.NETWORK_ELEMENT_TYPE_T3204);
        valueByName.put("NETWORK_ELEMENT_TYPE_UNMANAGED", NeTypeIds.NETWORK_ELEMENT_TYPE_UNMANAGED);
        valueByName.put("NETWORK_ELEMENT_TYPE_OSA5400SM", NeTypeIds.NETWORK_ELEMENT_TYPE_OSA5400SM);
        valueByName.put("NETWORK_ELEMENT_TYPE_OSA5400TC", NeTypeIds.NETWORK_ELEMENT_TYPE_OSA5400TC);
        valueByName.put("NETWORK_ELEMENT_TYPE_OSA5401XG", NeTypeIds.NETWORK_ELEMENT_TYPE_OSA5401XG);
        valueByName.put("NETWORK_ELEMENT_TYPE_OSA5401", NeTypeIds.NETWORK_ELEMENT_TYPE_OSA5401);
        valueByName.put("NETWORK_ELEMENT_TYPE_OSA5405I", NeTypeIds.NETWORK_ELEMENT_TYPE_OSA5405I);
        valueByName.put("NETWORK_ELEMENT_TYPE_OSA5405O", NeTypeIds.NETWORK_ELEMENT_TYPE_OSA5405O);
        valueByName.put("NETWORK_ELEMENT_TYPE_OSA5405MB", NeTypeIds.NETWORK_ELEMENT_TYPE_OSA5405MB);
        valueByName.put("NETWORK_ELEMENT_TYPE_OSA5405P", NeTypeIds.NETWORK_ELEMENT_TYPE_OSA5405P);
        valueByName.put("NETWORK_ELEMENT_TYPE_OSA5405S", NeTypeIds.NETWORK_ELEMENT_TYPE_OSA5405S);
        valueByName.put("NETWORK_ELEMENT_TYPE_OSA5331", NeTypeIds.NETWORK_ELEMENT_TYPE_OSA5331);
        valueByName.put("NETWORK_ELEMENT_TYPE_OSA5411", NeTypeIds.NETWORK_ELEMENT_TYPE_OSA5411);
        valueByName.put("NETWORK_ELEMENT_TYPE_OSA5412", NeTypeIds.NETWORK_ELEMENT_TYPE_OSA5412);
        valueByName.put("NETWORK_ELEMENT_TYPE_OSA5420", NeTypeIds.NETWORK_ELEMENT_TYPE_OSA5420);
        valueByName.put("NETWORK_ELEMENT_TYPE_OSA5421", NeTypeIds.NETWORK_ELEMENT_TYPE_OSA5421);
        valueByName.put("NETWORK_ELEMENT_TYPE_OSA5422", NeTypeIds.NETWORK_ELEMENT_TYPE_OSA5422);
        valueByName.put("NETWORK_ELEMENT_TYPE_OSA5410XG", NeTypeIds.NETWORK_ELEMENT_TYPE_OSA5410XG);
        valueByName.put("NETWORK_ELEMENT_TYPE_OSA5430", NeTypeIds.NETWORK_ELEMENT_TYPE_OSA5430);
        valueByName.put("NETWORK_ELEMENT_TYPE_OSA5440", NeTypeIds.NETWORK_ELEMENT_TYPE_OSA5440);
        valueByName.put("NETWORK_ELEMENT_TYPE_OSA_SOFTSYNC", NeTypeIds.NETWORK_ELEMENT_TYPE_OSA_SOFTSYNC);
        valueByName.put("NETWORK_ELEMENT_TYPE_ALM", NeTypeIds.NETWORK_ELEMENT_TYPE_ALM);
        valueByName.put("NETWORK_ELEMENT_TYPE_OSA5548C_SSU60", NeTypeIds.NETWORK_ELEMENT_TYPE_OSA5548C_SSU60);
        valueByName.put("NETWORK_ELEMENT_TYPE_OSA5548C_SSU200", NeTypeIds.NETWORK_ELEMENT_TYPE_OSA5548C_SSU200);
        valueByName.put("NETWORK_ELEMENT_TYPE_OSA5548C_TSG60", NeTypeIds.NETWORK_ELEMENT_TYPE_OSA5548C_TSG60);
        valueByName.put("NETWORK_ELEMENT_TYPE_OSA5548C_TSG200", NeTypeIds.NETWORK_ELEMENT_TYPE_OSA5548C_TSG200);
        valueByName.put("NETWORK_ELEMENT_TYPE_OSA5335_PTPGM", NeTypeIds.NETWORK_ELEMENT_TYPE_OSA5335_PTPGM);
        valueByName.put("NETWORK_ELEMENT_TYPE_OSA3350", NeTypeIds.NETWORK_ELEMENT_TYPE_OSA3350);
        valueByName.put("NETWORK_ELEMENT_TYPE_OSA3300", NeTypeIds.NETWORK_ELEMENT_TYPE_OSA3300);
        valueByName.put("NETWORK_ELEMENT_TYPE_OSA3200", NeTypeIds.NETWORK_ELEMENT_TYPE_OSA3200);
        valueByName.put("NETWORK_ELEMENT_TYPE_OSA3250", NeTypeIds.NETWORK_ELEMENT_TYPE_OSA3250);
        valueByName.put("NETWORK_ELEMENT_TYPE_OSA3230B", NeTypeIds.NETWORK_ELEMENT_TYPE_OSA3230B);
        valueByName.put("NETWORK_ELEMENT_TYPE_FSP_150EGM2", NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150EGM2);
        valueByName.put("NETWORK_ELEMENT_TYPE_FSP_150EGM4", NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150EGM4);
        valueByName.put("NETWORK_ELEMENT_TYPE_FSP_150EGM8", NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150EGM8);
        valueByName.put("NETWORK_ELEMENT_TYPE_FSP_XG304", NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_XG304);
        valueByName.put("NETWORK_ELEMENT_TYPE_FSP_XG304f", NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_XG304f);
        valueByName.put("NETWORK_ELEMENT_TYPE_FSP_XG304u", NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_XG304u);
        valueByName.put("NETWORK_ELEMENT_TYPE_FSP_XG304uf", NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_XG304uf);
        valueByName.put("NETWORK_ELEMENT_TYPE_FSP_XG308", NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_XG308);
        valueByName.put("NETWORK_ELEMENT_TYPE_FSP_XG308f", NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_XG308f);
        valueByName.put("NETWORK_ELEMENT_TYPE_FSP_XG312f", NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_XG312f);
        valueByName.put("NETWORK_ELEMENT_TYPE_FSP_Z4806", NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_Z4806);
        valueByName.put("NETWORK_ELEMENT_TYPE_FSP_XG480", NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_XG480);
        valueByName.put("NETWORK_ELEMENT_TYPE_FSP_XG404", NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_XG404);
        valueByName.put("NETWORK_ELEMENT_TYPE_FSP_XG418", NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_XG418);
        valueByName.put("NETWORK_ELEMENT_TYPE_FSP_XG118PROCSH", NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_XG118PROCSH);
        valueByName.put("NETWORK_ELEMENT_TYPE_OSA_PROXY", NeTypeIds.NETWORK_ELEMENT_TYPE_OSA_PROXY);
        valueByName.put("NETWORK_ELEMENT_TYPE_F8", NeTypeIds.NETWORK_ELEMENT_TYPE_F8);
        valueByName.put("NETWORK_ELEMENT_TYPE_CUSTOM_GNSS_XS", NeTypeIds.NETWORK_ELEMENT_TYPE_CUSTOM_GNSS_XS);
        valueByName.put("NETWORK_ELEMENT_TYPE_CUSTOM_GNSS_S", NeTypeIds.NETWORK_ELEMENT_TYPE_CUSTOM_GNSS_S);
        valueByName.put("NETWORK_ELEMENT_TYPE_CUSTOM_GNSS_M", NeTypeIds.NETWORK_ELEMENT_TYPE_CUSTOM_GNSS_M);
        valueByName.put("NETWORK_ELEMENT_TYPE_CUSTOM_GNSS_XL", NeTypeIds.NETWORK_ELEMENT_TYPE_CUSTOM_GNSS_XL);
        valueByName.put("NETWORK_ELEMENT_TYPE_CUSTOM_PTP_BC", NeTypeIds.NETWORK_ELEMENT_TYPE_CUSTOM_PTP_BC);
        valueByName.put("NETWORK_ELEMENT_TYPE_FSP_XG108", NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_XG108);
        valueByName.put("NETWORK_ELEMENT_TYPE_FSP_XG108H", NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_XG108H);
        valueByName.put("NETWORK_ELEMENT_TYPE_FSP_XG108SH", NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_XG108SH);
        valueByName.put("NETWORK_ELEMENT_TYPE_FSP_XO106", NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_XO106);
        valueByName.put("NETWORK_ELEMENT_TYPE_FSP_XJ128SH", NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_XJ128SH);
        valueByName.put("CUSTOM_DEVICES_THRESHHOLD", NeTypeIds.CUSTOM_DEVICES_THRESHHOLD);
        valueByName.put("OPTICAL_ROUTER_THRESHOLD", NeTypeIds.OPTICAL_ROUTER_THRESHOLD);
    }

    public int getIdByName(String name) {
        return valueByName.getOrDefault(name, NeTypeIds.NETWORK_ELEMENT_TYPE_ANY);
    }
}
