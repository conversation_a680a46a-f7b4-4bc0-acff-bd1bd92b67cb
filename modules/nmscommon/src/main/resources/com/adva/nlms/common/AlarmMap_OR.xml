<?xml version="1.0" ?>
<alarms xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="AlarmMap.xsd">
    <category name="opticalrouter-event">
        <alarm>
            <name>Loss Of Signal</name>
            <short-name>lossOfSignal</short-name>
            <message/>
            <severity-working>CRITICAL</severity-working>
            <severity-protecting>MINOR</severity-protecting>
            <severity-no-service>NE_DEFINED</severity-no-service>
            <trap>
                <ne-type>OPTICAL_ROUTER</ne-type>
                <alarm-id>15000</alarm-id>
                <raise-clear>
                    <name>remove</name>
                    <number>15001</number>
                </raise-clear>
            </trap>
        </alarm>
        <alarm>
            <name>Rx loss of signal alarm: on</name>
            <short-name>rxLossOfSignal</short-name>
            <message/>
            <severity-working>CRITICAL</severity-working>
            <severity-protecting>MINOR</severity-protecting>
            <severity-no-service>NE_DEFINED</severity-no-service>
            <trap>
                <ne-type>OPTICAL_ROUTER</ne-type>
                <alarm-id>15002</alarm-id>
                <raise-clear>
                    <name>remove</name>
                    <number>15003</number>
                </raise-clear>
            </trap>
        </alarm>
        <alarm>
            <name>Tx loss of signal alarm: on</name>
            <short-name>txLossOfSignal</short-name>
            <message/>
            <severity-working>CRITICAL</severity-working>
            <severity-protecting>MINOR</severity-protecting>
            <severity-no-service>NE_DEFINED</severity-no-service>
            <trap>
                <ne-type>OPTICAL_ROUTER</ne-type>
                <alarm-id>15004</alarm-id>
                <raise-clear>
                    <name>remove</name>
                    <number>15005</number>
                </raise-clear>
            </trap>
        </alarm>
    </category>
</alarms>