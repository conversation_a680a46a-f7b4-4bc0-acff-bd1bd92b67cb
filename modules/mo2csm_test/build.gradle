/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: tomaszw
 */

// independent MO 2 CSM (ML Topology) test module
plugins {
    id 'com.adva.gradle.plugin.aspectj-weaver'
}

setupModule(project)

aspectjWeave {
    sourceSets = [ "main" ]

    if (briefOutput) {
        lintLevel = 'ignore'
    }
}

dependencies {

    implementation modep( mod_nmscommon )
    implementation modep( mod_global_security_api )
    implementation modep( mod_rest_api )
    implementation modep( mod_rest_infra )
    implementation modep( mod_property )
    implementation modep( mod_mediation )
    implementation libs.slf4j.api
    implementation libs.jakarta.annotation.api
    implementation libs.jersey.container.servlet
    implementation modep( mod_persistence_common )
    implementation modep( mod_net_transactions )
    implementation modep(mod_security_api)

    implementation libs.spring.beans
    implementation libs.spring.context
    implementation libs.jakarta.ws.rs.api
    implementation libs.aspectjtools
    implementation libs.aspectjrt

    testImplementation libs.bundles.junit
    testImplementation modep( mod_test_utils )
    testImplementation libs.jakarta.inject

    aspectjpath modep(mod_global_security_api)
}
